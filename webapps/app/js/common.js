/**
 * 权限
 */

const PERMISSION_CODE_WARNING_MSG = 'WPSS01';
const PERMISSION_CODE_FLOW = 'WFSS01';


function selectedThenBack(searchType, urid, name) {
    sessionStorage.setItem(searchType, JSON.stringify({urid: urid, name: name}));
    sessionStorage.setItem("history.back", true);
    history.back();
}

function validateForm(formObj) {
    var files = formObj.find("input");
    var validate = true;
    files.each(function () {
        if ($(this).attr("require") || $(this).attr("require") === "true") {
            if (!$(this).val()) {
                alert($(this).attr("placeholder"));
                validate = false;
            }
        }
        if ($(this).attr("type") && $(this).attr("type").toLowerCase() === "number") {

            if ($(this).val() && !isNumber($(this).val())) {
                alert($(this).attr("placeholder") + ";保证格式正确");
                validate = false;
            }
        }

    });
    return validate;
}

function isNumber(val) {
    try {
        return !isNaN(Number(val));
    } catch (e) {
        return false;
    }
}

function getPayState(state) {
    switch (state) {
        case "1":
            return "未支付";
        case "2":
            return "已支付";
        case "3":
            return "支付失败";
        case "4":
            return "支付中";
        case "5":
            return "未知";
        case "6":
            return "退票";
        case "7":
            return "部分";
    }
    return state;
}

window.alert = function (name) {
    var iframe = document.createElement("IFRAME");
    iframe.style.display = "none";
    iframe.setAttribute("src", 'data:text/plain,');
    document.documentElement.appendChild(iframe);
    window.frames[0].window.alert(name);
    iframe.parentNode.removeChild(iframe);
};

/**
 * ajax请求，依赖jquery及weui
 * @param requestUrl 请求地址
 * @param params 1, simple:true-请求地址绝对路径，false-本地请求地址相对路径; 2, method:POST,GET
 * @param callback 回调方法
 */
function ajaxRequest(requestUrl, params, callback) {

    var url;
    if (params.simpleUrl) {
        url = requestUrl;
    } else {
        url = getContextPath() + requestUrl
    }

    var thisMethod = 'POST';
    if (params.method) {
        thisMethod = params.method;
    }


    if (!params.loginTag) {
        params.tag = 'ats';
    }
    $.ajax({
        // 是否使用缓存
        cache: true,
        // http请求方式
        type: thisMethod,
        // 请求url
        url: url,
        // 请求参数
        data: params,
        // 是否异步请求
        async: true,
        // 返回数据格式
        dataType: 'json',
        // 请求失败回调
        error: function (e) {
            $.hideLoading();
            if (e.message === '已经完成') {
                alert("请求完成");
                return;
            }
            $.toast(e.message, "cancel");

        },
        // 请求成功回调
        success: function (data) {
            $.hideLoading();
            if (data === null) {
                $.toast("数据获取失败", "cancel");
            }
            if (data.successful === true || 'true' === data.successful) {
                // 登录成功
                callback(safeJson(data));
            } else {
                // 登录异常
                $.toast(safeJson(data).message, "cancel");
            }
        }
    });
}

// 遍历解析Json
function safeJson(jsonObj) {

    var jsonStr = JSON.stringify(jsonObj);
    jsonStr = jsonStr.replace(/</g, '&lt;').replace(/>/g, '&gt;');

    for (let word of banWords) {
        jsonStr = jsonStr.replace(new RegExp(word, "gm"), "***");

    }
    console.log(jsonStr);

    return JSON.parse(jsonStr);

}

function ajaxRequestWithFailCallback(requestUrl, params, callback, fail) {

    var url;
    if (params.simpleUrl) {
        url = requestUrl;
    } else {
        url = getContextPath() + requestUrl
    }

    var thisMethod = 'POST';
    if (params.method) {
        thisMethod = params.method;
    }


    params.tag = 'ats';
    $.ajax({
        // 是否使用缓存
        cache: true,
        // http请求方式
        type: thisMethod,
        // 请求url
        url: url,
        // 请求参数
        data: params,
        // 是否异步请求
        async: true,
        // 返回数据格式
        dataType: 'json',
        // 请求失败回调
        error: function (e) {
            $.hideLoading();
            $.toast(e.message, "forbidden");


        },
        // 请求成功回调
        success: function (data) {
            $.hideLoading();
            if (data.successful === true || 'true' === data.successful) {
                callback(safeJson(data));
            } else {
                fail(safeJson(data));

            }
        }
    });
}

function getContextPath() {
    var pathName = document.location.pathname;
    var index = pathName.substr(1).indexOf("/");
    return pathName.substr(0, index + 1);
}


const TODO_TAG = 1;
const DONE_TAG = 2;
const iconImg = {
    ZHSQ: 'icon_account_apply.png',
    ZJFK: 'icon_single_payment_apply.png',
    ZJFKBATCH: 'icon_batch_payment_apply.png',
    XBSQ: 'icon_zjxb_apply.png',
    ZJJH: 'icon_zjjh_apply.png',
    JHTZ: 'icon_jhtz_apply.png',
    ZJSH: 'fund_collect_apply.png',
    ZJXB: 'icon_fund_dial.png',
    ZJDBSQ: 'icon_fund_transfer.png',
    NBJKSQ: 'icon_inner_loan.png',
    RZHKSQ: 'icon_financing_repayment.png',
    NBZHSQ: 'icon_inner_account_apply.png',
    WTFKSQ: 'icon_delegate_payment.png',
    NBJKFK: 'icon_inner_loan_pay.png',
    NBJKHK: 'icon_inner_loan_repay.png',
    NBDQCKZR: 'icon_fixed_deposit_into.png',
    NBDQCKZC: 'icon_fixed_deposit_out.png',
    GYLRZSQ: 'icon_supply_chain_financing.png',
    YESXSQ: 'icon_balance_limit.png',
    ZHWHSSSQ: 'icon_account_apply.png',
    ZJJHHZ: 'icon_zjjh_apply.png',
    SXSQ: 'icon_balance_limit.png',
    DKSQ: 'icon_financing_repayment.png',
    TQHKSQ: 'icon_jhtz_apply.png',
    DKZQSQ: 'icon_zjjh_apply.png',
    PKCL: 'icon_zjjh_apply.png',
    NBQPFKSQ: 'icon_single_payment_apply.png',


};

const iconImgPoint = {
    ZHSQ: 'icon_account_apply_point.png',
    ZJFK: 'icon_single_payment_apply_point.png',
    ZJFKBATCH: 'icon_batch_payment_apply_point.png',
    XBSQ: 'icon_zjxb_apply_point.png',
    ZJJH: 'icon_zjjh_apply_point.png',
    JHTZ: 'icon_jhtz_apply_point.png',
    ZJSH: 'fund_collect_apply_point.png',
    ZJXB: 'icon_fund_dial_point.png',
    ZJDBSQ: 'icon_fund_transfer_point.png',
    NBJKSQ: 'icon_inner_loan_point.png',
    RZHKSQ: 'icon_financing_repayment_point.png',
    NBZHSQ: 'icon_inner_account_apply_point.png',
    WTFKSQ: 'icon_delegate_payment_point.png',
    NBJKFK: 'icon_inner_loan_pay_point.png',
    NBJKHK: 'icon_inner_loan_repay_point.png',
    NBDQCKZR: 'icon_fixed_deposit_into_point.png',
    NBDQCKZC: 'icon_fixed_deposit_out_point.png',
    GYLRZSQ: 'icon_supply_chain_financing_point.png',
    YESXSQ: 'icon_balance_limit_point.png',
    ZHWHSSSQ: 'icon_account_apply_point.png',
    ZJJHHZ: 'icon_zjjh_apply_point.png',
    SXSQ: 'icon_balance_limit_point.png',
    DKSQ: 'icon_financing_repayment_point.png',
    TQHKSQ: 'icon_jhtz_apply_point.png',
    DKZQSQ: 'icon_zjjh_apply_point.png',
    PKCL: 'icon_zjjh_apply_point.png',
    NBQPFKSQ: 'icon_single_payment_apply_point.png',

};

const iconImgDoneFlow = {
    ZHSQ: 'arc_account_apply.png',
    ZJFK: 'arc_single_payment.png',
    ZJFKBATCH: 'arc_batch_payment.png',
    XBSQ: 'arc_zjxb.png',
    ZJJH: 'arc_fund_plan.png',
    JHTZ: 'arc_fund_plan_adjust.png',
    ZJSH: 'arc_fund_collect.png',
    ZJXB: 'arc_fund_dial.png',
    ZJDBSQ: 'arc_fund_transfer.png',
    NBJKSQ: 'arc_inner_loan.png',
    RZHKSQ: 'arc_financing_repayment.png',
    NBZHSQ: 'arc_inner_account_apply.png',
    WTFKSQ: 'arc_delegate_payment.png',
    NBJKFK: 'arc_inner_loan_pay.png',
    NBJKHK: 'arc_inner_loan_repay.png',
    NBDQCKZR: 'arc_fixed_deposit_into.png',
    NBDQCKZC: 'arc_fixed_deposit_out.png',
    GYLRZSQ: 'arc_supply_chain_financing.png',
    YESXSQ: 'arc_balance_limit.png',
    ZHWHSSSQ: 'arc_account_apply.png',
    ZJJHHZ: 'arc_fund_plan.png',
    SXSQ: 'arc_balance_limit.png',
    DKSQ: 'arc_financing_repayment.png',
    TQHKSQ: 'arc_fund_plan_adjust.png',
    DKZQSQ: 'arc_fund_plan.png',
    PKCL: 'arc_fund_plan.png',
    NBQPFKSQ: 'arc_single_payment.png',

};

var delegate = {

    ready: function (f) {
        ajaxRequest("/sysParam/getParam.do", {}, function (data) {
            f(data);
        });
    }

};

var toTreeData = function toTreeData(data) {
    let resData = data;
    let tree = [];

    for (let i = 0; i < resData.length; i++) {
        // 第一个为最顶级组织
        if (resData[i].parentId === 'bizroot') {
            let obj = {
                id: resData[i].id,
                title: resData[i].name,
                pages: []
            };
            tree.push(obj);
            resData.splice(i, 1);
            i--;
        }
    }
    run(tree);

    function run(chiArr) {
        if (resData.length !== 0) {
            for (let i = 0; i < chiArr.length; i++) {
                for (let j = 0; j < resData.length; j++) {
                    if (chiArr[i].id === resData[j].parentId) {

                        let obj = {
                            id: resData[j].id,
                            title: resData[j].name,
                            pages: []
                        };
                        chiArr[i].pages.push(obj);
                        resData.splice(j, 1);
                        j--;
                    }
                }
                // console.log(chiArr[i].children);
                run(chiArr[i].pages);
            }
        }
    }

    return tree;
};

var translateDataToTree = function (data) {
    let parents = getParents(data);
    let pages = data.filter(value => value.parentId !== 'undefined' && value.parentId != null)
    let translator = (parents, pages) => {
        parents.forEach((parent) => {
            pages.forEach((current, index) => {
                if (current.parentId === parent.id) {
                    let temp = JSON.parse(JSON.stringify(pages))
                    temp.splice(index, 1)
                    translator([current], temp)
                    typeof parent.pages !== 'undefined' ? parent.pages.push(current) : parent.pages = [current]
                }
            })
        })
    };

    translator(parents, pages);

    function hasParents(parentId) {
        for (let i = 0; i < data.length; i++) {
            if (data[i].id === parentId) {
                return true;
            }
        }
        return false;
    }

    function getParents() {
        let mParents = [];
        data.forEach((current, index) => {

            if (!hasParents(current.parentId, data)) {
                mParents.push(current);
            }
        });
        return mParents;
    }

    return parents;
};


var overscroll = function (els) {
    var el = els;
    el.addEventListener('touchstart', function () {
        var top = this.scrollTop
            , totalScroll = this.scrollHeight
            , currentScroll = top + this.offsetHeight;
        //If we're at the top or the bottom of the containers
        //scroll, push up or down one pixel.
        //
        //this prevents the scroll from "passing through" to
        //the body.
        if (top === 0) {
            this.scrollTop = 1;
        } else if (currentScroll === totalScroll) {
            this.scrollTop = top - 1;
        }
    });
    el.addEventListener('touchmove', function (evt) {
        //if the content is actually scrollable, i.e. the content is long enough
        //that scrolling can occur
        if (this.offsetHeight < this.scrollHeight)
            evt._isScroller = true;
    });

};


// 浙政钉埋点开始
/*
(function (w, d, s, q, i) {
    w[q] = w[q] || [];
    var f = d.getElementsByTagName(s)[0], j = d.createElement(s);
    j.async = true;
    j.id = 'beacon-aplus';
    j.src = 'https://alidt.alicdn.com/alilog/mlog/aplus_cloud.js';
    f.parentNode.insertBefore(j, f);
})(window, document, 'script', 'aplus_queue');
*/
// zwdd_upload_common();

// 浙政钉埋点结束
function zwdd_upload_common() {

    var pageName = window.document.title;
    var pageUrl = getPathWithoutParam();
    var pageId = hex_md5(pageUrl);
    if (pageName == '') {
        pageName = '未知';
    }
    var userId = sessionStorage.getItem("_user_id");
    if (userId != null && userId.length > 0) {
        aplus_queue.push({
            action: 'aplus.setMetaInfo',
            arguments: ['aplus-rhost-v', 'alog.zjzwfw.gov.cn']
        });
        aplus_queue.push({
            action: 'aplus.setMetaInfo',
            arguments: ['aplus-rhost-g', 'alog.zjzwfw.gov.cn']
        });
        aplus_queue.push({
            action: "aplus.setMetaInfo",
            arguments: ["_user_id", sessionStorage.getItem("_user_id")]
        });
        aplus_queue.push({
            'action': 'aplus.sendPV',
            'arguments': [{
                is_auto: false
            }, {
                // 当前你的应用信息，此两行请勿修改
                sapp_id: '4180',
                sapp_name: 'ZJGL_APP',
                // 自定义PV参数key-value键值对（只能是这种平铺的json，不能做多层嵌套），如：
                page_id: pageId,
                page_name: pageName,
                page_url: pageUrl
            }]
        });

        var u = navigator.userAgent
        var isAndroid = u.indexOf('Android') > -1
        var isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)

        aplus_queue.push({
            action: 'aplus.setMetaInfo',
            arguments: ['appId', isAndroid ? '28302650' : isIOS ? '28328447' : '47130293']
        });

    }
}

function getPathWithoutParam() {
    var pageUrl = window.location.href;
    if (pageUrl.indexOf('?') !== -1) {
        pageUrl = pageUrl.substr(0, pageUrl.indexOf('?'))
    }
    return pageUrl;
}


// 浙政钉埋点结束


const banWords = [];


function ddAjaxRequestCallback(msg, requestUrl, params, callback) {
    $.showLoading(msg);

    let url = getContextPath() + requestUrl;
    let thisMethod = 'POST';
    if (params.method) {
        thisMethod = params.method;
    }
    params.tag = 'ats';
    $.ajax({
        // 是否使用缓存
        cache: true,
        // http请求方式
        type: thisMethod,
        // 请求url
        url: url,
        // 请求参数
        data: params,
        // 是否异步请求
        async: true,
        // 返回数据格式
        dataType: 'json',
        // 请求失败回调
        error: function (e) {
            $.hideLoading();
            $.toast(e.message, "forbidden");


        },
        // 请求成功回调
        success: function (data) {
            $.hideLoading();
            if (data.successful === true || 'true' === data.successful) {
                callback(data);
            } else {
                alert(data.message);
            }
        }
    });
}

// 对Date的扩展，将 Date 转化为指定格式的String
// 月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，
// 年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
// 例子：
// (new Date()).Format("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02 08:09:04.423
// (new Date()).Format("yyyy-M-d h:m:s.S")      ==> 2006-7-2 8:9:4.18
Date.prototype.Format = function (fmt) {
    var o = {
        "M+": this.getMonth() + 1, //月份
        "d+": this.getDate(), //日
        "H+": this.getHours(), //小时
        "m+": this.getMinutes(), //分
        "s+": this.getSeconds(), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}

var checkMsgCode = function (checkSuccess) {

    $.confirm({
        title: '短信验证',
        text: '请先发送短信验证',
        onOK: function () {

            ajaxRequestWithFailCallback('/flowMsg/send.do', {}, function (sendData) {
                $.toast(sendData.message);
                $.promptFg("输入收到的验证码", function (text) {

                    var reg = new RegExp("^[0-9]*$");
                    if (reg.test(text) && text.length > 0 && text.length <= 6) {
                        var checkParams = {};
                        checkParams.verifyCode = text;
                        ajaxRequestWithFailCallback('/flowMsg/check.do', checkParams, function (checkData) {
                            $.toast(checkData.message);
                            $.closeModal();
                            checkSuccess();

                        }, function (failInfo) {
                            $.toast(failInfo.message, 'cancel');
                        });
                    } else {
                        $.toast("验证码错误", "cancel");
                    }

                }, function () {
                    //点击取消后的回调函数
                });
            }, function (failInfoSend) {
                $.toast(failInfoSend.message, 'cancel');
            });


        },
        onCancel: function () {
        }
    });

}


let checkPayState = function (agree, afterFunc, params) {
    $.showLoading("处理中...");
    let reqParams = {};
    reqParams.flowTypeId = sessionStorage.getItem("FLOWTYPEID");
    reqParams.dealType = agree ? 1 : 2;
    if (params === undefined) {
        reqParams.workflowId = preParams.workflowId;
        reqParams.itemId = preParams.itemId;
        reqParams.dealState = 1;
    } else {
        reqParams.workflowId = params.workflowId;
        reqParams.itemId = params.itemId;
        reqParams.dealState = 1;
    }


    ajaxRequest('/saasflow/checkAutoPayStateBeforeApprove.do', reqParams, function (data) {
        console.log(data);
        $.hideLoading();
        if (data.result.needPrompt === true) {
            fgConfirm(data.result.promptTitle, data.result.data, agree, afterFunc, data.result);
        } else {
            afterFunc(0);
        }
    })
}

let fgConfirm = function (title, subTitleArray, agree, afterFunc, result) {

    if (result.needPrompt) {
        let html = '<div class="weui-cells">'
        let i = 0;
        for (let subTitle of subTitleArray) {
            /*if (i++ > 3) {
                break;
            }*/
            html += '<div class="weui-cell"><div class="weui-cell__bd">' +
                '<span style="font-weight: bold">' + subTitle.title + '</span>' +
                '<br/>' + simplifyMsg(subTitle.message) + '</div>' +
                '</div>';
        }
        html += '</div>'
        if (!result.canApprove) {
            $.modal({
                title: title,
                text: html,
                buttons: [
                    {text: "取消", className: "default"},

                ]
            });

            return;
        }
        if (result.needDetailVerification) {
            $.modal({
                title: title,
                text: html,
                buttons: [
                    {text: "取消", className: "default"},
                    {
                        text: "审批支付", onClick: function () {
                            inputPassCode(1, result.needPassword, afterFunc)
                        }
                    },
                    {
                        text: "审批不支付", onClick: function () {
                            inputPassCode(0, result.needPassword, afterFunc)

                        }
                    }

                ]
            });
        } else {
            let html = '<div class="weui-cells">'
            for (let subTitle of subTitleArray) {
                html += '<div class="weui-cell"><div class="weui-cell__bd">' + subTitle.title + '</div>' +
                    '</div>';
            }
            html += '</div>'
            $.modal({
                title: title,
                text: html,
                buttons: [
                    {text: "取消", className: "default"},
                    {
                        text: "继续审批", onClick: function () {
                            afterFunc(-1);
                        }
                    }

                ]
            });
        }

    } else {

    }


}

let inputPassCode = function (payTag, needPassword, afterFunc) {
    if (needPassword) {
        $.prompt({
            text: "请输入支付密码",
            title: "",
            onOK: function (passCode) {
                afterFunc(payTag, passCode)

            },
            onCancel: function () {
                console.log("取消了");
            },
            input: ''
        });
    } else {
        afterFunc(payTag, "")
    }

}

let simplifyMsg = function (msg){
    return msg;
}