//稳定性监控

try {
    const config = {
        bid: 'ZJGL_APP_zzdpro',
        signkey: '1234567890abcdef',
        gateway: 'https://wpk-gate.zjzwfw.gov.cn'
    };
    const wpk = new wpkReporter(config);
    wpk.installAll();
    window._wpk = wpk;
} catch (err) {
    console.error('WpkReporter init fail', err);
}

//通用采集 SDK
(function(w, d, s, q, i) {
    w[q] = w[q] || [];
    var f = d.getElementsByTagName(s)[0],j = d.createElement(s);
    j.async = true;
    j.id = 'beacon-aplus';
    j.src = 'https://alidt.alicdn.com/alilog/mlog/aplus_cloud.js';
    f.parentNode.insertBefore(j, f);
})(window, document, 'script', 'aplus_queue');



var pageName = window.document.title;
var pageUrl = getPathWithoutParam();
var pageId = hex_md5(pageUrl);


aplus_queue.push({
    action: 'aplus.setMetaInfo',
    arguments: ['aplus-rhost-v', 'alog.zjzwfw.gov.cn']
});
aplus_queue.push({
    action: 'aplus.setMetaInfo',
    arguments: ['aplus-rhost-g', 'alog.zjzwfw.gov.cn']
});

var u = navigator.userAgent
var isAndroid = u.indexOf('Android') > -1
var isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)

aplus_queue.push({
    action: 'aplus.setMetaInfo',
    arguments: ['appId', isAndroid ? '28302650' : isIOS ? '28328447' : '47130293']
});

//用户信息埋点
// 如采集用户信息是异步行为需要先执行这个BLOCK埋点
aplus_queue.push({
    action: 'aplus.setMetaInfo',
    arguments: ['_hold', 'BLOCK']
});

//基础埋点
// 单页应用 或 “单个页面”需异步补充PV日志参数还需进行如下埋点：
aplus_queue.push({
    action: 'aplus.setMetaInfo',
    arguments: ['aplus-waiting', 'MAN']
});
// 单页应用路由切换后 或 在异步获取到pv日志所需的参数后再执行sendPV：
aplus_queue.push({
    'action':'aplus.sendPV',
    'arguments':[{
        is_auto: false
    }, {
        // 当前你的应用信息，此两行请勿修改
        sapp_id: '4180',
        sapp_name: 'ZJGL_APP',
        // 自定义PV参数key-value键值对（只能是这种平铺的json，不能做多层嵌套），如：
        page_id: pageId,
        page_name: pageName,
        page_url: pageUrl
    }]
});

// 设置会员ID
aplus_queue.push({
    action: "aplus.setMetaInfo",
    arguments: ["_user_id", sessionStorage.getItem(sessionStorage.getItem("_user_id"))]
});
aplus_queue.push({
    action: "aplus.setMetaInfo",
    arguments: ["_dev_id", ""]
});

// 如采集用户信息是异步行为，需要先设置完用户信息后再执行这个START埋点
// 此时被block住的日志会携带上用户信息逐条发出
aplus_queue.push({
    action: 'aplus.setMetaInfo',
    arguments: ['_hold', 'START']
});

function getPathWithoutParam() {
    var pageUrl = window.location.href;
    if (pageUrl.indexOf('?') !== -1) {
        pageUrl = pageUrl.substr(0, pageUrl.indexOf('?'))
    }
    return pageUrl;
}
