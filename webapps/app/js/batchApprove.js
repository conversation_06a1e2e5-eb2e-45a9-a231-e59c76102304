let batchAgreeBtn = $('#batch-agree');
let batchAgreeRefuse = $('#batch-refuse');
let agreeAllCheck = $('#weuiAgree');
let batchTip = $('#weui-agree__text_tip');
let weuiAgree = $('.weui-agree');
let amountSum = $('#amount-summary');
let batchBtn = $('#batch');
let container = $('#container')
let tagNav = $('#tagnav')
let footer = $('.weui-footer')

let todo = sessionStorage.getItem("TODO");
if (todo !== '1') {
    batchBtn.hide();
}

let isAmountNote = false;
let singlePaymentTag = false;

$.toast.prototype.defaults.duration = 500;

let openBatch = function () {
    if (batchAgreeRefuse.attr('disabled') === 'disabled') {
        //批量同意和拒绝按钮不可见
        hideBatchButtons(false)
        batchBtn.html("取消");
    } else {
        hideBatchButtons(true);
        batchBtn.html("批量");

    }

};

let selectAllFlag = false;

let selectAll = function () {

    if (selectAllFlag) {
        $('.weui-check').prop('checked', null);

    } else {
        $('.weui-check').prop('checked', true);

    }


    selectAllFlag = !selectAllFlag;

    checkBoxChange();

};


let checkBoxChange;
checkBoxChange = function () {

    let count = 0;
    let tipCount = 0;
    let currencyAmount = {};
    let currencyNum = {};
    let sumTip;
    let totalAmount = 0;


    $.each($('.weui-check'), function () {

        if ($(this).prop('checked') === true) {

            if (singlePaymentTag) {
                // 单笔付款金额统计
                let strings = $(this).data('amount').split(";");
                for (let i = 0; i < strings.length - 1; i++) {

                    let s = strings[i];
                    let currCode = s.substring(0, s.indexOf("("));
                    let num = s.substring(s.indexOf("(") + 1, s.indexOf(")")).replace("笔", "");
                    let amount = s.substring(s.indexOf(":") + 1, s.length);
                    if (currencyAmount[currCode] !== undefined) {
                        let totalAmount = currencyAmount[currCode];
                        totalAmount += amount.replace(/,/g, '') * 1;
                        currencyAmount[currCode] = totalAmount;
                    } else {
                        currencyAmount[currCode] = amount.replace(/,/g, '') * 1;
                    }

                    if (currencyNum[currCode] !== undefined) {

                        let totalNum = currencyNum[currCode];
                        totalNum += parseInt(num);
                        currencyNum[currCode] = totalNum;
                    } else {
                        currencyNum[currCode] = parseInt(num);
                    }
                }

            }
            if (isAmountNote) {

                let amount = $(this).data('amount');
                totalAmount += parseFloat(amount);
            }
            if (!singlePaymentTag) {
                count++;
                tipCount = count;
            } else {
                count += $(this).data('itemid').split(',').length;
                tipCount++;
            }


        }
    });
    sumTip = '';
    for (let key in currencyAmount) {
        sumTip += key + '(' + currencyNum[key] + '笔):' + formatMoney(currencyAmount[key], 0) + ';</br>';
    }
    amountSum.html(sumTip);

    batchTip.html(count);
    if (isAmountNote) {
        totalAmount = totalAmount.toFixed(2);
        batchTip.html(`${count}笔/共${totalAmount}元`)
    }
    countChecked = count;

    if (tipCount === 0) {
        agreeAllCheck.prop('checked', null);

    } else {
        if ($('.weui-check').length === tipCount) {
            agreeAllCheck.prop('checked', true);
        }
    }

};
let beforeSendBatchApprove = function (agree, postFunc) {
    batchApprove(agree, postFunc);


}
let countChecked = 0;
let batchApprove = function (agree, postFunc) {


    let itemIds = [];
    let workflowIds = [];
    $.each($('.weui-check'), function () {

        if ($(this).prop('checked') === true) {
            let name = $(this).attr('name');
            itemIds.push(name.substring(name.indexOf('_') + 1, name.length));
            let workflowId = $(this).data('workflowid');
            workflowIds.push(workflowId);
        }

    });

    if (itemIds.length === 0) {
        $.toast("请至少选择一条数据", "cancel");
        return;
    }
    console.log(itemIds.length);
    console.log(itemIds.join(","));
    console.log(workflowIds.join(","));


    $.confirm("您确定要提交选中的" + countChecked + "条单据吗?", "确认提交审批?", function () {
        let params = {};
        params.itemId = itemIds.join(",");
        params.workflowId = workflowIds.join(",");
        if (siczyFlowMsgCheck == 'true') {
            checkPayState(agree, function (payTag, passCode) {
                checkMsgCode(function () {
                    requestApprove(agree, itemIds, workflowIds, postFunc, payTag, passCode);
                    // requestApprove(agree, itemIds, workflowIds, postFunc, );
                });
            }, params)
        } else {
            checkPayState(agree, function (payTag, passCode) {
                requestApprove(agree, itemIds, workflowIds, postFunc, payTag, passCode);

            }, params)
            // requestApprove(agree, itemIds, workflowIds, postFunc);
        }
    }, function () {
        $.toast("取消操作", "cancel");
    });

};

let requestApprove = function (agree, itemIds, workflowIds, postFunc, payTag, passCode) {
    let params = {};
    params.dealMsg = agree ? "批量同意处理(" + itemIds.length + "条)" : "批量拒绝处理(" + itemIds.length + "条)";
    params.dealType = agree ? 1 : 2;
    params.flowTypeId = sessionStorage.getItem("FLOWTYPEID");
    params.workflowId = workflowIds.join(",");
    params.itemId = itemIds.join(",");
    params.payTag = payTag;
    params.passCode = passCode;
    params.simplePassCode = 1;
    $.showLoading("处理中");
    ajaxRequest('/saasflow/sendApproval.do', params, function (data) {
        console.log(data);
        $.hideLoading();

        $.alert(data.result.data[0].returnMessage, "审批结果", function () {

            postFunc();
        });
    });
}

let hideBatchButtons = function (hide) {

    batchAgreeRefuse.attr("disabled", hide ? "disabled" : null);

    if (hide) {
        batchAgreeBtn.hide();
        batchAgreeRefuse.hide();
        weuiAgree.hide()

        hideCheckBox();
        selectAllFlag = false;
        agreeAllCheck.prop('checked', null);
        batchTip.html(0);
        $('#batch').html("批量");

        amountSum.html("");

    } else {
        weuiAgree.show()
        batchAgreeBtn.show();
        batchAgreeRefuse.show();
        showCheckBox();
        $('#batch').html("取消");

    }
    let height = -50;//下拉刷新高度
    if (tagNav.css('display') == 'none') {
        height = height + parseInt(footer.css('height'))
        console.log('none-height=' + height);
        container.css('height', 'calc(100% - ' + height + 'px )')
        container.css({'margin-top': '-50px', 'margin-bottom': footer.css('height')})
    } else {
        height = height + parseInt(footer.css('height')) + parseInt(tagNav.css('height'))
        console.log('block-height=' + height);
        container.css('height', 'calc(100% - ' + height + 'px )')
        container.css({'margin-top': '-10px', 'margin-bottom': footer.css('height')})
    }

};

let hideCheckBox = function () {

    $('.check_cell').hide();
    $('.weui-check').prop('checked', null);
    $('.weui-check').attr('disabled', 'disabled');


};

let showCheckBox = function () {
    $('.check_cell').show();
    $('.weui-check').attr('disabled', null);
};


function formatMoney(s, type) {
    if (/[^0-9\.]/.test(s))
        return "0.00";
    if (s == null || s == "null" || s == "")
        return "0.00";
    s = s.toString().replace(/^(\d*)$/, "$1.");
    s = (s + "00").replace(/(\d*\.\d\d)\d*/, "$1");
    s = s.replace(".", ",");
    var re = /(\d)(\d{3},)/;
    while (re.test(s))
        s = s.replace(re, "$1,$2");
    s = s.replace(/,(\d\d)$/, ".$1");
    if (type == 0) {
        var a = s.split(".");
        if (a[1] == "00") {
            s = a[0];
        }
    }
    return s;
}
