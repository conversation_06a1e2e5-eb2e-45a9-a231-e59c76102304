var clickf = false;

function getObjectURL(file) {

    var url = null;
    if (window.createObjectURL != undefined) {
        url = window.createObjectURL(file)
    } else if (window.URL != undefined) {
        url = window.URL.createObjectURL(file)
    } else if (window.webkitURL != undefined) {
        url = window.webkitURL.createObjectURL(file)
    }
    return url
};
var current;
$(function () {
    $("#img").on("change", ".file-class", function () {
        var srcs = getObjectURL(this.files[0]); //获取路径

        var nextHTML = '<div class="imgbox">' +
            '<div class="num-img">' +
            '<input type="file" class="file-class" />' +
            '<img src="" class="close" />' +
            '<img src="" class="btn-img" />' +
            '<img src="' + srcs + '" class="pre-img" />' +
            '</div>' +
            '</div>';

        $(this).parent().parent().before(nextHTML);
//		$(this).val(''); //必须制空
        $(this).parent().parent().prev().find(".btn-img").hide(); //this指的是input
        $(this).parent().parent().prev().find('.close').show();

        $(".close").on("click", function () {
            var img = $(this).parent().children(".pre-img");
            imgShow("#outerdiv", "#innerdiv", "#bigimg", img);
            current = $(this);
        });


        $(".pre-img").on("click", function () {
            var _this = $(this);//将当前的pimg元素作为_this传入函数
            imgShow("#outerdiv", "#innerdiv", "#bigimg", _this);
        });
    });

    $("#delete").on("click", function () {
        if ($('.imgbox').length > 1) {
            current.parent().parent().remove();
        }
    });

    function imgShow(outerdiv, innerdiv, bigimg, _this) {
        var src = _this.attr("src");
        $(bigimg).attr("src", src);

        $("<img/>").attr("src", src).load(function () {
            var windowW = $(window).width();
            var windowH = $(window).height();
            var realWidth = this.width;
            var realHeight = this.height;
            var imgWidth, imgHeight;
            var scale = 0.8;

            if (realHeight > windowH * scale) {
                imgHeight = windowH * scale;
                imgWidth = imgHeight / realHeight * realWidth;
                if (imgWidth > windowW * scale) {
                    imgWidth = windowW * scale;
                }
            } else if (realWidth > windowW * scale) {
                imgWidth = windowW * scale;
                imgHeight = imgWidth / realWidth * realHeight;
            } else {
                imgWidth = realWidth;
                imgHeight = realHeight;
            }
            $(bigimg).css("width", imgWidth);

            var w = (windowW - imgWidth) / 2;
            var h = (windowH - imgHeight) / 2;
            $(innerdiv).css({"top": h, "left": w});
            $(outerdiv).fadeIn("fast");
        });

        $(outerdiv).click(function () {
            $(this).fadeOut("fast");
        });
    }
});
