if (!$) {
    alert("pie_y.js 依赖jJquery，请引入Jquery");
}
/*
 * opciones可以指定如下参数
 * title 	非必填（头部标题），已删除
 * data		必填，数组
 * imgSrc	必填，图片的路径
 * ctx		必填，当前系统应用名字
 * pieStartX 非必填，不填则取canvas中心点
 * pieStartY 非必填，不填则取canvas中心点
 * pieR		非必填，不填取(canvas.width()-50)/2或者(canvas.height()-50)，两者取小
 * fix		非必填，是否图形适配（boolean类型，默认false）
 * selectFn	非必填，选中时间回调方法:function(selectIndex)
 * clickFn	非必填，点击事件回调方法： function(e, x, y)
 */
$.fn.extend({
    PieGraphic: function (opciones) {
        opciones.fix = opciones.fix == undefined ? true : opciones.fix;//默认为true
        var id = $(this).attr("id");
        var canvas = document.getElementById(id);
        if (canvas == null) {
            return false;
        }
        var kongge = 50;
        var canvasWidth = $(this).width();
        var canvasHeight = $(this).height();
        var canvasLeft = $(this).position().left;
        var canvasTop = $(this).position().top;
        var pieStartX = opciones.pieStartX ? opciones.pieStartX : canvasWidth / 2;
        var pieStartY = opciones.pieStartY ? opciones.pieStartY : canvasHeight / 2;
        var pieR = canvasWidth < canvasHeight ? (canvasWidth - kongge) / 2 : (canvasHeight - kongge) / 2;
        var middlePieR = pieR * 3 / 5;
        pieR = opciones.pieR ? opciones.pieR : pieR;
        pieStartY = pieStartY > (pieR + kongge / 2) ? (pieR + kongge / 2) : pieStartY;
        var markStartAngle = 0;
        var moving = false;//防止上一次移动尚未结束，发起新一次移动
        var startMovingTime = 0;
        var thisObj = this;
        var img = new Image();
        img.src = opciones.imgSrc;

        var totalCount = 0;//记录总数
        for (var i = 0; i < opciones.data.length; ++i) {
            opciones.data[i].count = parseFloat(opciones.data[i].count);
            totalCount += opciones.data[i].count;
        }

        var colors = new Array("#3bb8b0", "#a4c862", "#bf9ec9", "#e6b254", "#6ac2ee", "#ee8d8c");//6种颜色循环
        //var firstEachPie = new Array();//后续使用，记录最开始的起始截止坐标
        var eachPie = new Array();

        function drawPie(canvas, opciones, totalCount, cStartAngle, selectIndex, n) {//绘制饼图
            var ver = 50;
            var cdate = new Date();
            if (!moving) {
                startMovingTime = cdate.getTime();
            }
            if (opciones.data.length > 1 && (n == undefined || n < ver)) {
                moving = true;
                var sleepTime = 800 / ver;
                if (n != undefined) {
                    if ((cdate.getTime() - startMovingTime) < n * 800 / ver) {//如果时间区间小于多次周期时间合，那么需要sleep，否则不用sleep
                        sleepTime = n * 800 / ver - (cdate.getTime() - startMovingTime);
                    } else {
                        sleepTime = 0;
                    }
                }
                setTimeout(function () {
                    if (n != undefined) {
                        n++;
                    } else {
                        n = 0;
                    }
                    draw(canvas, opciones, totalCount, markStartAngle + n * cStartAngle / ver);
                    drawPie(canvas, opciones, totalCount, cStartAngle, selectIndex, n);
                }, sleepTime);
            }
            if (opciones.data.length <= 1 || n >= ver) {
                setTimeout(function () {
                    draw(canvas, opciones, totalCount, markStartAngle + cStartAngle, selectIndex);

                    moving = false;
                    markStartAngle += cStartAngle;
                    if (markStartAngle > Math.PI * 2) {
                        markStartAngle -= Math.PI * 2;
                    }
                }, 60);
            }
        }

        function draw(canvas, opciones, totalCount, cStartAngle, selectIndex) {
            cleanCanvas(canvas);
            var context = canvas.getContext('2d');
            var oldWidth = parseInt(window.getComputedStyle(canvas).width);
            canvas.width = oldWidth * ratio;
            canvas.style.width = oldWidth + 'px';
            var oldHeight = parseFloat(window.getComputedStyle(canvas).height);
            canvas.height = oldHeight * ratio;
            canvas.style.height = oldHeight + 'px';

            var devicePixelRatio = window.devicePixelRatio || 1;
            var backingStoreRatio = context.webkitBackingStorePixelRatio ||
                context.mozBackingStorePixelRatio ||
                context.msBackingStorePixelRatio ||
                context.oBackingStorePixelRatio ||
                context.backingStorePixelRatio || 1;
            var ratio = devicePixelRatio / backingStoreRatio;
            canvas.width = oldWidth * ratio;
            canvas.height = oldHeight * ratio;
            context.scale(ratio, ratio);
            var startAngle = cStartAngle, selectStartAngle, selectEndAngle;//后两个参数记录选中项的起始、截至角度，为了突出块的美观，下面画白色中心圆使用
            for (var i = 0; i < opciones.data.length; ++i) {
                var endAngle = startAngle + Math.PI * 2 * opciones.data[i].count / totalCount;
                context.save();
                context.fillStyle = opciones.data[i].color ? opciones.data[i].color : colors[i % colors.length];
                context.beginPath();
                context.moveTo(pieStartX, pieStartY);
                if (selectIndex != undefined && selectIndex == i && opciones.data.length > 1) {
                    context.moveTo(pieStartX, pieStartY + 20);
                    context.arc(pieStartX, pieStartY + 20, pieR, startAngle, endAngle, false);
                    selectStartAngle = startAngle;
                    selectEndAngle = endAngle;
                } else {
                    context.arc(pieStartX, pieStartY, pieR, startAngle, endAngle, false);
                }
                eachPie[i] = {};
                if (startAngle > Math.PI * 2) {
                    eachPie[i].startAngle = startAngle - Math.PI * 2;
                    eachPie[i].endAngle = endAngle - Math.PI * 2;
                } else {
                    eachPie[i].startAngle = startAngle;
                    eachPie[i].endAngle = endAngle;
                }
                //if(firstEachPie.length<opciones.data.length){
                //	firstEachPie[i] = eachPie[i];
                //}

                startAngle = endAngle;
                //不关闭路径路径会一直保留下去，当然也可以利用这个特点做出意想不到的效果
                context.closePath();
                context.fill();
                context.restore();
            }

            context.beginPath();
            context.moveTo(pieStartX, pieStartY + 20);
            if (selectStartAngle != undefined && opciones.fix) {//为了选中块美观
                var pianyi = 0.01;//偏移//去除毛边
                context.arc(pieStartX, pieStartY, middlePieR, selectEndAngle - pianyi, Math.PI * 2 + selectStartAngle + pianyi, false);
                if (selectEndAngle - selectStartAngle < 0.1) {
                    pianyi = 0;
                }
                context.moveTo(pieStartX, pieStartY);
                context.arc(pieStartX, pieStartY + 20, middlePieR, selectStartAngle - pianyi, selectEndAngle + pianyi, false);
            } else {
                context.arc(pieStartX, pieStartY, middlePieR, 0, Math.PI * 2, false);
            }
            //不关闭路径路径会一直保留下去，当然也可以利用这个特点做出意想不到的效果
            context.closePath();
            context.fillStyle = 'white';
            context.fill();

            //thisObj.drawTitle(context, pieStartX, pieStartY, opciones.title);
            var si = opciones.data.length == 1 ? 0 : selectIndex;
            if (si != undefined) {
                thisObj.drawMiddleTitle(context, pieStartX, pieStartY + (middlePieR - 18 - 12) / 5, opciones.data[si].showText1);
                thisObj.drawMiddleTitle(context, pieStartX, pieStartY + (middlePieR - 18 - 12) * 3 / 5, opciones.data[si].showText2);
                setTimeout(function () {
                    context.drawImage(img, pieStartX - 20, pieStartY + (middlePieR - 18 - 12));
                    context.restore();
                }, 10);
            }
        }

        this.drawMiddleTitle = function (context, startX, startY, title) {
            context.fillStyle = "#000000";
            context.font = "normal 16px 黑体";
            context.textBaseline = "top";
            startX = startX - context.measureText(title).width / 2;
            startY -= 24;
            context.fillText(title, startX, startY);
        }
        this.drawTitle = function (context, startX, startY, title) {
            var titles = title.split("\r\n");
            context.fillStyle = "#000000";
            context.font = "normal 24px 黑体";
            context.textBaseline = "top";
            for (var i = 0; i < titles.length; ++i) {
                var x = startX - context.measureText(titles[i]).width / 2;
                var y = (startY - pieR) / 2 - 30 * (titles.length - i) + 24;
                context.fillText(titles[i], x, y);
            }
        }

        draw(canvas, opciones, totalCount, markStartAngle);
        $(canvas).on("touchstart", function (e) {
            if (moving || opciones.data.length == 0) {
                return;//上一次移动尚未结束//或者没有数据
            }
            e.clientX = e.originalEvent.touches[0].clientX;
            e.clientY = e.originalEvent.touches[0].clientY;
            var cx = (e.clientX + document.body.scrollLeft - pieStartX) - canvasLeft;
            var cy = (e.clientY + document.body.scrollTop - pieStartY) - canvasTop;
            if (!checkClickValidate(cx, cy)) {
                return;
            }
            var thisAngle = getDeg(cx, cy) * Math.PI * 2 / 360;
            var pickIndex = -1;
            for (var i = 0; i < eachPie.length; ++i) {
                if (eachPie[i].startAngle < thisAngle && eachPie[i].endAngle > thisAngle
                    || (eachPie[i].startAngle - Math.PI * 2 < thisAngle && eachPie[i].endAngle - Math.PI * 2 > thisAngle)) {//考虑跨界
                    pickIndex = i;
                    break;
                }
            }
            thisObj.pickPie(pickIndex == -1 ? 0 : pickIndex);
            if (opciones.clickFn) {
                opciones.clickFn(e, cx, cy);
            }
        });
        this.pickPie = function (index) {
            if (moving) {
                return;
            }
            var selectMiddleAngle = (eachPie[index].startAngle + eachPie[index].endAngle) / 2;
            if (selectMiddleAngle > Math.PI * 2) {
                selectMiddleAngle = selectMiddleAngle - Math.PI * 2;
            }
            var moveAngle;
            if (selectMiddleAngle > Math.PI / 2) {
                moveAngle = Math.PI * 2 - (selectMiddleAngle - Math.PI / 2);
            } else {
                moveAngle = Math.PI / 2 - selectMiddleAngle;
            }
            drawPie(canvas, opciones, totalCount, moveAngle, index);
            if (opciones.selectFn) {
                opciones.selectFn(index);
            }
        }

        function cleanCanvas(canvas) {//清屏
            var context = canvas.getContext("2d");
            context.fillStyle = "rgb(255,255,255)";
            var width = $(canvas).width();
            var height = $(canvas).height();
            context.fillRect(0, 0, width, height);
        }

        function getDeg(x, y) {//根据三角形xy，获取tan的角度(非负)
            var thisAngle = Math.atan(y / x);
            thisAngle = 180 * thisAngle / Math.PI;
            if (x < 0 && y > 0) {
                thisAngle = 180 - Math.abs(thisAngle);
            } else if (x < 0 && y < 0) {
                thisAngle = 180 + Math.abs(thisAngle);
            } else if (x > 0 && y < 0) {
                thisAngle = 360 - Math.abs(thisAngle);
            }
            return thisAngle;
        }

        function checkClickValidate(x, y) {//判断本次点击是否合法，是否需要转动饼状图//往外模糊0.2
            if (x == 0 && y == 0) {
                return true;
            } else if (x == 0) {
                return pieR * 1.2 > y;
            } else if (y == 0) {
                return pieR * 1.2 > x;
            } else {
                return pieR * 1.2 > Math.sqrt(x * x + y * y);
            }
        }

        return this;
    }
});
