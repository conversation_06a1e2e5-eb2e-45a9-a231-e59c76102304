/**
 * author:z<PERSON><PERSON>
 * date:2017-04-07
 */
var utils = {
    //dialog之确认框
    confirm: function (title, tip, yescallback, nocallback) {

        var html = '<div id="dialog-container" style="width: 100%; height: 100%; position: fixed;top: 0;left: 0;background-color: rgba(0,0,0,0.5); display: block; color: #777777;">' +
            '<div class="dialog" style="width: 80%; height: 150px; margin: 0 auto; margin-top:200px;background-color: #F0F0F0; border-radius: 10px;">' +
            '<div class="title" style="margin: 0 auto; width: 100%; height: 50px; text-align: center;line-height: 50px; font-size: 20px; font-weight: bold;">' + title + '</div>' +
            '<div class= "title-tip" style="margin: 0 auto; width: 100%; height: 50px;  text-align: center;line-height: 30px; font-size: 18px; word-break: break-all;">' + tip + '</div>' +
            '<div class="hr" style="width: 100%; height: 1px; background-color: #cacaca;" ></div>' +

            '<div style="width: 100%;height: 50px; display: inline-block;">' +
            '<div class="yes" style="width: 50%; height: 49px;line-height: 49px;text-align: center; color: #3A94CA; " >确认</div>' +
            '<div class="hr" style="width: 1px; height: 49px; background-color: #cacaca;position: relative;left: 50%; top: -50px;"></div>' +
            '<div class="no" style="width: 50%; height: 49px;line-height: 49px;text-align: center; position: relative; left: 50%; top: -100px; color: #3A94CA;" >取消</div>' +
            '</div>' +
            '</div>' +
            '</div>';

        $("body").append(html);
        $('.yes').unbind('click');
        $('.yes').on('click', function (e) {
            if (yescallback != null) {
                yescallback();
                $('#dialog-container').remove();
            }
        });
        //确保取消按钮未绑定事件
        $('.no').unbind('click');
        $('.no').on('click', function (e) {
            if (nocallback != null) {
                nocallback();
                $('#dialog-container').remove();
            }
        });
    },
    prompt: function (title, yescallback, nocallback) {
        var html = '<div id="dialog-container" style="width: 100%; height: 100%; position: fixed;top: 0;left: 0;background-color: rgba(0,0,0,0.5); display: block; color: #777777; z-index:998">' +
            '<div class="dialog" style="width: 80%; height: 160px; margin: 0 auto; margin-top:200px;background-color: #F0F0F0; border-radius: 10px;">' +
            '<div class="title" style="margin: 0 auto; width: 100%; height: 50px; text-align: center;line-height: 50px; font-size: 20px; font-weight: bold;">' + title + '</div>' +
            '<input type="text" id="input-text" style="width: 90%; height: 40px; margin-left:5%;margin-right:5%; text-align: left; font-size: 16px; word-break: break-all; outline:none"/>' +
            '<div class="hr" style="width: 100%; height: 1px; background-color: #cacaca;" ></div>' +

            '<div style="width: 100%;height: 50px; display: inline-block; z-index:999">' +
            '<div class="yes" style="width: 50%; height: 49px;line-height: 53px;text-align: center; color: #3A94CA; " >确认</div>' +
            '<div class="hr" style="width: 1px; height: 54px; background-color: #cacaca; position:relative; left:50%; top:-50px;"></div>' +
            '<div class="no" style="width: 50%; height: 49px;line-height: 49px;text-align: center; color: #3A94CA; position:relative; left:50%; top:-100px;" >取消</div>' +
            '</div>' +
            '</div>' +
            '</div>';

        $("body").append(html);
        $('.yes').unbind('click');
        $('.yes').on('click', function (e) {
            if (yescallback != null) {
                yescallback($('#input-text').val());
                if (!$('#input-text').val() == '') {
                    $('#dialog-container').remove();
                }

            }
        });
        //确保取消按钮未绑定事件
        $('.no').unbind('click');
        $('.no').on('click', function (e) {
            if (nocallback != null) {
                nocallback();
                $('#dialog-container').remove();
            }
        });
    },
    //检查数字字符串整数位长度
    checkNum: function (num) {
        if (num.indexOf('.') != -1) {
            return num.split('.')[0].length;
        } else {
            return num.length;
        }
    },
    //通用ajax请求
    ajaxRequest: function (requestUrl, params, callback) {
        $.ajax({
            // 是否使用缓存
            cache: true,
            // http请求方式
            type: 'POST',
            // 请求url
            url: requestUrl,
            // 请求参数
            data: params,
            // 是否异步请求
            async: true,
            // 返回数据格式
            dataType: 'json',
            // 请求失败回调
            error: function (data) {
                mui('body').progress().hide();
                mui.toast('网络连接异常');
            },
            // 请求成功回调
            success: function (data) {
                mui('body').progress().hide();
                if (data.successful == true || 'true' == data.successful) {
                    callback(data);
                } else {

                    mui.toast('错误：' + data.message);

                }
            }
        });
    },
    //数字千分符格式化
    formatNumberRgx: function (num) {
        var parts = num.toString().split(".");
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        return parts.join(".");
    },
    clearNoNum: function (obj) {
        obj.value = obj.value.replace(/[^\d.]/g, "");  //清除“数字”和“.”以外的字符
        obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
        obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');//只能输入两个小数
        if (obj.value.indexOf(".") < 0 && obj.value != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
            obj.value = parseFloat(obj.value);
        }
    },
    formatMoney: function (s, type) {
        if (/[^0-9\.]/.test(s))
            return "0";
        if (s == null || s == "")
            return "0";
        s = s.toString().replace(/^(\d*)$/, "$1.");
        s = (s + "00").replace(/(\d*\.\d\d)\d*/, "$1");
        s = s.replace(".", ",");
        var re = /(\d)(\d{3},)/;
        while (re.test(s))
            s = s.replace(re, "$1,$2");
        s = s.replace(/,(\d\d)$/, ".$1");
        if (type == 0) {// 不带小数位(默认是有小数位)
            var a = s.split(".");
            if (a[1] == "00") {
                s = a[0];
            }
        }
        return s;
    }
}
