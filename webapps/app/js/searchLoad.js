function load(type) {
    if (sessionStorage.getItem("history.back") && sessionStorage.getItem("history.back") != "false") {
        switch (type) {
            case "queryAccounts":
                fillorganization();
                fillaccount();
                fillaccounttype();
                break;
            case "queryBanktransaction":
                fillorganization();
                fillaccount();
                fillmoneyway();
                break;
            case "queryPay":
                fillorganization();
                fillaccount();
                fillpaytype();
                break;
            case "settings":
                fillorganization();
                break;
            case "delegateuser":
                filldelegateuser();
                break;

        }
    }
    sessionStorage.setItem("history.back", false);
    clearFill();
}

/**
 * 清除所有缓存
 * 在页面进入时
 */
function clearFill() {
    sessionStorage.removeItem("organization");
    sessionStorage.removeItem("account");
    sessionStorage.removeItem("paytype");
    sessionStorage.removeItem("accounttype");
    sessionStorage.removeItem("moneyway");
}

function fillmoneyway() {
    var moneyway = sessionStorage.getItem("moneyway");
    if (moneyway) {
        moneyway = JSON.parse(moneyway);
        $("#moneyway").val(moneyway.name);
        $("#moneywayid").val(moneyway.urid);
    }
}

function filldelegateuser() {
    var delegateuser = sessionStorage.getItem("delegateuser");
    if (delegateuser) {
        delegateuser = JSON.parse(delegateuser);
        try {
            if ($("#delegateuser")[0].tagName != "INPUT") {
                $("#delegateuser").html(delegateuser.name);
            } else {
                $("#delegateuser").val(delegateuser.name);
            }
        } catch (e) {
            $("#delegateuser").val(delegateuser.name);
        }
        $("#delegateuserid").val(delegateuser.urid);
    }
}

function fillorganization() {
    var organization = sessionStorage.getItem("organization");
    if (organization) {
        organization = JSON.parse(organization);
        try {
            if ($("#organization")[0].tagName != "INPUT") {
                $("#organization").html(organization.name);
            } else {
                $("#organization").val(organization.name);
            }
        } catch (e) {
            $("#organization").val(organization.name);
        }
        $("#organizationid").val(organization.urid);
    }
}

function fillaccount() {
    var account = sessionStorage.getItem("account");
    if (account) {
        account = JSON.parse(account);
        $("#account").val(account.name);
        $("#accountid").val(account.urid);
    }
}

function fillpaytype() {
    var paytype = sessionStorage.getItem("paytype");
    if (paytype) {
        paytype = JSON.parse(paytype);
        $("#paytype").val(paytype.name);
        $("#paytypeid").val(paytype.urid);
    }
}

function fillaccounttype() {
    var accounttype = sessionStorage.getItem("accounttype");
    if (accounttype) {
        accounttype = JSON.parse(accounttype);
        $("#accounttype").val(accounttype.name);
        $("#accounttypeid").val(accounttype.urid);
    }
}
