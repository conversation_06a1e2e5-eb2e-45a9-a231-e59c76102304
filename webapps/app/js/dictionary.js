function getAccountstateValue(label) {
    switch (label) {
        case "新建":
            return 1;
        case "开户":
            return 2;
        case "变更":
            return 3;
        case "冻结":
            return 4;
        case "销户":
            return 5;
    }
}

function getAccountstateLabel(value) {
    value += "";
    switch (label) {
        case "1":
            return "新建";
        case "2":
            return "开户";
        case "3":
            return "变更";
        case "4":
            return "冻结";
        case "5":
            return "销户";
    }
}


String.prototype.endWith = function (str) {
    var reg = new RegExp(str + "$");
    return reg.test(this);
}
String.prototype.startWith = function (str) {
    if (str == null || str == "" || this.length == 0 || str.length > this.length)
        return false;
    if (this.substr(0, str.length) == str)
        return true;
    else
        return false;
    return true;
}

function submitCallBackEndWith(submitCallBack, bo) {
    if (!submitCallBack) {
        return false;
    } else {
        var array = submitCallBack.split(",");
        for (var str in array) {
            if (array[str].endWith(bo)) {
                return true;
            }
        }
    }
    return false;
}

function changeDic(type, value) {
    switch (type) {
        case "币种":
            switch (value) {
                case "1":
                case "CNY":
                    value = "人民币";
                    break;
                case "2":
                case "USD":
                    value = "美元";
                    break;
                case "3":
                case "JPY":
                    value = "日元";
                    break;
                case "4":
                case "HKD":
                    value = "港币";
                    break;
                case "5":
                case "EUR":
                    value = "欧元";
                    break;
                case "6":
                case "SGD":
                    value = "新加坡元";
                    break;
                case "7":
                case "GBP":
                    value = "英镑";
                    break;
                case "8":
                case "AUD":
                    value = "澳元";
                    break;
                case "9":
                case "NGN":
                    value = "奈拉";
                    break;
            }
            break;
    }
    return value;
}

function delegateStatus(state) {
    switch (state) {
        case "2":
            return "启用";
        case "1":
            return "停用";
        case "0":
            return "过期";
        default :
            return "-";
    }
}
