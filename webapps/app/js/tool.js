//字符串加千分符
function formatNumberRgx(num) {
	var parts = num.toString().split(".");
	parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
	if(num.toString().indexOf('.')==-1){
		parts[1]='00';
	}else if(parts[1].length==1){
		parts[1]+='0'; 
	}else if(parts[1].length>=3){
		parts[1]=parts[1].substr(0,2);
	}
	return parts.join("."); 
};
function formatNumSuffix(num, numSuffix){
	num = num.toString();
	var index = num.indexOf(".");
	if(index>-1){
		if(index+3>num.length){
			return num;
		}else{
			return num.substring(0, index+3);
		}
	}
	return num;
}
// 睡
function sleep(numberMillis) {
	var now = new Date();
	var exitTime = now.getTime() + numberMillis;
	while (true) {
		now = new Date();
		if (now.getTime() > exitTime)
			return;
	}
}


	
// 兼容火狐、IE8
// 显示遮罩层
function showMask() {
	$("#mask").css("height", $(document).height());
	$("#mask").css("width", $(document).width());
	$("#mask").show();
}
// 隐藏遮罩层
function hideMask() {

	$("#mask").hide();
}
function tips(message) {
	el = $.tips({
		content : message,
		stayTime : 2000,
		type : "success"
	})
	el.on("tips:hide", function() {
		hideMask();
		history.go(-1);
	})

}

function createFormNode(form, params) {
	var node = document.createElement("input");
	node.setAttribute("type", "text");
	node.setAttribute("name", params.name);
	node.setAttribute("value", params.value);
	node.setAttribute("class", "hide");
	form.appendChild(node);

	return form;
}
// 增加币种符号

function moneyChange(e) {
	var a= new Array("CNY", "EUR", "USD", "GBP", "AUD", "JPY", "HKD");
	var b=new Array("¥","€","$","￡","A$","J￥","HK$");
	for (var i = 0; i < a.length; i++) {
		if (a[i] == e)
			return b[i];
	}
	return e;
}
//对Date的扩展，将 Date 转化为指定格式的String
//月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符， 
//年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字) 
//例子： 
//(new Date()).Format("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02 08:09:04.423 
//(new Date()).Format("yyyy-M-d h:m:s.S")      ==> 2006-7-2 8:9:4.18 
Date.prototype.Format = function (fmt) { //author: meizz 
 var o = {
     "M+": this.getMonth() + 1, //月份 
     "d+": this.getDate(), //日 
     "h+": this.getHours(), //小时 
     "m+": this.getMinutes(), //分 
     "s+": this.getSeconds(), //秒 
     "q+": Math.floor((this.getMonth() + 3) / 3), //季度 
     "S": this.getMilliseconds() //毫秒 
 };
 if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
 for (var k in o)
 if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
 return fmt;
}

/*
string 字符串;
str 指定字符;
split(),用于把一个字符串分割成字符串数组;
split(str)[0],读取数组中索引为0的值（第一个值）,所有数组索引默认从0开始;
*/
function getStr(string,str){
  var str_before = string.split(str)[0];
  var str_after = string.split(str)[1];
  alert('前：'+str_before+' - 后：'+str_after);
}
function getPageArea() {
	if (document.compatMode == 'BackCompat') {
		return {
			width: Math.max(document.body.scrollWidth, document.body.clientWidth),
			height: Math.max(document.body.scrollHeight, document.body.clientHeight)
		};
	} else {
		return {
			width: Math.max(document.documentElement.scrollWidth, document.documentElement.clientWidth),
			height: Math.max(document.documentElement.scrollHeight, document.documentElement.clientHeight)
		};
	}
}


function formatNoteCode(notecode){
	var code = '';
	if(notecode.indexOf("_")!=-1){
		var tmp = notecode.split('_');
		for(var i=0;i<tmp.length;i++){
			code+=tmp[i];
		}
	}else{
		code = notecode;
	}
	return code;
}

var html = null;
function ajaxLoading(){
	html = '<div id="loading-container" style="width: 100%; height: 100%; position: fixed; top: 0;left: 0;background-color: rgba(0,0,0,0);text-align: center;">'+
				'<div class="mui-btn mui-btn-primary loading-button" style="width: 100px; height: 50px; line-height: 40px; margin-top: 200px; background-color: rgba(0,0,0,0); border: 0;"><img src="../../img/cloud/loading.gif"/></div>'+
			'</div>';
	$('body').append(html);
}

function stopLoading(){
	if(html!=null){
		$('#loading-container').remove();
	}
}



