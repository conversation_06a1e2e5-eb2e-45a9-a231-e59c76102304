let onlyConfirm = function (config) {
    let configJson = {};
    if(typeof config==='string'){
        configJson.title = '提示';
        configJson.info = config;
    }else {
        configJson = config;
    }
    let body = $('body');
    body.find('#staticBackdrop').remove();
    let html = $(
        '<div class="modal fade" id="staticBackdrop" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">' +
        '<div class="modal-dialog">' +
        '<div class="modal-content">' +
        '<div class="modal-header">' +
        '<h5 class="modal-title" id="staticBackdropLabel">' + configJson.title + '</h5>' +
        '<button type="button" class="btn-close" aria-label="Close"  data-bs-dismiss="modal">' +
        '</div>' +
        '<div class="modal-body">' + configJson.info + '</div>' +
        '<div class="modal-footer">' +
        '<button id="ok-btn" type="button" class="btn btn-primary">好的</button>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>');

    body.append(html);
    html.modal('show');
    html.find('#ok-btn').on('click', function () {
        html.modal('hide');
        if (typeof config.onOk === 'function') {
            config.onOk();
        }


    })
}

let showLoading = function () {
    let body = $('body');
    body.find('#lh-loading').remove();
    let html = '<div style="position:absolute; top:30%; left:50%;"  class="text-center" id="lh-loading">' +
        '<div class="spinner-border" role="status">' +
        '<span class="visually-hidden">Loading...</span>' +
        '</div>' +
        '</div>';
    body.append(html);
}

let hideLoading = function () {
    $('body').find('#lh-loading').remove();
}

let toast = function (text) {
    let body = $('body');

    body.find('#lh-toast').remove();
    let html = $(
        '<div id="lh-toast" aria-live="polite" aria-atomic="true" class="d-flex justify-content-center align-items-center" style="height: 200px;">' +
        '<div style="position: absolute; top: 100px" class="toast" role="alert" aria-live="assertive" aria-atomic="true">' +
        '<div class="toast-body" style="font-weight: bold;">' + text + '</div>' +
        '</div>' +
        '</div>');
    body.append(html);
    html.find('.toast').toast({
        delay: 2000,
    })
    html.find('.toast').toast('show')
}

function bootAjaxRequest(requestUrl, params, callback) {

    showLoading();
    if (params == null) {
        params = {};
    }
    params.tag = 'ats';
    $.ajax({
        // 是否使用缓存
        cache: true,
        // http请求方式
        type: 'POST',
        // 请求url
        url: getContextPath() + requestUrl,
        // 请求参数
        data: params,
        // 是否异步请求
        async: true,
        // 返回数据格式
        dataType: 'json',
        // 请求失败回调
        error: function () {
            hideLoading();
            onlyConfirm({
                title:'错误',
                info:'网络请求错误'
            })
        },
        // 请求成功回调
        success: function (data) {
            hideLoading();
            setTimeout(function () {
                callback(data);
            }, 100);
        }
    });
}

function getContextPath() {
    let pathName = document.location.pathname;
    let index = pathName.substr(1).indexOf("/");
    return pathName.substr(0, index + 1);
}
