/**
 * 选择列表插件
 * varstion 2.0.0
 * by <PERSON><PERSON><PERSON>
 * <PERSON><PERSON><PERSON>@DCloud.io
 */

.mui-picker {
    background-color: #ddd;
    position: relative;
    height: 200px;
    overflow: hidden;
    border: solid 1px rgba(0, 0, 0, 0.1);
    -webkit-user-select: none;
    user-select: none;
    box-sizing: border-box;
}
.mui-picker-inner {
    box-sizing: border-box;
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    -webkit-mask-box-image: -webkit-linear-gradient(bottom, transparent, transparent 5%, #fff 20%, #fff 80%, transparent 95%, transparent);
    -webkit-mask-box-image: linear-gradient(top, transparent, transparent 5%, #fff 20%, #fff 80%, transparent 95%, transparent);
}
.mui-pciker-list,
.mui-pciker-rule {
    box-sizing: border-box;
    padding: 0px;
    margin: 0px;
    width: 100%;
    height: 36px;
    line-height: 36px;
    position: absolute;
    left: 0px;
    top: 50%;
    margin-top: -18px;
}
.mui-pciker-rule-bg {
    z-index: 0;
    /*background-color: #cfd5da;*/
}
.mui-pciker-rule-ft {
    z-index: 2;
    border-top: solid 1px rgba(0, 0, 0, 0.1);
    border-bottom: solid 1px rgba(0, 0, 0, 0.1);
    /*-webkit-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);*/
    /*box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);*/
}
.mui-pciker-list {
    z-index: 1;
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    -webkit-transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
}
.mui-pciker-list li {
    width: 100%;
    height: 100%;
    position: absolute;
    text-align: center;
    vertical-align: middle;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    overflow: hidden;
    box-sizing: border-box;
    font-size: 16px;
    font-family: "Helvetica Neue", "Helvetica", "Arial", "sans-serif";
    color: #888;
    padding: 0px 8px;
    white-space: nowrap;
    -webkit-text-overflow: ellipsis;
    text-overflow: ellipsis;
    overflow: hidden;
    cursor: default;
    visibility: hidden;
}
.mui-pciker-list li.highlight,
.mui-pciker-list li.visible {
    visibility: visible;
}
.mui-pciker-list li.highlight {
    color: #222;
}