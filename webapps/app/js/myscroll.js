/**
 * 外部需要实现连个function
 *
 */

var myScroll,
    pullDownEl, pullDownOffset,
    pullUpEl, pullUpOffset = 0,
    generatedCount = 0;
var _start = 1, _end = false, pageRecordNum = 15;


function refresh(totalCount) {
    myScroll.refresh();
    if (totalCount == "_end" || totalCount <= pageRecordNum * _start) {
        _end = true;
        hiddenPullUp();
    } else {
        _end = false;
        showPullUp();
    }
}

function createEle(tag, classs, styles) {
    var t = document.createElement(tag);
    if (classs) {
        t.setAttribute("class", classs);
    }
    if (styles) {
        t.setAttribute("style", styles);
    }
    return t;
}


/**
 * 下拉刷新 （自定义实现此方法）
 * myScroll.refresh();        // 数据加载完成后，调用界面更新方法
 */
function pullDownAction(first) {
    var params = {"start": 0, "limit": pageRecordNum};
    params = initparams(params);
    var totalCount = ajaxRequire(true, params);
    _start = 1;
    if (totalCount == "_end" || totalCount <= pageRecordNum * _start) {
        _end = true;
        hiddenPullUp();
    } else {
        _end = false;
        showPullUp();
    }

    myScroll.refresh();		//数据加载完成后，调用界面更新方法   Remember to refresh when contents are loaded (ie: on ajax completion)
    if (first && !_end) {
        myScroll.maxScrollY += pullUpOffset;
    }
}

function initparams(params) {
    if (typeof(getCondition) != "function") {
        return params;
    }
    var json = getCondition();
    return extend({}, [json, params]);
}

/**
 * 滚动翻页 （自定义实现此方法）
 * myScroll.refresh();        // 数据加载完成后，调用界面更新方法
 */
function pullUpAction() {
    if (_end) {
        return;
    }
    var params = {"start": _start * pageRecordNum + 1, "limit": pageRecordNum};
    params = initparams(params);
    var totalCount = ajaxRequire(false, params);
    _start++;
    if (totalCount == "_end" || totalCount <= pageRecordNum * _start) {
        _end = true;
        hiddenPullUp();
    }

    myScroll.refresh();		// 数据加载完成后，调用界面更新方法 Remember to refresh when contents are loaded (ie: on ajax completion)
    if (!_end) {
        myScroll.maxScrollY += pullUpOffset;
    }
}

/**
 * 初始化iScroll控件
 */
function loaded() {
    /*if(typeof(appendNode)!="function"){
        alert("请实现function appendNode(data, update)");
        return;
    }*/
    if (typeof(ajaxRequire) != "function") {
        alert("请实现function ajaxRequire(update, params)");
        return;
    }

    pullDownEl = document.getElementById('pullDown');
    pullDownOffset = pullDownEl.offsetHeight;
    pullUpEl = document.getElementById('pullUp');
    pullUpOffset = pullUpEl.offsetHeight;

    myScroll = new iScroll('wrapper', {
        scrollbarClass: 'myScrollbar', /* 重要样式 */
        useTransition: false, /* 此属性不知用意，本人从true改为false */
        topOffset: pullDownOffset,
        onRefresh: function () {
            if (pullDownEl.className.match('loading')) {
                pullDownEl.className = '';
                pullDownEl.querySelector('.pullDownLabel').innerHTML = '下拉刷新...';
            } else if (pullUpEl.className.match('loading')) {
                if (pullUpTag) {
                    pullUpEl.className = '';
                    pullUpEl.querySelector('.pullUpLabel').innerHTML = '上拉加载更多...';
                }
            }
        },
        onScrollMove: function () {
            if (this.y > 5 && !pullDownEl.className.match('flip')) {
                pullDownEl.className = 'flip';
                pullDownEl.querySelector('.pullDownLabel').innerHTML = '松手开始更新...';
                this.minScrollY = 0;
            } else if (this.y < 5 && pullDownEl.className.match('flip')) {
                pullDownEl.className = '';
                pullDownEl.querySelector('.pullDownLabel').innerHTML = '下拉刷新...';
                this.minScrollY = -pullDownOffset;
            } else if (this.y + pullUpOffset < (this.maxScrollY - 5) && this.maxScrollY < -70 && !pullUpEl.className.match('flip')) {
                if (pullUpTag) {
                    pullUpEl.className = 'flip';
                    pullUpEl.querySelector('.pullUpLabel').innerHTML = '松手开始更新...';
                }
                //this.maxScrollY = this.maxScrollY;
            } else if (this.y + pullUpOffset > (this.maxScrollY + 5) && pullUpEl.className.match('flip')) {
                if (pullUpTag) {
                    pullUpEl.className = '';
                    pullUpEl.querySelector('.pullUpLabel').innerHTML = '上拉加载更多...';
                }
                //this.maxScrollY = pullUpOffset;
            }
        },
        onScrollEnd: function () {
            if (pullDownEl.className.match('flip')) {
                pullDownEl.className = 'loading';
                pullDownEl.querySelector('.pullDownLabel').innerHTML = '加载中...';
                pullDownAction(false);	// Execute custom function (ajax call?)
            } else if (pullUpEl.className.match('flip')) {// || (this.y - this.maxScrollY) > 10
                if (pullUpTag) {
                    pullUpEl.className = 'loading';
                    pullUpEl.querySelector('.pullUpLabel').innerHTML = '加载中...';
                    pullUpAction();	// Execute custom function (ajax call?)
                }
            }
        }
    });
    pullDownAction(true);//初始化刷新
}

var pullUpTag = true;

function formatDate(date, format) {
    if (!date)
        return "-";
    if (!format)
        format = "yyyy-MM-dd";
    switch (typeof date) {
        case "string":
            date = new Date(date.replace(/-/g, "/"));
            break;
        case "number":
            date = new Date(date);
            break;
    }
    if (!date instanceof Date)
        return 1;
    var dict = {
        "yyyy": date.getFullYear(),
        "YY": date.getFullYear() - 2000,
        "M": date.getMonth() + 1,
        "d": date.getDate(),
        "H": date.getHours(),
        "m": date.getMinutes(),
        "s": date.getSeconds(),
        "MM": ("" + (date.getMonth() + 101)).substr(1),
        "dd": ("" + (date.getDate() + 100)).substr(1),
        "HH": ("" + (date.getHours() + 100)).substr(1),
        "hh": ("" + (date.getHours() + 100)).substr(1),
        "mm": ("" + (date.getMinutes() + 100)).substr(1),
        "ss": ("" + (date.getSeconds() + 100)).substr(1)
    };
    return format.replace(/(YY|yyyy|MM?|dd?|HH?|hh?|ss?|mm?)/g, function () {
        return dict[arguments[0]];
    });
}

function trimNumber(obj) {
    if (!obj) {
        return "";
    }
    var str = obj;
    var start = 0;
    var end = str.length - 1;
    while (start <= end) {
        if (str[start] >= '0' && str[start] <= '9') {
            ++start;
        } else {
            break;
        }
    }
    if (start < end) {
        while (end > start) {
            if (str[end] >= '0' && str[end] <= '9') {
                --end;
            } else {
                break;
            }
        }
    }
    if (start > end) {
        return "";
    } else {
        return str.substring(start, end + 1);
    }
}

function extend(des, src, override) {
    if (src instanceof Array) {
        for (var i = 0, len = src.length; i < len; i++)
            extend(des, src[i], override);
    } else {
        for (var i in src) {
            if (override || !(i in des)) {
                des[i] = src[i];
            }
        }
    }
    return des;
}

function hiddenPullUp() {
    document.getElementById("pullUp").style.display = "none";
}

function showPullUp() {
    document.getElementById("pullUp").style.display = "";
}


//初始化绑定iScroll控件
document.addEventListener('touchmove', function (e) {
    e.preventDefault();
}, {
    passive: false
});
document.addEventListener('DOMContentLoaded', loaded, false);
