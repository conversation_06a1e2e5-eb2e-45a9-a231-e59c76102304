<%@ page import="com.fingard.app.delegate.helper.EndpointProperties" %>
<%@ page import="java.io.File" %>
<%@ page import="cn.hutool.core.io.FileUtil" %>
<%@page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/include.jsp" %>


<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>App服务</title>
    <!-- head 中 -->
    <!-- 最新版本的 Bootstrap 核心 CSS 文件 -->
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style type="text/css">
        h1 {
            color: #649CFF;
        }

        .jumbotron {
            padding: 30px;
        }

    </style>
</head>

<body>
<div class="jumbotron">
    <button id="my-btn">小程序</button>
</div>
<!-- 最新的 Bootstrap 核心 JavaScript 文件 -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@3.3.7/dist/js/bootstrap.min.js" integrity="sha384-Tc5IQib027qvyjSMfHjOMaLkfuWVxZxUPnCJA7l2mCWNIpG9mGCD8wGNIcPD7Txa" crossorigin="anonymous"></script>
<script src="http://res.wx.qq.com/open/js/jweixin-1.6.0.js" ></script>
<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script>


    $.ajax({
        // 是否使用缓存
        cache: true,
        // http请求方式
        type: "POST",
        // 请求url
        url: "${ctx}/sysParam/wxparams.do",
        // 请求参数
        data: {},
        // 是否异步请求
        async: true,
        // 返回数据格式
        dataType: 'json',
        // 请求失败回调
        error: function (e) {
            alert(e);
        },
        // 请求成功回调
        success: function (data) {

            console.log(data);
        }
    });

</script>
</body>

</html>
