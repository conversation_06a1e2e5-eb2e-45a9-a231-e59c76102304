<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<%
    String userId = request.getParameter("userid");
    String joinType = request.getParameter("jointype");
    String period = request.getParameter("period");
    String warningdate = request.getParameter("warningdate");

%>
<c:set var="userId" value="<%=userId%>"/>
<c:set var="joinType" value="<%=joinType%>"/>
<c:set var="period" value="<%=period%>"/>
<c:set var="warningdate" value="<%=warningdate%>"/>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>预警消息详情</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

    <style>
        html,
        body {
        }

        body {
            background: #eee;
            font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
            font-size: 14px;
            color: #000;
            margin: 0;
            padding: 0;
        }

        .divider {
            width: 95%;
            height: 1px;
            margin: 0 auto;
            background-color: #eeeeee;
        }

        .panel-divider {
            width: 100%;
            height: 10px;
            margin: 0 auto;
            background-color: #eeeeee;
        }

    </style>
</head>
<body>
<div class="container">


</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script src="//g.alicdn.com/dingding/dingtalk-jsapi/2.6.41/dingtalk.open.js"></script>

<script>

    let financialInstitutionDataCache = {};

    let goto = function (i) {

        let contractList = financialInstitutionDataCache[i];
        let name = financialInstitutionDataCache[10000 - i];

        sessionStorage.setItem("contractList", contractList);
        sessionStorage.setItem("name", name);
        location.href = "warningMsgContractDetail.jsp";

        console.log(contractList);
    };
    let getData = function () {

        let params = {};
        params.userId = '${userId}';
        params.joinType = '${joinType}';
        params.period = '${period}';
        params.warningdate = '${warningdate}';

        $.hideLoading();
        ajaxRequest('/saasalertMsg/queryWarningMsg.do', params, function (data) {

            console.log(data);

            let container = $('.container');

            let listData = data.result.data;

            if(listData==null || listData.length==0){
                $.alert({
                    title: '数据获取异常',
                    text: '预警消息查询失败',
                    onOK: function () {
                        dd.biz.navigation.close({
                            onSuccess: function (result) {
                            },
                            onFail: function (err) {
                            }
                        })

                    }
                });

                return;
            }

            let i = 0;
            for (let data of listData) {


                let financialInstitutionList = data.financialInstitutionList;
                let financialInstitutionHtml = "";
                for (let financialInstitution of financialInstitutionList) {
                    financialInstitutionDataCache[i] = JSON.stringify(financialInstitution.contractList);
                    financialInstitutionDataCache[10000 - i] = financialInstitution.institutionName;

                    financialInstitutionHtml = financialInstitutionHtml +
                        "<div class=\"weui-form-preview__bd\" onclick=\"goto(" + i + ")\">\n" +
                        "            <div class=\"weui-form-preview__item\">\n" +
                        "                <label class=\"weui-form-preview__label\">机构名称</label>\n" +
                        "                <span class=\"weui-form-preview__value\">" + financialInstitution.institutionName + "</span>\n" +
                        "            </div>\n" +
                        "            <div class=\"weui-form-preview__item\">\n" +
                        "                <label class=\"weui-form-preview__label\">机构利息汇总</label>\n" +
                        "                <span class=\"weui-form-preview__value\">" + financialInstitution.institutionInterest + "</span>\n" +
                        "            </div>\n" +
                        "            <div class=\"weui-form-preview__item\">\n" +
                        "                <label class=\"weui-form-preview__label\">机构本金汇总</label>\n" +
                        "                <span class=\"weui-form-preview__value\">" + financialInstitution.institutionPrincipal + "</span>\n" +
                        "            </div>\n" +
                        "        </div>";
                    financialInstitutionHtml += "<div class='divider'></div>";
                    i++;
                }


                let dataHtml = "<div class=\"weui-form-preview\">\n" +
                    "            <div class=\"weui-form-preview__hd\">\n" +
                    "                <label class=\"weui-form-preview__label\">借款人</label>\n" +
                    "                <em class=\"weui-form-preview__value\">" + data.borrower + "</em>\n" +
                    "                <label class=\"weui-form-preview__label\">借款组织</label>\n" +
                    "                <em class=\"weui-form-preview__value\">" + data.orgName + "</em>\n" +
                    "                <label class=\"weui-form-preview__label\">本金汇总</label>\n" +
                    "                <em class=\"weui-form-preview__value\">" + data.orgPrincipal + "</em>\n" +
                    "                <label class=\"weui-form-preview__label\">利息汇总</label>\n" +
                    "                <em class=\"weui-form-preview__value\">" + data.orgInterest + "</em>\n" +
                    "            </div>\n" +
                    financialInstitutionHtml +
                    "    <div class=\"panel-divider\"></div>";
                container.append(dataHtml);

            }

            $.hideLoading();


        });

    };

    $(document).ready(function () {
        console.log("has ready");


        getData();

    });


</script>
</body>
</html>

