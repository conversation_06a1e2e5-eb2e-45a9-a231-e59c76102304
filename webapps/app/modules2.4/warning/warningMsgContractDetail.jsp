<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<%
    String userId = request.getParameter("userId");
    String joinType = request.getParameter("joinType");
    String period = request.getParameter("period");
%>
<c:set var="userId" value="<%=userId%>"/>
<c:set var="joinType" value="<%=joinType%>"/>
<c:set var="period" value="<%=period%>"/>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>预警消息详情</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

    <style>
        html,
        body {
        }

        body {
            background: #eee;
            font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
            font-size: 14px;
            color: #000;
            margin: 0;
            padding: 0;
        }

        .divider {
            width: 95%;
            height: 1px;
            margin: 0 auto;
            background-color: #eeeeee;
        }

        .panel-divider {
            width: 100%;
            height: 10px;
            margin: 0 auto;
            background-color: #eeeeee;
        }

    </style>
</head>
<body>
<div class="container">


</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>

<script>

    let contractListStr = sessionStorage.getItem('contractList');
    let name = sessionStorage.getItem('name');
    document.title = name;

    let contractList = JSON.parse(contractListStr);
    console.log(contractList);

    let setData = function () {

        let container = $('.container');

        for (let contract of contractList) {

            let  financialInstitutionHtml = 
                "<div class=\"weui-form-preview__bd\">\n" +
                "            <div class=\"weui-form-preview__item\">\n" +
                "                <label class=\"weui-form-preview__label\">应还日期</label>\n" +
                "                <span class=\"weui-form-preview__value\">" + contract.rePayDate + "</span>\n" +
                "            </div>\n" +
                "            <div class=\"weui-form-preview__item\">\n" +
                "                <label class=\"weui-form-preview__label\">利息</label>\n" +
                "                <span class=\"weui-form-preview__value\">" + contract.interest + "</span>\n" +
                "            </div>\n" +
                "            <div class=\"weui-form-preview__item\">\n" +
                "                <label class=\"weui-form-preview__label\">本金</label>\n" +
                "                <span class=\"weui-form-preview__value\">" + contract.principal + "</span>\n" +
                "            </div>\n" +
                "        </div>";

            let dataHtml = "<div class=\"weui-form-preview\">\n" +
                "            <div class=\"weui-form-preview__hd\">\n" +
                "                <label class=\"weui-form-preview__label\">合同编号</label>\n" +
                "                <em class=\"weui-form-preview__value\">" + contract.contractCode + "</em>\n" +
                "            </div>\n" +
                financialInstitutionHtml +
                "    <div class=\"panel-divider\"></div>";
            container.append(dataHtml);


        }


    };

    $(document).ready(function () {
        console.log("has ready");


        setData();

    });


</script>
</body>
</html>

