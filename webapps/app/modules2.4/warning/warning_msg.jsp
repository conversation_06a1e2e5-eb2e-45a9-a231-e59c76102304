<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>消息提醒</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

    <style>
        html,
        body {
        }

        body {
            background: #eee;
            font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
            font-size: 14px;
            color: #000;
            margin: 0;
            padding: 0;
        }

        .weui-cells {
            margin-top: 0;
            margin-bottom: 4.25em;
            font-size: 15px;
        }


        .msg-content {
            font-size: 14px;
        }

        .msg-title {
            font-size: 14px;
        }

        .weui-cell__ft {
            position: absolute;
            top: 0.625rem;
            right: 1.25rem;
        }

        #no-permission {
            position: relative;
            display: none;
        }

        #no-permission img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        #no-permission span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }


    </style>
</head>
<body>
<div class="container">
    <div class="content" style="margin-bottom: 40px;height:100%;overflow: auto;-webkit-overflow-scrolling: touch;">
        <div class="weui-pull-to-refresh__layer">
            <div class='weui-pull-to-refresh__arrow'></div>
            <div class='weui-pull-to-refresh__preloader'></div>
            <div class="down">下拉刷新</div>
            <div class="up">释放刷新</div>
            <div class="refresh">正在刷新</div>
        </div>
        <div class="weui-cells">


        </div>
        <div id="no-permission" style="display: none;">
            <img src="${ctx}/img/report/<EMAIL>" alt="暂无权限">
            <span>暂无权限</span>
        </div>
    </div>


    <%@include file='/common/bottom_bar.jsp' %>

</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>

<script>

    const pageSize = 20;
    var onTop = true;

    window.onscroll = function () {
        var scrollT = document.documentElement.scrollTop || document.body.scrollTop; //滚动条的垂直偏移
        console.log(scrollT)

        onTop = (scrollT === 0);

    };


    let getData = function () {


        let permissions = '${permissionCode}';
        if (permissions.indexOf(PERMISSION_CODE_WARNING_MSG) !== -1) {

            currentPageNum = 2;
            let url = '/saasalertMsg/getAlertMsgList.do';
            let params = {};
            params.pageNum = 1;
            params.pageSize = pageSize;

            ajaxRequest(url, params, function (response) {
                console.log(response);
                content.pullToRefreshDone();

                let cells = $('.weui-cells');
                cells.html('');
                if (response.result.totalNum === 0) {
                    // 没有预警数据
                    cells.append(
                        '<div class="weui-msgbox" style="position: fixed; line-height: 50%">' +
                        '    <p>' +
                        '        <i class="weui-icon-info-circle"></i>暂无预警消息' +
                        '    </p>' +
                        '</div>');
                } else {

                    let data = response.result.data;
                    let refreshHeader = '<div class="weui-pull-to-refresh__layer">\n' +
                        '                <div class=\'weui-pull-to-refresh__arrow\'></div>\n' +
                        '                <div class=\'weui-pull-to-refresh__preloader\'></div>\n' +
                        '                <div class="down">下拉刷新</div>\n' +
                        '                <div class="up">释放刷新</div>\n' +
                        '                <div class="refresh">正在刷新</div>\n' +
                        '            </div>';
                    // cells.append(refreshHeader);
                    for (let i = 0; i < data.length; i++) {

                        let imgUrl = data[i].isRead === '0' ? '${ctx}/img/czdn/warning/icon_alert_msg_unread.png' : '${ctx}/img/czdn/warning/icon_alert_msg_read.png';


                        let cell = $(' <div class="weui-cell"  data-read="' + data[i].isRead + '"  data-warningid="' + data[i].warningId + '">\n' +
                            '        <div class="weui-cell__hd">\n' +
                            '            <img style="width: 50px;height: 50px" src="' + imgUrl + '"/>\n' +
                            '        </div>\n' +
                            '        <div class="weui-cell__bd">\n' +
                            '            <p style="font-size: 16px; font-weight: bold">' + data[i].warningName + '</p>\n' +
                            '            <p style="font-size: 14px;margin-top: 4px " class="msg-content">' + data[i].warningMessage + '</p>\n' +
                            '        </div>\n' +
                            '<div class="weui-cell__ft">' + data[i].warningDate + '</div>' +
                            '    </div>');

                        if (data[i].isRead === '1') {
                            cell.css("background-color", "rgba(247,247,247,1)")
                        }

                        cell.on('touchstart', function () {

                            click = true;
                        });

                        cell.on('touchmove', function () {
                            click = false;
                        });

                        cell.on('touchend', function () {
                            if (click) {
                                console.log($(this).data('warningid'));
                                dealMsg($(this).data('warningid'), $(this).data('read'));
                            }
                        });
                        cells.append(cell);


                    }

                }

                let currDataSize = $('.weui-cells').find('.weui-cell').length;
                if (currDataSize >= pageSize) {
                    cells.append('<div class="weui-panel__ft">\n' +
                        '            <a href="javascript:getMore();" class="weui-cell weui-cell_access weui-cell_link" style="text-align: center">\n' +
                        '                <div class="weui-cell__bd" id="getmore">查看更多<i id="loading" class="weui-loading"></i></div>\n' +
                        '            </a>\n' +
                        '        </div>');

                    $('#loading').hide();
                }
            })
        } else {
            // 没有预警权限
            $('#no-permission').css('display', 'block');

        }


    };

    let click = true;
    let currentPageNum = 2;

    let getMore = function () {

        $('#no-more-tip').remove();
        $('#loading').show();

        let url = '/saasalertMsg/getAlertMsgList.do';
        let params = {};
        params.pageNum = currentPageNum;
        params.pageSize = pageSize;
        currentPageNum++;

        ajaxRequest(url, params, function (response) {
            $('#loading').hide();

            console.log(response);
            let more = $('.weui-panel__ft');
            if (response.result.totalNum === 0) {
                currentPageNum--;
                // 没有预警数据
                $('.weui-cells').append('<div id="no-more-tip" class="weui-loadmore weui-loadmore_line">\n' +
                    '    <span class="weui-loadmore__tips">暂无更多数据</span>\n' +
                    '</div>\n');
            } else {

                let data = response.result.data;
                for (let i = 0; i < data.length; i++) {

                    let imgUrl = data[i].isRead === '0' ? '${ctx}/img/czdn/warning/icon_alert_msg_unread.png' : '${ctx}/img/czdn/warning/icon_alert_msg_read.png';


                    let cell = $(' <div class="weui-cell"  data-read="' + data[i].isRead + '" data-warningid="' + data[i].warningId + '">\n' +
                        '        <div class="weui-cell__hd">\n' +
                        '            <img style="width: 50px;height: 50px" src="' + imgUrl + '"/>\n' +
                        '        </div>\n' +
                        '        <div class="weui-cell__bd">\n' +
                        '            <p class="msg-title">' + data[i].warningName + '</p>\n' +
                        '            <p class="msg-content">' + data[i].warningMessage + '</p>\n' +
                        '        </div>\n' +
                        '<div class="weui-cell__ft">' + data[i].warningDate + '</div>' +
                        '    </div>');

                    cell.on('touchstart', function () {

                        click = true;
                    });

                    cell.on('touchmove', function () {
                        click = false;
                    });

                    cell.on('touchend', function () {
                        if (click) {
                            console.log($(this).data('warningid'));

                            dealMsg($(this).data('warningid'), $(this).data('read'));

                        }
                    });
                    more.before(cell);
                }

            }

        })


    };

    let dealMsg = function (msgId, read) {

        if (read == 0) {

            $.confirm("您确定要标记该条消息已读吗?", "确认标记已读?", function () {
                let url = '/saasalertMsg/dealAlertMsg.do';
                let params = {};
                params.warningId = msgId;

                ajaxRequest(url, params, function (response) {
                    console.log(response);
                    getData();

                    if (response.result.data[0]) {
                        let data = response.result.data[0];
                        if (data.isSuccess === '0') {
                            $.toast(data.respMsg, "cancel");
                        } else {
                            $.toast(data.respMsg);
                        }
                    }
                })
            }, function () {
                $.toast("取消操作", "cancel");
            });

        }

    }

    let content = $('.content');

    let canRefresh = true;

    $(document).ready(function () {
        console.log("has ready");


        getData();

        content.on('touchstart', function () {
            canRefresh = true;
        });

        content.on('touchmove', function () {
            canRefresh = false;
        });

        content.on('touchend', function () {
            canRefresh = true;
        });
        content.pullToRefresh({
            distance: 50,
            onRefresh: function () {
                if (onTop) {
                    $('.weui-msgbox').hide();

                    setTimeout(function () {
                        getData();


                    }, 500);
                }
            }
        });
    });


    overscroll(document.getElementsByClassName('content')[0]);


</script>


</body>
</html>

