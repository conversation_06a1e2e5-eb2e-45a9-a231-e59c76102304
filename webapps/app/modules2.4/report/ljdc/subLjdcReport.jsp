<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style>

        #noMsg {
            position: relative;
            display: none;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }
    </style>
</head>
<body>

<div id="cell-list" class="weui-cells" style="margin-top: 0">

</div>
<div id="noMsg">
    <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
    <span>暂无数据</span>
</div>
<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script>
    $(document).ready(function () {
        let plateId = sessionStorage.getItem('PLATEID');
        let bankId = sessionStorage.getItem('BANKID');
        let level = sessionStorage.getItem('LEVEL');
        let name = sessionStorage.getItem("PLATENAME");
        document.title = name;

        getData(plateId, bankId, level);

    });

    let getData = function (plateId, bankId, plateLevel) {
        let url = '/saasreport/getCapitalStockDailyReportDetail.do';
        let params = {};
        params.bankId = bankId;
        params.plateId = plateId;
        params.plateLevel = plateLevel;
        ajaxRequest(url, params, function (response) {
            console.log(response);
            let list = response.result.data;
            let cells = $('#cell-list');
            cells.html('');
            if (list != null && list.length > 0) {

                let header = '<div class="weui-cell" style="background-color: #649CFF">' +
                    '        <div class="weui-cell__bd" style="color:white;">账户性质</div>' +
                    '        <div class="weui-cell__bd" style="color:white; margin-left: 15px">金额</div>' +
                    '        <div class="weui-cell__ft" style="color:white;">占比</div>' +
                    '    </div>';
                cells.append(header);
                for (let data of list) {
                    let cell = $('<div class="weui-cell">' +
                        '<div class="weui-cell__bd">' + data.accountType + '</div>' +
                        '<div class="weui-cell__bd" style="margin-left: 15px">' + data.amount + '</div>' +
                        '<div class="weui-cell__ft">' + data.proportion + '</div>' +
                        '</div>');
                    cells.append(cell);
                }
            } else {

                showNoMsg(true);
            }
        })
    };

    let showNoMsg = function (show) {
        let noMsg = $('#noMsg');
        if (show) {
            noMsg.css('display', 'block');
        } else {
            noMsg.css('display', 'none');
        }
    }
</script>
</body>
</html>
