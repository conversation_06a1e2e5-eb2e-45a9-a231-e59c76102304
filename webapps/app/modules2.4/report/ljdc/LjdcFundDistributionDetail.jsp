<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style>

        #noMsg {
            position: relative;
            display: none;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }
    </style>
</head>
<body>

<div id="cell-list" class="weui-cells" style="margin-top: 0">

</div>
<div id="noMsg">
    <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
    <span>暂无数据</span>
</div>
<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script>
    $(document).ready(function () {
        let plateId = sessionStorage.getItem('PLATEID_DETAIL');
        let bankId = sessionStorage.getItem('BANKID_DETAIL');
        let level = sessionStorage.getItem('LEVEL_DETAIL');
        let name = sessionStorage.getItem("PLATENAME_DETAIL");
        document.title = name;

        getData(plateId, bankId, level);

    });

    let getData = function (plateId, bankId, plateLevel) {
        let url = '/ljdc/report/ljdcGetFundDistributionDetail.do';
        let params = {};
        params.bankId = bankId;
        params.plateOrgId = plateId;
        ajaxRequest(url, params, function (response) {
            console.log(response);
            let tip = response.result.extra;
            let summary = response.result.data;
            let cells = $('#cell-list');
            cells.html('');
            if (summary != null && response.result.totalNum > 0) {

                let header = '<div class="weui-cell" style="background-color: #649CFF; font-weight: bold">' +
                    '        <div class="weui-cell__bd" style="color:white;">账户性质</div>' +
                    '        <div class="weui-cell__bd" style="color:white; margin-left: 15px">金额</div>' +
                    '        <div class="weui-cell__ft" style="color:white;">占比</div>' +
                    '    </div>';
                cells.append(header);
                for (let key in summary) {
                    let data = summary[key];
                    let type = $('<div class="weui-cell">' +
                        '<div class="weui-cell__bd" style="color: #649CFF; font-weight: bold" >' + tip[key] + '</div></div>');
                    cells.append(type);
                    for (let cellData of data) {
                        let cell = $('<div class="weui-cell">' +
                            '<div class="weui-cell__bd">' + cellData.accountType + '</div>' +
                            '<div class="weui-cell__bd" style="margin-left: 15px">' + cellData.amount + '</div>' +
                            '<div class="weui-cell__ft">' + cellData.proportion + '</div>' +
                            '</div>');
                        cells.append(cell);
                    }

                }
            } else {

                showNoMsg(true);
            }
        })
    };

    let showNoMsg = function (show) {
        let noMsg = $('#noMsg');
        if (show) {
            noMsg.css('display', 'block');
        } else {
            noMsg.css('display', 'none');
        }
    }
</script>
</body>
</html>
