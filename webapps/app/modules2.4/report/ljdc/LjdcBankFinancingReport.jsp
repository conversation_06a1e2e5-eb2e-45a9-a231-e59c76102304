<%@ page contentType="text/html;charset=UTF-8" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>银行类融资表</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style>
        #tagnav {
            position: fixed;
            top: 0;
            z-index: 999;
            background: #649CFF;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .weui-navigator-list li {
            line-height: 40px;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a {
            color: #93FFDB;
            font-weight: bold;
            border-bottom: 1px solid #93FFDB;
        }

        .weui-navigator-list li.weui-state-disabled a {
            color: rgba(255, 255, 255, 0.2);
            border-bottom: 1px solid #649CFF;
        }

        .weui-navigator-list li a {
            color: #ffffff;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a:after {
            background-color: transparent;
        }

        #reportTitle {
            padding-top: 43px;
            background: #649CFF;
            color: #fff;
        }

        #left, #right {
            display: inline-block;
            width: 48%;
            text-align: center;
        }

        .leftTip, .leftBottomTip, .rightBottomTip, .rightTip {
            display: inline-block;
            padding-top: 8px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            position: relative;
        }

        .leftTip:before, .leftBottomTip:before, .rightBottomTip:before, .rightTip:before {
            content: ' ';
            width: 11px;
            height: 11px;
            display: inline-block;
            border: 1px solid #ffffff;
            border-radius: 6px;
            position: absolute;
            top: 10px;
            left: -18px;
        }

        .leftTip:before {
            background: #FCE172;
        }

        .rightTip:before {
            background: #7BE5C2;
        }

        .leftBottomTip:before {
            background: #006aaf;
        }

        .rightBottomTip:before {
            background: #e5163a;
        }

        .leftAmount, .rightAmount {
            padding-bottom: 10px;
            display: block;
            font-size: 18px;
            font-weight: 500;
        }

        .line {
            display: inline-block;
            width: 1%;
            max-width: 1px;
            background: #fff;
            height: 30px;
        }

        .titleInfo {
            padding-left: 20px;
            padding-top: 18px;
            font-size: 17px;
            color: #5A5858;
            /*font-weight: bold;*/
            display: block;
        }

        .titleInfo-level_1 {
            padding-left: 20px;
            padding-top: 18px;
            font-size: 17px;
            font-weight: bold;
            color: #212121;
            display: block;
        }

        .level-1 {
            color: #999999;
            font-weight: bold;

        }

        .titleInfo-level_0 {
            padding-left: 20px;
            padding-top: 18px;
            font-size: 17px;
            font-weight: bold;
            color: #333333;
            display: block;
        }

        .level-0 {
            color: #333333;
            font-weight: bold;

        }

        .level-none {
            color: #999999;
        }

        .titleInfoArrow:after {
            content: " ";
            display: inline-block;
            height: 7px;
            width: 7px;
            border-width: 2px 2px 0 0;
            border-color: #c8c8cd;
            border-style: solid;
            transform: matrix(.71, .71, -.71, .71, 0, 0);
            webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);
            top: -2px;
            position: absolute;
            top: 50%;
            margin-top: -4px;
            right: 22px;
        }

        .amountAndTip {
            display: inline-block;
            width: calc(48% - 20px);
            padding-left: 20px;
        }

        .amountAndTip .amount {
            display: block;
            color: #FF7979;
            font-size: 16px;
            font-weight: bold;
        }

        .amountAndTip .tip {
            font-size: 10px;
            /*color: #999999;*/
            display: block;
        }

        #noMsg {
            position: relative;
            display: none;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }
    </style>
</head>
<body>
<div id="tagnav" class="weui-navigator weui-navigator-wrapper">
    <ul class="weui-navigator-list"></ul>
</div>
<div id="reportTitle"></div>
<div id="chartSection"></div>
<div id="chartListInfo"></div>
<div id="noMsg">
    <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
    <span>暂无数据</span>
</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/iscroll-lite.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script src="${ctx}/js/echarts.min.js"></script>

<script>
    $(function () {
        var reportActiveCurrCode = sessionStorage.getItem('reportActiveCurrCode');
        var realCurrCode = 0;
        var tabIndex = 0;
        var currCodeArray = JSON.parse(reportActiveCurrCode);
        let tabs = $('.weui-navigator-list');
        for (let i = 0; i < currCodeArray.length; i++) {


            let tabItem = currCodeArray[i];
            let currName = tabItem.bankName;
            let currCode = tabItem.bankId;

            if (i === 0) {
                realCurrCode = currCode;
            }

            let tab = $('<li data-currCode="' + currCode + '"><a>' + currName + '</a></li>');

            tabs.append(tab);
        }
        //使用这个是为了避免滑动时出现滚动条。
        TagNav('#tagnav', {
            type: 'scrollToNext',
            curClassName: 'weui-state',
            //不能使用weui-state-active，随便写一个，以免下面的重写失效。
            index: tabIndex
        });
        //重写切换事件和选中事件
        $("#tagnav li").eq(tabIndex).addClass('weui-state-active');
        $('#tagnav').on('click', 'li',
            function (event) {
                event.preventDefault();
                $(this).addClass('weui-state-active');
                if (!$(this).hasClass('weui-state-disabled')) {
                    $(this).siblings('li').removeClass('weui-state-active');
                    requestData($(this).attr('data-currCode'));
                }
            });
        requestData(realCurrCode);
        $(document.body).on('touchmove', function (e) {
            e.preventDefault();
        })
    });

    function requestData(currCode) {
        realCurrCode = currCode;

        showNoMsg(false);
        var param = {'bankLocationId': currCode, 'tag': 'ats'};
        $.ajax({
            cache: true,
            type: 'POST',
            url: "${ctx}/ljdc/report/ljdcGetBankFinancing.do",
            data: param,
            async: true,
            dataType: 'json',
            beforeSend: function () {
                $.showLoading();
            },
            error: function () {
                $.hideLoading();
                $.toast('网络服务情况异常，请检查', "forbidden");
                showNoMsg(true);
            },
            success: function (data) {
                $.hideLoading();
                if (data.successful === true || "true" === data.successful) {
                    let dataArray = data.result.data;
                    if (dataArray.length === 3) {
                        //顶部数据
                        layoutTopTitle(dataArray[0].reportData);
                        //chart数据
                        let chartSection = $('#chartSection');
                        chartSection.html('');
                        chartSection.append(
                            '<div id="chartView" style="width: 100%;height: 268px;"></div>' +
                            '<div class="chartPadding" style="width: 100%;height: 10px;background: #F1F3F5"></div>');
                        let myChart = echarts.init(document.getElementById('chartView'));
                        let option = dataCollation(dataArray[1].reportData);
                        myChart.setOption(option);
                        //列表数据
                        layoutListInfo(dataArray[2].reportData);
                    } else {
                        // 暂无数据
                        showNoMsg(true);
                    }
                } else {
                    $.toast('网络服务情况异常，请检查', "forbidden");
                    showNoMsg(true);
                }
            }
        });
    }

    function showNoMsg(show) {
        if (show) {
            $("#reportTitle").html('');
            $("#chartSection").html('');
            $("#chartListInfo").html('');
            $("#noMsg").css('display', 'block');
        } else {
            $("#noMsg").css('display', 'none');
        }
    }

    function layoutTopTitle(reportDataOne) {
        let titleLayout = $("#reportTitle");
        titleLayout.html('');
        let dic = reportDataOne[0];
        titleLayout.append(
            '<div id="left"><span class="leftTip">账户余额/亿元</span><span class="leftAmount">' + dic.accountAmount + '</span></div>' +
            '<div class="line"></div>' +
            '<div id="right"><span class="rightTip">日均余额/亿元</span><span class="rightAmount">' + dic.dailyBalance + '</span></div>' +
            '<div id="right"><span class="rightBottomTip">贷款余额/亿元</span><span class="rightAmount">' + dic.loanAmount + '</span></div>' +
            '<div class="line"></div>' +
            '<div id="left"><span class="leftBottomTip">存贷比</span><span class="leftAmount">' + dic.loanDepositRatio + '</span></div>');

    }

    function layoutListInfo(reportDataThree) {
        let listLayout = $('#chartListInfo');
        listLayout.html('');
        for (let i = 0; i < reportDataThree.length; i++) {
            let dic = reportDataThree[i];
            let cell = $('<div class="cell" style="position: relative;" data-item = ' + dic.bankLocationId + '>' +
                '<span class="titleInfo-level_1">' + dic.item + '-' + dic.loanContractCode + '</span>' +
                '<div class="amountAndTip"><span class="amount">' + dic.creditAmount + '</span><span class="tip level-1">授信额度</span></div>' +
                '<div class="amountAndTip"><span class="amount">' + dic.loanAmount + '</span><span class="tip level-1">贷款余额</span></div>' +
                '<div class="amountAndTip"><span class="amount">' + dic.loanRate + '</span><span class="tip level-1">融资成本</span></div>' +
                '<div class="amountAndTip"><span class="amount">' + dic.loanDepositRatio + '</span><span class="tip level-1">存贷比</span></div>' +
                '<div style="width: 100%;height: 10px;background: #ffffff"></div>' +
                '<div class="cellPadding" style="width: 100%;height: 5px;background: #F1F3F5"></div>' +
                '</div>');

            listLayout.append(cell);

        }


    }

    function dataCollation(reportDataTwo) {
        let seriesData = [];

        let xAxisData = [];
        let accountBalanceData = [];
        let dailyBalanceData = [];
        let ratioData = [];


        for (let cellData of reportDataTwo) {
            xAxisData.push(cellData.amountDate);
            accountBalanceData.push(cellData.accountAmount);
            dailyBalanceData.push(cellData.dailyBalance);
            ratioData.push(cellData.loanDepositRatio);
        }

        let accountBalance = {
            name: "账户余额",
            type: 'bar',
            data: accountBalanceData,
            yAxisIndex: 0,
            color: "#FCE172"
        };

        let dailyBanlance = {
            name: "日均余额",
            type: 'bar',
            data: dailyBalanceData,
            yAxisIndex: 0,
            color: "#7BE5C2"

        };

        let loadDeposityRatio = {
            yAxisIndex: 1,
            name: "存贷比",
            type: 'line',
            data: ratioData,
            color: '#006aaf',
        };

        seriesData.push(accountBalance);
        seriesData.push(dailyBanlance);
        seriesData.push(loadDeposityRatio);


        var option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                formatter: '{b}</br>账户余额：{c0}</br>日均余额：{c1}</br>存贷比：{c2} %'

            },
            grid: {
                top: '15%',
                left: '5%',
                right: '5%',
                bottom: '3%',
                containLabel: true,
            },
            xAxis: [
                {
                    type: 'category',
                    data: xAxisData,
                    axisLine: {
                        lineStyle: {
                            color: '#D6D6D6',
                        }
                    },
                    axisLabel: {
                        color: '#888888',
                        fontSize: 10,
                    },
                    axisTick: {
                        show: false
                    },
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#fff',
                        }
                    },
                    nameTextStyle: {
                        color: "#888888"
                    },
                    axisLabel: {
                        color: '#888888'
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(67,67,67,0.05)'
                        }
                    },
                },
                {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#fff',
                        }
                    },
                    nameTextStyle: {
                        color: "#888888"
                    },
                    axisLabel: {
                        color: '#888888'
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(67,67,67,0.05)'
                        }
                    },
                },
                {
                    type: 'value',
                    splitLine: {
                        show: false
                    },
                    nameTextStyle: {
                        color: "#888888"
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#fff',
                        }
                    },
                    axisLabel: {
                        color: '#888888'
                    }
                }
            ],
            series: seriesData
        };
        return option;
    }

</script>

</body>
</html>

