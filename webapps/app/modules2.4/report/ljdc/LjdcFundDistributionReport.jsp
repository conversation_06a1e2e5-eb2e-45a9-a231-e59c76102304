<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>地产资金分布情况</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style>
        #tagnav {
            position: fixed;
            top: 0;
            z-index: 999;
            background: #649CFF;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .weui-navigator-list li {
            line-height: 40px;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a {
            color: #93FFDB;
            font-weight: bold;
            border-bottom: 1px solid #93FFDB;
        }

        .weui-navigator-list li.weui-state-disabled a {
            color: rgba(255, 255, 255, 0.2);
            border-bottom: 1px solid #649CFF;
        }

        .weui-navigator-list li a {
            color: #ffffff;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a:after {
            background-color: transparent;
        }

        #reportTitle {
            padding-top: 43px;
            background: #649CFF;
            color: #fff;
        }

        #left, #right {
            display: inline-block;
            width: 48%;
            text-align: center;
        }

        .leftTip, .leftBottomTip, .rightBottomTip, .rightTip {
            display: inline-block;
            padding-top: 8px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            position: relative;
        }

        .leftTip:before, .leftBottomTip:before, .rightBottomTip:before, .rightTip:before {
            content: ' ';
            width: 11px;
            height: 11px;
            display: inline-block;
            border: 1px solid #ffffff;
            border-radius: 6px;
            position: absolute;
            top: 10px;
            left: -18px;
        }

        .leftTip:before {
            background: #FCE172;
        }

        .rightTip:before {
            background: #7BE5C2;
        }

        .leftBottomTip:before {
            background: #006aaf;
        }

        .rightBottomTip:before {
            background: #e5163a;
        }

        .leftAmount, .rightAmount {
            padding-bottom: 10px;
            display: block;
            font-size: 18px;
            font-weight: 500;
        }

        .line {
            display: inline-block;
            width: 1%;
            max-width: 1px;
            background: #fff;
            height: 30px;
        }

        .titleInfo {
            padding-left: 20px;
            padding-top: 18px;
            font-size: 17px;
            color: #5A5858;
            /*font-weight: bold;*/
            display: block;
        }

        .titleInfo-level_1 {
            padding-left: 20px;
            padding-top: 18px;
            font-size: 17px;
            font-weight: bold;
            color: #649CFF;
            display: block;
        }

        .level-1 {
            color: #649CFF;
            font-weight: bold;

        }

        .titleInfo-level_0 {
            padding-left: 20px;
            padding-top: 18px;
            font-size: 17px;
            font-weight: bold;
            color: #333333;
            display: block;
        }

        .level-0 {
            color: #333333;
            font-weight: bold;

        }

        .level-none {
            color: #999999;
        }

        .titleInfoArrow:after {
            content: " ";
            display: inline-block;
            height: 7px;
            width: 7px;
            border-width: 2px 2px 0 0;
            border-color: #c8c8cd;
            border-style: solid;
            transform: matrix(.71, .71, -.71, .71, 0, 0);
            webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);
            top: -2px;
            position: absolute;
            top: 50%;
            margin-top: -4px;
            right: 22px;
        }

        .amountAndTip {
            display: inline-block;
            width: calc(48% - 20px);
            padding-left: 20px;
        }

        .amountAndTip .amount {
            display: block;
            color: #FF7979;
            font-size: 16px;
            font-weight: bold;
        }

        .amountAndTip .tip {
            font-size: 10px;
            /*color: #999999;*/
            display: block;
        }

        #noMsg {
            position: relative;
            display: none;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }
    </style>
</head>
<body>
<div id="tagnav" class="weui-navigator weui-navigator-wrapper">
    <ul class="weui-navigator-list"></ul>
</div>
<div id="reportTitle"></div>
<div id="chartSection" hidden="hidden"></div>
<div id="chartListInfo"></div>
<div id="noMsg">
    <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
    <span>暂无数据</span>
</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/iscroll-lite.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script src="${ctx}/js/echarts.min.js"></script>

<script>

    var reportActiveCurrCode;
    $(function () {
        reportActiveCurrCode = sessionStorage.getItem('reportActiveCurrCode');

        let currBank = sessionStorage.getItem("FUND_CURR_BANK");
        var realCurrCode = 0;
        if (currBank !== null && currBank !== undefined && currBank !== '') {
            realCurrCode = currBank;
        }

        var tabIndex = 0;
        var currCodeArray = JSON.parse(reportActiveCurrCode);
        let tabs = $('.weui-navigator-list');
        let tabAllBank = $('<li data-currCode="0"><a>全部</a></li>');


        tabs.append(tabAllBank);
        for (let i = 0; i < currCodeArray.length; i++) {


            let tabItem = currCodeArray[i];
            let currName = tabItem.bankName;
            let currCode = tabItem.bankId;

            if (currCode == currBank) {
                tabIndex = i + 1;
            }

            let tab = $('<li data-currCode="' + currCode + '"><a>' + currName + '</a></li>');

            tabs.append(tab);
        }
        //使用这个是为了避免滑动时出现滚动条。
        TagNav('#tagnav', {
            type: 'scrollToNext',
            curClassName: 'weui-state',
            //不能使用weui-state-active，随便写一个，以免下面的重写失效。
            index: tabIndex
        });
        //重写切换事件和选中事件
        $("#tagnav li").eq(tabIndex).addClass('weui-state-active');
        $('#tagnav').on('click', 'li',
            function (event) {
                event.preventDefault();
                $(this).addClass('weui-state-active');
                if (!$(this).hasClass('weui-state-disabled')) {
                    $(this).siblings('li').removeClass('weui-state-active');
                    let currBank = $(this).attr('data-currCode');

                    requestData(currBank);
                    sessionStorage.setItem("FUND_CURR_BANK", currBank)
                }
            });
        requestData(realCurrCode);
        $(document.body).on('touchmove', function (e) {
            e.preventDefault();
        })
    });

    function requestData(currCode) {
        realCurrCode = currCode;

        showNoMsg(false);
        var param = {'bankId': currCode, 'tag': 'ats'};
        $.ajax({
            cache: true,
            type: 'POST',
            url: "${ctx}/ljdc/report/ljdcGetFundDistribution.do",
            data: param,
            async: true,
            dataType: 'json',
            beforeSend: function () {
                $.showLoading();
            },
            error: function () {
                $.hideLoading();
                $.toast('网络服务情况异常，请检查', "forbidden");
                showNoMsg(true);
            },
            success: function (data) {
                $.hideLoading();
                if (data.successful == true || "true" == data.successful) {
                    var dataArray = data.result.data;
                    if (dataArray.length == 3) {
                        //顶部数据
                        layoutTopTitle(dataArray[0].reportData);
                        //chart数据
                        /*$('#chartSection').html('');
                        $('#chartSection').append(
                            '<div id="chartView" style="width: 100%;height: 268px;"></div>' +
                            '<div class="chartPadding" style="width: 100%;height: 10px;background: #F1F3F5"></div>');
                        var myChart = echarts.init(document.getElementById('chartView'));
                        var option = dataCollation(dataArray[1].reportData);
                        myChart.setOption(option);*/
                        //列表数据
                        layoutListInfo(dataArray[2].reportData);
                    } else {
                        // 暂无数据
                        showNoMsg(true);
                    }
                } else {
                    $.toast('网络服务情况异常，请检查', "forbidden");
                    showNoMsg(true);
                }
            }
        });
    }

    function showNoMsg(show) {
        if (show) {
            $("#reportTitle").html('');
            $("#chartSection").html('');
            $("#chartListInfo").html('');
            $("#noMsg").css('display', 'block');
        } else {
            $("#noMsg").css('display', 'none');
        }
    }

    function layoutTopTitle(reportDataOne) {
        $("#reportTitle").html('');
        let dic = reportDataOne[0];
        $("#reportTitle").append(
            '<div id="left"><span class="leftTip">总金额/万元</span><span class="leftAmount">' + dic.totalAmount + '</span></div>' +
            '<div class="line"></div>' +
            '<div id="right"><span class="rightTip">自由金额/万元</span><span class="rightAmount">' + dic.ownAmount + '</span></div>' +
            '<div id="right"><span class="rightBottomTip">弱监管金额/万元</span><span class="rightAmount">' + dic.weakSupervisionAmount + '</span></div>' +
            '<div class="line"></div>' +
            '<div id="left"><span class="leftBottomTip">强监管金额/万元</span><span class="leftAmount">' + dic.strongSupervisionAmount + '</span></div>');

    }

    function layoutListInfo(reportDataThree) {

        delegate.ready(function (sysParam) {


            var param = {};
            $('#chartListInfo').html('');
            for (let i = 0; i < reportDataThree.length; i++) {
                let arrowStyle = "titleInfoArrow"
                let dic = reportDataThree[i];
                param[dic.plateId] = dic.plateName;

                if (dic.plateType == 0) {
                    let cell = $('<div class="cell" style="position: relative;" data-level=' + dic.plateType + ' data-item = ' + dic.plateName + ' data-id = ' + dic.plateId + '>' +
                        '<span class="titleInfo-level_1">' + dic.plateName + '</span>' +
                        '<div class="amountAndTip"><span class="amount">' + dic.totalAmount + '</span><span class="tip level-1">总金额</span></div>' +
                        '<div class="amountAndTip"><span class="amount">' + dic.ownAmount + '</span><span class="tip level-1">自由资金</span></div>' +
                        '<div class="amountAndTip"><span class="amount">' + dic.weakSupervisionAmount + '</span><span class="tip level-1">弱监管资金</span></div>' +
                        '<div class="amountAndTip"><span class="amount">' + dic.strongSupervisionAmount + '</span><span class="tip level-1">强监管资金</span></div>' +
                        '<div style="width: 100%;height: 10px;background: #ffffff"></div>' +
                        '<div class="cellPadding" style="width: 100%;height: 5px;background: #F1F3F5"></div>' +
                        '</div>');

                    $("#chartListInfo").append(cell);


                } else {

                    $("#chartListInfo").append(
                        '<div class="cell ' + arrowStyle + '" style="position: relative;" data-level=' + dic.plateType + ' data-item = ' + dic.plateName + ' data-id = ' + dic.plateId + '>' +
                        '<span class="titleInfo">' + dic.plateName + '</span>' +
                        '<div class="amountAndTip"><span class="amount">' + dic.totalAmount + '</span><span class="tip level-none">总金额</span></div>' +
                        '<div class="amountAndTip"><span class="amount">' + dic.ownAmount + '</span><span class="tip level-none">自由资金</span></div>' +
                        '<div class="amountAndTip"><span class="amount">' + dic.weakSupervisionAmount + '</span><span class="tip level-none">弱监管资金</span></div>' +
                        '<div class="amountAndTip"><span class="amount">' + dic.strongSupervisionAmount + '</span><span class="tip level-none">强监管资金</span></div>' +
                        '<div style="width: 100%;height: 10px;background: #ffffff"></div>' +
                        '<div class="cellPadding" style="width: 100%;height: 5px;background: #F1F3F5"></div>' +
                        '</div>');
                }
            }


            let canClick = true;
            document.addEventListener("touchstart", function (ev) {
                canClick = false;
            });

            document.addEventListener("touchend", function (ev) {
                canClick = true;
            });

            $(".cell").on('click', function (event) {
                event.preventDefault();
                if ($(this).hasClass('titleInfoArrow') && canClick) {
                    //可以钻取。
                    let plateName = $(this).attr('data-item');
                    let plateId = $(this).data('id');
                    let level = $(this).data('level');
                    let currCode = $('#tagnav .weui-state-active').attr('data-currcode');
                    sessionStorage.setItem('PLATEID_ORG', plateId);
                    sessionStorage.setItem('BANKID_ORG', currCode);
                    sessionStorage.setItem('LEVEL_ORG', level);
                    sessionStorage.setItem('PLATENAME_ORG', plateName);
                    sessionStorage.setItem('reportActiveCurrCode', reportActiveCurrCode);

                    location.href = "LjdcFundDistributionReportOrg.jsp";
                }
            });
        })

    }

    function dataCollation(reportDataTwo) {
        var xAxisData = [];
        var seriesData = [];

        var amounts = [];
        let dic = reportDataTwo[0];


        xAxisData.push("自由资金");
        xAxisData.push("弱监管资金");
        xAxisData.push("强监管资金");
        amounts.push(dic.ownAmount);
        amounts.push(dic.weakSupervisionAmount);
        amounts.push(dic.strongSupervisionAmount);


        let barData = {
            name: "金额",
            type: 'bar',
            stack: 'Itme',
            data: amounts
        }
        seriesData.push(barData);

        var option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            color: ['#FCE172'],
            grid: {
                top: '8%',
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true,
            },
            xAxis: [
                {
                    type: 'category',
                    data: xAxisData,
                    axisLine: {
                        lineStyle: {
                            color: '#D6D6D6',
                        }
                    },
                    axisLabel: {
                        color: '#888888',
                        fontSize: 10,
                        interval: 0,
                    },
                    axisTick: {
                        show: false
                    },

                }
            ],
            yAxis: [
                {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#fff',
                        }
                    },
                    axisLabel: {
                        color: '#888888'
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(67,67,67,0.05)'
                        }
                    }
                }
            ],
            series: seriesData
        };
        return option;
    }

</script>

</body>
</html>

