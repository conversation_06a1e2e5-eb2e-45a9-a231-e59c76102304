<%@ page import="com.fingard.app.delegate.framework.util.DateUtil" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<%
    String areaid = request.getParameter("areaid");
    String querydate = request.getParameter("querydate");
    String tenantid = request.getParameter("tenantid");


    String dateShow = DateUtil.formatDate(DateUtil.parse(querydate, "yyyy-MM-dd"), "yyyy年MM月dd日");
%>
<c:set var="areaid" value="<%=areaid%>"/>
<c:set var="querydate" value="<%=querydate%>"/>
<c:set var="tenantid" value="<%=tenantid%>"/>
<c:set var="dateShow" value="<%=dateShow%>"/>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>资金实时余额</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

    <style>
        html,
        body {
        }

        body {
            background: #eee;
            font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
            font-size: 14px;
            color: #000;
            margin: 0;
            padding: 0;
        }

        .divider {
            width: 95%;
            height: 1px;
            margin: 0 auto;
            background-color: #eeeeee;
        }

        .panel-divider {
            width: 100%;
            height: 10px;
            margin: 0 auto;
            background-color: #eeeeee;
        }

        .amount {
            color: #5e5e5e;
            font-weight: bold;
            font-size: 1.2em;

        }

        .jkzh-title {
            font-size: 18px;
            font-weight: bold;
            color: #0d0d0d;
        }

        .jkzh-info {
            font-size: 16px;
            color: #0d0d0d;
        }

        .jkzh-sub-title {
            font-size: 15px;
            font-weight: bold;
            color: #0d0d0d;
        }

        .weui-form-preview__bd {

            padding: 5px 15px 0;
        }

        .jkzh-header {
            padding: 5px 15px 0;
        }

        .unit-label {
            position: absolute;
            right: 15px;
        }


    </style>
</head>
<body>
<div class="container">


</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script src="//g.alicdn.com/dingding/dingtalk-jsapi/2.6.41/dingtalk.open.js"></script>

<script>

    let getData = function () {

        let params = {};
        params.areaid = '${areaid}';
        params.querydate = '${querydate}';
        params.tenantid = '${tenantid}';


        $.showLoading();
        ajaxRequest('/jkzh/getDingTalkAccountBalance.do', params, function (data) {
            $.hideLoading();
            console.log(data);

            let container = $('.container');

            let resultData = data.result;


            if (resultData == null) {
                $.alert({
                    title: '数据获取异常',
                    text: '预警消息查询失败',
                    onOK: function () {
                        dd.biz.navigation.close({
                            onSuccess: function (result) {
                            },
                            onFail: function (err) {
                            }
                        })

                    }
                });

                return;
            }
            let raisedBalanceList = resultData.raisedBalanceList;
            let selfFundTotalList = resultData.selfFundTotalList;
            let selfFundDetailList = resultData.selfFundDetailList;
            let i = 0;
            var balanceHtml = '';
            for (let data of raisedBalanceList) {
                balanceHtml +=
                    "<div class=\"weui-form-preview__bd\">\n" +
                    "            <div class=\"weui-form-preview__item\">\n" +
                    "                <label class=\"weui-form-preview__label\">币种</label>\n" +
                    "                <span class=\"weui-form-preview__value\">" + data.currencyName + "</span>\n" +
                    "            </div>\n" +
                    "            <div class=\"weui-form-preview__item\">\n" +
                    "                <label class=\"weui-form-preview__label\">余额</label>\n" +
                    "                <span class=\"weui-form-preview__value amount\">" + data.amount + "</span>\n" +
                    "            </div>\n" +
                    " </div>";
            }
            let dataHtml = '<div class="weui-form-preview">\n' +
                '            <div class="jkzh-header">\n' +
                '                <div class=" jkzh-title">资金实时余额</div>' +
                '                <div class=" jkzh-info">日期：${dateShow}&nbsp; <span class="unit-label">单位：万元<span></div>' +
                '            </div>\n' +
                '    <div class="panel-divider"></div>' +
                '            <div class="weui-form-preview__hd">\n' +
                '                <label class="weui-form-preview__label jkzh-sub-title">募集资金</label>\n' +
                '                <em class="weui-form-preview__value">&nbsp;</em>\n' +
                '            </div>\n' +
                balanceHtml +
                '    <div class="panel-divider"></div>';
            container.append(dataHtml);

            let totalInfo = '<div class="weui-form-preview">\n' +
                '            <div class="weui-form-preview__hd">\n' +
                '                <label class="weui-form-preview__label jkzh-sub-title">自有资金（可动用）</label>\n' +
                '                <em class="weui-form-preview__value amount">' + selfFundTotalList[0].amount + '</em>\n' +
                '            </div>\n' +
                '            </div>\n' +
                "    <div class=\"panel-divider\"></div>";
            // container.append(totalInfo);

            var balanceDetailHtml = '';
            for (let data of selfFundDetailList) {
                balanceDetailHtml +=
                    "<div class=\"weui-form-preview__bd\">\n" +
                    "            <div class=\"weui-form-preview__item\">\n" +
                    "                <label class=\"weui-form-preview__label\">" + data.areaName + "</label>\n" +
                    "                <span class=\"weui-form-preview__value amount\">" + data.amount + "</span>\n" +
                    "            </div>\n" +
                    " </div>";
            }
            let detailHtml = '<div class="weui-form-preview jkzh-detail">\n' +
                '            <div class="weui-form-preview__hd">\n' +
                '                <label class="weui-form-preview__label jkzh-sub-title">自有资金（可动用）</label>\n' +
                '                <em class="weui-form-preview__value">' + selfFundTotalList[0].amount + '</em>\n' +
                '            </div>\n' +
                balanceDetailHtml;
            container.append(detailHtml);
            $.hideLoading();


        });

    };


    $(document).ready(function () {
        console.log("has ready");
        getData();

    });


</script>
</body>
</html>

