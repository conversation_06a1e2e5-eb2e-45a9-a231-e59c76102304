<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>统计分析</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

    <style>
        body {
            background: #F2F4F6;
            font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
            font-size: 14px;
            color: #000;
            margin: 0;
            padding: 0;
        }


        #no-permission {
            position: relative;
            display: none;
        }

        #no-permission img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        #no-permission span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }

        .weui-grids {
            padding-bottom: 55px;
            margin-top: 10px;
        }

        .weui-grids:before {
            border-top: 20px solid #F2F4F6;
        }

        .weui-grids:after {
            border-left: 20px solid #F2F4F6;
        }

        .weui-grid {
            background: #fff;
            padding: 30px 10px;
            width: 50%;
        }

        .weui-grid:before {
            border-right: 20px solid #F2F4F6;
        }

        .weui-grid:after {
            border-bottom: 20px solid #F2F4F6;
        }

        .weui-grid__icon {
            width: 64px;
            height: 64px;
        }

        .weui-tabbar__label {
            font-size: 6px;
            line-height: 16px;
        }

        .weui-tabbar__item.weui-bar__item--on .weui-tabbar__label {
            color: #649CFF;
        }


    </style>
</head>
<body>
<div class="weui-grids" id="reportTypeList">

</div>
<div id="no-permission" style="display: none;">
    <img src="${ctx}/img/report/<EMAIL>" alt="暂无权限">
    <span>暂无权限</span>
</div>


<%@include file='/common/bottom_bar.jsp' %>


<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script>

    const LJDC_REPORT = "RQQS76";
    const CQYZ_REPORT1 = "RQQS79";
    const CQYZ_REPORT2 = "RQQS81";
    const LJDC_REPORT_FUND_DISTRIBUTION = "RQQS90";
    const LJDC_REPORT_BANK_FINANCING = "RQQS93";
    const KMMY_REPORT_PAYANDREC = "RQQS115";
    const JDGROUP_WEEK_REPORT = "RQQS132";
    const BANK_ACCOUNT_BALANCE_SUMMARY = "RQQS133";
    const CNTC_ACCOUNT_DETAIL = "FDQS06";
    const TQLS_FUTURE_LOAN_REPAYMENT = "RQQS143";
    const TQLS_BALANCE_RECONCILIATION = "RQQS162";


    let reportOptions = {
        'RQQS01': {
            name: '资金汇总统计',
            fileName: 'CapitalSummaryReport',
            iconImg: 'report_cunliang',
            code: 'RQQS01'
        },
        'RQQS03': {
            name: '银行余额统计',
            fileName: 'BankBalanceReport',
            iconImg: 'report_bank',
            code: 'RQQS03'
        },
        'RQQS05': {
            name: '付款结算量次',
            fileName: 'PaymentSettlementReport',
            iconImg: 'report_liangci',
            code: 'RQQS05'
        },
        'RQQS07': {
            name: '资金池报告',
            fileName: 'MergeOneReport',
            iconImg: 'report_zijinchi',
            code: ''
        },
        'RQQS09': {
            name: '应收票据到期预测',
            fileName: 'MergeOneReport',
            iconImg: 'report_piaoju',
            code: 'RQQS09'
        },
        'RQQS61': {
            name: '信用证到期预测',
            fileName: 'LetterOfCreditReport',
            iconImg: 'report_xinyong',
            code: 'RQQS61'
        },
        'RQQS63': {
            name: '融资到期情况',
            fileName: 'MaturityOfFinancingReport',
            iconImg: 'report_rzdaoqi',
            code: 'RQQS63'
        },
        'RQQS65': {
            name: '融资余额分布情况',
            fileName: 'DistributionMaturityOfFinancingReport',
            iconImg: 'report_rzyue',
            code: 'RQQS63'
        },
        'RQQS67': {
            name: '授信情况',
            fileName: 'CreditReport',
            iconImg: 'report_shouxin',
            code: 'RQQS67'
        },
        'RQQS69': {
            name: '账户可用资金统计',
            fileName: 'CanUseAmountSummaryReport',
            iconImg: 'report_keyong',
            code: 'RQQS69'
        },
        'RQQS71': {
            name: '资金池余额分布',
            fileName: 'MergeOneReport',
            iconImg: 'report_zijinchiyue',
            code: 'RQQS71'
        },
        'RQQS73': {
            name: '单位资金统计',
            fileName: 'JyGroupSummaryReport',
            iconImg: 'report_jyzijin',
            code: 'RQQS73'
        },
        'RQQS76': {
            name: '集团资金存量日报表',
            fileName: 'ldjc',
            iconImg: 'report_bank',
            code: 'RQQS69'
        },
        'RQQS79': {
            name: '各区域余额明细报表',
            fileName: 'cqyz',
            iconImg: 'report_bank',
            code: 'RQQS71'
        },
        'RQQS81': {
            name: '可调用资金余额汇总',
            fileName: 'cqyz',
            iconImg: 'report_bank',
            code: 'RQQS73'
        },
        'RQQS82': {
            name: '理财资金统计',
            fileName: 'FinancialBalance',
            iconImg: 'report_jyzijin',
            code: 'RQQS82'
        },
        'RQQS90': {
            name: '地产资金分布情况',
            fileName: 'ljdc/LjdcFundDistributionReport.jsp',
            iconImg: 'report_licai',
            code: 'RQQS90'
        },
        'RQQS93': {
            name: '银行类融资情况',
            fileName: 'ljdc/LjdcBankFinancingReport.jsp',
            iconImg: 'report_rzyue',
            code: 'RQQS93'
        },
        'RQQS114': {
            name: '账户融资经营性资金管理报表',
            fileName: 'cdct_financing_operating_report',
            iconImg: 'report_rzyue',
            code: 'RQQS114'
        },
        'RQQS121': {
            name: '资金余额表',
            fileName: 'kmmy_fund_balance',
            iconImg: 'report_bank',
            code: 'RQQS121'
        },
        'RQQS115': {
            name: '克明面业收支统计表',
            fileName: 'KMMYPaymentsStatistics',
            iconImg: 'report_shouzhi',
            code: 'RQQS115'
        },
        'RQQS124': {
            name: '银行账户余额汇总表',
            fileName: 'SCZYBankBalance',
            iconImg: 'report_bank',
            code: 'RQQS124'
        },

        'RQQS132': {
            name: '资金周报表',
            fileName: 'JdGroupFundWeekReport',
            iconImg: 'report_keyong',
            code: 'RQQS132'
        },

        'RQQS133': {
            name: '银行账户余额汇总表',
            fileName: 'new_fund_summary_bank_balance_financing',
            iconImg: 'report_bank',
            code: 'RQQS133'
        },
        'FDQS06': {
            name: '账户明细查询',
            fileName: 'cntc_account_detail',
            iconImg: 'report_keyong',
            code: 'FDQS06'
        },
        'RQQS143': {
            name: '未来贷款还款表',
            fileName: 'tqls_future_loan_repayment',
            iconImg: 'report_keyong',
            code: 'RQQS143'
        },
        'RQQS156': {
            name: '融资到期情况',
            fileName: 'cdcyjt_financing_out_of_date',
            iconImg: 'report_keyong',
            code: 'RQQS143'
        },
        'RQQS158': {
            name: '融资余额分布',
            fileName: 'cdcyjt_financing_distribution',
            iconImg: 'report_rzyue',
            code: 'RQQS143'
        },
        'RQQS162': {
            name: '余额对账查看',
            fileName: 'tqls_balance_reconciliation',
            iconImg: 'report_bank',
            code: 'RQQS162'
        },
        'RQQS165': {
            name: '资金统计表',
            fileName: 'zjsjk_fund_flow_situation',
            iconImg: 'report_keyong',
            code: 'RQQS165'
        },

    };


    var report_activeCurrCodes = [];

    // 清除
    sessionStorage.removeItem("CQYZ_ID");
    sessionStorage.removeItem("CQYZ_NAME");
    sessionStorage.removeItem("CQYZ_DATE");
    sessionStorage.removeItem("CQYZ_CALLABLE_DATE");
    sessionStorage.removeItem("FUND_CURR_BANK");
    sessionStorage.removeItem("TQLS_CURRENT_UNIT_ID");
    sessionStorage.removeItem("TQLS_CURRENT_UNIT_NAME");


    function GoToReportCon(href, code, reportType) {
        if (code === CNTC_ACCOUNT_DETAIL) {
            sessionStorage.setItem('reportTitle', '账户明细查询');
            location.href = 'standard/cntc_account_detail.jsp';
            return;
        }

        if (code === KMMY_REPORT_PAYANDREC) {
            GoToKMMYReportCon(1);
            return;
        }
        if (code === JDGROUP_WEEK_REPORT) {
            sessionStorage.setItem('reportTitle', '资金周报表');
            location.href = 'standard/JdGroupFundWeekReport.jsp';
            return;
        }

        if (code === BANK_ACCOUNT_BALANCE_SUMMARY) {
            sessionStorage.setItem('reportTitle', '银行账户余额汇总表');
            location.href = 'standard/new_fund_summary_bank_balance_financing.jsp';
            return;
        }

        if (code === TQLS_FUTURE_LOAN_REPAYMENT) {
            sessionStorage.setItem('reportTitle', '未来贷款还款表');
            location.href = 'standard/tqls_future_loan_repayment.jsp';
            return;
        }

        if (code === TQLS_BALANCE_RECONCILIATION) {
            sessionStorage.setItem('reportTitle', '余额对账情况');
            location.href = 'standard/tqls_balance_reconciliation.jsp';
            return;
        }

        ddAjaxRequestCallback('加载中', '/saasreport/getReportActiveCurrCode.do', {'reportType': reportType}, function (data) {
            if (data.result.totalNum === 0) {
                $.toast("暂无数据", "cancel");
                return;
            }
            var flag = false;
            var dataList = data.result.data[0];
            var activeCurrCodes = dataList.reportActiveCurrCode;
            //拿到币种信息
            for (var i = 0; i < activeCurrCodes.length; i++) {
                let dic = activeCurrCodes[i];
                if (dic['hasData'] === true) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                //获取币种信息
                sessionStorage.setItem('reportTitle', reportOptions[code].name)
                sessionStorage.setItem('reportActiveCurrCode', JSON.stringify(activeCurrCodes));
                sessionStorage.setItem('currCode', '0');
                location.href = href;
            } else {
                $.toast("暂无数据", "cancel");
            }
        });


    }

    function GoToLjdcReportCon(paramsStr) {

        let params = JSON.parse(decodeURI(paramsStr));
        let url = '/saasreport/getActiveBank.do';
        if (params.url) {
            url = params.url;
        }
        ddAjaxRequestCallback('加载中', url, {"reportType": params.reportType}, function (data) {

            if (data.result.totalNum === 0) {
                $.toast("暂无数据", "cancel");
                return;
            }
            sessionStorage.setItem('reportTitle', params.name);
            sessionStorage.setItem('reportActiveCurrCode', JSON.stringify(data.result.data));
            location.href = params.page;
        })
    }


    function GoToCQYZReportCon(type) {
        if (type === 1) {
            sessionStorage.setItem('reportTitle', '各区域余额明细报表');
            location.href = 'cqyz/cqyzSummaryPicker.jsp';
        }
        if (type === 2) {
            sessionStorage.setItem('reportTitle', '可调用资金余额汇总');
            location.href = 'cqyz/cqyzDatePicker.jsp';
        }
    }

    function GoToKMMYReportCon(type) {
        if (type === 1) {
            sessionStorage.setItem('reportTitle', '克明面业收支统计表');
            location.href = 'standard/newKMMYPaymentsStatistics.jsp';
        }

    }

    $(function () {
        ddAjaxRequestCallback('加载中', '/saasreport/getReportTypeLocal.do', {}, function (data) {

            var permissionCodeStr = '${permissionCode}';
            let reportGridView = $('#reportTypeList');
            reportGridView.html('');
            let count = 0;

            for (let report of data.result.data) {
                let permissionCode = report.reportPermissionCode;
                let reportType = report.reportType;

                if (permissionCodeStr.indexOf(permissionCode) !== -1) {
                    // 当前报表有权限
                    count++;
                    // report_activeCurrCodes[permissionCode] = report.reportActiveCurrCode;
                    if (reportOptions[permissionCode]) {
                        let href = "standard/" + reportOptions[permissionCode].fileName + ".jsp";
                        let grid = $('<a href="javascript:GoToReportCon(\'' + href + '\', \'' + permissionCode + '\', \'' + reportType + '\');" class="weui-grid js_grid">' +
                            '<div class="weui-grid__icon">' +
                            '<img src="../../img/report/' + reportOptions[permissionCode].iconImg + '@3x.png" alt="">' +
                            '</div>' +
                            '<p class="weui-grid__label">' + reportOptions[permissionCode].name + '</p>' +
                            '</a>');
                        reportGridView.append(grid);
                    }
                }
            }
            if (permissionCodeStr.indexOf(LJDC_REPORT) !== -1 && '${customer}' === 'ljdc') {
                count++;
                let params = {};
                params.name = '集团资金存量日报表';
                params.page = 'ljdc/LjdcCapitalStockDailyReport.jsp';
                params.reportType = 13;

                reportGridView.prepend(
                    '<a href="javascript:GoToLjdcReportCon(\'' + encodeURI(JSON.stringify(params)) + '\');" class="weui-grid js_grid">' +
                    '<div class="weui-grid__icon">' +
                    '<img src="../../img/report/' + reportOptions[LJDC_REPORT].iconImg + '@3x.png" alt="">' +
                    '</div>' +
                    '<p class="weui-grid__label">' + reportOptions[LJDC_REPORT].name + '</p>' +
                    '</a>');
            }
            if (permissionCodeStr.indexOf(CQYZ_REPORT1) !== -1 && '${customer}' === 'cqyz') {
                count++;
                reportGridView.append(
                    '<a href="javascript:GoToCQYZReportCon(1);" class="weui-grid js_grid">' +
                    '<div class="weui-grid__icon">' +
                    '<img src="../../img/report/' + reportOptions[CQYZ_REPORT1].iconImg + '@3x.png" alt="">' +
                    '</div>' +
                    '<p class="weui-grid__label">' + reportOptions[CQYZ_REPORT1].name + '</p>' +
                    '</a>');
            }
            if (permissionCodeStr.indexOf(CQYZ_REPORT2) !== -1 && '${customer}' === 'cqyz') {
                count++;
                reportGridView.append(
                    '<a href="javascript:GoToCQYZReportCon(2);" class="weui-grid js_grid">' +
                    '<div class="weui-grid__icon">' +
                    '<img src="../../img/report/' + reportOptions[CQYZ_REPORT2].iconImg + '@3x.png" alt="">' +
                    '</div>' +
                    '<p class="weui-grid__label">' + reportOptions[CQYZ_REPORT2].name + '</p>' +
                    '</a>');
            }

            if (permissionCodeStr.indexOf(LJDC_REPORT_FUND_DISTRIBUTION) !== -1 && '${customer}' === 'ljdc') {
                count++;
                let params = {};
                params.name = reportOptions[LJDC_REPORT_FUND_DISTRIBUTION].name;
                params.page = reportOptions[LJDC_REPORT_FUND_DISTRIBUTION].fileName;
                params.reportType = 20;
                reportGridView.prepend(
                    '<a href="javascript:GoToLjdcReportCon(\'' + encodeURI(JSON.stringify(params)) + '\');" class="weui-grid js_grid">' +
                    '<div class="weui-grid__icon">' +
                    '<img src="../../img/report/' + reportOptions[LJDC_REPORT_FUND_DISTRIBUTION].iconImg + '@3x.png" alt="">' +
                    '</div>' +
                    '<p class="weui-grid__label">' + reportOptions[LJDC_REPORT_FUND_DISTRIBUTION].name + '</p>' +
                    '</a>');
            }

            if (permissionCodeStr.indexOf(LJDC_REPORT_BANK_FINANCING) !== -1 && '${customer}' === 'ljdc') {
                count++;
                let params = {};
                params.name = reportOptions[LJDC_REPORT_BANK_FINANCING].name;
                params.page = reportOptions[LJDC_REPORT_BANK_FINANCING].fileName;
                params.url = '/ljdc/report/ljdcGetActiveBankLocation.do';
                params.reportType = 21;
                reportGridView.prepend(
                    '<a href="javascript:GoToLjdcReportCon(\'' + encodeURI(JSON.stringify(params)) + '\');" class="weui-grid js_grid">' +
                    '<div class="weui-grid__icon">' +
                    '<img src="../../img/report/' + reportOptions[LJDC_REPORT_BANK_FINANCING].iconImg + '@3x.png" alt="">' +
                    '</div>' +
                    '<p class="weui-grid__label">' + reportOptions[LJDC_REPORT_BANK_FINANCING].name + '</p>' +
                    '</a>');
            }

            if (count === 0) {
                $('#no-permission').css('display', 'block');
            }

        });
    })

</script>


</body>
</html>

