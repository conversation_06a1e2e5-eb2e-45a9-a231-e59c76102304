<%@ page contentType="text/html;charset=UTF-8" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css">
    <link rel="stylesheet" href="${ctx}/css/jquery-weui.min.css">
    <style>

    </style>
</head>
<body>

<div class="weui-cells">
    <a class="weui-cell weui-cell_access" href="javascript:goSelectArea();">
        <div class="weui-cell__hd">
            <div class="weui-cell__hd"><label for="area-name" class="weui-label">区域</label></div>
        </div>
        <div id="area-name" class="weui-cell__bd">
        </div>
        <div class="weui-cell__ft">
        </div>
    </a>
    <div class="weui-cell">
        <div class="weui-cell__hd"><label for="datetime-picker" class="weui-label">截止日期</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" id="datetime-picker" type="text" readonly="readonly">
        </div>
    </div>
</div>

<div id="search" class="weui-btn weui-btn_primary" style="margin-left: 15px; margin-right:15px;margin-top: 30px">查询
</div>

<div id="reset" class="weui-btn weui-btn_default" style="margin-left: 15px; margin-right:15px;">重置
</div>
<script src="${ctx}/js/jquery.min.js"></script>
<script src="${ctx}/js/jquery-weui.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>

<script>

    var isPageHide = false;
    window.addEventListener('pageshow', function () {
        if (isPageHide) {
            window.location.reload();
        }
    });
    window.addEventListener('pagehide', function () {
        isPageHide = true;
    });

    document.title = sessionStorage.getItem('reportTitle');
    let ids = sessionStorage.getItem("CQYZ_ID");
    let names = sessionStorage.getItem("CQYZ_NAME");
    let preDate = sessionStorage.getItem("CQYZ_DATE");

    let area = $('#area-name');
    let datePicker = $('#datetime-picker');
    area.attr('data-ids', ids);
    area.html(names);
    datePicker.val((preDate == null || preDate === '') ? nowDate() : preDate);

    if (ids === null || ids === '') {
        resetPicker();
    }

    $(document).ready(function () {
        datePicker.datetimePicker({
            title: '截止日期',
            defaultValue: [new Date().getFullYear(), new Date().getMonth() + 1, new Date().getDate()],

            times: function () {
                return [];
            },
            parse: function (str) {
                return str.split("-");
            },
            onChange: function (picker, values, displayValues) {
                console.log(values);
            }
        });
        $('#search').on('click', function () {
            let date = datePicker.val();
            let orgIds = area.data('ids');
            if (date === '') {
                $.toast("截止日期不能为空", 'cancel');
            } else if (orgIds === '' || orgIds === '-1') {
                $.toast("查询区域不能为空", 'cancel');
            } else {

                console.log(date);
                console.log(orgIds);
                sessionStorage.setItem("CQYZ_DATE", date);

                window.location.href = 'cqyzSummaryBalance.jsp?date=' + date;
                sessionStorage.setItem("CQYZ_ORG_IDS", orgIds);
            }
        });

        $('#reset').on('click', function () {
            resetPicker();
        })


    });

    function goSelectArea() {
        window.location.href = 'cqyzAreaSelect.jsp';
    }

    function nowDate() {
        let date = new Date();
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        if (month < 10) {
            month = '0' + month;
        }
        let day = date.getDate();
        if (day < 10) {
            day = '0' + day;
        }

        return year + '-' + month + '-' + day;
    }

    function resetPicker() {
        // 默认选择全部
        area.attr('data-ids', '0');
        area.html('全部');
        datePicker.val(nowDate());
        // 清除
        sessionStorage.removeItem("CQYZ_ID");
        sessionStorage.removeItem("CQYZ_NAME");
        sessionStorage.removeItem("CQYZ_DATE");
    }
</script>
</body>
</html>
