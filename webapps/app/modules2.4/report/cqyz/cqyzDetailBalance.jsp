<%@ page contentType="text/html;charset=UTF-8" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css">
    <link rel="stylesheet" href="${ctx}/css/jquery-weui.min.css">
    <style>
        .weui-cell {
            height: 180px;
        }

        .title {
            color: #5A5858;
            font-size: 14px;
            font-weight: bold;
            position: absolute;
            top: 10px;

        }

        .bottom {

            position: absolute;
            top: 145px;
            color: #FFB06B;
        }

        .start {
            font-size: 16px;
            color: #FF7979;
            position: absolute;
            top: 35px;
        }

        .center {
            font-size: 16px;
            color: #FF7979;
            position: absolute;
            top: 35px;
            left: 44%;
        }

        .end {
            font-size: 16px;
            color: #FF7979;
            position: absolute;
            top: 35px;
            left: 80%;
        }

        .start-label {
            position: absolute;
            top: 60px;
        }

        .center-label {
            position: absolute;
            top: 60px;
            left: 44%;
        }

        .end-label {

            position: absolute;
            top: 60px;
            left: 80%;
        }


        .start-bottom {
            font-size: 16px;
            color: #FF7979;
            position: absolute;
            top: 85px;
        }

        .center-bottom {
            font-size: 16px;
            color: #FF7979;
            position: absolute;
            top: 85px;
            left: 44%;
        }

        .end-bottom {
            font-size: 16px;
            color: #FF7979;
            position: absolute;
            top: 85px;
            left: 80%;
        }

        .start-label-bottom {
            position: absolute;
            top: 110px;
        }

        .center-label-bottom {
            position: absolute;
            top: 110px;
            left: 44%;
        }

        .end-label-bottom {

            position: absolute;
            top: 110px;
            left: 80%;
        }

        .bottom-label {
            position: absolute;
            top: 170px;
        }

        .label {
            max-width: 120px;
            font-size: 10px;
            color: grey;
        }

        .header-content {
            color: white;
        }
    </style>
</head>
<body>
<div style="padding: 15px">
    <div class="">报告截止日期：${param.date}</div>
    <div class="">单位：万元</div>
</div>

<div class="weui-cells" id="list" style="margin-top: 6px">

</div>


<script src="${ctx}/js/jquery.min.js"></script>
<script src="${ctx}/js/jquery-weui.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>

<script>

    $(document).ready(function () {
        let orgId = sessionStorage.getItem("CQYZ_ORG_ID");
        let orgName = sessionStorage.getItem("CQYZ_ORG_NAME");
        let date = '${param.date}';
        getData(orgId, orgName, date);

    });

    let getData = function (orgIds, orgName, date) {
        let url = '/cqyz/report/cqyzGetBalanceDetails.do';
        let params = {};
        params.orgId = orgIds;
        params.orgName = orgName;
        params.date = date;
        $.showLoading("加载中...");
        ajaxRequest(url, params, function (data) {
            console.log(data);

            let cells = $('.weui-cells');
            let header = data.result.data.total[0];
            let leafList = data.result.data.leaf;
            document.title = header.orgName;

            let rootCell = '<div style="background-color: #649CFF; height: 150px" class="weui-cell">' +
                '        <div class="title header-content">' + header.orgName + '</div>' +
                '        <div class="start header-content">' + header.totalBalance + '</div>' +
                '        <div class="center header-content">' + header.regulatedBalance + '</div>' +
                '        <div class="end header-content">' + header.unRegulatedBalance + '</div>' +
                '        <div class="label start-label header-content">账面总资金余额</div>' +
                '        <div class="label center-label header-content">监管资金余额</div>' +
                '        <div class="label end-label header-content">非监管资金余额</div>' +
                '        <div class="start-bottom header-content">' + header.totalBalanceOfHkl + '</div>' +
                '        <div class="center-bottom header-content">' + header.regulatedBalanceOfHkl + '</div>' +
                '        <div class="end-bottom header-content">' + header.unRegulatedBalanceOfHkl + '</div>' +
                '        <div class="label start-label-bottom header-content">账面总资金余额<br/>(HKL share)</div>' +
                '        <div class="label center-label-bottom header-content">监管资金余额<br/>(HKL share)</div>' +
                '        <div class="label end-label-bottom header-content">非监管资金余额<br/>(HKL share)</div>' +
                '    </div>';

            cells.append(rootCell);

            for (let cellData of leafList) {
                let cell = '<div class="weui-cell">' +
                    '        <div class="title">' + cellData.orgName + '</div>' +
                    '        <div class="start">' + cellData.totalBalance + '</div>' +
                    '        <div class="center">' + cellData.regulatedBalance + '</div>' +
                    '        <div class="end">' + cellData.unRegulatedBalance + '</div>' +
                    '        <div class="label start-label">账面总资金余额</div>' +
                    '        <div class="label center-label">监管资金余额</div>' +
                    '        <div class="label end-label">非监管资金余额</div>' +
                    '        <div class="start-bottom ">' + cellData.totalBalanceOfHkl + '</div>' +
                    '        <div class="center-bottom">' + cellData.regulatedBalanceOfHkl + '</div>' +
                    '        <div class="end-bottom">' + cellData.unRegulatedBalanceOfHkl + '</div>' +
                    '        <div class="label start-label-bottom">账面总资金余额<br/>(HKL share)</div>' +
                    '        <div class="label center-label-bottom">监管资金余额<br/>(HKL share)</div>' +
                    '        <div class="label end-label-bottom">非监管资金余额<br/>(HKL share)</div>' +
                    '        <div class="bottom">' + cellData.proportion + '</div>' +
                    '        <div class="label bottom-label">HKL持股占比</div>' +
                    '    </div>';

                cells.append(cell);
            }

        })

    }

</script>
</body>
</html>
