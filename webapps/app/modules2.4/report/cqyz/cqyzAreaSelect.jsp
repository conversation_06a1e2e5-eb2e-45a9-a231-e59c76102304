<%@ page contentType="text/html;charset=UTF-8" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css">
    <link rel="stylesheet" href="${ctx}/css/jquery-weui.min.css">
    <style>

    </style>
</head>
<body>

<div class="weui-cells weui-cells_checkbox" style="margin-bottom: 100px">


</div>

<div class="weui-footer_fixed-bottom"
     style="background-color: white; padding: 15px; bottom: 0;z-index: 2; text-align: center">
    <div id="confirm" class="weui-btn weui-btn_primary weui-btn_inline">确定</div>
    <div id="cancel" class="weui-btn weui-btn_default weui-btn_inline" onclick="history.back()">取消</div>
</div>

<script src="${ctx}/js/jquery.min.js"></script>
<script src="${ctx}/js/jquery-weui.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>

<script>
    document.title = sessionStorage.getItem('reportTitle');

    $(document).ready(function () {

        getData();

        $('#confirm').on('click', function () {

            let ids = [];
            let names = [];
            $("input:checkbox[class='weui-check']:checked").each(function () {
                ids.push($(this).attr('id'));
                let name = $(this).parent().parent().find('p').html();
                names.push(name);
            });

            if (ids.indexOf('0') !== -1 && ids.length > 1) {
                $.toast("全部和具体区域不能同时勾选！", 'cancel');
                return;
            }

            if (ids.length === 1 && ids[0] === '0') {
                sessionStorage.removeItem("CQYZ_ID");
                sessionStorage.removeItem("CQYZ_NAME");
            } else {
                sessionStorage.setItem("CQYZ_ID", ids.join(','));
                sessionStorage.setItem("CQYZ_NAME", names.join(','));
            }

            console.log(ids.join(','));
            console.log(names.join(','));
            history.back();


        });


    });

    function getData() {

        let url = '/cqyz/report/cqyzGetEntity.do';
        let params = {};
        $.showLoading("加载中...");
        ajaxRequest(url, params, function (data) {
            let ids = sessionStorage.getItem("CQYZ_ID");

            let listData = data.result.data;
            if (listData.length > 0) {

                let cells = $('.weui-cells');
                cells.html('');
                let cellHeader;
                if (ids === null || ids === '' || ids.length === 0) {
                    cellHeader = '<label class="weui-cell weui-check__label" for="0">' +
                        '    <div class="weui-cell__hd">' +
                        '      <input type="checkbox" class="weui-check" name="0" id="0" checked="checked">' +
                        '      <i class="weui-icon-checked"></i>' +
                        '    </div>' +
                        '    <div class="weui-cell__bd">' +
                        '      <p>全部</p>' +
                        '    </div>' +
                        '  </label>';
                } else {
                    cellHeader = '<label class="weui-cell weui-check__label" for="0">' +
                        '    <div class="weui-cell__hd">' +
                        '      <input type="checkbox" class="weui-check" name="0" id="0">' +
                        '      <i class="weui-icon-checked"></i>' +
                        '    </div>' +
                        '    <div class="weui-cell__bd">' +
                        '      <p>全部</p>' +
                        '    </div>' +
                        '  </label>';
                }

                cells.append(cellHeader);
                for (let cellData of listData) {
                    let cell;
                    if (ids != null && ids.indexOf(cellData.entityId) !== -1) {
                        cell = '<label class="weui-cell weui-check__label" for="' + cellData.entityId + '">' +
                            '    <div class="weui-cell__hd">' +
                            '      <input checked= "checked" type="checkbox" class="weui-check" name="' + cellData.entityId + '" id="' + cellData.entityId + '">' +
                            '      <i class="weui-icon-checked"></i>' +
                            '    </div>' +
                            '    <div class="weui-cell__bd">' +
                            '      <p>' + cellData.entityName + '</p>' +
                            '    </div>' +
                            '  </label>';
                    } else {
                        cell = '<label class="weui-cell weui-check__label" for="' + cellData.entityId + '">' +
                            '    <div class="weui-cell__hd">' +
                            '      <input type="checkbox" class="weui-check" name="' + cellData.entityId + '" id="' + cellData.entityId + '">' +
                            '      <i class="weui-icon-checked"></i>' +
                            '    </div>' +
                            '    <div class="weui-cell__bd">' +
                            '      <p>' + cellData.entityName + '</p>' +
                            '    </div>' +
                            '  </label>';
                    }
                    cells.append(cell);
                }

            }
        });
    }
</script>
</body>
</html>
