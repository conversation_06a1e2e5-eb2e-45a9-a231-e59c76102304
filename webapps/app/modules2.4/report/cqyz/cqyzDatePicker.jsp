<%@ page contentType="text/html;charset=UTF-8" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css">
    <link rel="stylesheet" href="${ctx}/css/jquery-weui.min.css">
    <style>

    </style>
</head>
<body>

<div class="weui-cells">
    <div class="weui-cell">
        <div class="weui-cell__hd"><label for="datetime-picker" class="weui-label">截止日期</label></div>
        <div class="weui-cell__bd">
            <input class="weui-input" id="datetime-picker" type="text" readonly="readonly">
        </div>
    </div>
</div>

<div id="search" class="weui-btn weui-btn_primary" style="margin-left: 15px; margin-right:15px;margin-top: 30px">查询
</div>
<script src="${ctx}/js/jquery.min.js"></script>
<script src="${ctx}/js/jquery-weui.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>

<script>

    var isPageHide = false;
    window.addEventListener('pageshow', function () {
        if (isPageHide) {
            window.location.reload();
        }
    });
    window.addEventListener('pagehide', function () {
        isPageHide = true;
    });

    let datePicker = $("#datetime-picker");
    let preDate = sessionStorage.getItem("CQYZ_CALLABLE_DATE");
    datePicker.val(nowDate());

    $(document).ready(function () {

        document.title = sessionStorage.getItem('reportTitle');
        datePicker.datetimePicker({
            title: '截止日期',
            defaultValue: [new Date().getFullYear(), new Date().getMonth() + 1, new Date().getDate()],
            times: function () {
                return [];
            },
            parse: function (str) {
                return str.split("-");
            },
            onChange: function (picker, values, displayValues) {
                console.log(values);
            }
        });
        $('#search').on('click', function () {
            let date = $('#datetime-picker').val();
            if (date === '') {
                $.toast("截止日期不能为空", 'cancel');
            } else {
                sessionStorage.setItem("CQYZ_CALLABLE_DATE", date);
                window.location.href = 'cqyzCallableFundSummary.jsp?date=' + date;
            }
        })


    });

    function nowDate() {
        let date = new Date();
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        if (month < 10) {
            month = '0' + month;
        }
        let day = date.getDate();
        if (day < 10) {
            day = '0' + day;
        }

        return year + '-' + month + '-' + day;
    }
</script>
</body>
</html>
