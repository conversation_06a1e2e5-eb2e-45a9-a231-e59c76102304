<%@ page contentType="text/html;charset=UTF-8" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css">
    <link rel="stylesheet" href="${ctx}/css/jquery-weui.min.css">
    <style>

        .weui-cell {
            height: 70px;
        }

        .title {
            color: #5A5858;
            font-size: 14px;
            font-weight: bold;
            position: absolute;
            top: 15px;

        }

        .start {
            font-size: 16px;
            color: #FF7979;
            position: absolute;
            top: 35px;
        }

        .center {
            font-size: 16px;
            color: #FF7979;
            position: absolute;
            top: 35px;
            left: 44%;
        }

        .end {
            font-size: 16px;
            color: #FF7979;
            position: absolute;
            top: 35px;
            right: 45px;
        }

        .start-label {
            position: absolute;
            top: 60px;
        }

        .center-label {
            position: absolute;
            top: 60px;
            left: 44%;
        }

        .end-label {

            position: absolute;
            top: 60px;
            right: 45px;
        }


        .label {
            font-size: 10px;
            color: grey;
        }

        .header-content {
            color: white;
        }

        .weui-cell_access .weui-cell__ft {
            position: absolute;
            right: 10px;
        }

        .weui-cell_access .weui-cell__ft:after {
            position: absolute;
            right: 10px;
        }
    </style>
</head>
<body>
<div style="padding: 15px">
    <div class="">报告截止日期：${param.date}</div>
    <div class="">不含IP项目</div>
    <div class="">单位：万元</div>
</div>

<h4 style="margin-top: 15px;margin-left: 15px">资金余额-项目口径</h4>
<div class="weui-cells" id="project-list" style="margin-top: 6px">

</div>


<h4 style="margin-top: 15px;margin-left: 15px">资金余额-HKL Share</h4>
<div class="weui-cells" id="hkl-list" style="margin-top: 6px">

</div>


<script src="${ctx}/js/jquery.min.js"></script>
<script src="${ctx}/js/jquery-weui.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>

<script>

    document.title = sessionStorage.getItem('reportTitle');
    $(document).ready(function () {
        let orgIds = sessionStorage.getItem("CQYZ_ORG_IDS");
        let date = '${param.date}';
        getData(orgIds, date);

    });

    let getData = function (orgIds, date) {
        let url = '/cqyz/report/cqyzBalanceSummary.do';
        let params = {};
        params.orgIds = orgIds;
        params.date = date;
        $.showLoading("加载中...");

        ajaxRequest(url, params, function (data) {
            console.log(data);

            initList(1, data.result.project);
            initList(2, data.result.hkl);


        })

    }

    let initList = function (listType, list) {
        let cells;
        if (listType === 1) {
            cells = $('#project-list');
        } else {
            cells = $('#hkl-list');
        }

        let rootData = list.root[0];
        if (list.root.length > 0) {
            let rootCell = '<div style="background-color: #649CFF" class="weui-cell" data-name="' + rootData.orgName + '" data-id="' + rootData.orgId + '">' +
                '        <div class="title header-content">' + rootData.orgName + '</div>' +
                '        <div class="start header-content">' + rootData.totalAmount + '</div>' +
                '        <div class="center header-content">' + rootData.regulatedAmount + '</div>' +
                '        <div class="end header-content">' + rootData.unRegulatedAmount + '</div>' +
                '        <div class="label start-label header-content">账面总资金余额</div>' +
                '        <div class="label center-label header-content">监管资金余额</div>' +
                '        <div class="label end-label header-content">非监管资金余额</div>' +
                '        <div class=""></div>' +
                '    </div>';

            cells.append(rootCell);
        }

        let subListData = list.sub;

        for (let cellData of subListData) {
            let cell = $('<div class="weui-cell  weui-cell_access" data-name="' + cellData.orgName + '" data-id="' + cellData.orgId + '">' +
                '        <div class="title" style="margin-right: 15px">' + cellData.orgName + '</div>' +
                '        <div class="start">' + cellData.totalAmount + '</div>' +
                '        <div class="center">' + cellData.regulatedAmount + '</div>' +
                '        <div class="end">' + cellData.unRegulatedAmount + '</div>' +
                '        <div class="label start-label">账面总资金余额</div>' +
                '        <div class="label center-label">监管资金余额</div>' +
                '        <div class="label end-label">非监管资金余额</div>' +
                '        <div class="weui-cell__ft forward"></div>' +
                '    </div>');

            cells.append(cell);

            cell.on('click', function () {

                let id = $(this).data('id');
                let name = $(this).data('name');

                sessionStorage.setItem("CQYZ_ORG_ID", id);
                sessionStorage.setItem("CQYZ_ORG_NAME", name);

                window.location.href = 'cqyzDetailBalance.jsp?date=${param.date}';
            });
        }

        let totalData = list.total[0];
        let totalCell = '<div style="background-color: #649CFF" class="weui-cell" data-name="' + totalData.orgName + '" data-id="' + totalData.orgId + '">' +
            '        <div class="title header-content" style="margin-right: 15px">' + totalData.orgName + '</div>' +
            '        <div class="start header-content">' + totalData.totalAmount + '</div>' +
            '        <div class="center header-content">' + totalData.regulatedAmount + '</div>' +
            '        <div class="end header-content">' + totalData.unRegulatedAmount + '</div>' +
            '        <div class="label start-label header-content">账面总资金金额</div>' +
            '        <div class="label center-label header-content">监管资金余额</div>' +
            '        <div class="label end-label header-content">非监管资金余额</div>' +
            '        <div class=""></div>' +

            '    </div>';
        cells.append(totalCell);


    }
</script>
</body>
</html>
