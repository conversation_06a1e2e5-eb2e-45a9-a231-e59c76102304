<%@ page contentType="text/html;charset=UTF-8" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style>


        #noMsg {
            position: relative;
            display: none;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }

        .weui-cell {
            height: 70px;
        }

        .title {
            color: #5A5858;
            font-size: 14px;
            position: absolute;
            top: 15px;
            font-weight: bold;

        }

        .start-a {
            font-size: 16px;
            color: #FF7979;
            position: absolute;
            top: 40px;
        }

        .center {
            font-size: 16px;
            color: #FF7979;
            position: absolute;
            top: 40px;
            left: 44%;
        }

        .end {
            font-size: 16px;
            color: #FF7979;
            position: absolute;
            top: 40px;
            right: 30px;
        }

        .start-label {
            position: absolute;
            top: 65px;
        }

        .center-label {
            position: absolute;
            top: 65px;
            left: 44%;
        }

        .end-label {

            position: absolute;
            top: 65px;
            right: 30px;
        }


        .label {
            font-size: 10px;
            color: grey;
        }
    </style>
</head>
<body>
<div class="weui-cell">
    <div class="weui-cell__ft">报告截止日期：${param.date}</div>
    <div class="weui-cell__ft" style="margin-left: 20px">单位：万元</div>

</div>

<div id="cell-list" class="weui-cells" style="margin-top: 0">


</div>
<div id="noMsg" style="display: none">
    <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
    <span>暂无数据</span>
</div>
<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script>

    document.title = sessionStorage.getItem('reportTitle');

    $(document).ready(function () {

        let date = '${param.date}';

        getData(date);
    });

    let getData = function (date) {
        let url = '/cqyz/report/cqyzGetCallableFundSummary.do';
        let params = {};
        params.date = date;
        $.showLoading("加载中...");
        ajaxRequestWithFailCallback(url, params, function (data) {
            console.log(data);

            let list = $('#cell-list');

            if (data.result.data.length > 0) {
                let datas = data.result.data;
                for (let cellData of datas) {
                    let cell = '<div class="weui-cell">' +
                        '        <div class="title">' + cellData.balanceType + '</div>' +
                        '        <div class="start-a">' + cellData.total + '</div>' +
                        '        <div class="center">' + cellData.solePropriety + '</div>' +
                        '        <div class="end">' + cellData.jointVenture + '</div>' +
                        '        <div class="label start-label">合计</div>' +
                        '        <div class="label center-label">独资</div>' +
                        '        <div class="label end-label">合资</div>' +
                        '    </div>';
                    list.append(cell);
                }

            } else {
                $('#noMsg').css('display', 'block');
            }
        }, function () {

            $('#noMsg').css('display', 'block');

        });
    };


</script>
</body>
</html>
