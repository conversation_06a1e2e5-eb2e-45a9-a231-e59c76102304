<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>付款结算量次</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style>
        #tagnav {
            position: fixed;
            top: 0;
            z-index: 999;
            background: #649CFF;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .weui-navigator-list li {
            line-height: 40px;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a {
            color: #93FFDB;
            font-weight: bold;
            border-bottom: 1px solid #93FFDB;
        }

        .weui-navigator-list li.weui-state-disabled a {
            color: rgba(255, 255, 255, 0.2);
            border-bottom: 1px solid #649CFF;
        }

        .weui-navigator-list li a {
            color: #ffffff;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a:after {
            background-color: transparent;
        }

        #reportTitle {
            padding-top: 43px;
            background: #649CFF;
            color: #fff;
        }

        #center {
            width: 100%;
            text-align: center;
        }

        #center .tip {
            display: block;
            padding-top: 30px;
            font-size: 12px;
        }

        #center .amount {
            display: block;
            padding-bottom: 30px;
            font-size: 24px;
            line-height: 24px;
        }

        #left, #right {
            display: inline-block;
            width: 48%;
            text-align: center;
        }

        .leftTip, .rightTip {
            display: inline-block;
            padding-top: 38px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            position: relative;
        }

        .leftTip:before, .rightTip:before {
            content: ' ';
            width: 11px;
            height: 11px;
            display: inline-block;
            border: 1px solid #ffffff;
            border-radius: 6px;
            position: absolute;
            top: 40px;
            left: -15px;
        }

        .leftTip:before {
            background: #FB8D8D;
        }

        .rightTip:before {
            background: #84DD86;
        }

        .leftAmount, .rightAmount {
            padding-bottom: 30px;
            display: block;
            font-size: 18px;
            font-weight: 500;
        }

        .line {
            display: inline-block;
            width: 1%;
            max-width: 1px;
            background: #fff;
            height: 30px;
        }

        .titleInfo {
            padding-left: 20px;
            padding-top: 18px;
            font-size: 17px;
            color: #5A5858;
            font-weight: bold;
            display: block;
        }

        .titleInfoArrow:after {
            content: " ";
            display: inline-block;
            height: 7px;
            width: 7px;
            border-width: 2px 2px 0 0;
            border-color: #c8c8cd;
            border-style: solid;
            transform: matrix(.71, .71, -.71, .71, 0, 0);
            webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);
            top: -2px;
            position: absolute;
            top: 50%;
            margin-top: -4px;
            right: 22px;
        }

        .amountAndTip {
            display: inline-block;
            width: calc(48% - 20px);
            padding-left: 20px;
        }

        .amountAndTip .amount {
            display: block;
            color: #FF7979;
            font-size: 16px;
            font-weight: bold;
        }

        .amountAndTip .tip {
            font-size: 10px;
            color: #999999;
            display: block;
        }

        #noMsg {
            position: relative;
            display: none;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }

        #segment {
            padding: 20px 40px;
            padding-bottom: 0px;
        }

        .weui-tab-nav .weui-nav-blue.bg-blue {
            border-color: #649cff;
            color: #fff;
            background: #649cff;
        }

        .weui-tab-nav .weui-nav-blue {
            display: block;
            -webkit-box-flex: 1;
            -webkit-flex: 1;
            flex: 1;
            width: 100%;
            height: 30px;
            padding: 0;
            font-size: 14px;
            line-height: 31px;
            text-align: center;
            border: 1px solid #649cff;
            border-width: 1px 1px 1px 0;
            color: #649cff;
            white-space: nowrap;
            background: #fdfdfd;
        }

        #datepicker {
            text-align: center;
        }

        #datepicker .dateText {
            font-weight: 500;
            font-size: 15px;
            color: #649CFF;
            vertical-align: middle;
        }

        #datepicker img {
            width: 14px;
            height: 14px;
            vertical-align: middle;
        }

        .weui-popup__overlay, .weui-popup__container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            bottom: unset;
        }

        .weui-popup__modal {
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
            background: white;
            top: 0;
            z-index: 1001;
            bottom: unset;
        }

        .weui-picker-modal.picker-modal-inline {
            background: #ffffff;
        }

        .weui-picker-modal .picker-center-highlight {
            background: #EDF2FB;
            width: 80%;
            left: 10%;
            z-index: -1;
        }

        .weui-picker-modal .picker-center-highlight:before, .weui-picker-modal .picker-center-highlight:after {
            background: #ffffff;
        }

        #cancel, #confirm {
            display: inline-block;
            margin-right: 5%;
            border: 1px solid #649cff;
            border-radius: 5px;
            width: 35%;
            height: 40px;
            line-height: 40px;
        }

        #cancel {
            color: #649cff;
        }

        #confirm {
            background: #649cff;
            margin-right: 0;
            margin-left: 5%;
        }
    </style>
</head>
<body>
<div id="tagnav" class="weui-navigator weui-navigator-wrapper">
    <ul class="weui-navigator-list"></ul>
</div>
<div id="reportTitle">
    <!-- <div id="center"><span class="tip">账户余额/万元</span><span class="amount">3828,222.72</span></div> -->
    <!-- <div id="left"><span class="leftTip">账户余额/万元</span><span class="leftAmount">10000</span></div>
    <div class="line"></div>
    <div id="right"><span class="rightTip">上缴金额/万元</span><span class="rightAmount">18888</span></div> -->
</div>
<div id="segment">
    <!-- <div class="weui-tab" style="height:44px;">
      <div class="weui-tab-nav">
        <a href="javascript:switchTypeCon('1');" class="weui-navbar__item weui-nav-blue"> 按日 </a>
        <a href="javascript:switchTypeCon('2');" class="weui-navbar__item weui-nav-blue"> 按月 </a>
        <a href="javascript:switchTypeCon('3');" class="weui-navbar__item weui-nav-blue"> 按季 </a>
      </div>
    </div> -->
</div>
<div id="datepicker">
    <!--  <span class="dateText open-popup" data-target="#datePickerPannel" ></span>
    <img src="${ctx}/img/report/<EMAIL>" alt="编辑" class="open-popup" data-target="#datePickerPannel"> -->
</div>
<div id="chartSection"></div>
<div id="chartListInfo"></div>

<div id="datePickerPannel" class="weui-popup__container popup-bottom">
    <div class="weui-popup__overlay"></div>
    <div class="weui-popup__modal">
        <div class="weui-tab"
             style="height:44px;padding-top: 20px;padding-bottom: 0px;padding-left: 10%;padding-right: 10%;">
            <div class="weui-tab-nav">
                <a href="javascript:switchTypeOnPickerView('1');" class="weui-navbar__item weui-nav-blue"> 按日 </a>
                <a href="javascript:switchTypeOnPickerView('2');" class="weui-navbar__item weui-nav-blue"> 按月 </a>
                <a href="javascript:switchTypeOnPickerView('3');" class="weui-navbar__item weui-nav-blue"> 按季 </a>
            </div>
        </div>
        <div id="picker-container"></div>
        <input class="weui-input" id="inline" type="text" value="" style="display: none;">
        <div style="padding: 20px;text-align: center;color: #ffffff;"><span id="cancel">取消</span><span
                id="confirm">确定</span></div>
    </div>
</div>

<div id="noMsg">
    <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
    <span>暂无数据</span>
</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/iscroll-lite.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script src="${ctx}/js/echarts.min.js"></script>

<script>
    var type = "1";
    var typeIndex = 0;
    var dateCon = "";
    var dateTextTip = "";
    var pickerSelectedValue = [];
    var years = [];
    var months = ["01月", "02月", "03月", "04月", "05月", "06月", "07月", "08月", "09月", "10月", "11月", "12月"];
    var nowDate = '';
    $(function () {
        var reportActiveCurrCode = sessionStorage.getItem('reportActiveCurrCode');
        var realCurrCode = sessionStorage.getItem('currCode');
        var tabIndex = 0;
        var currCodeArray = JSON.parse(reportActiveCurrCode);
        let tabs = $('.weui-navigator-list');
        for (let i = 0; i < currCodeArray.length; i++) {
            let tabItem = currCodeArray[i];
            let currName = tabItem.currName;
            let currCode = tabItem.currCode;
            if (currCode === realCurrCode) {
                tabIndex = i;
            }
            let tab;
            if (tabItem.hasData) {
                tab = $('<li data-currCode="' + currCode + '"><a>' + currName + '</a></li>');
            } else {
                tab = $('<li class= "weui-state-disabled"><a>' + currName + '</a></li>');
            }
            tabs.append(tab);
        }
        //使用这个是为了避免滑动时出现滚动条。
        TagNav('#tagnav', {
            type: 'scrollToNext',
            curClassName: 'weui-state',
            //不能使用weui-state-active，随便写一个，以免下面的重写失效。
            index: tabIndex
        });
        //重写切换事件和选中事件
        $("#tagnav li").eq(tabIndex).addClass('weui-state-active');
        $('#tagnav').on('click', 'li',
            function (event) {
                event.preventDefault();
                $(this).addClass('weui-state-active');
                if (!$(this).hasClass('weui-state-disabled')) {
                    $(this).siblings('li').removeClass('weui-state-active');
                    requestData($(this).attr('data-currCode'));
                }
            });

        //处理时间
        var myDate = new Date();
        var year = myDate.getFullYear(); //获取完整的年份(4位,1970-????)
        var month = myDate.getMonth() + 1; //获取当前月份(0-11,0代表1月)
        month = month + "";
        if (month.length == 1) {
            month = '0' + month;
        }

        //当前年份往前30年，往后5年处理。
        for (var i = 0; i < 36; i++) {
            yearStr = year - 30 + i;
            years.push(yearStr + "年");
        }

        dateCon = year + '-' + month;
        nowDate = dateCon;
        dateTextTip = year + "年" + month + "月";
        $('.dateText').text(dateTextTip);

        //打开时间选择器的modal
        $(document).on("open", ".weui-popup__modal", function () {
            console.log('点击模态');
            $('#datePickerPannel .weui-tab').tab({defaultIndex: typeIndex, activeClass: "bg-blue"});
            var cols = [];
            var dateConArr = dateCon.split("-");
            var dateValues = [];
            dateValues.push(dateConArr[0] + "年");
            dateValues.push(dateConArr[1] + "月");
            if (type === "1") {
                cols = [
                    {
                        textAlign: 'center',
                        values: years
                    },
                    {
                        textAlign: 'center',
                        values: months
                    }
                ];
            } else {
                cols = [
                    {
                        textAlign: 'center',
                        values: years
                    }
                ];
            }
            if ($("#picker-container").html() !== "") {
                $("#inline").picker("destroy");
                $("#picker-container").html('');
            }
            $("#inline").picker({
                container: '#picker-container',
                cols: cols,
                value: dateValues,
                onChange: function (picker, values, displayValues) {
                    console.log(picker, values, displayValues);
                    pickerSelectedValue = values;
                }
            });
            if (type === "1") {
                $(".picker-items-col").css("width", "40%")
            } else {
                $(".picker-items-col").css("width", "80%")
            }
            console.log("open popup");
        }).on("close", ".weui-popup__modal", function () {
            console.log("close popup");
        });

        $("#cancel").on('click', function (event) {
            event.preventDefault();
            console.log('取消');
            $.closePopup();
        });
        $("#confirm").on('click', function (event) {
            event.preventDefault();
            console.log('确定');
            let selectedYear = parseInt(pickerSelectedValue[0]);
            let selectedMonth = parseInt(pickerSelectedValue[1]);
            if (selectedMonth < 10) {
                selectedMonth = '0' + selectedMonth;
            }
            if (type === '1') {
                dateCon = selectedYear + '-' + selectedMonth;
                dateTextTip = selectedYear + "年" + selectedMonth + "月";
            } else {
                dateCon = selectedYear + "";
                dateTextTip = selectedYear + "年";
            }
            $('.dateText').text(dateTextTip);
            //确认后查询中的segment联动界面中的segment。
            switchTypeCon(type, false);
            let currCode = $('#tagnav .weui-state-active').attr('data-currcode');
            requestData(currCode);
            $.closePopup();
        });

        requestData(realCurrCode);
    });

    function requestData(currCode) {
        console.log('切换到-currCode=' + currCode);
        showNoMsg(false)
        var param = {'currCode': currCode, 'tag': 'ats', 'dateType': type, "date": dateCon};
        console.log('param=' + JSON.stringify(param));
        $.ajax({
            cache: true,
            type: 'POST',
            url: "<%=basePath%>/saasreport/getPaymentSettlement.do",
            data: param,
            async: true,
            dataType: 'json',
            beforeSend: function () {
                $.showLoading();
            },
            error: function () {
                $.hideLoading();
                $.toast('网络服务情况异常，请检查', "forbidden");
                showNoMsg(true);
            },
            success: function (data) {
                $.hideLoading();
                if (data.successful == true || "true" == data.successful) {
                    var dataArray = data.result.data;
                    if (dataArray.length == 3) {
                        //顶部数据
                        layoutTopTitle(dataArray[0].reportData);
                        $('#segment').html('');
                        $('#segment').append(
                            '<div class="weui-tab" style="height:44px;">' +
                            '<div class="weui-tab-nav">' +
                            '<a href="javascript:switchTypeCon(\'1\',true);" class="weui-navbar__item weui-nav-blue">按日</a> ' +
                            '<a href="javascript:switchTypeCon(\'2\',true);" class="weui-navbar__item weui-nav-blue">按月</a> ' +
                            '<a href="javascript:switchTypeCon(\'3\',true);" class="weui-navbar__item weui-nav-blue">按季</a> ' +
                            '</div>' +
                            '</div>');
                        $('#segment .weui-tab').tab({defaultIndex: typeIndex, activeClass: "bg-blue"});
                        //更换日期
                        $("#datepicker").html('');
                        $("#datepicker").append(
                            '<span class="dateText openModal"></span>' +
                            '<img src="${ctx}/img/report/<EMAIL>" class="openModal" alt="编辑">');
                        $('.dateText').text(dateTextTip);
                        $('.openModal').on('click', function (event) {
                            event.preventDefault();
                            $("#datePickerPannel").popup();
                        });
                        //chart数据
                        $('#chartSection').html('');
                        $('#chartSection').append(
                            '<div id="chartView" style="width: 100%;height: 268px;"></div>' +
                            '<div class="chartPadding" style="width: 100%;height: 10px;background: #F1F3F5"></div>');
                        var myChart = echarts.init(document.getElementById('chartView'));
                        var option = dataCollation(dataArray[1].reportData);
                        myChart.setOption(option);
                        //列表数据
                        layoutListInfo(dataArray[2].reportData);
                    } else {
                        // 暂无数据
                        showNoMsg(true);
                    }
                } else {
                    $.toast('网络服务情况异常，请检查', "forbidden");
                    showNoMsg(true);
                }
            }
        });
    }

    function showNoMsg(show) {
        if (show) {
            $("#reportTitle").html('');
            $("#chartSection").html('');
            $("#chartListInfo").html('');
            $("#noMsg").css('display', 'block');
        } else {
            $("#noMsg").css('display', 'none');
        }
    }

    function layoutTopTitle(reportDataOne) {
        $("#reportTitle").html('');
        let dic = reportDataOne[0];
        $("#reportTitle").append(
            '<div id="left"><span class="leftTip">付款总额/万元</span><span class="leftAmount">' + dic.totalAmount + '</span></div>' +
            '<div class="line"></div>' +
            '<div id="right"><span class="rightTip">付款笔数/笔</span><span class="rightAmount">' + dic.totalCount + '</span></div>');
    }

    function dateHandle(dateStr) {
        let str = dateStr.replace('年', '-');
        str = str.replace('月', '-');
        str = str.replace('日', '');
        let array = str.split('-');
        let month = array[1];
        let day = array[2];
        if (month.length === 1) {
            month = '0' + month;
        }
        if (day.length < 2) {
            day = day.length == 0 ? '' : '-0' + day;
        } else {
            day = '-' + day;
        }
        return array[0] + '-' + month + day;
    }

    function layoutListInfo(reportDataThree) {
        $('#chartListInfo').html('');
        for (let i = 0; i < reportDataThree.length; i++) {
            let dic = reportDataThree[i];
            $("#chartListInfo").append(
                '<div class="cell titleInfoArrow" style="position: relative;" data-item = ' + i + '>' +
                '<span class="titleInfo">' + dic.payDate + '</span>' +
                '<div class="amountAndTip"><span class="amount">' + dic.totalAmount + '</span><span class="tip">付款金额</span></div>' +
                '<div class="amountAndTip"><span class="amount" style="color:#84DD86">' + dic.totalCount + '</span><span class="tip">付款笔数</span></div>' +
                '<div style="width: 100%;height: 10px;background: #ffffff"></div>' +
                '<div class="cellPadding" style="width: 100%;height: 1px;background: #F1F3F5"></div>' +
                '</div>');
        }
        var clickable;
        $(".cell").on('touchstart', function (event) {
            clickable = true;
        });
        $(".cell").on('touchmove', function (event) {
            clickable = false;
        });
        $(".cell").on('click', function (event) {
            event.preventDefault();
            if (!clickable) {
                return;
            }
            if ($(this).hasClass('titleInfoArrow')) {
                //可以钻取。
                let itemId = $(this).attr('data-item');
                let currCode = $('#tagnav .weui-state-active').attr('data-currcode');
                let navTitle = $(this).find('.titleInfo').text();
                let date = "";
                if (type === "1" || type === '2') {
                    date = dateHandle(navTitle);
                }
                if (type === '3') {
                    let array = ['10', '07', '04', '01'];
                    date = parseInt(navTitle) + '-' + array[parseInt(itemId)];
                }
                sessionStorage.setItem('date', date);
                sessionStorage.setItem('dateType', type);
                sessionStorage.setItem('currCode', currCode);
                sessionStorage.setItem('navTitle', navTitle);
                // sessionStorage.setItem('date', dateCon);
                location.href = "subPaymentSettlementReport.jsp";
            }
        });
    }

    function switchTypeCon(typeCon, isHandle) {
        type = typeCon;
        typeIndex = parseInt(typeCon) - 1;
        if (isHandle === true) {
            //nowDate当前日期，格式 yyyy-MM
            var nowDateArray = nowDate.split("-");
            if (type === "1") {
                dateCon = nowDate;
                dateTextTip = nowDateArray[0] + "年" + nowDateArray[1] + "月";
            } else {
                dateCon = nowDateArray[0];
                dateTextTip = nowDateArray[0] + "年";
            }
        }

        $(".dateText").text(dateTextTip);
        let currCode = $('#tagnav .weui-state-active').attr('data-currcode');
        requestData(currCode);
    }

    function layoutPopupPickerView() {
        $('#datePickerPannel .weui-tab').tab({defaultIndex: typeIndex, activeClass: "bg-blue"});
        var cols = [];
        var dateConArr = dateCon.split("-");
        var dateValues = [];
        dateValues.push(dateConArr[0] + "年");
        dateValues.push("01月");
        if (type === "1") {
            cols = [
                {
                    textAlign: 'center',
                    values: years
                },
                {
                    textAlign: 'center',
                    values: months
                }
            ];
        } else {
            cols = [
                {
                    textAlign: 'center',
                    values: years
                }
            ];
        }
        $("#inline").picker("destroy");
        $("#picker-container").html('');
        $("#inline").picker({
            container: '#picker-container',
            cols: cols,
            value: dateValues,
            onChange: function (picker, values, displayValues) {
                console.log(picker, values, displayValues);
                pickerSelectedValue = values;
            }
        });
        if (type === "1") {
            $(".picker-items-col").css("width", "40%")
        } else {
            $(".picker-items-col").css("width", "80%")
        }
    }

    function switchTypeOnPickerView(typeCon) {
        console.log('切换=' + typeCon);
        type = typeCon;
        typeIndex = parseInt(typeCon) - 1;
        layoutPopupPickerView();
    }

    function dataCollation(reportDataTwo) {
        var xAxisData = [];
        var seriesData = [];
        var totalAmountData = [];
        var totalCountData = [];

        for (let i = 0; i < reportDataTwo.length; i++) {
            let dic = reportDataTwo[i];
            let payDate = dic.payDate;
            let len = reportDataTwo.length > 4 ? 4 : 6;
            if (payDate.length > len) {
                payDate = payDate.substring(0, len);
            }
            xAxisData.push(payDate)
            totalAmountData.push(dic.totalAmount);
            totalCountData.push(dic.totalCount);
        }
        if (type === "1") {
            //按日
            let lineData1 = {
                name: '付款金额',
                type: 'line',
                color: '#FB8D8D',
                data: totalAmountData
            }
            let lineData2 = {
                name: '付款笔数',
                type: 'line',
                yAxisIndex: 1,
                color: '#84DD86',
                data: totalCountData
            }
            seriesData.push(lineData1);
            seriesData.push(lineData2);
        }
        if (type === "2" || type === "3") {
            //按月或按年
            let barData1 = {
                name: '付款金额',
                type: 'bar',
                // barGap: '-100%',
                color: '#FB8D8D',
                data: totalAmountData
            }
            let lineData1 = {
                name: '付款笔数',
                type: 'line',
                yAxisIndex: 1,
                z: 2,
                color: '#84DD86',
                data: totalCountData
            }
            seriesData.push(barData1);
            seriesData.push(lineData1);
        }
        var option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
            },
            grid: {
                top: '15%',
                left: '5%',
                right: '5%',
                bottom: '3%',
                containLabel: true,
            },
            xAxis: [
                {
                    type: 'category',
                    data: xAxisData,
                    axisLine: {
                        lineStyle: {
                            color: '#D6D6D6',
                        }
                    },
                    axisLabel: {
                        color: '#888888',
                        fontSize: 10,
                    },
                    axisTick: {
                        show: false
                    },
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    name: '万元',
                    axisLine: {
                        lineStyle: {
                            color: '#fff',
                        }
                    },
                    nameTextStyle: {
                        color: "#888888"
                    },
                    axisLabel: {
                        color: '#888888'
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(67,67,67,0.05)'
                        }
                    },
                },
                {
                    type: 'value',
                    name: '笔数',
                    splitLine: {
                        show: false
                    },
                    nameTextStyle: {
                        color: "#888888"
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#fff',
                        }
                    },
                    axisLabel: {
                        color: '#888888'
                    }
                }
            ],
            series: seriesData
        };
        return option;
    }
</script>


</body>
</html>

