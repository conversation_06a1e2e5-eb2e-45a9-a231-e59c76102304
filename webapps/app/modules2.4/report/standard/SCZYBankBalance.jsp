<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>银行账户余额汇总表</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style>
        /*#tagnav {*/
        /*    position: fixed;*/
        /*    top: 0;*/
        /*    z-index: 999;*/
        /*    background: #649CFF;*/
        /*    border-bottom: 1px solid rgba(255, 255, 255, 0.2);*/
        /*}*/

        /*.weui-navigator-list li {*/
        /*    line-height: 40px;*/
        /*}*/

        /*.weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a {*/
        /*    color: #93FFDB;*/
        /*    font-weight: bold;*/
        /*    border-bottom: 1px solid #93FFDB;*/
        /*}*/

        /*.weui-navigator-list li.weui-state-disabled a {*/
        /*    color: rgba(255, 255, 255, 0.2);*/
        /*    border-bottom: 1px solid #649CFF;*/
        /*}*/

        /*.weui-navigator-list li a {*/
        /*    color: #ffffff;*/
        /*}*/

        /*.weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a:after {*/
        /*    background-color: transparent;*/
        /*}*/

        #reportTitle {
            /*padding-top: 43px;*/
            background: #649CFF;
            color: #fff;
        }

        #center {
            width: 100%;
            text-align: center;
        }

        #center .date {
            padding-top: 30px;
            font-size: 16px;
            padding-bottom: 10px;
            display: block;
        }

        #center .tip {
            display: block;
            /*padding-top: 30px;*/
            font-size: 12px;
        }

        #center .amount {
            display: block;
            padding-bottom: 30px;
            font-size: 24px;
            line-height: 24px;
        }

        #left, #right {
            display: inline-block;
            width: 48%;
            text-align: center;
        }

        .leftTip, .rightTip {
            display: inline-block;
            padding-top: 20px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            position: relative;
        }

        /*.leftTip:before, .rightTip:before {*/
        /*    content: ' ';*/
        /*    width: 11px;*/
        /*    height: 11px;*/
        /*    display: inline-block;*/
        /*    border: 1px solid #ffffff;*/
        /*    border-radius: 6px;*/
        /*    position: absolute;*/
        /*    top: 40px;*/
        /*    left: -15px;*/
        /*}*/

        /*.leftTip:before {*/
        /*    background: #84DD86;*/
        /*}*/

        /*.rightTip:before {*/
        /*    background: #FFD392;*/
        /*}*/

        .leftAmount, .rightAmount {
            padding-bottom: 30px;
            display: block;
            font-size: 18px;
            font-weight: 500;
        }

        .line {
            display: inline-block;
            width: 1%;
            max-width: 1px;
            background: #fff;
            height: 30px;
        }

        .titleInfo {
            padding-left: 20px;
            padding-top: 18px;
            font-size: 14px;
            color: #5A5858;
            font-weight: bold;
            display: block;
        }

        .titleInfoArrow:after {
            content: " ";
            display: inline-block;
            height: 7px;
            width: 7px;
            border-width: 2px 2px 0 0;
            border-color: #c8c8cd;
            border-style: solid;
            transform: matrix(.71, .71, -.71, .71, 0, 0);
            webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);
            top: -2px;
            position: absolute;
            top: 50%;
            margin-top: -4px;
            right: 22px;
        }

        .amountAndTip3 {
            display: inline-block;
            width: calc(30% - 20px);
            padding-left: 20px;
        }

        .amountAndTip3 .amount {
            display: block;
            color: #FF7979;
            font-size: 16px;
            font-weight: bold;
        }

        .amountAndTip3 .tip {
            font-size: 10px;
            color: #999999;
            display: block;
        }

        .amountAndTip {
            display: inline-block;
            width: calc(48% - 20px);
            padding-left: 20px;
        }

        .amountAndTip .amount {
            display: block;
            color: #FF7979;
            font-size: 16px;
            font-weight: bold;
        }

        .amountAndTip .tip {
            font-size: 10px;
            color: #999999;
            display: block;
        }

        #noMsg {
            position: relative;
            display: none;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }
    </style>
</head>
<body>
<%--<div id="tagnav" class="weui-navigator weui-navigator-wrapper">--%>
<%--    <ul class="weui-navigator-list"></ul>--%>
<%--</div>--%>
<div id="reportTitle">
    <!-- <div id="center"><span class="tip">总余额/万元</span><span class="amount" style="padding-bottom: 0px;">3828,222.72</span></div>
    <div id="left"><span class="leftTip">直联/万元</span><span class="leftAmount">10000</span></div>
    <div class="line"></div>
    <div id="right"><span class="rightTip">非直联/万元</span><span class="rightAmount">18888</span></div> -->
</div>
<div id="chartSection"></div>
<div id="chartListInfo" style="padding-bottom: 60px"></div>
<div id="noMsg">
    <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
    <span>暂无数据</span>
</div>

<%@include file='/common/bottom_bar.jsp' %>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/iscroll-lite.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script src="${ctx}/js/echarts1.min.js"></script>

<script>
    var myChart;
    var option;
    $(function () {
        requestData();
    });

    function requestData() {
        showNoMsg(false)
        $.ajax({
            cache: true,
            type: 'POST',
            url: "<%=basePath%>/cntc/getBankBalance.do",
            data: null,
            async: true,
            dataType: 'json',
            beforeSend: function () {
                $.showLoading();
            },
            error: function () {
                $.hideLoading();
                $.toast('网络服务情况异常，请检查', "forbidden");
                showNoMsg(true);
            },
            success: function (data) {
                $.hideLoading();
                if (data.successful === true || "true" === data.successful) {
                    var dataArray = data.result.data;
                    if (dataArray.length == 3) {
                        //顶部数据
                        layoutTopTitle(dataArray[0].reportData);
                        //chart数据
                        $('#chartSection').html('');
                        $('#chartSection').append(
                            '<div id="chartView" style="width: 100%;height: 268px;padding-bottom: 10px;"></div>' +
                            '<div class="chartPadding" style="width: 100%;height: 10px;background: #F1F3F5"></div>');
                        myChart = echarts.init(document.getElementById('chartView'));
                        myChart.on("click", pieConsole);
                        setValue(dataArray[1].reportData);
                        // var option = dataCollation(dataArray[1].reportData);
                        // myChart.setOption(option);
                        //列表数据
                        layoutListInfo(dataArray[2].reportData);
                    } else {
                        // 暂无数据
                        showNoMsg(true);
                    }
                } else {
                    $.toast(data.message, "cancel");
                    showNoMsg(true);
                }
            }
        });
    }

    function showNoMsg(show) {
        if (show) {
            $("#reportTitle").html('');
            $("#chartSection").html('');
            $("#chartListInfo").html('');
            $("#noMsg").css('display', 'block');
        } else {
            $("#noMsg").css('display', 'none');
        }
    }

    function layoutTopTitle(reportDataOne) {
        $("#reportTitle").html('');
        let dic = reportDataOne[0];
        var date = new Date().Format("yyyy-MM-dd");
        $("#reportTitle").append(
            '<div id="center"><span class="date">' + date + '</span><span class="tip">总余额/亿元</span><span class="amount" style="padding-bottom: 0px;">' + dic.totalAmount + '</span></div>' +
            '<div id="left"><span class="leftTip">协定存款/亿元</span><span class="leftAmount">' + dic.balance + '</span></div>' +
            '<div class="line"></div>' +
            '<div id="right"><span class="rightTip">定期存款/亿元</span><span class="rightAmount">' + dic.financingBalance + '</span></div>');
    }

    function layoutListInfo(reportDataThree) {
        $('#chartListInfo').html('');
        for (let i = 0; i < reportDataThree.length; i++) {
            let dic = reportDataThree[i];
            let arrowStyle = ""
            if (dic.bankId.length > 0) {
                arrowStyle = "titleInfoArrow";
            }
            $("#chartListInfo").append(
                '<div class="cell ' + arrowStyle + '" style="position: relative;" data-item = ' + dic.bankId + '>' +
                '<span class="titleInfo">' + dic.bankName + '</span>' +
                '<div class="amountAndTip"><span class="amount">' + dic.totalAmount + '</span><span class="tip">银行存款余额</span></div>' +
                '<div class="amountAndTip"><span class="amount" style="color:#FFB06B">' + dic.proportion + '</span><span class="tip">占比</span></div>' +
                '<div class="amountAndTip xdck-tip"><span class="amount">' + dic.balance + '</span><span class="tip" style="color: red">协定存款</span></div>' +
                '<div class="amountAndTip dqck-tip"><span class="amount">' + dic.financingBalance + '</span><span class="tip" style="color: red">定期存款</span></div>' +
                '<div style="width: 100%;height: 10px;background: #ffffff"></div>' +
                '<div class="cellPadding" style="width: 100%;height: 0.5px;background: #F1F3F5"></div>' +
                '</div>');
        }
        var clickable1;
        $(".dqck-tip").on('touchstart', function (event) {
            clickable1 = true;
        });
        $(".dqck-tip").on('touchmove', function (event) {
            clickable1 = false;
        });
        $(".dqck-tip").on('click', function (event) {
            let parent = $(this).parent();
            event.preventDefault();
            if (!clickable1) {
                return;
            }
            if (parent.hasClass('titleInfoArrow')) {
                let itemId = parent.attr('data-item');
                // let currCode = $('#tagnav .weui-state-active').attr('data-currcode');
                let name = parent.find('.titleInfo').text();
                sessionStorage.setItem('itemName', name + '存款');
                sessionStorage.setItem('itemId', itemId);
                location.href = "subSCZYBankBalance.jsp";
            }
        });

        var clickable2;
        $(".xdck-tip").on('touchstart', function (event) {
            clickable2 = true;
        });
        $(".xdck-tip").on('touchmove', function (event) {
            clickable2 = false;
        });
        $(".xdck-tip").on('click', function (event) {
            let parent = $(this).parent();
            event.preventDefault();
            if (!clickable2) {
                return;
            }
            if (parent.hasClass('titleInfoArrow')) {
                let itemId = parent.attr('data-item');
                // let currCode = $('#tagnav .weui-state-active').attr('data-currcode');
                let name = parent.find('.titleInfo').text();
                sessionStorage.setItem('itemName', name + '存款');
                sessionStorage.setItem('itemId', itemId);
                location.href = "subSCZYXdckBankBalance.jsp";
            }
        });
    }

    function dataCollation(srcData) {
        option = {
            color: ['#4EABF5', '#35C9DD', '#70BF73', '#D7E461', '#FFCD3A', '#FF794D', '#EE4C82', '#8562C5'],
            tooltip: {
                trigger: 'item',
                formatter: "{b}: {c}亿元"
            },
            legend: {
                orient: 'horizontal',
                y: 'bottom',
                left: 'center',
                itemWidth: 10,
                itemHeight: 10,
                align: 'left',
            },
            grid: {
                top: '20', //距上边距
                left: '25%', //距离左边距
                right: '25%', //距离右边距
                bottom: '20', //距离下边距
            },
            series: [{
                type: 'pie',
                radius: '50%',
                data: srcData,
                itemStyle: {
                    borderRadius: 0,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label: {
                    formatter: '{d}%',

                },
            }],
        }
        return option;
    }

    function setValue(data) {
        var srcData = [];
        for (var item of data) {
            var itemData = {};
            itemData.name = item.bankName;
            itemData.value = item.totalAmount;
            itemData.bankId = item.bankId;
            srcData.push(itemData);
        }
        var option = dataCollation(srcData);
        myChart.setOption(option);
    }

    function setValue2(data) {
        var srcData = [];
        for (var item of data) {
            var itemData = {};
            itemData.name = item.investmentProjectName;
            itemData.value = item.balance;
            srcData.push(itemData);
        }
        var option = dataCollation(srcData);
        myChart.setOption(option);
    }

    function pieConsole(param) {
        var bankId = option.series[param.seriesIndex].data[param.dataIndex].bankId;
        var bankName = param.name;
        var dic = {
            'bankId': bankId,
            'bankName': bankName
        }
        console.log('bankName:' + bankName + " bankId:" + bankId);
        sessionStorage.setItem('itemName', bankName + '存款');
        sessionStorage.setItem('itemId', bankId);
        location.href = "subSCZYBankBalance.jsp";
    }


</script>


</body>
</html>

