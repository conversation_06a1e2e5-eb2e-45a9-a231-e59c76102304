<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>账户融资经营性资金管理报表</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style>
        #tagnav {
            position: fixed;
            top: 0;
            z-index: 999;
            background: #649CFF;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .weui-navigator-list li {
            line-height: 40px;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a {
            color: #93FFDB;
            font-weight: bold;
            border-bottom: 1px solid #93FFDB;
        }

        .weui-navigator-list li.weui-state-disabled a {
            color: rgba(255, 255, 255, 0.2);
            border-bottom: 1px solid #649CFF;
        }

        .weui-navigator-list li a {
            color: #ffffff;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a:after {
            background-color: transparent;
        }

        #reportTitle {
            padding-top: 43px;
            background: #649CFF;
            color: #fff;
        }

        #left, #right {
            display: inline-block;
            width: 48%;
            text-align: center;
        }

        .leftTip, .rightTip {
            display: inline-block;
            padding-top: 38px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            position: relative;
        }

        .leftTip:before, .rightTip:before {
            content: ' ';
            width: 11px;
            height: 11px;
            display: inline-block;
            border: 1px solid #ffffff;
            border-radius: 6px;
            position: absolute;
            top: 42px;
            left: -20px;
        }

        .leftTip:before {
            background: #FCE172;
        }

        .rightTip:before {
            background: #7BE5C2;
        }

        .left-bottom:before {
            background: #3833fc;
        }

        .right-bottom:before {
            background: #e50096;
        }

        .leftAmount, .rightAmount {
            padding-bottom: 30px;
            display: block;
            font-size: 18px;
            font-weight: 500;
        }

        .line {
            display: inline-block;
            width: 1%;
            max-width: 1px;
            background: #fff;
            height: 30px;
        }

        .titleInfo {
            padding-left: 20px;
            padding-top: 18px;
            font-size: 17px;
            color: #5A5858;
            font-weight: bold;
            display: block;
        }

        .titleInfoArrow:after {
            content: " ";
            display: inline-block;
            height: 7px;
            width: 7px;
            border-width: 2px 2px 0 0;
            border-color: #c8c8cd;
            border-style: solid;
            transform: matrix(.71, .71, -.71, .71, 0, 0);
            webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);
            top: -2px;
            position: absolute;
            top: 50%;
            margin-top: -4px;
            right: 22px;
        }

        .amountAndTip {
            display: inline-block;
            width: calc(48% - 20px);
            padding-left: 20px;
        }

        .amountAndTip .amount {
            display: block;
            color: #FF7979;
            font-size: 16px;
            font-weight: bold;
        }

        .amountAndTip .tip {
            font-size: 10px;
            color: #999999;
            display: block;
        }

        #noMsg {
            position: relative;
            display: none;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }
    </style>
</head>
<body>
<div id="tagnav" class="weui-navigator weui-navigator-wrapper">
    <ul class="weui-navigator-list"></ul>
</div>
<div id="reportTitle"></div>
<div id="chartSection"></div>
<div id="chartListInfo"></div>
<div id="noMsg">
    <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
    <span>暂无数据</span>
</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/iscroll-lite.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script src="${ctx}/js/echarts.min.js"></script>

<script>
    $(function () {
        var reportActiveCurrCode = sessionStorage.getItem('reportActiveCurrCode');
        var realCurrCode = sessionStorage.getItem('currCode');
        var tabIndex = 0;
        var currCodeArray = JSON.parse(reportActiveCurrCode);
        let tabs = $('.weui-navigator-list');
        for (let i = 0; i < currCodeArray.length; i++) {
            let tabItem = currCodeArray[i];
            let currName = tabItem.currName;
            let currCode = tabItem.currCode;
            if (currCode === realCurrCode) {
                tabIndex = i;
            }
            let tab;
            if (tabItem.hasData) {
                tab = $('<li data-currCode="' + currCode + '"><a>' + currName + '</a></li>');
            } else {
                tab = $('<li class= "weui-state-disabled"><a>' + currName + '</a></li>');
            }
            tabs.append(tab);
        }
        //使用这个是为了避免滑动时出现滚动条。
        TagNav('#tagnav', {
            type: 'scrollToNext',
            curClassName: 'weui-state',
            //不能使用weui-state-active，随便写一个，以免下面的重写失效。
            index: tabIndex
        });
        //重写切换事件和选中事件
        $("#tagnav li").eq(tabIndex).addClass('weui-state-active');
        $('#tagnav').on('click', 'li',
            function (event) {
                event.preventDefault();
                $(this).addClass('weui-state-active');
                if (!$(this).hasClass('weui-state-disabled')) {
                    $(this).siblings('li').removeClass('weui-state-active');
                    requestData($(this).attr('data-currCode'));
                }
            });
        requestData(realCurrCode);
        $(document.body).on('touchmove', function (e) {
            e.preventDefault();
        })
    });

    function requestData(currCode) {
        showNoMsg(false)
        var param = {'currCode': currCode};
        $.ajax({
            cache: true,
            type: 'POST',
            url: "${ctx}/cdct/getFinancingManagementBalance.do",
            data: param,
            async: true,
            dataType: 'json',
            beforeSend: function () {
                $.showLoading();
            },
            error: function () {
                $.hideLoading();
                $.toast('网络服务情况异常，请检查', "forbidden");
                showNoMsg(true);
            },
            success: function (data) {
                $.hideLoading();
                if (data.successful == true || "true" == data.successful) {
                    var dataArray = data.result.data;
                    if (dataArray.length == 3) {
                        //顶部数据
                        layoutTopTitle(dataArray[0].reportData);
                        //chart数据
                        $('#chartSection').html('');
                        $('#chartSection').append(
                            '<div id="chartView" style="width: 100%;height: 268px;"></div>' +
                            '<div class="chartPadding" style="width: 100%;height: 10px;background: #F1F3F5"></div>');
                        var myChart = echarts.init(document.getElementById('chartView'));
                        var option = dataCollation(dataArray[1].reportData);
                        myChart.setOption(option);
                        //列表数据
                        layoutListInfo(dataArray[2].reportData);
                    } else {
                        // 暂无数据
                        showNoMsg(true);
                    }
                } else {
                    $.toast('网络服务情况异常，请检查', "forbidden");
                    showNoMsg(true);
                }
            }
        });
    }

    function showNoMsg(show) {
        if (show) {
            $("#reportTitle").html('');
            $("#chartSection").html('');
            $("#chartListInfo").html('');
            $("#noMsg").css('display', 'block');
        } else {
            $("#noMsg").css('display', 'none');
        }
    }

    function layoutTopTitle(reportDataOne) {
        $("#reportTitle").html('');
        let dic = reportDataOne[0];
        $("#reportTitle").append(
            '<div id="left"><span class="leftTip">融资余额/万元</span><span class="leftAmount">' + dic.financingBalance + '</span></div>' +
            '<div class="line"></div>' +
            '<div id="right"><span class="rightTip">经营余额/万元</span><span class="rightAmount">' + dic.operatingBalance + '</span></div>');
        /*+
        '<div id="left"><span class="leftTip left-bottom">融资性经营性/万元</span><span class="leftAmount">' + dic.financingToOperatingBalance + '</span></div>' +
        '<div class="line"></div>' +
        '<div id="right"><span class="rightTip right-bottom">经营性融资性/万元</span><span class="rightAmount">' + dic.operatingToFinancingBalance + '</span></div>')*/

    }

    function layoutListInfo(reportDataThree) {

        delegate.ready(function (sysParam) {


            $('#chartListInfo').html('');
            for (let i = 0; i < reportDataThree.length; i++) {
                let arrowStyle = ""
                let currOrg = '';
                let dic = reportDataThree[i];
                $("#chartListInfo").append(
                    '<div class="cell ' + arrowStyle + '" style="position: relative;" data-item = ' + dic.itemId + '>' +
                    '<span class="titleInfo">' + dic.orgName + currOrg + '</span>' +
                    '<div class="amountAndTip"><span class="amount">' + dic.financingBalance + '</span><span class="tip">融资余额</span></div>' +
                    '<div class="amountAndTip"><span class="amount">' + dic.operatingBalance + '</span><span class="tip">经营余额</span></div>' +
                    '<div class="amountAndTip"><span class="amount">' + dic.financingToOperatingBalance + '</span><span class="tip">融资性占用经营性</span></div>' +
                    '<div class="amountAndTip"><span class="amount">' + dic.operatingToFinancingBalance + '</span><span class="tip">经营性占用融资性</span></div>' +
                    '<div style="width: 100%;height: 10px;background: #ffffff"></div>' +
                    '<div class="cellPadding" style="width: 100%;height: 5px;background: #F1F3F5"></div>' +
                    '</div>');
            }
            var clickable;
            $(".cell").on('touchstart', function (event) {
                clickable = true;
            });
            $(".cell").on('touchmove', function (event) {
                clickable = false;
            });
            $(".cell").on('click', function (event) {
                event.preventDefault();
                if (!clickable) {
                    return;
                }
                if ($(this).hasClass('titleInfoArrow')) {
                    //可以钻取。
                    let itemId = $(this).attr('data-item');
                    let currCode = $('#tagnav .weui-state-active').attr('data-currcode');
                    let name = $(this).find('.titleInfo').text();
                    sessionStorage.setItem('itemName', name);
                    sessionStorage.setItem('itemId', itemId);
                    sessionStorage.setItem('currCode', currCode);
                    location.href = "subCapitalSummaryReport.jsp";
                }
            });
        })

    }

    function dataCollation(reportDataTwo) {
        var xAxisData = [];
        var seriesData = [];
        var accountAmountData = [];
        var collectAmountData = [];
        for (let i = 0; i < reportDataTwo.length; i++) {
            let dic = reportDataTwo[i];
            let orgName = dic.orgName;
            let len = reportDataTwo.length > 4 ? 4 : 6;
            if (orgName.length > len) {
                orgName = orgName.substring(0, len);
            }
            xAxisData.push(orgName)
            accountAmountData.push(dic.financingBalance);
            collectAmountData.push(dic.operatingBalance);
        }
        let barData1 = {
            name: "融资余额",
            type: 'bar',
            stack: 'Itme',
            data: accountAmountData
        }
        let barData2 = {
            name: "经营余额",
            type: 'bar',
            stack: 'Itme',
            data: collectAmountData
        }
        seriesData.push(barData2);
        seriesData.push(barData1);
        var option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            color: ['#7BE5C2', '#FCE172'],
            grid: {
                top: '8%',
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true,
            },
            xAxis: [
                {
                    type: 'category',
                    data: xAxisData,
                    axisLine: {
                        lineStyle: {
                            color: '#D6D6D6',
                        }
                    },
                    axisLabel: {
                        color: '#888888',
                        fontSize: 10,
                        interval: 0,
                    },
                    axisTick: {
                        show: false
                    },

                }
            ],
            yAxis: [
                {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#fff',
                        }
                    },
                    axisLabel: {
                        color: '#888888'
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(67,67,67,0.05)'
                        }
                    }
                }
            ],
            series: seriesData
        };
        return option;
    }

</script>


</body>
</html>

