<%@page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/include.jsp" %>

<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <title>单位列表</title>

    <link href="${ctx}/css/mui.min.css" rel="stylesheet">
    <link href="${ctx}/css/frozen.css" rel="stylesheet"/>
    <link href="${ctx}/css/mainstyle.css" rel="stylesheet"/>
    <link href="${ctx}/css/myscroll.css" rel="stylesheet"/>
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

    <script src="${ctx}/js/mui.min.js"></script>
    <script src="${ctx}/js/zepto.min.js"></script>
    <script src="${ctx}/js/zepto.weui.min.js"></script>
    <script src="${ctx}/js/common.js?version=${version}"></script>
    <style>

        * {
            touch-action: pan-y;
            -webkit-box-sizing: unset;
            box-sizing: unset;
        }

        .mui-input-clear {
            margin: 0;
        }

        .mui-scroll-wrapper {
            margin-top: 120px;
        }

        li.mui-table-view-cell > p {
            padding: 4px;
            font-size: 15px;
        }

        li.mui-table-view-cell > img {
            position: absolute;
            right: 2%;
            top: 20%;
        }

        li.mui-table-view:after {
            height: 0px;
        }

        .mui-table-view {
            margin-bottom: 50px;
        }

        .mui-btn-primary {
            margin-top: 10px;
            height: 10px;
            line-height: 10px;
        }


    </style>
</head>

<body>
<div style="padding: 10px">
    <div class="mui-input-row mui-search">
        <input style="margin: 0" id="search-input" type="search" class="mui-input-clear" placeholder="输入组织名称"/>
    </div>

    <button type="button" onclick="reset()" class="mui-btn-block mui-btn-primary mui-btn-outlined">重置</button>
</div>


<div id="pullrefresh" class="mui-scroll-wrapper">
    <div class="mui-scroll">
        <ul class="mui-table-view" id="data-list">

        </ul>
    </div>
</div>
<script>

    let currOrgId = sessionStorage.getItem("TQLS_CURRENT_UNIT_ID");

    let list = [];
    $(document).ready(function () {
        //mui 初始化
        mui.init({
            pullRefresh: {
                container: '#pullrefresh',
                down: {
                    contentrefresh: '',
                    callback: pulldownRefresh
                }
            }
        });

        resetData();


        //下拉刷新
        function pulldownRefresh() {
            setTimeout(function () {
                resetData();
            });
        }

        //点击列表切换组织
        mui('#data-list').on('tap', 'li', function () {
            var entityId = $(this).find('p').data('href');
            var entityName = $(this).find('p').html();
            sessionStorage.setItem("TQLS_CURRENT_UNIT_ID", entityId);
            sessionStorage.setItem("TQLS_CURRENT_UNIT_NAME", entityName);
            location.replace('tqls_future_loan_repayment.jsp');
            // alert(sessionStorage.getItem("TQLS_CURRENT_UNIT_ID"))
        });


        $('#search-input').bind('input propertychange', function (e) {
            datafilter($(this).val());
        });


        mui(".mui-icon-clear")[0].addEventListener('tap', function () {
            resetData();
        });

    });

    function reset() {

        sessionStorage.removeItem("TQLS_CURRENT_UNIT_ID");
        sessionStorage.removeItem("TQLS_CURRENT_UNIT_NAME");

        location.replace('tqls_future_loan_repayment.jsp');

    }

    function datafilter(orgName) {
        if (orgName !== '' || orgName.length > 0) {
            var listview = $('#data-list');
            listview.html('');
            for (var i = 0; i < list.length; i++) {

                if (list[i].name.indexOf(orgName) !== -1) {

                    var entityName = list[i].name;

                    var html;
                    if (list[i].id === currOrgId) {
                        html = '<li class="mui-table-view-cell">' +
                            '<p data-href="' + list[i].id + '">' + entityName + '</p>' +
                            '<img src="${ctx}/img/selected.png"/>' +
                            '</li>';
                    } else {
                        html = '<li class="mui-table-view-cell">' +
                            '<p data-href="' + list[i].id + '">' + entityName + '</p>' +
                            '<img src="${ctx}/img/selected.png" style="display: none"/>' +
                            '</li>';
                    }
                    listview.append(html);
                }
            }
        } else {
            resetData();
        }
    }

    function resetData() {
        $('#data-list').html("");
        $('#search-input').val('');

        ajaxRequest('${ctx}/tqls/getUnitList.do', {}, function (data) {

            mui('#pullrefresh').pullRefresh().endPulldownToRefresh();

            var count = data.result.totalNum;
            var listData = data.result.data;
            list = listData;
            if (count <= 0) {
                mui.toast('暂无数据');
                return;
            }

            var listview = $('#data-list');


            for (var i = 0; i < count; i++) {
                var html;

                var entityName = listData[i].name;

                if (listData[i].id === currOrgId) {
                    html = '<li class="mui-table-view-cell">' +
                        '<p data-href="' + listData[i].id + '">' + entityName + '</p>' +
                        '<img src="${ctx}/img/selected.png"/>' +
                        '</li>';
                } else {
                    html = '<li class="mui-table-view-cell">' +
                        '<p data-href="' + listData[i].id + '">' + entityName + '</p>' +
                        '<img src="${ctx}/img/selected.png" style="display: none"/>' +
                        '</li>';
                }
                listview.append(html);
            }
        });
    };

    function ajaxRequest(requestUrl, params, callback) {
        $.ajax({
            cache: true,
            type: 'POST',
            url: requestUrl,
            data: params,
            async: true,
            dataType: 'json',
            error: function (data) {
                mui.toast('网络连接异常');
            },
            success: function (data) {
                if (data.successful == true || 'true' == data.successful) {
                    callback(data);
                } else {
                    mui.toast('错误：' + data.message);
                }
            }
        });
    };
</script>


</body>

</html>
