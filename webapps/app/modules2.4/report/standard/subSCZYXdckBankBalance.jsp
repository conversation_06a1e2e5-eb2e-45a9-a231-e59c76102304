<%@ page contentType="text/html;charset=UTF-8" language="java" %>
    <%@include file='/common/include.jsp' %>

        <!DOCTYPE html>
        <html lang="en">

        <head>
            <meta charset="utf-8">
            <title>银行账户余额汇总表</title>
            <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
            <link rel="stylesheet" href="${ctx}/css/weui.min.css" />
            <link rel="stylesheet" href="${ctx}/css/weuix.min.css" />
            <style>
                #search-header {
                    height: 230px;
                    padding: 10px;
                    background-color: whitesmoke;
                }

                #center .tip {
                    display: block;
                    padding-top: 30px;
                    font-size: 12px;
                }

                #center .amount {
                    display: block;
                    padding-bottom: 30px;
                    font-size: 24px;
                    line-height: 24px;
                }

                .titleInfo {
                    padding-left: 20px;
                    padding-top: 18px;
                    font-size: 14px;
                    color: #5A5858;
                    font-weight: bold;
                    display: block;
                }

                .titleInfoArrow:after {
                    content: " ";
                    display: inline-block;
                    height: 7px;
                    width: 7px;
                    border-width: 2px 2px 0 0;
                    border-color: #c8c8cd;
                    border-style: solid;
                    transform: matrix(.71, .71, -.71, .71, 0, 0);
                    webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);
                    top: -2px;
                    position: absolute;
                    top: 50%;
                    margin-top: -4px;
                    right: 22px;
                }

                .amountAndTip3 {
                    display: inline-block;
                    width: calc(30% - 20px);
                    padding-left: 20px;
                }

                .amountAndTip3 .amount {
                    display: block;
                    color: #FF7979;
                    font-size: 16px;
                    font-weight: bold;
                }

                .amountAndTip3 .tip {
                    font-size: 10px;
                    color: #999999;
                    display: block;
                }

                .amountAndTip {
                    display: inline-block;
                    width: calc(48% - 20px);
                    padding-left: 20px;
                }

                .amountAndTip .amount {
                    display: block;
                    color: #FF7979;
                    font-size: 16px;
                    font-weight: bold;
                }

                .amountAndTip .tip {
                    font-size: 10px;
                    color: #999999;
                    display: block;
                }

                #noMsg {
                    position: relative;
                    display: none;
                }

                #noMsg img {
                    width: 95px;
                    height: 95px;
                    display: inline-block;
                    position: absolute;
                    margin-left: 50%;
                    margin-top: 50%;
                    left: -50px;
                    top: 50px;
                }

                #noMsg span {
                    display: inline-block;
                    position: absolute;
                    margin-left: 50%;
                    margin-top: 50%;
                    left: -32px;
                    top: 145px;
                    color: #999999;
                    font-size: 14px;
                }
            </style>
        </head>

        <body>
            <div id="search-header">
                <div class="weui-cell" style="background-color: whitesmoke">
                    <div class="weui-cell__hd"><label class="weui-label">开始日期</label></div>
                    <div class="weui-cell__bd">
                        <input id="start-date" class="weui-input" type="date" value="">
                    </div>
                </div>
                <div class="weui-cell" style="background-color: whitesmoke">
                    <div class="weui-cell__hd"><label class="weui-label">截止日期</label></div>
                    <div class="weui-cell__bd">
                        <input id="end-date" class="weui-input" type="date" value="">
                    </div>
                </div>
                <div class="weui-cell" style="background-color: whitesmoke">
                    <div class="weui-cell__hd"><label class="weui-label">最小金额</label></div>
                    <div class="weui-cell__bd">
                        <input id="min-amount" class="weui-input" type="number" placeholder="输入最小金额">
                    </div>
                </div>
                <div class="weui-cell" style="background-color: whitesmoke">
                    <div class="weui-cell__hd"><label class="weui-label">最大金额</label></div>
                    <div class="weui-cell__bd">
                        <input id="max-amount" class="weui-input" type="number" placeholder="输入最大金额">
                    </div>
                </div>
                <div class="weui-cell weui-cell_vcode" style="background-color: whitesmoke">
                    <div class="weui-cell__bd" style="color: #0d0d0d">
                        <input id="opp-account-name" class="weui-input" type="text" placeholder="输入对方户名">
                    </div>
                    <div class="weui-cell__ft">
                        <button class="weui-vcode-btn" onclick="query()">查询</button>
                    </div>
                </div>
            </div>
            <a id="toggleSearchBtn" href="javascript:toggleSearchHeader();" class="weui-btn weui-btn_default">收起查询条件</a>

            <div id="chartListInfo"></div>
            <div id="noMsg">
                <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
                <span>暂无数据</span>
            </div>

            <script src="${ctx}/js/zepto.min.js"></script>
            <script src="${ctx}/js/zepto.weui.min.js"></script>
            <script src="${ctx}/js/iscroll-lite.min.js"></script>
            <script src="${ctx}/js/common.js?version=${version}"></script>
            <script src="${ctx}/js/echarts1.min.js"></script>

            <script>
                var myChart;
                var option;
                $(function () {

                    initDate();
                    document.title = sessionStorage.getItem('itemName');
                    requestData();

                });

                let initDate = function () {
                    let endDate = $('#end-date').val();
                    let startDate = $('#start-date').val();
                    console.log('startdate=' + startDate + "; enddate=" + endDate);
                    if (startDate === '' && endDate === '') {
                        // 获取当前日期
                        var currentDate = new Date();

                        // 获取当月的第一天
                        var firstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

                        // 获取当前日期
                        var lastDay = new Date();

                        // 格式化日期
                        var options = { year: 'numeric', month: '2-digit', day: '2-digit' };
                        var formattedFirstDay = firstDay.toLocaleDateString('zh-CN', options).replace(/\//g, "-");
                        var formattedLastDay = lastDay.toLocaleDateString('zh-CN', options).replace(/\//g, "-");
                        console.log('firstDay=' + formattedFirstDay + "; lastDay=" + formattedLastDay);
                        $('#end-date').val(formattedLastDay);
                        $('#start-date').val(formattedFirstDay);
                    }
                }

                let query = function () {

                    let oppAccountName = $('#opp-account-name').val();
                    let maxAmount = $('#max-amount').val();
                    let minAmount = $('#min-amount').val();
                    let endDate = $('#end-date').val();
                    let startDate = $('#start-date').val();
                    if (!oppAccountName && !maxAmount && !minAmount) {
                        requestData();
                        return;
                    }
                    console.log("oppaccountname=" + oppAccountName + "; max-amount=" + maxAmount + "; minamount=" + minAmount + ";" +
                        "enddate=" + endDate + "; startdate=" + startDate)
                    if ((startDate && endDate) && !compareDates(startDate, endDate)) {
                        $.toast("开始日期不能晚于截止日期", 'cancel');
                        return;
                    }
                    if (minAmount && maxAmount && parseFloat(maxAmount) < parseFloat(minAmount)) {
                        $.toast("最大金额不得小于最小基恩", 'cancel');
                        return;
                    }



                    requestData({
                        oppAccountName: oppAccountName,
                        amountFrom: minAmount,
                        amountTo: maxAmount,
                        dateTo: endDate,
                        dateFrom: startDate,
                    })
                }

                let isSearchHeaderVisible = true;

                let toggleSearchHeader = function () {
                    if (isSearchHeaderVisible) {
                        $("#search-header").hide();
                        isSearchHeaderVisible = false;
                        $('#toggleSearchBtn').html('展开查询条件')
                    } else {
                        $("#search-header").show();
                        isSearchHeaderVisible = true;
                        $('#toggleSearchBtn').html('收起查询条件')
                    }
                }


                function requestData(queryParams) {
                    showNoMsg(false)
                    var param = { 'bankId': sessionStorage.getItem('itemId') };
                    let endDate = $('#end-date').val();
                    let startDate = $('#start-date').val();
                    if (startDate) {
                        param.dateFrom = startDate;
                    }
                    if (endDate) {
                        param.dateTo = endDate;
                    }

                    if (queryParams) {
                        param = queryParams;
                    }

                    $.ajax({
                        cache: true,
                        type: 'POST',
                        url: "${ctx}/cntc/getAccountTradeList.do",
                        data: param,
                        async: true,
                        dataType: 'json',
                        beforeSend: function () {
                            $.showLoading();
                        },
                        error: function () {
                            $.hideLoading();
                            $.toast('网络服务情况异常，请检查', "forbidden");
                            showNoMsg(true);
                        },
                        success: function (data) {
                            $.hideLoading();
                            if (data.successful === true || "true" === data.successful) {
                                layoutListInfo(data.result.data);

                            } else {
                                $.toast('网络服务情况异常，请检查', "forbidden");
                                showNoMsg(true);
                            }
                        }
                    });
                }

                function showNoMsg(show) {
                    if (show) {
                        $("#chartListInfo").html('');
                        $("#noMsg").css('display', 'block');
                    } else {
                        $("#noMsg").css('display', 'none');
                    }
                }


                function layoutListInfo(reportDataThree) {
                    $('#chartListInfo').html('');
                    for (let i = 0; i < reportDataThree.length; i++) {
                        let dic = reportDataThree[i];

                        $("#chartListInfo").append(
                            '<div class="cell" style="position: relative;">' +
                            '<span class="titleInfo">' + dic.bankName + '</span>' +
                            '<div class="amountAndTip"><span class="amount">' + dic.amount + '</span><span class="tip">交易金额</span></div>' +
                            '<div class="amountAndTip"><span class="">' + dic.currCode + '</span><span class="tip">币种</span></div>' +
                            '<div class="amountAndTip"><span class="">' + dic.oppAccountName + '</span><span class="tip">对方户名</span></div>' +
                            '<div class="amountAndTip"><span class="">' + dic.date + '</span><span class="tip">交易日期</span></div>' +
                            '<div class="amountAndTip"><span class="">' + dic.accountNumber + '</span><span class="tip">账号</span></div>' +
                            '<div class="amountAndTip"><span class="">' + dic.moneyWay + '</span><span class="tip">交易方向</span></div>' +
                            '<div class="amountAndTip"><span class="amount">' + dic.balance + '</span><span class="tip">当前余额</span></div>' +
                            '<div style="width: 100%;height: 10px;background: #ffffff"></div>' +
                            '<div class="cellPadding" style="width: 100%;height: 5px;background: #F1F3F5"></div>' +
                            '</div>');

                    }
                }

                function compareDates(date1, date2) {
                    // 将日期字符串转换为 Date 对象
                    let d1 = new Date(date1);
                    let d2 = new Date(date2);
                    // 比较日期大小
                    return d1 <= d2;
                }

            </script>


        </body>

        </html>