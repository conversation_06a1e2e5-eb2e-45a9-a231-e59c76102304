<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>资金情况表</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style>
        #tagnav {
            position: fixed;
            top: 0;
            z-index: 999;
            background: #649CFF;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .weui-navigator-list li {
            line-height: 40px;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a {
            color: #93FFDB;
            font-weight: bold;
            border-bottom: 1px solid #93FFDB;
        }

        .weui-navigator-list li.weui-state-disabled a {
            color: rgba(255, 255, 255, 0.2);
            border-bottom: 1px solid #649CFF;
        }

        .weui-navigator-list li a {
            color: #ffffff;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a:after {
            background-color: transparent;
        }

        #reportTitle {
            padding-top: 43px;
            background: #649CFF;
            color: #fff;
        }

        #center .tip {
            display: block;
            padding-top: 10px;
            font-size: 12px;
            text-align: center;
            color: rgba(255, 255, 255, 0.7);

        }

        #center .amount {
            display: block;
            padding-bottom: 10px;
            font-size: 24px;
            line-height: 24px;
            text-align: center;
        }

        #left, #right {
            display: inline-block;
            width: 48%;
            text-align: center;
        }

        .leftTip, .rightTip {
            display: inline-block;
            padding-top: 10px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            position: relative;
        }

        .leftTip:before, .rightTip:before {
            content: ' ';
            width: 8px;
            height: 8px;
            display: inline-block;
            border: 1px solid #ffffff;
            border-radius: 6px;
            position: absolute;
            top: 13px;
            left: -15px;
        }

        .leftTip:before {
            background: #f1f1f1;
        }

        .rightTip:before {
            background: #f1f1f1;
        }

        .leftAmount, .rightAmount {
            padding-bottom: 5px;
            display: block;
            font-size: 18px;
            font-weight: 500;
        }

        .line {
            display: inline-block;
            width: 1%;
            max-width: 1px;
            background: #fff;
            height: 30px;
        }

        .titleInfo {
            padding-left: 20px;
            padding-top: 18px;
            font-size: 17px;
            color: #5A5858;
            font-weight: bold;
            display: block;
        }

        .titleInfoArrow:after {
            content: " ";
            display: inline-block;
            height: 7px;
            width: 7px;
            border-width: 2px 2px 0 0;
            border-color: #c8c8cd;
            border-style: solid;
            transform: matrix(.71, .71, -.71, .71, 0, 0);
            webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);
            top: -2px;
            position: absolute;
            top: 50%;
            margin-top: -4px;
            right: 22px;
        }

        .amountAndTip {
            display: inline-block;
            width: calc(48% - 20px);
            padding-left: 20px;
        }

        .amountAndTip .amount {
            display: block;
            color: #FF7979;
            font-size: 16px;
            font-weight: bold;
        }

        .amountAndTip .tip {
            font-size: 10px;
            color: #999999;
            display: block;
        }

        #noMsg {
            position: relative;
            display: none;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }

        .weui-dialog__btn {
            color: #649CFF;
        }
    </style>
</head>
<body>
<div id="tagnav" class="weui-navigator weui-navigator-wrapper">
    <ul class="weui-navigator-list"></ul>
</div>
<div id="reportTitle"></div>
<div id="chartSection"></div>
<div id="chartListInfo"></div>
<div id="noMsg">
    <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
    <span>暂无数据</span>
</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/iscroll-lite.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script src="${ctx}/js/echarts.min.js"></script>

<script>
    $(function () {
        var reportActiveCurrCode = sessionStorage.getItem('reportActiveCurrCode');
        var realCurrCode = sessionStorage.getItem('currCode');
        var tabIndex = 0;
        var currCodeArray = JSON.parse(reportActiveCurrCode);
        let tabs = $('.weui-navigator-list');
        for (let i = 0; i < currCodeArray.length; i++) {
            let tabItem = currCodeArray[i];
            let currName = tabItem.currName;
            let currCode = tabItem.currCode;
            if (currCode === realCurrCode) {
                tabIndex = i;
            }
            let tab;
            if (tabItem.hasData) {
                tab = $('<li data-currCode="' + currCode + '"><a>' + currName + '</a></li>');
            } else {
                tab = $('<li class= "weui-state-disabled"><a>' + currName + '</a></li>');
            }
            tabs.append(tab);
        }
        //使用这个是为了避免滑动时出现滚动条。
        TagNav('#tagnav', {
            type: 'scrollToNext',
            curClassName: 'weui-state',
            //不能使用weui-state-active，随便写一个，以免下面的重写失效。
            index: tabIndex
        });
        //重写切换事件和选中事件
        $("#tagnav li").eq(tabIndex).addClass('weui-state-active');
        $('#tagnav').on('click', 'li',
            function (event) {
                event.preventDefault();
                $(this).addClass('weui-state-active');
                if (!$(this).hasClass('weui-state-disabled')) {
                    $(this).siblings('li').removeClass('weui-state-active');
                    requestData($(this).attr('data-currCode'));
                }
            });
        requestData(realCurrCode);
        $(document.body).on('touchmove', function (e) {
            e.preventDefault();
        })


    });

    function click_month_flow(type) {
        sessionStorage.setItem("currCode", $('#tagnav .weui-state-active').attr('data-currcode'));
        sessionStorage.setItem("type", type);
        location.href = 'sub_zjsjk_fund_flow_situation_month_detail.jsp';

    }

    function requestData(currCode) {
        showNoMsg(false)
        var param = {'currCode': currCode, 'tag': 'ats'};
        $.ajax({
            cache: true,
            type: 'POST',
            url: "${ctx}/zjsjk/getFundFlowSituation.do",
            data: param,
            async: true,
            dataType: 'json',
            beforeSend: function () {
                $.showLoading();
            },
            error: function () {
                $.hideLoading();
                $.toast('网络服务情况异常，请检查', "forbidden");
                showNoMsg(true);
            },
            success: function (data) {
                $.hideLoading();
                if (data.successful == true || "true" == data.successful) {
                    var dataArray = data.result.data;
                    if (dataArray.length == 3) {
                        //顶部数据
                        layoutTopTitle(dataArray[0].reportData);
                        //chart数据
                        $('#chartSection').html('');
                        $('#chartSection').append(
                            '<div id="chartView" style="width: 100%;height: 368px;"></div>' +
                            '<div class="chartPadding" style="width: 100%;height: 10px;background: #F1F3F5"></div>');
                        var myChart = echarts.init(document.getElementById('chartView'));
                        var option = dataCollation(dataArray[1].reportData);
                        myChart.setOption(option);
                        //列表数据
                        layoutListInfo(dataArray[2].reportData);
                    } else {
                        // 暂无数据
                        showNoMsg(true);
                    }
                } else {
                    $.toast('网络服务情况异常，请检查', "forbidden");
                    showNoMsg(true);
                }
            }
        });
    }

    function showNoMsg(show) {
        if (show) {
            $("#reportTitle").html('');
            $("#chartSection").html('');
            $("#chartListInfo").html('');
            $("#noMsg").css('display', 'block');
        } else {
            $("#noMsg").css('display', 'none');
        }
    }

    function layoutTopTitle(reportDataOne) {
        $("#reportTitle").html('');
        let dic = reportDataOne[0];
        $("#reportTitle").append(
            '<div id="center"><span class="tip">总余额/亿元</span><span class="amount" style="padding-bottom: 0px;">' + dic.totalAmount + '</span></div>' +
            '<div id="left"><span class="leftTip">活期存款余额/亿元</span><span class="leftAmount">' + dic.demandDeposit + '</span></div>' +
            '<div class="line"></div>' +
            '<div id="right"><span class="rightTip">定期存款余额/亿元</span><span class="rightAmount">' + dic.timeDeposit + '</span></div>' +
            '<div id="left" onclick="click_month_flow(2)"><span class="leftTip">本月累计收入/亿元</span><span class="leftAmount">' + dic.monthInflow + '</span></div>' +
            '<div class="line"></div>' +
            '<div id="right" onclick="click_month_flow(1)"><span class="rightTip">本月累计支出/亿元</span><span class="rightAmount">' + dic.monthOutflow + '</span></div>' +
            '<div id="left"><span class="leftTip">本日累计收入/亿元</span><span class="leftAmount">' + dic.dayInflow + '</span></div>' +
            '<div class="line"></div>' +
            '<div id="right"><span class="rightTip">本日累计支出/亿元</span><span class="rightAmount">' + dic.dayOutFlow + '</span></div>'
        );
    }

    function layoutListInfo(reportDataThree) {
        $('#chartListInfo').html('');
        for (let i = 0; i < reportDataThree.length; i++) {
            let arrowStyle = "titleInfoArrow"
            let currOrg = '';
            let dic = reportDataThree[i];

            $("#chartListInfo").append(
                '<div class="cell ' + arrowStyle + '" style="position: relative;" data-item = ' + dic.orgId + '>' +
                '<span class="titleInfo">' + dic.orgName + currOrg + '</span>' +
                '<div class="amountAndTip"><span class="tip">账户总余额</span><span class="amount">' + dic.totalAmount + '</span></div>' +
                '<div class="amountAndTip"><span class="tip">内部归集资金</span><span class="amount">' + dic.ivAmount + '</span></div>' +

                '<div class="balance-area">' +
                '<div class="amountAndTip"><span class="tip">活期余额</span><span class="amount">' + dic.demandDeposit + '</span></div>' +
                '<div class="amountAndTip"><span class="tip">定期余额</span><span class="amount">' + dic.timeDeposit + '</span></div>' +
                '</div>' +
                '<div class="detail-area">' +
                '<div class="amountAndTip"><span class="tip">本月收入</span><span class="amount">' + dic.monthInflow + '</span></div>' +
                '<div class="amountAndTip"><span class="tip">本月支出</span><span class="amount">' + dic.monthOutflow + '</span></div>' +
                '</div>' +
                '<div class="detail-area">' +
                '<div class="amountAndTip"><span class="tip">本日收入</span><span class="amount">' + dic.dayInflow + '</span></div>' +
                '<div class="amountAndTip"><span class="tip">本日支出</span><span class="amount">' + dic.dayOutFlow + '</span></div>' +
                '</div>' +
                '<div style="width: 100%;height: 10px;background: #ffffff"></div>' +
                '<div class="cellPadding" style="width: 100%;height: 5px;background: #F1F3F5"></div>' +
                '</div>');
        }
        var clickable;
        $(".cell").on('touchstart', function (event) {
            clickable = true;
        });
        $(".cell").on('touchmove', function (event) {
            clickable = false;
        });

        $(".cell").on('click', function (event) {
            event.preventDefault();
            if (!clickable) {
                return;
            }
            let itemId = $(this).attr('data-item');
            let currCode = $('#tagnav .weui-state-active').attr('data-currcode');
            let name = $(this).find('.titleInfo').text();
            sessionStorage.setItem('itemName', name);
            sessionStorage.setItem('itemName_sub_org', name);
            sessionStorage.setItem('itemId_sub_org', itemId);
            sessionStorage.setItem('itemId', itemId);
            sessionStorage.setItem('currCode', currCode);
            sessionStorage.setItem('currCode_sub_org', currCode);

            if ($(this).hasClass('titleInfoArrow')) {
                $.modal({
                    title: "请选择",
                    text: "查看内容",
                    buttons: [
                        {
                            text: "组织余额", onClick: function () {
                                location.href = "sub_zjsjk_fund_flow_situation_org.jsp";
                            }
                        },
                        {
                            text: "收入支出", onClick: function () {
                                location.href = "sub_zjsjk_fund_flow_situation_detail.jsp";
                            }
                        },
                        {
                            text: "取消", className: "default", onClick: function () {
                                //关闭当前弹窗
                            }
                        },
                    ]
                });

                //可以钻取。
            }

        })


    }


    function dataCollation(reportDataTwo) {
        var xAxisData = [];
        var dayDemandBalance = [];
        var dayTimeBalance = [];
        var monthInflowData = [];
        var monthOutflowData = [];
        for (let i = 0; i < reportDataTwo.length; i++) {
            let dic = reportDataTwo[i];
            let orgName = dic.orgName;

            xAxisData.push(orgName)
            dayDemandBalance.push(dic.demandDeposit);
            dayTimeBalance.push(dic.timeDeposit);
            monthInflowData.push(dic.monthInflow);
            monthOutflowData.push(dic.monthOutflow);
        }
        var option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    crossStyle: {
                        color: '#999'
                    }
                },
                position: ['30%', '50%']
            },
            grid: {
                top: '25%',
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            legend: {
                left: 'right',
                data: ['本日活期余额', '本日定期余额', '本月支出', '本月收入'],
                itemWidth: 10,
                itemHeight: 10,
            },
            xAxis: [
                {
                    type: 'category',
                    data: xAxisData,
                    axisPointer: {
                        type: 'shadow'
                    },
                    axisLabel: {
                        interval: 0,
                        rotate: 45,//倾斜度 -90 至 90 默认为0

                    }
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    name: '元',
                    axisLabel: {
                        formatter: '{value}'
                    }
                }
            ],
            color: ['#FCE172', '#908CFF', '#12b298', '#f95a50'],
            series: [
                {
                    name: '本日活期余额',
                    type: 'bar',
                    stack: 'balance',
                    tooltip: {
                        valueFormatter: function (value) {
                            return value + '/元';
                        }
                    },
                    data: dayDemandBalance
                },
                {
                    name: '本日定期余额',
                    type: 'bar',
                    stack: 'balance',
                    tooltip: {
                        valueFormatter: function (value) {
                            return value + '/元';
                        }
                    },
                    data: dayTimeBalance
                },
                {
                    name: '本月支出',
                    type: 'line',
                    tooltip: {
                        valueFormatter: function (value) {
                            return value + ' °C';
                        }
                    },
                    data: monthOutflowData
                },
                {
                    name: '本月收入',
                    type: 'line',
                    tooltip: {
                        valueFormatter: function (value) {
                            return value + '';
                        }
                    },
                    data: monthInflowData
                }
            ]
        };
        return option;
    }

</script>


</body>
</html>

