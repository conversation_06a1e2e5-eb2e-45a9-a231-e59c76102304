<%@ page import="cn.hutool.core.date.DateUtil" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<%
    String todayDate = DateUtil.today();
%>
<c:set var="todayDate" value="<%=todayDate%>"/>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>资金余额表</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style>
        #tagnav {
            position: fixed;
            top: 0;
            z-index: 999;
            background: #649CFF;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .weui-navigator-list li {
            line-height: 40px;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a {
            color: #93FFDB;
            font-weight: bold;
            border-bottom: 1px solid #93FFDB;
        }

        .weui-navigator-list li.weui-state-disabled a {
            color: rgba(255, 255, 255, 0.2);
            border-bottom: 1px solid #649CFF;
        }

        .weui-navigator-list li a {
            color: #ffffff;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a:after {
            background-color: transparent;
        }

        #reportTitle {
            min-height: 44px;
            padding-top: 20px;
            background: #649CFF;
            color: #fff;
        }

        #pick-date {
            background: #649CFF;
            border: none;
            font-size: 18px;
            font-weight: bold;
            position: absolute;
            top: 20px;
            right: 20px;
            text-align: end;
            color: white;

        }


        #center {
            width: 100%;
            text-align: center;
        }

        #center .tip {
            display: block;
            padding-top: 20px;
            font-size: 12px;
        }

        #center .amount {
            display: block;
            padding-bottom: 15px;
            font-size: 24px;
            line-height: 24px;
        }

        #left, #right {
            display: inline-block;
            width: 45%;
            text-align: center;
        }

        .leftTip, .rightTip {
            display: inline-block;
            padding-top: 20px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            position: relative;
        }

        .leftTip:before, .rightTip:before {
            content: ' ';
            width: 11px;
            height: 11px;
            display: inline-block;
            border: 1px solid #ffffff;
            border-radius: 6px;
            position: absolute;
            top: 23px;
            left: -20px;
        }

        .leftTip:before {
            background: #FCE172;
        }

        .rightTip:before {
            background: #908CFF;
        }

        .leftAmount, .rightAmount {
            padding-bottom: 15px;
            display: block;
            font-size: 18px;
            font-weight: 500;
        }

        .line {
            display: inline-block;
            width: 1%;
            max-width: 1px;
            background: #fff;
            height: 30px;
        }

        .titleInfo {
            padding-left: 20px;
            padding-top: 18px;
            font-size: 14px;
            color: #5A5858;
            font-weight: bold;
            display: block;
        }

        .titleInfoArrow:after {
            content: " ";
            display: inline-block;
            height: 7px;
            width: 7px;
            border-width: 2px 2px 0 0;
            border-color: #c8c8cd;
            border-style: solid;
            transform: matrix(.71, .71, -.71, .71, 0, 0);
            webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);
            position: absolute;
            top: 50%;
            margin-top: -4px;
            right: 22px;
        }

        .amountAndTip {
            display: inline-block;
            width: calc(48% - 20px);
            padding-left: 20px;
        }

        .amountAndTip .amount {
            display: block;
            color: #FF7979;
            font-size: 16px;
            font-weight: bold;
        }

        .amountAndTip .tip {
            font-size: 10px;
            color: #999999;
            display: block;
        }

        #noMsg {
            position: relative;
            display: none;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }
    </style>
</head>
<body>
<div style="display: none;" id="tagnav" class="weui-navigator weui-navigator-wrapper">
    <ul class="weui-navigator-list"></ul>
</div>
<input type="text" id="pick-date" placeholder="${todayDate}"/>
<div id="reportTitle">

    <!-- <div id="center"><span class="tip">账户余额/万元</span><span class="amount">3828,222.72</span></div>
    <div id="left"><span class="leftTip">账户余额/万元</span><span class="leftAmount">10000</span></div>
    <div class="line"></div>
    <div id="right"><span class="rightTip">上缴金额/万元</span><span class="rightAmount">18888</span></div> -->
</div>
<div id="chartSection"></div>
<div id="chartListInfo"></div>
<div id="noMsg">
    <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
    <span>暂无数据</span>
</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/iscroll-lite.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script src="${ctx}/js/echarts.min.js"></script>

<script>
    $(function () {
        document.title = '资金余额表';
        requestData(null);
        $('#pick-date').datetimePicker({
            title: '日期',
            times: function () {
                return [];
            },
            parse: function (str) {
                return str.split("-");
            },
            onChange: function (picker, values, displayValues) {
                console.log(values);
                var date = values[0] + '-' + values[1] + '-' + values[2];
                sessionStorage.setItem("picked-date", date);
            },
            onClose: function () {
                requestData(sessionStorage.getItem("picked-date"));

            }
        });


    });

    function dateChange(date) {

        var formatterDate = date.replace(/\\/, '-');
        requestData(formatterDate);
        alert(formatterDate)
    }

    function requestData(queryDate) {
        console.log('查询日期=' + queryDate);
        showNoMsg(false)
        var param = {'queryDate': queryDate};
        $.ajax({
            cache: true,
            type: 'POST',
            url: "${ctx}/kmmy/getFundBalance.do",
            data: param,
            async: true,
            dataType: 'json',
            beforeSend: function () {
                $.showLoading();
            },
            error: function () {
                $.hideLoading();
                $.toast('网络服务情况异常，请检查', "forbidden");
                showNoMsg(true);
            },
            success: function (data) {
                $.hideLoading();
                if (data.successful === true || "true" === data.successful) {
                    var dataArray = data.result.data;
                    if (dataArray.length === 3 && dataArray[1].reportData.length > 0) {
                        //顶部数据
                        layoutTopTitle(dataArray[0].reportData);
                        //chart数据
                        $('#chartSection').html('');
                        $('#chartSection').append(
                            '<div id="chartView" style="width: 100%;height: 220px;"></div>' +
                            '<div class="chartPadding" style="width: 100%;height: 10px;background: #F1F3F5"></div>');
                        var myChart = echarts.init(document.getElementById('chartView'));
                        var option = dataCollation(dataArray[0].reportData);
                        myChart.setOption(option);
                        //列表数据
                        layoutListInfo(dataArray[1].reportData);
                    } else {
                        // 暂无数据
                        showNoMsg(true);
                    }
                } else {
                    $.toast('网络服务情况异常，请检查', "forbidden");
                    showNoMsg(true);
                }
            }
        });
    }

    function showNoMsg(show) {
        if (show) {
            $("#reportTitle").html('');
            $("#chartSection").html('');
            $("#chartListInfo").html('');
            $("#noMsg").css('display', 'block');
        } else {
            $("#noMsg").css('display', 'none');
        }
    }

    function layoutTopTitle(reportDataOne) {
        $("#reportTitle").html('');
        let dic = reportDataOne[0];
        $("#reportTitle").append('<div id="center"><span class="tip">账面余额/万元</span><span class="amount">' + dic.bookBalanceShow + '</span></div>' +
            ' <div id="left"><span class="leftTip">可用余额/万元</span><span class="leftAmount">' + dic.availableBalanceShow + '</span></div>\n' +
            '    <div class="line"></div>\n' +
            '    <div id="right"><span class="rightTip">受限余额/万元</span><span class="rightAmount">' + dic.limitBalanceShow + '</span></div>')
    }

    function layoutListInfo(reportDataThree) {
        $('#chartListInfo').html('');
        for (let i = 0; i < reportDataThree.length; i++) {
            let dic = reportDataThree[i];
            $("#chartListInfo").append(
                '<div class="cell" style="position: relative;" data-item = ' + dic.itemId + '>' +
                '<span class="titleInfo">' + dic.bankName + '</span>' +
                '<div class="amountAndTip"><span class="amount">' + dic.availableBalanceShow + '</span><span class="tip">可用资金</span></div>' +
                '<div class="amountAndTip"><span class="amount" style="color:#FFB06B">' + dic.limitBalanceShow + '</span><span class="tip">受限资金</span></div>' +
                '<div class="amountAndTip"><span class="amount">' + dic.bookBalanceShow + '</span><span class="tip">账面资金</span></div>' +

                '<div style="width: 100%;height: 10px;background: #ffffff"></div>' +
                '<div class="cellPadding" style="width: 100%;height: 1px;background: #F1F3F5"></div>' +
                '</div>');


        }
    }


    function comdify(n) {
        var re = /\d{1,3}(?=(\d{3})+$)/g;
        var n1 = n.replace(/^(\d+)((\.\d+)?)$/, function (s, s1, s2) {
            return s1.replace(re, "$&,") + s2;
        });
        return n1;
    }

    function dataCollation(reportDataTwo) {
        let dic = reportDataTwo[0];
        var orgNameData = ['可用余额', '受限余额'];
        var nameData = ['可用余额', '受限余额'];
        var seriesData = [
            {
                value: dic.availableBalance, name: nameData[0]
            }, {
                value: dic.limitBalance,
                name: nameData[1]
            }
        ];

        var option = {
            tooltip: {
                trigger: 'item',
                // formatter: "{b}: {c}"
                formatter: function (params, ticket, callback) {
                    return params.name + ": " + comdify(params.value);
                },
            },
            legend: {
                orient: 'vertical',
                right: '5%',
                top: 'center',
                data: orgNameData,
                itemWidth: 8,
                itemHeight: 8,
                textStyle: {
                    fontSize: 8,
                },
                selectedMode: false,
                formatter: function (name) {
                    var index = nameData.indexOf(name);
                    var dic = seriesData[index];
                    return name + ' ' + comdify(dic.value);
                }
            },
            color: ['#FCE172', '#908CFF', '#FB8D8D', '#7BE5C2', '#B496F1', '#FF91BB'],
            series: [
                {
                    type: 'pie',
                    center: ["30%", "50%"],
                    radius: ['35%', '70%'],
                    avoidLabelOverlap: false,
                    label: {
                        normal: {
                            show: false,
                            position: 'center'
                        }
                    },
                    labelLine: {
                        normal: {
                            show: false
                        }
                    },
                    data: seriesData
                }
            ]
        };
        return option;
    }
</script>


</body>
</html>

