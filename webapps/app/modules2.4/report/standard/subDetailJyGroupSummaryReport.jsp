<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>交易明细</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style>
        #tagnav {
            position: fixed;
            top: 0;
            z-index: 999;
            background: #649CFF;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .weui-navigator-list li {
            line-height: 40px;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a {
            color: #93FFDB;
            font-weight: bold;
            border-bottom: 1px solid #93FFDB;
        }

        .weui-navigator-list li.weui-state-disabled a {
            color: rgba(255, 255, 255, 0.2);
            border-bottom: 1px solid #649CFF;
        }

        .weui-navigator-list li a {
            color: #ffffff;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a:after {
            background-color: transparent;
        }

        #reportTitle {
            padding-top: 43px;
            background: #649CFF;
            color: #fff;
        }

        #center {
            width: 100%;
            text-align: center;
        }

        #center .tip {
            display: block;
            padding-top: 30px;
            font-size: 12px;
        }

        #center .amount {
            display: block;
            padding-bottom: 30px;
            font-size: 24px;
            line-height: 24px;
        }

        #left, #right {
            display: inline-block;
            width: 48%;
            text-align: center;
        }

        .leftTip, .rightTip {
            display: inline-block;
            padding-top: 38px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            position: relative;
        }

        .leftAmount, .rightAmount {
            padding-bottom: 30px;
            display: block;
            font-size: 18px;
            font-weight: 500;
        }

        .line {
            display: inline-block;
            width: 1%;
            max-width: 1px;
            background: #fff;
            height: 30px;
        }

        .titleInfo {
            padding-left: 20px;
            padding-top: 18px;
            font-size: 17px;
            color: #5A5858;
            font-weight: bold;
            display: block;
        }

        .titleInfoArrow:after {
            content: " ";
            display: inline-block;
            height: 7px;
            width: 7px;
            border-width: 2px 2px 0 0;
            border-color: #c8c8cd;
            border-style: solid;
            transform: matrix(.71, .71, -.71, .71, 0, 0);
            webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);
            top: -2px;
            position: absolute;
            top: 50%;
            margin-top: -4px;
            right: 22px;
        }

        .amountAndTip {
            display: inline-block;
            width: calc(48% - 20px);
            padding-left: 20px;
        }

        .amountAndTip .amount {
            display: block;
            color: #FF7979;
            font-size: 16px;
            font-weight: bold;
        }

        .amountAndTip .tip {
            font-size: 10px;
            color: #999999;
            display: block;
        }

        .info1, .info2 {
            padding-left: 20px;
            font-size: 14px;
            color: #5A5858;
        }

        .cellLine {
            height: 1px;
            background: #F1F3F5;
            margin-left: 20px;
            margin-right: 20px;
            margin-top: 10px;
        }

        .leftInfo, .rightInfo {
            display: inline-block;
            font-size: 14px;
            color: #5A5858;
            padding: 10px 20px;
        }

        .rightInfo {
            float: right;
            line-height: 22px;
            color: #888888;
            font-size: 12px;
        }

        #noMsg {
            position: relative;
            display: none;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }
    </style>
</head>
<body>
<div id="tagnav" class="weui-navigator weui-navigator-wrapper">
    <ul class="weui-navigator-list"></ul>
</div>
<div id="reportTitle"></div>
<div id="chartSection"></div>
<div id="chartListInfo">
    <!-- <div class="cell">
      <div class="titleInfo">中国银行</div>
      <div class="info1" style="padding-top:10px">账户性质: 基本户</div>
      <div class="info2">账户余额: 188,221.82</div>
      <div class="cellLine"></div>
      <div class="bottomInfo"><span class="leftInfo">是否直联: 否</span><span class="rightInfo">是否可用: 否</span></div>
      <div class="cellPadding" style="width: 100%;height: 5px;background: #F1F3F5"></div>
    </div> -->
</div>
<div class="weui-loadmore" style="display: none;">
    <i class="weui-loading"></i>
    <span class="weui-loadmore__tips">正在加载</span>
</div>
<div id="noMsg">
    <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
    <span>暂无数据</span>
</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/iscroll-lite.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script src="${ctx}/js/echarts.min.js"></script>

<script>
    var itemId = "";
    var pageNum = 1;
    $(function () {
        var reportActiveCurrCode = sessionStorage.getItem('reportActiveCurrCode');
        var realCurrCode = sessionStorage.getItem('currCode');
        itemId = sessionStorage.getItem('itemId');
        var navTitle = sessionStorage.getItem('navTitle');
        document.title = navTitle + "交易明细";
        var tabIndex = 0;
        var currCodeArray = JSON.parse(reportActiveCurrCode);
        let tabs = $('.weui-navigator-list');
        for (let i = 0; i < currCodeArray.length; i++) {
            let tabItem = currCodeArray[i];
            let currName = tabItem.currName;
            let currCode = tabItem.currCode;
            if (currCode === realCurrCode) {
                tabIndex = i;
            }
            let tab;
            if (tabItem.hasData) {
                tab = $('<li data-currCode="' + currCode + '"><a>' + currName + '</a></li>');
            } else {
                tab = $('<li class= "weui-state-disabled"><a>' + currName + '</a></li>');
            }
            tabs.append(tab);
        }
        //使用这个是为了避免滑动时出现滚动条。
        TagNav('#tagnav', {
            type: 'scrollToNext',
            curClassName: 'weui-state',
            //不能使用weui-state-active，随便写一个，以免下面的重写失效。
            index: tabIndex
        });
        //重写切换事件和选中事件
        $("#tagnav li").eq(tabIndex).addClass('weui-state-active');
        $('#tagnav').on('click', 'li',
            function (event) {
                event.preventDefault();
                $(this).addClass('weui-state-active');
                if (!$(this).hasClass('weui-state-disabled')) {
                    $(this).siblings('li').removeClass('weui-state-active');
                    requestData($(this).attr('data-currCode'));
                }
            });
        requestData(realCurrCode);
        //分页加载更多
        var loading = false;  //状态标记
        $(document.body).infinite().on("infinite", function () {
            if (loading) return;
            loading = true;
            setTimeout(function () {
                pageNum++;
                requestData(realCurrCode);
                loading = false;
            }, 1500);
        });
        $(document.body).on('touchmove', function (e) {
            e.preventDefault();
        });

    });

    function requestData(currCode) {
        showNoMsg(false)
        var param = {'currCode': currCode, 'tag': 'ats', 'orgId': itemId, 'pageNum': pageNum};
        $.ajax({
            cache: true,
            type: 'POST',
            url: "<%=basePath%>/saasreport/getJYJTCapitalSummaryDetail.do",
            data: param,
            async: true,
            dataType: 'json',
            beforeSend: function () {
                $.showLoading();
            },
            error: function () {
                $.hideLoading();
                $.toast('网络服务情况异常，请检查', "forbidden");
                showNoMsg(true);
            },
            success: function (data) {
                $(".weui-loadmore").css('display', 'block');
                $.hideLoading();
                if (data.successful == true || "true" == data.successful) {
                    var dataArray = data.result.data;
                    if (dataArray.length == 3) {
                        //顶部数据
                        layoutTopTitle(dataArray[0].reportData, data.result.queryPeriodText);
                        //列表数据
                        layoutListInfo(dataArray[1].reportData);

                    } else {
                        // 暂无数据
                        showNoMsg(true);
                    }
                } else {
                    $.toast('网络服务情况异常，请检查', "forbidden");
                    showNoMsg(true);
                }
            }
        });
    }

    function showNoMsg(show) {
        if (show) {
            $("#reportTitle").html('');
            $("#chartSection").html('');
            $("#chartListInfo").html('');
            $("#noMsg").css('display', 'block');
        } else {
            $("#noMsg").css('display', 'none');
        }
    }

    function layoutTopTitle(reportDataOne, queryPeriodText) {
        $("#reportTitle").html('');
        let dic = reportDataOne[0];
        $("#reportTitle").append(
            '<div id="left"><span class="leftTip">' + queryPeriodText + '收入/万元</span><span class="leftAmount">' + dic.monthInput + '</span></div>' +
            '<div class="line"></div>' +
            '<div id="right"><span class="rightTip">' + queryPeriodText + '支出/万元</span><span class="rightAmount">' + dic.monthOutput + '</span></div>');
    }

    function layoutListInfo(reportDataThree) {
        if (reportDataThree.length > 0) {
            for (let i = 0; i < reportDataThree.length; i++) {
                let dic = reportDataThree[i];
                let moneyWay = dic.moneyWay === '1' ? '<span style="color:#FF7979">-' + dic.amount + '元</span>' : '<span style="color:#84DD86">+' + dic.amount + '元</span>';

                $("#chartListInfo").append(
                    '<div class="cell">' +
                    '<div class="titleInfo">' + dic.bankAccountNumber + '</div>' +
                    '<div class="info1" style="padding-top:10px">对方户名: ' + dic.oppositeAccountName + '</div>' +
                    '<div class="info2">用途: ' + dic.purpose + '</div>' +
                    '<div class="cellLine"></div>' +
                    '<div class="bottomInfo"><span class="leftInfo">' + moneyWay + '</span><span class="rightInfo">' + dic.tradeTime + '</span></div>' +
                    '<div class="cellPadding" style="width: 100%;height: 5px;background: #F1F3F5"></div>' +
                    '</div>');
            }
        } else {
            $(document.body).destroyInfinite();
            $(".weui-loadmore").css('display', 'none');
            $("#chartListInfo").append('<div class="weui-loadmore weui-loadmore_line">\n' +
                '<span class="weui-loadmore__tips">暂无更多数据</span>\n' +
                '</div>\n')
            return;
        }

    }
</script>


</body>
</html>

