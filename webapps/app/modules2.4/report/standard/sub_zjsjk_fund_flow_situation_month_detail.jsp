<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style>
        #tagnav {
            position: fixed;
            top: 0;
            z-index: 999;
            background: #649CFF;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .weui-navigator-list li {
            line-height: 40px;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a {
            color: #93FFDB;
            font-weight: bold;
            border-bottom: 1px solid #93FFDB;
        }

        .weui-navigator-list li.weui-state-disabled a {
            color: rgba(255, 255, 255, 0.2);
            border-bottom: 1px solid #649CFF;
        }

        .weui-navigator-list li a {
            color: #ffffff;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a:after {
            background-color: transparent;
        }

        #reportTitle {
            padding-top: 43px;
            background: #649CFF;
            color: #fff;
        }

        #center {
            width: 100%;
            text-align: center;
        }

        #center .tip {
            display: block;
            padding-top: 10px;
            font-size: 12px;
        }

        #center .amount {
            display: block;
            padding-bottom: 10px;
            font-size: 24px;
            line-height: 24px;
        }

        #left, #right {
            display: inline-block;
            width: 48%;
            text-align: center;
        }

        .leftTip, .rightTip {
            display: inline-block;
            padding-top: 12px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            position: relative;
        }

        .leftTip:before, .rightTip:before {
            content: ' ';
            width: 8px;
            height: 8px;
            display: inline-block;
            border: 1px solid #ffffff;
            border-radius: 6px;
            position: absolute;
            top: 15px;
            left: -15px;
        }

        .leftTip:before {
            background: #f95a50;
        }

        .rightTip:before {
            background: #12b298;
        }

        .leftAmount, .rightAmount {
            padding-bottom: 10px;
            display: block;
            font-size: 18px;
            font-weight: 500;
        }

        .line {
            display: inline-block;
            width: 1%;
            max-width: 1px;
            background: #fff;
            height: 30px;
        }

        .titleInfo {
            padding-left: 20px;
            font-size: 16px;
            color: #0d0d0d;
            font-weight: bold;
            display: block;
            margin-bottom: 10px;
            margin-top: 10px;
        }

        .titleInfoArrow:after {
            content: " ";
            display: inline-block;
            height: 7px;
            width: 7px;
            border-width: 2px 2px 0 0;
            border-color: #c8c8cd;
            border-style: solid;
            transform: matrix(.71, .71, -.71, .71, 0, 0);
            webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);
            top: -2px;
            position: absolute;
            top: 50%;
            margin-top: -4px;
            right: 22px;
        }

        .amountAndTip {
            display: inline-block;
            width: calc(48% - 20px);
            padding-left: 20px;
        }

        .amountAndTip .amount {
            display: block;
            color: #FF7979;
            font-size: 16px;
            font-weight: bold;
        }

        .amountAndTip .tip {
            font-size: 10px;
            color: #999999;
            display: block;
        }

        #noMsg {
            position: relative;
            display: none;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }
    </style>
</head>
<body>
<div id="chartListInfo"></div>
<div id="noMsg">
    <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
    <span>暂无数据</span>
</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/iscroll-lite.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>

<script>
    $(function () {
        let currCode = sessionStorage.getItem('currCode');
        let type = sessionStorage.getItem('type');
        document.title = (currCode === '0' ? '折本币' : currCode) + '-' + (type === '2' ? "收入" : "支出");

        requestData(currCode, type);
    });

    function requestData(currCode, type) {
        showNoMsg(false)
        var param = {'currCode': currCode, 'type': type, 'tag': 'ats'};
        $.ajax({
            cache: true,
            type: 'POST',
            url: "${ctx}/zjsjk/getFundFlowSituationMonthDetail.do",
            data: param,
            async: true,
            dataType: 'json',
            beforeSend: function () {
                $.showLoading();
            },
            error: function () {
                $.hideLoading();
                $.toast('网络服务情况异常，请检查', "forbidden");
                showNoMsg(true);
            },
            success: function (data) {
                $.hideLoading();
                if (data.successful === true || "true" === data.successful) {
                    var dataArray = data.result.data;
                    if (dataArray.length === 3) {
                        layoutListInfo(dataArray[2].reportData);
                    } else {
                        // 暂无数据
                        showNoMsg(true);
                    }
                } else {
                    $.toast('网络服务情况异常，请检查', "forbidden");
                    showNoMsg(true);
                }
            }
        });
    }

    function showNoMsg(show) {
        if (show) {
            $("#reportTitle").html('');
            $("#chartSection").html('');
            $("#chartListInfo").html('');
            $("#noMsg").css('display', 'block');
        } else {
            $("#noMsg").css('display', 'none');
        }
    }


    function layoutListInfo(reportDataThree) {
        $('#chartListInfo').html('');
        for (let i = 0; i < reportDataThree.length; i++) {
            let dic = reportDataThree[i];
            let moneyWay = dic.moneyWay;
            let amountColor;
            let amountTag = '';
            let bankName = (dic.bankName !== '' && dic.bankName != null) ? '-' + dic.bankName : '';
            if (moneyWay === '1') {
                //支出
                amountColor = '#12b298'
                amountTag = '-';
            } else {
                amountColor = '#f95a50'
            }
            $("#chartListInfo").append(
                '<div class="cell" style="position: relative;" data-item = ' + dic.itemId + '>' +
                '<div class="titleInfo">' + dic.account + bankName + '</div>' +
                '<div class="amountAndTip"><span class="tip">组织</span><span class="">' + dic.orgName + '</span></div>' +
                '<div class="amountAndTip"><span class="tip">发生金额</span><span class="amount" style="color: ' + amountColor + '">' + amountTag + dic.amount + '</span></div>' +
                '<div class="amountAndTip"><span class="tip">发生日期</span><span class="">' + dic.date + '</span></div>' +
                '<div class="amountAndTip" style="width: 90%;overflow: visible; word-break: break-word"><span class="tip">用途</span><span class="">' + dic.purpose + '</span></div>' +
                '<div class="amountAndTip" style="width: 90%;overflow: visible; word-break: break-word"><span class="tip">备注</span><span class="">' + dic.memo + '</span></div>' +
                '<div style="width: 100%;height: 10px;background: #ffffff"></div>' +
                '<div class="cellPadding" style="width: 100%;height: 1px;background: #F1F3F5"></div>' +
                '</div>');

        }
    }

</script>


</body>
</html>

