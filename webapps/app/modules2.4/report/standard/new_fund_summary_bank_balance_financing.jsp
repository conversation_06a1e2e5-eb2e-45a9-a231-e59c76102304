<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>银行账户余额汇总表</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style>


        .weui-navigator-list li {
            line-height: 40px;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a {
            color: #EA5432;
            font-weight: bold;
            border-bottom: 1px solid #EA5432;
        }

        .weui-navigator-list li.weui-state-disabled a {
            color: rgba(255, 255, 255, 0.2);
            border-bottom: 1px solid #649CFF;
        }

        .weui-navigator-list li a {
            color: #ffffff;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a:after {
            background-color: transparent;
        }

        #reportTitle {
            padding-top: 10px;
            background: #649CFF;
            color: #fff;
        }

        #center {
            width: 100%;
            text-align: center;
        }

        #center .tip {
            display: block;
            padding-top: 30px;
            font-size: 12px;
        }

        #center .amount {
            display: block;
            padding-bottom: 10px;
            font-size: 24px;
            line-height: 24px;

        }

        .amount {
            table-layout: fixed;
            word-wrap: break-word;
            word-break: normal;
            overflow: hidden;
        }

        #left, #right {
            display: inline-block;
            width: 48%;
            text-align: center;
        }

        .leftTip, .rightTip {
            display: inline-block;
            padding-top: 38px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            position: relative;
        }


        .leftAmount, .rightAmount {
            padding-bottom: 30px;
            display: block;
            font-size: 18px;
            font-weight: 500;
        }

        .line {
            display: inline-block;
            width: 1%;
            max-width: 1px;
            background: #fff;
            height: 30px;
        }

        .titleInfo {
            padding-left: 20px;
            padding-top: 18px;
            font-size: 17px;
            color: #5A5858;
            font-weight: bold;
            display: block;
            word-wrap: break-word;
        }

        .titleInfoArrow:after {
            content: " ";
            display: inline-block;
            height: 7px;
            width: 7px;
            border-width: 2px 2px 0 0;
            border-color: #c8c8cd;
            border-style: solid;
            transform: matrix(.71, .71, -.71, .71, 0, 0);
            webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);
            top: -2px;
            position: absolute;
            top: 50%;
            margin-top: -4px;
            right: 22px;
        }

        .amountAndTip {
            display: inline-block;
            width: calc(48% - 20px);
            padding-left: 20px;
        }

        .amountAndTip .amount {
            display: block;
            color: #FF7979;
            font-size: 16px;
            font-weight: bold;
        }

        .amountAndTip .tip {
            font-size: 10px;
            color: #999999;
            display: block;
        }

        #noMsg {
            position: relative;
            display: none;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }

        #segment {
            padding: 20px 40px;
        }

        .weui-tab-nav .weui-nav-blue.bg-blue {
            border-color: #649cff;
            color: #fff;
            background: #649cff;
        }

        .weui-tab-nav .weui-nav-blue {
            display: block;
            -webkit-box-flex: 1;
            -webkit-flex: 1;
            flex: 1;
            width: 100%;
            height: 30px;
            padding: 0;
            font-size: 14px;
            line-height: 31px;
            text-align: center;
            border: 1px solid #649cff;
            border-width: 1px 1px 1px 0;
            color: #649cff;
            white-space: nowrap;
            background: #fdfdfd;
        }
    </style>
</head>
<body>
<%@include file='/common/bottom_bar.jsp' %>

<div id="reportTitle">
    <!-- <div id="center"><span class="tip">账户余额/万元</span><span class="amount">3828,222.72</span></div>
    <div id="left"><span class="leftTip">账户余额/万元</span><span class="leftAmount">10000</span></div>
    <div class="line"></div>
    <div id="right"><span class="rightTip">上缴金额/万元</span><span class="rightAmount">18888</span></div> -->
</div>
<div id="segment">
    <!-- <div class="weui-tab" style="height:44px;">
      <div class="weui-tab-nav">
        <a href="javascript:switchTypeCon('2');" class="weui-navbar__item weui-nav-blue"> 融资机构 </a>
        <a href="javascript:switchTypeCon('1');" class="weui-navbar__item weui-nav-blue"> 组织 </a>
      </div>
    </div> -->
</div>
<div id="chartSection"></div>
<div id="chartListInfo"></div>
<div id="noMsg">
    <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
    <span>暂无数据</span>
</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/iscroll-lite.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script src="${ctx}/js/echarts.min.js"></script>

<script>
    var type = "1";
    var typeIndex = 0;
    $(function () {

        requestData();
    });

    function requestData() {
        showNoMsg(false)
        var param = {'tag': 'ats', 'queryType': type};
        console.log('param=' + JSON.stringify(param));
        $.ajax({
            cache: true,
            type: 'POST',
            url: "${ctx}/api/zllcy/getFundSummaryBankBalance.do",
            data: param,
            async: true,
            dataType: 'json',
            beforeSend: function () {
                $.showLoading();
            },
            error: function () {
                $.hideLoading();
                $.toast('网络服务情况异常，请检查', "forbidden");
                showNoMsg(true);
            },
            success: function (data) {
                $.hideLoading();
                if (data.successful === true || "true" === data.successful) {
                    var dataArray = data.result.data;
                    if (dataArray.length === 3) {
                        //顶部数据
                        layoutTopTitle(dataArray[0].reportData);
                        $('#segment').html('');
                        $('#segment').append(
                            '<div class="weui-tab" style="height:44px;">' +
                            '<div class="weui-tab-nav">' +
                            '<a href="javascript:switchTypeCon(\'1\');" class="weui-navbar__item weui-nav-blue">按组织</a> ' +
                            '<a href="javascript:switchTypeCon(\'2\');" class="weui-navbar__item weui-nav-blue">按银行</a> ' +
                            '</div>' +
                            '</div>');
                        $('#segment .weui-tab').tab({defaultIndex: typeIndex, activeClass: "bg-blue"});
                        //chart数据
                        $('#chartSection').html('');
                        $('#chartSection').append(
                            '<div id="chartView" style="width: 100%;height: 268px;"></div>' +
                            '<div class="chartPadding" style="width: 100%;height: 10px;background: #F1F3F5"></div>');
                        myChart = echarts.init(document.getElementById('chartView'));
                        setChartValue(dataArray[1].reportData);
                        //列表数据
                        layoutListInfo(dataArray[2].reportData);
                    } else {
                        // 暂无数据
                        showNoMsg(true);
                    }
                } else {
                    $.toast('网络服务情况异常，请检查', "forbidden");
                    showNoMsg(true);
                }
            }
        });
    }

    function showNoMsg(show) {
        if (show) {
            $("#reportTitle").html('');
            $("#chartSection").html('');
            $("#chartListInfo").html('');
            $("#noMsg").css('display', 'block');
        } else {
            $("#noMsg").css('display', 'none');
        }
    }

    function layoutTopTitle(reportDataOne) {
        $("#reportTitle").html('');
        let dic = reportDataOne[0];
        $("#reportTitle").append(
            '<div id="center"><span class="tip">总余额</span><span class="amount" style="padding-bottom: 0;">' + dic.totalAmount + '</span></div>' +
            '<div id="left"><span class="leftTip">活期余额</span><span class="leftAmount">' + dic.balance + '</span></div>' +
            '<div class="line"></div>' +
            '<div id="right"><span class="rightTip">定期余额</span><span class="rightAmount">' + dic.financingBalance + '</span></div>');
    }

    function layoutListInfo(reportDataThree) {
        $('#chartListInfo').html('');
        for (let i = 0; i < reportDataThree.length; i++) {
            let dic = reportDataThree[i];
            let cell_item = $('<div class="cell titleInfoArrow" style="position: relative;" data-item="' + dic.itemId + '">' +
                '<span class="titleInfo">' + dic.itemName + '</span>' +
                '<div class="amountAndTip"><span class="amount">' + dic.totalAmount + '</span><span class="tip">银行存款余额</span></div>' +
                '<div class="amountAndTip"><span class="amount" style="color:#FFB06B">' + dic.rate + '</span><span class="tip">占比</span></div>' +
                '<div class="amountAndTip"><span class="amount">' + dic.balance + '</span><span class="tip">活期存款</span></div>' +
                '<div class="amountAndTip"><span class="amount">' + dic.financingBalance + '</span><span class="tip">定期存款</span></div>' +
                '<div style="width: 100%;height: 10px;background: #ffffff"></div>' +
                '<div class="cellPadding" style="width: 100%;height: 1px;background: #F1F3F5"></div>' +
                '</div>');
            var clickable;
            cell_item.on('touchstart', function (event) {
                clickable = true;
            });
            cell_item.on('touchmove', function (event) {
                clickable = false;
            });

            cell_item.on('click', function (event) {
                event.preventDefault();
                if (!clickable) {
                    return;
                }
                if ($(this).hasClass('titleInfoArrow')) {
                    let itemId = dic.itemId;
                    let name = dic.itemName;
                    sessionStorage.setItem('itemName', name);
                    sessionStorage.setItem('itemId', itemId);
                    if (type === '2') {
                        location.href = "sub_fund_bank_balance_investment_varieties.jsp";
                    }
                    if (type === '1') {
                        location.href = "sub_new_fund_summary_bank_balance_financing.jsp";

                    }

                }
            });
            $("#chartListInfo").append(cell_item);
        }


    }

    function switchTypeCon(typeCon) {
        type = typeCon;
        if (typeCon === '1') {
            typeIndex = 0
        }

        if (typeCon === '2') {
            typeIndex = 1
        }


        requestData();
    }


    function setChartValue(data) {
        var srcData = [];
        for (var item of data) {
            var itemData = {};
            itemData.name = item.itemName;
            itemData.value = item.totalAmount;
            itemData.itemId = item.itemId;
            srcData.push(itemData);
        }
        var option = dataCollation(srcData);
        myChart.setOption(option);
    }


    function dataCollation(srcData) {
        option = {
            color: ['#4EABF5', '#35C9DD', '#70BF73', '#D7E461', '#FFCD3A', '#FF794D', '#EE4C82', '#8562C5'],
            tooltip: {
                trigger: 'item',
                formatter: "{b}: {c}亿元",
                position:['10%','50%'],
            },
            legend: {
                orient: 'horizontal',
                y: 'bottom',
                left: 'center',
                itemWidth: 10,
                itemHeight: 10,
                align: 'left',
            },
            grid: {
                top: '20', //距上边距
                left: '25%', //距离左边距
                right: '25%', //距离右边距
                bottom: '20', //距离下边距
            },
            series: [{
                type: 'pie',
                radius: '50%',
                data: srcData,
                itemStyle: {
                    borderRadius: 0,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label: {
                    formatter: '{d}%',

                },
            }],
        }
        return option;
    }
</script>


</body>
</html>

