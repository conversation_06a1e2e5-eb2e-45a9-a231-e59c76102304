<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>融资到期情况</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style>
        #tagnav {
            position: fixed;
            top: 0;
            z-index: 999;
            background: #649CFF;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .weui-navigator-list li {
            line-height: 40px;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a {
            color: #93FFDB;
            font-weight: bold;
            border-bottom: 1px solid #93FFDB;
        }

        .weui-navigator-list li.weui-state-disabled a {
            color: rgba(255, 255, 255, 0.2);
            border-bottom: 1px solid #649CFF;
        }

        .weui-navigator-list li a {
            color: #ffffff;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a:after {
            background-color: transparent;
        }

        #reportTitle {
            padding-top: 20px;
            padding-bottom: 20px;
            background: #649CFF;
            color: #fff;
        }

        #center {
            width: 100%;
            text-align: center;
        }

        #center .tip {
            display: block;
            padding-top: 30px;
            font-size: 12px;
        }

        #center .amount {
            display: block;
            padding-bottom: 10px;
            font-size: 24px;
            line-height: 24px;
        }

        .legend {
            margin-top: 20px;
            font-size: 12px;
            text-align: center;
        }

        .legend span {
            position: relative;
            display: inline-block;
            padding-left: 20px;
            padding-right: 20px;
            padding-bottom: 15px;
        }

        .titleInfo {
            padding-left: 20px;
            padding-top: 18px;
            font-size: 17px;
            color: #5A5858;
            font-weight: bold;
            display: block;
        }


        .amountAndTip {
            display: inline-block;
            width: calc(48% - 20px);
            padding-left: 20px;
        }

        .amountAndTip .amount {
            display: block;
            color: #FF7979;
            font-size: 16px;
            font-weight: bold;
        }

        .amountAndTip .tip {
            font-size: 10px;
            color: #999999;
            display: block;
        }

        #noMsg {
            position: relative;
            display: none;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }
    </style>
</head>
<body>

<div id="reportTitle">
    <!-- <div class="legend">
      <span class="one">应付票据</span>
      <span class="two">进口信用证</span>
      <span class="three">贷款</span>
    </div> -->
</div>
<div id="chartSection"></div>
<div id="chartListInfo"></div>
<div id="noMsg">
    <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
    <span>暂无数据</span>
</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/iscroll-lite.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script src="${ctx}/js/echarts.min.js"></script>

<script>
    var totalAmounts = [];
    var chartInfoTitles = [];
    $(function () {
        let unitId = sessionStorage.getItem("TQLS_CURRENT_UNIT_ID");
        if (unitId === null) {
            ajaxRequest('/tqls/getUnitList.do', {}, function (data) {
                unitId = data.result.data[0].id;
                sessionStorage.setItem("TQLS_CURRENT_UNIT_ID", unitId);
                sessionStorage.setItem("TQLS_CURRENT_UNIT_NAME", data.result.data[0].name);
                requestData(unitId);
            });
        } else {
            requestData(unitId);
        }
        $(document.body).on('touchmove', function (e) {
            e.preventDefault();
        })
    });

    function requestData(orgId) {
        showNoMsg(false)

        var param = {};
        param.queryUnitId = orgId;
        $.ajax({
            cache: true,
            type: 'POST',
            url: "${ctx}/tqls/getFutureLoanRepayment.do",
            data: param,
            async: true,
            dataType: 'json',
            beforeSend: function () {
                $.showLoading();
            },
            error: function () {
                $.hideLoading();
                // $.toast('网络服务情况异常，请检查', "forbidden");
                showNoMsg(true);
            },
            success: function (data) {
                $.hideLoading();
                if (data.successful == true || "true" == data.successful) {
                    var dataArray = data.result.data;
                    if (dataArray.length == 3) {
                        //顶部数据
                        layoutTopTitle(dataArray[0].reportData);
                        //chart数据
                        $('#chartSection').html('');
                        $('#chartSection').append(
                            '<div id="chartView" style="width: 100%;height: 268px;"></div>' +
                            '<div class="chartPadding" style="width: 100%;height: 10px;background: #F1F3F5"></div>');
                        var myChart = echarts.init(document.getElementById('chartView'));
                        var option = dataCollation(dataArray[1].reportData);
                        myChart.setOption(option);
                        //列表数据
                        layoutListInfo(dataArray[2].reportData);
                    } else {
                        // 暂无数据
                        showNoMsg(true);
                    }
                } else {
                    // $.toast('网络服务情况异常，请检查', "forbidden");
                    showNoMsg(true);
                    layoutTopTitle(null);

                }
            }
        });
    }

    function showNoMsg(show) {
        if (show) {
            $("#reportTitle").html('');
            $("#chartSection").html('');
            $("#chartListInfo").html('');
            $("#noMsg").css('display', 'block');
        } else {
            $("#noMsg").css('display', 'none');
        }
    }

    function layoutTopTitle(reportDataOne) {

        let unitName = sessionStorage.getItem("TQLS_CURRENT_UNIT_NAME") === null ? "选择事业部" : sessionStorage.getItem("TQLS_CURRENT_UNIT_NAME");

        if(reportDataOne!==null) {
            $("#reportTitle").html('');
            let dic = reportDataOne[0];
            $("#reportTitle").append(
                '<div id="center"><span class="tip">待还款金额/万元</span><span class="amount">' + dic.totalAmount + '</span></div>' +
                '<div class="legend">' + unitName +
                '</div>');
        }else {
            $("#reportTitle").append(
                '<div id="center"><span class="tip">待还款金额/万元</span><span class="amount">0.00</span></div>' +
                '<div class="legend">' + unitName +
                '</div>');
        }


        $('.legend').on('click', function () {
            // location.href = 'tqls_unit_list.jsp';
            location.replace('tqls_unit_list.jsp');

        })
    }

    function layoutListInfo(reportDataThree) {
        $('#chartListInfo').html('');
        for (let i = 0; i < reportDataThree.length; i++) {
            let dic = reportDataThree[i];
            $("#chartListInfo").append(
                '<div class="cell" style="position: relative;">' +
                '<span class="titleInfo">' + dic.subOrgName + '</span>' +
                '<div class="amountAndTip"><span class="amount">' + dic.firstMonthAmount + '</span><span class="tip"><span style="font-weight: bold">' + dic.firstMonth + '</span>待还款金额/万元</span></div>' +
                '<div class="amountAndTip"><span class="amount">' + dic.secondMonthAmount + '</span><span class="tip"><span style="font-weight: bold">' + dic.secondMonth + '</span>待还款金额/万元</span></div>' +
                '<div class="amountAndTip"><span class="amount">' + dic.thirdMonthAmount + '</span><span class="tip"><span style="font-weight: bold">' + dic.thirdMonth + '</span>待还款金额/万元</span></div>' +
                '<div style="width: 100%;height: 10px;background: #ffffff"></div>' +
                '<div class="cellPadding" style="width: 100%;height: 5px;background: #F1F3F5"></div>' +
                '</div>');
        }
    }

    function dataCollation(reportDataTwo) {
        var xAxisData = [];
        var seriesData = [];
        var amountData = [];
        for (let i = 0; i < reportDataTwo.length; i++) {
            let dic = reportDataTwo[i];
            let payDate = dic.month;
            xAxisData.push(payDate);
            amountData.push(dic.monthAmount);
        }

        let barData = {
            name: "待还款金额",
            type: 'bar',
            stack: 'Itme',
            data: amountData,
            color: '#649CFF'
        }

        seriesData.push(barData);
        return {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
            },
            grid: {
                top: '8%',
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true,
            },
            xAxis: [
                {
                    type: 'category',
                    data: xAxisData,
                    axisLine: {
                        lineStyle: {
                            color: '#D6D6D6',
                        }
                    },
                    axisLabel: {
                        color: '#888888',
                        fontSize: 10,
                        interval: 0,
                    },
                    axisTick: {
                        show: false
                    },

                }
            ],
            yAxis: [
                {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#fff',
                        }
                    },
                    axisLabel: {
                        color: '#888888'
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(67,67,67,0.05)'
                        }
                    }
                }
            ],
            series: seriesData
        };
    }

</script>


</body>
</html>

