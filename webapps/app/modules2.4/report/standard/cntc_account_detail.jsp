<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<html>
<head>
    <title>账户明细查询</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0;"/>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker3.css"
          rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/select2/4.1.0-rc.0/css/select2.min.css" rel="stylesheet">

    <style>

        .form-control:disabled, .form-control[readonly] {
            background-color: #FFFFFF;
        }

        p {
            margin-bottom: 0;
            font-weight: bold;
        }

        .btn {
            background-color: #649CFF;
            border-color: #649CFF;
        }

    </style>
</head>
<body>


<div class="root-div" style="padding: 15px">

    <!--基础信息-->
    <div class="input-group mb-3">
        <select class="form-control" id="account-list" style="width: 100%">

        </select>
    </div>

    <div class="input-group mb-3">
        <span class="input-group-text">从</span>
        <input readOnly id="start-date" type='text' class="form-control form-control-sm"/>
        <span class="input-group-text">到</span>
        <input readOnly id="end-date" type='text' class="form-control form-control-sm"/>
    </div>
    <div class="mb-3 d-grid gap-2 d-md-flex justify-content-md-end">
        <button class="btn btn-primary" type="button" id="query">查询</button>
    </div>
    <div class="mb-3">

        <input id="query-list" type='text' class="form-control form-control-sm" placeholder="输入交易单信息查询"/>

    </div>

    <ul class="list-group list-group-flush">
        <%--<li class="list-group-item">
            <div class="d-flex w-100 justify-content-between mb-3">
                <h5 class="mb-1">中国人民银行</h5>
                <small>2022-09-10 10:01:01</small>
            </div>
            <div class="grid mb-1">
                <p>任磊</p>
                <small>对方户名</small>
            </div>
            <div class="grid mb-1">
                <p>62222123456</p>
                <small>账号</small>
            </div>
            <div class="row">
                <p class="col" style="margin-bottom: 0">10001</p>
                <p class="col" style="margin-bottom: 0">CNY</p>
                <p class="col" style="margin-bottom: 0">2333333</p>
            </div>
            <div class="row">
                <small class="col">交易金额</small>
                <small class="col">币种</small>
                <small class="col">当前余额</small>
            </div>
        </li>--%>
    </ul>


    <div style="margin-top: 20px" id="no-data-hint" class="text-center">
        <p style="color: #cccccc">暂无数据</p>
    </div>
</div><!-- root div -->


<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/select2/4.1.0-rc.0/js/select2.full.min.js"></script>
<script src="../../../js/new_common_util.js"></script>

<script>

    const amountColor = {
        "1": '#FF0000',
        "2": '#000000'
    }
    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const startDay = getFirstDayOfMonth(year, month)
    const endDay = getLastDayOfMonth(year, month);


    let listData = [];
    $('#no-data-hint').hide();
    $(document).ready(function () {

        // 查询账户列表
        bootAjaxRequest("/cntc/getAccountList.do", {}, function (data) {
            console.log(data);
            let account_list = $("#account-list");
            account_list.html('');
            let list = data.result.data;
            for (let item of list) {
                account_list.append('<option value="' + item.accountId + '">' + item.accountNumber + '</option>')
            }
        })

        let start_date = $('#start-date');
        start_date.datepicker({
            format: 'yyyy-mm-dd',
        });
        start_date.val(startDay);

        let end_date = $('#end-date');
        end_date.datepicker({
            language:'zh-CN',
            format: 'yyyy-mm-dd',
        });
        end_date.val(endDay);

        $('#account-list').select2();

        $('#query-list').bind('input propertychange', function () {
            set_data(listData, $(this).val())
        })

        $('#query').on('click', function () {
            $('#no-data-hint').hide();
            let accountId = $("#account-list").val();
            let startDate = $('#start-date').val();
            let endDate = $('#end-date').val();
            console.log(accountId + "," + startDate + "," + endDate)
            if (startDate.length === 0 || endDate.length === 0) {
                toast("开始日期或截止日期不能为空！")
                return;
            }

            get_data({
                account: accountId,
                startDate: startDate,
                endDate: endDate
            });

        })


    });

    let get_data = function (params) {
        bootAjaxRequest('/cntc/getAccountBalanceDetail.do', params, function (data) {

            console.log(data);
            listData = data.result.data;
            if (data.result.data.length === 0) {

                $('.list-group').html('');
                $('#no-data-hint').show();
            } else {
                set_data(data.result.data);
            }
        })
    }

    let set_data = function (list, filter) {
        $('.list-group').html('');
        for (let item of list) {

            let color = (item.moneyWay==='1')?'#FF0000':'#000000';
            let item_html = '<li class="list-group-item">' +
                '<div class="d-flex w-100 justify-content-between">' +
                '<p class="mb-1">' + item.bank + '</p>' +
                '<p>' + item.oppAccountName + '</p>' +
                '<small>' + item.date + '</small>' +
                '</div>' +
                '<div class="grid mb-1">' +
                '<p>' + item.accountNumber + '</p>' +
                '<small>账号</small>' +
                '</div>' +
                '<div class="row">' +
                '<p class="col" style="margin-bottom: 0; color: ' +color + '">' + item.amount + '</p>' +
                '<p class="col" style="margin-bottom: 0">' + item.currencyName + '</p>' +
                '<p class="col" style="margin-bottom: 0">' + item.currentBalance + '</p>' +
                '</div>' +
                '<div class="row">' +
                '<small class="col">交易金额</small>' +
                '<small class="col">币种</small>' +
                '<small class="col">当前余额</small>' +
                '</div>' +
                '</li>';
            if (filter) {
                if (item.oppAccountName.indexOf(filter) !== -1 ||
                    item.currentBalance.indexOf(filter) !== -1 ||
                    item.amount.indexOf(filter) !== -1 ||
                    item.currencyName.indexOf(filter) !== -1 ||
                    item.accountNumber.indexOf(filter) !== -1 ||
                    item.bank.indexOf(filter) !== -1 ||
                    item.date.indexOf(filter) !== -1||
                    item.moneyWay.indexOf(filter) !== -1) {
                    $('.list-group').append(item_html)
                }
            } else {

                $('.list-group').append(item_html)
            }
        }
    }

    // 查询月份开始结束
    //开始日期
    function getFirstDayOfMonth(year, month) {
        // return new Date(year, month-1, 1);
        let data = new Date(year, month - 1, 1);
        return (
            data.getFullYear() +
            "-" +
            (data.getMonth() + 1 > 9
                ? data.getMonth() + 1
                : "0" + (data.getMonth() + 1)) +
            "-" +
            (data.getDate() > 9 ? data.getDate() : "0" + data.getDate())
        );
    }

    //结束日期
    function getLastDayOfMonth(year, month) {
        let data = new Date(year, month, 0);
        return (
            data.getFullYear() +
            "-" +
            (data.getMonth() + 1 > 9
                ? data.getMonth() + 1
                : "0" + (data.getMonth() + 1)) +
            "-" +
            (data.getDate() > 9 ? data.getDate() : "0" + data.getDate())
        );
    }


</script>
</body>
</html>
