<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style>
        #tagnav {
            position: fixed;
            top: 0;
            z-index: 999;
            background: #649CFF;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .weui-navigator-list li {
            line-height: 40px;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a {
            color: #93FFDB;
            font-weight: bold;
            border-bottom: 1px solid #93FFDB;
        }

        .weui-navigator-list li.weui-state-disabled a {
            color: rgba(255, 255, 255, 0.2);
            border-bottom: 1px solid #649CFF;
        }

        .weui-navigator-list li a {
            color: #ffffff;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a:after {
            background-color: transparent;
        }

        #reportTitle {
            padding-top: 43px;
            padding-bottom: 40px;
            background: #649CFF;
            color: #fff;
        }

        #center {
            width: 100%;
            text-align: center;
        }

        #center .tip {
            display: block;
            padding-top: 10px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);

        }

        #center .amount {
            display: block;
            padding-bottom: 10px;
            font-size: 24px;
            line-height: 24px;

        }

        #left, #right {
            display: inline-block;
            width: 48%;
            text-align: center;
        }

        .leftTip, .rightTip {
            display: inline-block;
            padding-top: 38px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            position: relative;
        }

        .leftTip:before, .rightTip:before {
            content: ' ';
            width: 11px;
            height: 11px;
            display: inline-block;
            border: 1px solid #ffffff;
            border-radius: 6px;
            position: absolute;
            top: 40px;
            left: -15px;
        }

        .leftTip:before {
            background: #FCE172;
        }

        .rightTip:before {
            background: #7BE5C2;
        }

        .leftAmount, .rightAmount {
            padding-bottom: 30px;
            display: block;
            font-size: 18px;
            font-weight: 500;
        }

        .line {
            display: inline-block;
            width: 1%;
            max-width: 1px;
            background: #fff;
            height: 30px;
        }

        .field-text {
            color: #5A5858;
            max-width: 50%;
        }

        .titleInfo {
            padding-left: 10px;
            padding-top: 10px;
            font-size: 18px;
            color: #ff5053;
            font-weight: bold;
            display: block;
            word-break: break-all;
        }

        .titleInfoArrow:after {
            content: " ";
            display: inline-block;
            height: 7px;
            width: 7px;
            border-width: 2px 2px 0 0;
            border-color: #c8c8cd;
            border-style: solid;
            transform: matrix(.71, .71, -.71, .71, 0, 0);
            webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);
            top: -2px;
            position: absolute;
            top: 50%;
            margin-top: -4px;
            right: 22px;
        }

        .amountAndTip {
            display: inline-block;
            width: calc(50% - 20px);
            padding-left: 10px;
        }

        .amountAndTip .amount {
            display: block;
            color: #FF7979;
            font-size: 16px;
            font-weight: bold;
        }

        .amountAndTip .tip {
            font-size: 10px;
            color: #999999;
            display: block;
        }

        #noMsg {
            position: relative;
            display: none;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }
    </style>
</head>
<body>
<div style="display: none" id="tagnav" class="weui-navigator weui-navigator-wrapper">
    <ul class="weui-navigator-list"></ul>
</div>
<div id="reportTitle">
</div>
<div id="chartSection"></div>
<div id="chartListInfo"></div>
<div id="noMsg">
    <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
    <span>暂无数据</span>
</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/iscroll-lite.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script src="${ctx}/js/echarts.min.js"></script>

<script>
    $(function () {
        let reportActiveCurrCode = sessionStorage.getItem('reportActiveCurrCode');
        let realCurrCode = sessionStorage.getItem('currCode');
        let itemId = sessionStorage.getItem('accountId');
        let itemName = sessionStorage.getItem('accountName');
        document.title = itemName;
        let tabIndex = 0;
        let currCodeArray = JSON.parse(reportActiveCurrCode);
        let tabs = $('.weui-navigator-list');
        for (let i = 0; i < currCodeArray.length; i++) {
            let tabItem = currCodeArray[i];
            let currName = tabItem.currName;
            let currCode = tabItem.currCode;
            if (currCode === realCurrCode) {
                tabIndex = i;
            }
            let tab;
            if (tabItem.hasData) {
                tab = $('<li data-currCode="' + currCode + '"><a>' + currName + '</a></li>');
            } else {
                tab = $('<li class= "weui-state-disabled"><a>' + currName + '</a></li>');
            }
            tabs.append(tab);
        }
        //使用这个是为了避免滑动时出现滚动条。
        TagNav('#tagnav', {
            type: 'scrollToNext',
            curClassName: 'weui-state',
            //不能使用weui-state-active，随便写一个，以免下面的重写失效。
            index: tabIndex
        });
        //重写切换事件和选中事件
        $("#tagnav li").eq(tabIndex).addClass('weui-state-active');
        $('#tagnav').on('click', 'li',
            function (event) {
                event.preventDefault();
                $(this).addClass('weui-state-active');
                if (!$(this).hasClass('weui-state-disabled')) {
                    $(this).siblings('li').removeClass('weui-state-active');
                    requestData($(this).attr('data-currCode'), itemId);
                }
            });
        requestData(realCurrCode, itemId);
    });

    function requestData(currCode, itemId) {
        console.log('切换到-currCode=' + currCode);
        showNoMsg(false)
        let param = {'currCode': currCode, 'accountId': itemId, 'tag': 'ats'};
        $.ajax({
            cache: true,
            type: 'POST',
            url: "${ctx}/jtwl/getBankAccountTradeDetail.do",
            data: param,
            async: true,
            dataType: 'json',
            beforeSend: function () {
                $.showLoading();
            },
            error: function () {
                $.hideLoading();
                $.toast('网络服务情况异常，请检查', "forbidden");
                showNoMsg(true);
            },
            success: function (data) {
                $.hideLoading();
                if (data.successful == true || "true" == data.successful) {
                    let dataArray = data.result.data;
                    if (dataArray.length == 3) {
                        //顶部数据
                        layoutTopTitle(dataArray[0].reportData);
                        //chart数据
                        $('#chartSection').html('');
                        $('#chartSection').append(
                            '<div id="chartView" style="width: 100%;height: 220px;"></div>' +
                            '<div class="chartPadding" style="width: 100%;height: 10px;background: #F1F3F5"></div>');
                        let myChart = echarts.init(document.getElementById('chartView'));
                        let option = dataCollation(dataArray[1].reportData);
                        myChart.setOption(option);
                        //列表数据
                        layoutListInfo(dataArray[2].reportData);
                    } else {
                        // 暂无数据
                        showNoMsg(true);
                    }
                } else {
                    $.toast('网络服务情况异常，请检查', "forbidden");
                    showNoMsg(true);
                }
            }
        });
    }

    function showNoMsg(show) {
        if (show) {
            $("#reportTitle").html('');
            $("#chartSection").html('');
            $("#chartListInfo").html('');
            $("#noMsg").css('display', 'block');
        } else {
            $("#noMsg").css('display', 'none');
        }
    }

    function layoutTopTitle(reportDataOne) {
        $("#reportTitle").html('');
        let dic = reportDataOne[0];
        $("#reportTitle").append(
            '<div id="center"><span class="tip">账户余额/' + dic.currCode + '-' + dic.currName + '</span>' +
            '<span class="amount" style="padding: 20;">' + dic.amount + '<span style="font-size: 10px">万元</span></span>' +
            '</div>');

    }

    function layoutListInfo(reportDataThree) {
        $('#chartListInfo').html('');
        for (let i = 0; i < reportDataThree.length; i++) {
            let dic = reportDataThree[i];
            let moneyWayName = dic.moneyWay === '1' ? '支出' : '收入'
            let amountColor = dic.moneyWay === '1' ? '#54D6AD' : '#FF7979'
            $("#chartListInfo").append(
                '<div class="cell" style="position: relative;">' +
                '<span class="titleInfo" style="color: ' + amountColor + '">' + dic.amount + '</span>' +
                '<div class="amountAndTip"><span class="">' + moneyWayName + '</span><span class="tip">交易方向</span></div>' +
                '<div class="amountAndTip"><span class="" style="font-size: 14px">' + dic.tradeTime + '</span><span class="tip">交易日期</span></div>' +
                '<div class="amountAndTip"><span class="">' + dic.oppAccountName + '</span><span class="tip">对方户名</span></div>' +
                '<div class="amountAndTip"><span class="">' + dic.oppAccountNumber + '</span><span class="tip">对方账号</span></div>' +
                '<div class="amountAndTip"><span class="">' + dic.currCode + '-' + dic.currName + '</span><span class="tip">币种</span></div>' +
                '<div style="width: 100%;height: 10px;background: #ffffff"></div>' +
                '<div class="cellPadding" style="width: 100%;height: 5px;background: #F1F3F5"></div>' +
                '</div>');

            let clickable;
            $(".cell").on('touchstart', function (event) {
                clickable = true;
            });
            $(".cell").on('touchmove', function (event) {
                clickable = false;
            });
            $(".cell").on('click', function (event) {
                event.preventDefault();
                if (!clickable) {
                    return;
                }
                if ($(this).hasClass('titleInfoArrow')) {
                    let itemId = $(this).attr('data-item');
                    let currCode = $('#tagnav .weui-state-active').attr('data-currcode');
                    let name = $(this).find('.titleInfo').text();
                    sessionStorage.setItem('itemName', name);
                    sessionStorage.setItem('itemId', itemId);
                    sessionStorage.setItem('currCode', currCode);

                    // 建投物流
                    location.href = "sub_jtwl_bank_account_trade_detail.jsp";

                }
            });
        }
    }

    function comdify(n) {
        let re = /\d{1,3}(?=(\d{3})+$)/g;
        let n1 = n.replace(/^(\d+)((\.\d+)?)$/, function (s, s1, s2) {
            return s1.replace(re, "$&,") + s2;
        });
        return n1;
    }

    function dataCollation(reportDataTwo) {
        let groupedData = {};
        reportDataTwo.forEach(item => {
            if (!groupedData[item.tradeDate]) {
                groupedData[item.tradeDate] = {
                    "income": 0,
                    "expense": 0
                };
            }
            if (item.moneyWay === "1") {
                groupedData[item.tradeDate].expense += parseFloat(item.amount);
            }
            if (item.moneyWay === "2") {
                groupedData[item.tradeDate].income += parseFloat(item.amount);
            }
        });
        // console.log("groupedData:" + JSON.stringify(groupedData))

        let nameData = [];
        let receiveData = [];
        let payData = [];
        for (let key in groupedData) {
            if (groupedData.hasOwnProperty(key)) {
                nameData.push(key);
                receiveData.push(groupedData[key].income)
                payData.push(groupedData[key].expense)
            }
        }

        let option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                show: true,
                left: 'right',
                itemWidth: 10,
                itemHeight: 10
            },
            color: ['#FFD392', '#84DD86'],
            grid: {
                top: '18%',
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true,
            },
            xAxis: [
                {
                    type: 'category',
                    data: nameData,
                    axisLine: {
                        lineStyle: {
                            color: '#D6D6D6',
                        }
                    },
                    axisLabel: {
                        color: '#888888',
                        fontSize: 10,
                        interval: 0,
                    },
                    axisTick: {
                        show: false
                    },

                }
            ],
            yAxis: [
                {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#fff',
                        }
                    },
                    axisLabel: {
                        color: '#888888'
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(67,67,67,0.05)'
                        }
                    }
                }
            ],
            series: [
                {
                    name: '收入',
                    data: receiveData,
                    type: 'bar',
                    color: '#FF7979'
                },
                {
                    name: '支出',
                    data: payData,
                    type: 'bar',
                    color: '#54D6AD'
                },
            ]
        };
        return option;
    }
</script>


</body>
</html>

