<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>克明面业收支统计表</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style>
        #reportTitle {
            background: #649CFF;
            color: #fff;
        }

        #center {
            width: 100%;
            text-align: center;
        }

        #center .tip {
            padding-top: 30px;
            font-size: 12px;
        }

        #center .amount {
            display: block;
            font-size: 24px;
            line-height: 24px;
        }

        #left, #right {
            display: inline-block;
            width: 48%;
            text-align: center;
        }

        #center .tip, .leftTip, .rightTip {
            display: inline-block;
            padding-top: 38px;
            font-size: 12px;
            position: relative;
        }

        #center .tip:before, .leftTip:before, .rightTip:before {
            content: ' ';
            width: 11px;
            height: 11px;
            display: inline-block;
            border: 1px solid #ffffff;
            border-radius: 6px;
            position: absolute;
            top: 40px;
            left: -15px;
        }

        #center .tip:before {
            background: #74E2AB;
        }

        .leftTip:before {
            background: #908CFF;
        }

        .rightTip:before {
            background: #FB8D8D;
        }

        .leftAmount, .rightAmount {
            padding-bottom: 30px;
            display: block;
            font-size: 18px;
            font-weight: 500;
        }

        .leftAmount.hideColor, .rightAmount.hideColor {
            color: #649CFF;
        }

        .line {
            display: inline-block;
            width: 1%;
            max-width: 1px;
            background: #fff;
            height: 30px;
        }

        /*一行两列单排*/
        .amountAndTip1 {
            display: block;
            padding: 18px 20px;
            line-height: 16px;
        }

        .amountAndTip1 .amount {
            display: inline-block;
            font-size: 14px;
            font-weight: 500;
            width: 60%;
        }

        .amountAndTip1 .tip {
            font-size: 16px;
            font-weight: 500;
            display: inline-block;
            width: 40%;
            text-align: right;
        }

        #noMsg {
            position: relative;
            display: none;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: -50px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 45px;
            color: #999999;
            font-size: 14px;
        }

        #segment {
            padding: 20px 40px;
            padding-bottom: 0px;
        }

        .weui-tab-nav .weui-nav-blue.bg-blue {
            border-color: #649cff;
            color: #fff;
            background: #649cff;
        }

        .weui-tab-nav .weui-nav-blue {
            display: block;
            -webkit-box-flex: 1;
            -webkit-flex: 1;
            flex: 1;
            width: 100%;
            height: 30px;
            padding: 0;
            font-size: 14px;
            line-height: 31px;
            text-align: center;
            border: 1px solid #649cff;
            border-width: 1px 1px 1px 0;
            color: #649cff;
            white-space: nowrap;
            background: #fdfdfd;
        }

        #datepicker {
            text-align: center;
        }

        #datepicker .dateText {
            font-weight: 500;
            font-size: 15px;
            color: #649CFF;
            vertical-align: middle;
            padding-right: 5px;
        }

        #datepicker img {
            width: 14px;
            height: 14px;
            vertical-align: middle;
        }

        .weui-popup__overlay, .weui-popup__container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            bottom: unset;
        }

        .weui-popup__modal {
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
            background: white;
            top: 0;
            z-index: 1001;
            bottom: unset;
        }

        .weui-picker-modal.picker-modal-inline {
            background: #ffffff;
        }

        .weui-picker-modal .picker-center-highlight {
            background: #EDF2FB;
            width: 80%;
            left: 10%;
            z-index: -1;
        }

        .weui-picker-modal .picker-center-highlight:before, .weui-picker-modal .picker-center-highlight:after {
            background: #ffffff;
        }

        #cancel, #confirm {
            display: inline-block;
            margin-right: 5%;
            border: 1px solid #649cff;
            border-radius: 5px;
            width: 35%;
            height: 40px;
            line-height: 40px;
        }

        #cancel {
            color: #649cff;
        }

        #confirm {
            background: #649cff;
            margin-right: 0;
            margin-left: 5%;
        }

        /*cityPicker样式*/
        .city-picker .picker-items-col {
            -webkit-box-flex: none;
            -webkit-flex: none;
            -ms-flex: none;
            flex: none;
            max-width: 15rem;
        }

        .weui-picker-modal .picker-items {
            font-size: 0.7rem;
        }

        .city-picker .picker-items-col.col-province {
            width: 20% !important;
        }

        .city-picker .picker-items-col.col-city {
            width: 60% !important;
        }

    </style>
</head>
<body>

<div id="reportTitle">
    <!-- <div id="center"><span class="tip">账户余额/万元</span><span class="amount">3828,222.72</span></div> -->
    <!-- <div id="left"><span class="leftTip">账户余额/万元</span><span class="leftAmount">10000</span></div>
    <div class="line"></div>
    <div id="right"><span class="rightTip">上缴金额/万元</span><span class="rightAmount">18888</span></div> -->
</div>
<div id="segment">
    <!-- <div class="weui-tab" style="height:44px;">
      <div class="weui-tab-nav">
        <a href="javascript:switchTypeCon('1');" class="weui-navbar__item weui-nav-blue"> 按周 </a>
        <a href="javascript:switchTypeCon('2');" class="weui-navbar__item weui-nav-blue"> 按月 </a>
        <a href="javascript:switchTypeCon('3');" class="weui-navbar__item weui-nav-blue"> 按季 </a>
      </div>
    </div> -->
</div>
<div id="datepicker">
    <!--  <span class="dateText open-popup" data-target="#datePickerPannel" ></span>
     <img src="${ctx}/img/report/<EMAIL>" alt="编辑" class="open-popup" data-target="#datePickerPannel"> -->
</div>
<div id="chartSection"></div>
<div id="chartListInfo"></div>

<div id="datePickerPannel" class="weui-popup__container popup-bottom">
    <div class="weui-popup__overlay"></div>
    <div class="weui-popup__modal">
        <div class="weui-tab"
             style="height:44px;padding-top: 20px;padding-bottom: 0px;padding-left: 10%;padding-right: 10%;">
            <div class="weui-tab-nav">
                <a href="javascript:switchTypeOnPickerView('1');" class="weui-navbar__item weui-nav-blue"> 按周 </a>
                <a href="javascript:switchTypeOnPickerView('2');" class="weui-navbar__item weui-nav-blue"> 按月 </a>
                <a href="javascript:switchTypeOnPickerView('3');" class="weui-navbar__item weui-nav-blue"> 按季 </a>
            </div>
        </div>
        <div id="picker-container"></div>
        <input class="weui-input" id="inline" type="text" style="display: none;">
        <div style="padding: 20px;text-align: center;color: #ffffff;"><span id="cancel">取消</span><span
                id="confirm">确定</span></div>
    </div>
</div>

<div id="noMsg">
    <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
    <span>暂无数据</span>
</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/iscroll-lite.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script src="${ctx}/js/echarts.min.js"></script>
<script src="${ctx}/js/picker.multicols.js"></script>


<script>
    var gType = "1";
    var gTypeIndex = 0;
    var gDateTextTip = "";
    var gPickerSelectedValue = [];
    var gPickerSelectedName = [];
    //年的数据，默认5年
    var gYears = [];
    //周配置数据对应年份的数组
    var gWeekArrayDic = {};
    var gWeekArray = [];
    var gWeekIdentifyArray = [];
    //选中某周的id
    var gWeekId = '';
    //当前年
    var gCurrentYear = '';
    $(function () {
        //处理时间
        var myDate = new Date();
        var year = myDate.getFullYear(); //获取完整的年份(4位,1970-????)
        gCurrentYear = year;
        //当前年份往前，总共5年
        let several = 5;
        year = year - several;
        for (var i = several; i > 0; i--) {
            yearStr = year + i;
            gYears.push(yearStr + "年");
            let dic = {'code': yearStr, 'name': yearStr + '年', sub: [{'code': 'noconfig', 'name': '暂无周配置'}]};
            gWeekArray.push(dic);
            gWeekIdentifyArray.push(yearStr);
            requestWeekData(yearStr);
        }
        gDateTextTip = gCurrentYear + "年";
        $('.dateText').text(gDateTextTip.replace("&", ""));
        //打开时间选择器的modal
        $(document).on("open", ".weui-popup__modal", function () {
            console.log('点击模态');
            $('#datePickerPannel .weui-tab').tab({defaultIndex: gTypeIndex, activeClass: "bg-blue"});

            if ($("#picker-container").html() !== "") {
                $("#inline").picker("destroy");
                $("#picker-container").html('');
            }
            if (gType === "1") {
                let values = gDateTextTip;
                if (gDateTextTip.indexOf('&') < 0 || gDateTextTip.split('&')[1].length == 0) {
                    let subs = gWeekArray[gWeekIdentifyArray.indexOf(gCurrentYear)].sub;
                    values = gCurrentYear + '年' + '&' + subs[0].name;
                    gWeekId = subs[0].code;
                    gPickerSelectedValue = [gCurrentYear, gWeekId];
                }
                $("#inline").val(values);
                gPickerSelectedName = values.split("&");
                $("#inline").cityPicker({
                    showDistrict: false,
                    rawCitiesData: gWeekArray,
                    container: '#picker-container',
                    onChange: function (picker, values, displayValues) {
                        gPickerSelectedValue = values;
                        gPickerSelectedName = displayValues;
                    }
                });
                $(".picker-items-col").css("width", "40%")
            } else {
                $("#inline").picker({
                    container: '#picker-container',
                    cols: [
                        {
                            textAlign: 'center',
                            values: gYears
                        }
                    ],
                    value: [parseInt(gDateTextTip) + '年'],
                    onChange: function (picker, values, displayValues) {
                        gPickerSelectedValue = values;
                        gPickerSelectedName = displayValues;
                    }
                });
                $(".picker-items-col").css("width", "80%")
            }
            console.log("open popup");
        }).on("close", ".weui-popup__modal", function () {
            console.log("close popup");
        });
        //模态视图中的取消操作
        $("#cancel").on('click', function (event) {
            event.preventDefault();
            console.log('取消');
            $.closePopup();
        });
        //模态视图中的确认操作
        $("#confirm").on('click', function (event) {
            event.preventDefault();
            console.log('确定');
            let selectedYear = parseInt(gPickerSelectedValue[0]);
            gCurrentYear = selectedYear;
            if (gType === '1') {
                gWeekId = gPickerSelectedValue[1];
                gDateTextTip = gPickerSelectedName.join('&');
            } else {
                gDateTextTip = gPickerSelectedName[0];
            }
            $('.dateText').text(gDateTextTip.replace("&", ""));
            //确认后查询中的segment联动界面中的segment。
            switchTypeCon(gType, false);
            requestData();
            $.closePopup();
        });
        requestData();
    });

    //请求周配置信息
    function requestWeekData(year) {
        $.ajax({
            cache: true,
            type: 'POST',
            url: "<%=basePath%>/kmmy/getWorkWeek.do",
            data: {'year': year},
            async: true,
            dataType: 'json',
            beforeSend: function () {
                // $.showLoading();
            },
            error: function () {
                // $.hideLoading();
                // $.toast('网络服务情况异常，请检查', "forbidden");
                // showNoMsg(true);
            },
            success: function (data) {
                // $.hideLoading();
                if (data && (data.successful == true || "true" == data.successful)) {
                    var dataArray = data.result.data;
                    let weekInfos = [];
                    for (const dic of dataArray) {
                        let mDic = {};
                        let name = dic['name'];
                        let nameShow = name.split('年')[1];
                        mDic['name'] = nameShow;
                        mDic['code'] = dic['weekId'];
                        weekInfos.push(mDic);
                    }
                    if (dataArray.length == 0) {
                        weekInfos = [{'code': 'noconfig', 'name': '暂无周配置'}]
                    }
                    console.log('weekIds', weekInfos);
                    let weekObj = gWeekArray[gWeekIdentifyArray.indexOf(year)];
                    weekObj.sub = weekInfos;
                } else {
                    // $.toast('网络服务情况异常，请检查', "forbidden");
                    // showNoMsg(true);
                }
            }
        });
    }

    //请求报表数据并展示
    function requestData() {
        showNoMsg(false);
        var param = {'tag': 'ats', 'queryDateType': gType, "year": gCurrentYear};
        if (gType === '1' && gWeekId.length > 0) {
            if (gWeekId === 'noconfig') {
                gType = '2';
                param = {'tag': 'ats', 'queryDateType': gType, "year": gCurrentYear};
                switchTypeCon(gType, false);

            } else {
                param = {'tag': 'ats', 'queryDateType': gType, "year": gCurrentYear, 'weekId': gWeekId};
            }
        }
        console.log('param=' + JSON.stringify(param));
        $.ajax({
            cache: true,
            type: 'POST',
            url: "<%=basePath%>/kmmy/getPaymentsStatistics.do",
            data: param,
            async: true,
            dataType: 'json',
            beforeSend: function () {
                $.showLoading();
            },
            error: function () {
                $.hideLoading();
                $.toast('网络服务情况异常，请检查', "forbidden");
                showNoMsg(true);
            },
            success: function (data) {
                $.hideLoading();
                if (data.successful == true || "true" == data.successful) {
                    var dataArray = data.result.data;
                    if (dataArray.length == 3) {
                        //判断当前数据是否为空。
                        let paymentReportData = dataArray[0].paymentReportData;
                        let receiveReportData = dataArray[0].receiveReportData;
                        if (paymentReportData.concat(receiveReportData).length == 0) {
                            showNoMsg(true);
                            return;
                        }
                        //顶部数据
                        layoutTopTitle(true, dataArray[0]);
                        $('#segment').html('');
                        $('#segment').append(
                            '<div class="weui-tab" style="height:44px;">' +
                            '<div class="weui-tab-nav">' +
                            '<a href="javascript:switchTypeCon(\'1\',true);" class="weui-navbar__item weui-nav-blue">按周</a> ' +
                            '<a href="javascript:switchTypeCon(\'2\',true);" class="weui-navbar__item weui-nav-blue">按月</a> ' +
                            '<a href="javascript:switchTypeCon(\'3\',true);" class="weui-navbar__item weui-nav-blue">按季</a> ' +
                            '</div>' +
                            '</div>');
                        $('#segment .weui-tab').tab({defaultIndex: gTypeIndex, activeClass: "bg-blue"});
                        //更换日期
                        $("#datepicker").html('');
                        $("#datepicker").append(
                            '<span class="dateText openModal"></span>' +
                            '<img src="${ctx}/img/report/<EMAIL>" class="openModal" alt="编辑">');
                        let payDic = dataArray[0].paymentReportData[0];
                        let dateLabelString = payDic ? payDic.name : '';
                        if (dateLabelString.length == 0) {
                            if (gType == '1') {
                                $('.dateText').text(gDateTextTip.split('&')[0]);
                            } else {
                                $('.dateText').text(gDateTextTip.substr(0, 5));
                            }
                        } else {
                            gDateTextTip = dateLabelString.replace("年", "年&");
                            gDateTextTip = gDateTextTip.replace("周", "周 ");
                            $('.dateText').text(dateLabelString);
                        }

                        $('.openModal').on('click', function (event) {
                            event.preventDefault();
                            $("#datePickerPannel").popup();
                        });
                        //chart数据
                        $('#chartSection').html('');
                        $('#chartSection').append(
                            '<div id="chartView" style="width: 100%;height: 268px;"></div>');
                        var myChart = echarts.init(document.getElementById('chartView'));
                        // 绘制图表，没有修改图表内容。
                        var option = dataCollation(dataArray[1]);
                        myChart.setOption(option);
                        // 列表数据
                        if (gType === '1') {
                            layoutListInfo(dataArray[2]);
                        } else {
                            $('#chartListInfo').html('');
                        }
                    } else {
                        // 暂无数据
                        showNoMsg(true);
                    }
                } else {
                    $.toast('网络服务情况异常，请检查', "forbidden");
                    showNoMsg(true);
                }
            }
        });
    }

    function showNoMsg(show) {
        if (show) {
            layoutTopTitle(false);
            $('#segment').html('');
            $('#segment').append(
                '<div class="weui-tab" style="height:44px;">' +
                '<div class="weui-tab-nav">' +
                '<a href="javascript:switchTypeCon(\'1\',true);" class="weui-navbar__item weui-nav-blue">按周</a> ' +
                '<a href="javascript:switchTypeCon(\'2\',true);" class="weui-navbar__item weui-nav-blue">按月</a> ' +
                '<a href="javascript:switchTypeCon(\'3\',true);" class="weui-navbar__item weui-nav-blue">按季</a> ' +
                '</div>' +
                '</div>');
            $('#segment .weui-tab').tab({defaultIndex: gTypeIndex, activeClass: "bg-blue"});
            //更换日期
            $("#datepicker").html('');
            $("#datepicker").append(
                '<span class="dateText openModal"></span>' +
                '<img src="${ctx}/img/report/<EMAIL>" class="openModal" alt="编辑">');
            if (gType == '1') {
                $('.dateText').text(gDateTextTip.split('&')[0]);
            } else {
                $('.dateText').text(gDateTextTip.substr(0, 5));
            }
            $('.openModal').on('click', function (event) {
                event.preventDefault();
                $("#datePickerPannel").popup();
            });
            $("#chartSection").html('');
            $("#chartListInfo").html('');
            $("#noMsg").css('display', 'block');
        } else {
            $("#noMsg").css('display', 'none');
        }
    }

    function layoutTopTitle(hasData, reportDataOne) {
        let payAmount = '0';
        let recAmount = '0';
        let netInFlow = '';
        let spanColor = '';
        $("#reportTitle").html('');
        if (hasData) {
            let payDic = reportDataOne['paymentReportData'][0];
            let recDic = reportDataOne['receiveReportData'][0];
            let netInFlowDic = reportDataOne['netInflowReportData'][0];
            payAmount = payDic ? payDic.amount : '0';
            recAmount = recDic ? recDic.amount : '0';
            netInFlow = netInFlowDic ? netInFlowDic.amount : '';
        } else {
            spanColor = 'hideColor'
        }

        $("#reportTitle").append(
            '<div id="center"><span class="tip">合计净流入/万元</span><span class="amount">' + netInFlow + '</span></div>' +
            '<div id="left"><span class="leftTip">合计收入/万元</span><span class="leftAmount ' + spanColor + ' ">' + recAmount + '</span></div>' +
            '<div class="line"></div>' +
            '<div id="right"><span class="rightTip">合计支出/万元</span><span class="rightAmount ' + spanColor + '">' + payAmount + '</span></div>');
    }

    //list的样式重新调整
    function layoutListInfo(reportDataThree) {
        $('#chartListInfo').html('');
        let payList = reportDataThree['paymentReportData'];
        let recList = reportDataThree['receiveReportData'];
        let inList = reportDataThree['netInflowReportData'];
        let newList = recList.concat(payList);
        newList = newList.concat(inList);
        let styleString = '';
        $("#chartListInfo").append('<div class="chartPadding" style="width: 100%;height: 10px;background: #F1F3F5"></div>');
        for (let dic of newList) {
            let name = dic.name;
            if (name.indexOf('收入合计') > 0 || name.indexOf('支出合计') > 0 || name.indexOf('净流入') > 0) {
                let colorStr = '#EDF2FB';
                if (name.indexOf('净流入') > 0) {
                    colorStr = '#c8fbe2'
                }
                styleString = 'style="background: ' + colorStr + '"';
            } else {
                styleString = '';
            }
            $("#chartListInfo").append(
                '<div class="cell" style="position: relative;">' +
                '<div class="amountAndTip1" ' + styleString + ' ><span class="amount" style="color:#5A5858">' + name + '</span><span class="tip" style="color:#FF7979">' + dic.amount + '</span></div>' +
                '<div class="cellPadding" style="width: 100%;height: 1px;background: #F1F3F5"></div>' +
                '</div>');
        }
    }

    //切换segments
    function switchTypeCon(typeCon, isHandle) {
        gType = typeCon;
        gTypeIndex = parseInt(typeCon) - 1;
        if (isHandle === true) {
            gDateTextTip = gCurrentYear + "年";
            requestData();
        }
        $('.dateText').text(gDateTextTip.replace("&", ""));
    }

    //加载模态中的数据
    function layoutPopupPickerView() {
        $('#datePickerPannel .weui-tab').tab({defaultIndex: gTypeIndex, activeClass: "bg-blue"});

        if ($("#picker-container").html() !== "") {
            $("#inline").picker("destroy");
            $("#picker-container").html('');
        }
        if (gType === "1") {
            let values = gDateTextTip;
            if (gDateTextTip.indexOf('&') < 0 || gDateTextTip.split('&')[1].length == 0) {
                let subs = gWeekArray[gWeekIdentifyArray.indexOf(gCurrentYear)].sub;
                values = gCurrentYear + '年' + '&' + subs[0].name;
                gWeekId = subs[0].code;
                gPickerSelectedValue = [gCurrentYear, gWeekId];
            }
            $("#inline").val(values);
            gPickerSelectedName = values.split("&");
            $("#inline").cityPicker({
                showDistrict: false,
                rawCitiesData: gWeekArray,
                container: '#picker-container',
                onChange: function (picker, values, displayValues) {
                    gPickerSelectedValue = values;
                    gPickerSelectedName = displayValues;
                }
            });
            $(".picker-items-col").css("width", "40%")
        } else {
            $("#inline").picker({
                container: '#picker-container',
                cols: [
                    {
                        textAlign: 'center',
                        values: gYears
                    }
                ],
                value: [parseInt(gDateTextTip) + '年'],
                onChange: function (picker, values, displayValues) {
                    console.log(picker, values, displayValues);
                    gPickerSelectedValue = values;
                    gPickerSelectedName = displayValues;
                }
            });
            $(".picker-items-col").css("width", "80%")
        }
    }

    //切换模态界面中的segments
    function switchTypeOnPickerView(typeCon) {
        console.log('切换=' + typeCon);
        gType = typeCon;
        gTypeIndex = parseInt(typeCon) - 1;
        layoutPopupPickerView();
    }

    //数据处理，绘制图形。

    function dataCollation(reportDataTwo) {
        var xAxisData = [];
        var seriesData = [];

        let yAxisCustom = [];
        let weekDataOut = [];
        let weekDataIn = [];
        let weekDataPureIn = [];

        weekDataOut.push(reportDataTwo.paymentReportData[0].amount)
        weekDataOut.push(reportDataTwo.paymentReportData[0].tzAmount)
        weekDataOut.push(reportDataTwo.paymentReportData[0].rzAmount)
        weekDataOut.push(reportDataTwo.paymentReportData[0].jyAmount)

        weekDataIn.push(reportDataTwo.receiveReportData[0].amount)
        weekDataIn.push(reportDataTwo.receiveReportData[0].tzAmount)
        weekDataIn.push(reportDataTwo.receiveReportData[0].rzAmount)
        weekDataIn.push(reportDataTwo.receiveReportData[0].jyAmount)

        weekDataPureIn.push(reportDataTwo.netInflowReportData[0].amount)
        weekDataPureIn.push(reportDataTwo.netInflowReportData[0].tzAmount)
        weekDataPureIn.push(reportDataTwo.netInflowReportData[0].rzAmount)
        weekDataPureIn.push(reportDataTwo.netInflowReportData[0].jyAmount)


        if (gType === "1") {
            //按周，两个柱状
            let lineData1 = {
                data: [
                    {
                        value: reportDataTwo.receiveReportData[0].amount,
                        itemStyle: {
                            color: '#908CFF'
                        },
                    },
                    {
                        value: reportDataTwo.paymentReportData[0].amount,
                        itemStyle: {
                            color: '#FB8D8D'
                        }
                    },
                    {
                        value: reportDataTwo.netInflowReportData[0].amount,
                        itemStyle: {
                            color: '#74E2AB'
                        }
                    }
                ],
                type: 'bar'
            };
            xAxisData = ['收入', '支出', '净流入'];
            yAxisCustom = [{
                type: 'value',
                name: '金额/万元',
                axisLine: {
                    lineStyle: {
                        color: '#fff',
                    }
                },
                nameTextStyle: {
                    color: "#888888"
                },
                axisLabel: {
                    color: '#888888'
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: 'rgba(67,67,67,0.05)'
                    }
                },
            }]
            seriesData.push(lineData1);

        }
        if (gType === "2" || gType === "3") {
            var paymentReportData = reportDataTwo.paymentReportData;
            var receiveReportData = reportDataTwo.receiveReportData;
            var netInflowReportData = reportDataTwo.netInflowReportData;
            var payList = [];
            var recList = [];
            var netInflowList = [];
            var preDic = {'amount': 0};

            for (let i = 0; i < receiveReportData.length; i++) {
                let recDic = receiveReportData[i];
                let payDic = paymentReportData.length > i ? paymentReportData[i] : preDic;
                let netInFlowDic = netInflowReportData.length > i ? netInflowReportData[i] : preDic;
                let payDate = recDic.name;
                let len = receiveReportData.length > 4 ? 4 : 6;
                if (payDate.length > len) {
                    payDate = payDate.substring(0, len);
                }
                xAxisData.push(payDate);
                payList.push(payDic.amount);
                recList.push(recDic.amount);
                netInflowList.push(netInFlowDic.amount);
            }
            //按月或按年
            let lineData1 = {
                name: '收入',
                type: 'line',
                color: '#908CFF',
                data: recList
            }
            let lineData2 = {
                name: '支出',
                type: 'line',
                z: 2,
                color: '#FB8D8D',
                data: payList
            }
            let lineData3 = {
                name: '净流入',
                type: 'line',
                z: 3,
                color: '#74E2AB',
                data: netInflowList
            }
            yAxisCustom = [
                {
                    type: 'value',
                    name: '金额/万元',
                    axisLine: {
                        lineStyle: {
                            color: '#fff',
                        }
                    },
                    nameTextStyle: {
                        color: "#888888"
                    },
                    axisLabel: {
                        color: '#888888'
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(67,67,67,0.05)'
                        }
                    },
                }
            ]
            seriesData.push(lineData1);
            seriesData.push(lineData2);
            seriesData.push(lineData3);
        }
        var option;
        if (gType === '1') {
            option = {
                title: {
                    text: '',
                    subtext: ''
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['本周支出', '本周收入', '本周净收入']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    boundaryGap: [0, 0.01]
                },
                yAxis: {
                    type: 'category',
                    data: ['现金流', '投资性', '融资性', '经营性']
                },
                series: [
                    {
                        color: '#FB8D8D',
                        name: '本周支出',
                        type: 'bar',
                        data: weekDataOut
                    },
                    {
                        color: '#908CFF',
                        name: '本周收入',
                        type: 'bar',
                        data: weekDataIn
                    },
                    {
                        color: '#74E2AB',
                        name: '本周净收入',
                        type: 'bar',
                        data: weekDataPureIn
                    }
                ]
            };
        } else {
            option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                },
                grid: {
                    top: '15%',
                    left: '5%',
                    right: '5%',
                    bottom: '3%',
                    containLabel: true,
                },
                xAxis: [
                    {
                        type: 'category',
                        data: xAxisData,
                        axisLine: {
                            lineStyle: {
                                color: 'rgba(67,67,67,0.05)'
                            }
                        },
                        axisLabel: {
                            color: '#888888',
                            fontSize: 10,
                        },
                        axisTick: {
                            show: false
                        }
                    }
                ],
                yAxis: yAxisCustom,
                series: seriesData
            };
        }
        return option;
    }
</script>


</body>
</html>

