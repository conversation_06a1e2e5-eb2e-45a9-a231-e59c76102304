<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>付款结算量次</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style>
        #tagnav {
            position: fixed;
            top: 0;
            z-index: 999;
            background: #649CFF;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .weui-navigator-list li {
            line-height: 40px;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a {
            color: #93FFDB;
            font-weight: bold;
            border-bottom: 1px solid #93FFDB;
        }

        .weui-navigator-list li.weui-state-disabled a {
            color: rgba(255, 255, 255, 0.2);
            border-bottom: 1px solid #649CFF;
        }

        .weui-navigator-list li a {
            color: #ffffff;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a:after {
            background-color: transparent;
        }

        #reportTitle {
            padding-top: 43px;
            background: #649CFF;
            color: #fff;
        }

        #center {
            width: 100%;
            text-align: center;
        }

        #center .tip {
            display: block;
            padding-top: 30px;
            font-size: 12px;
        }

        #center .amount {
            display: block;
            padding-bottom: 30px;
            font-size: 24px;
            line-height: 24px;
        }

        #left, #right {
            display: inline-block;
            width: 48%;
            text-align: center;
        }

        .leftTip, .rightTip {
            display: inline-block;
            padding-top: 38px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            position: relative;
        }

        .leftAmount, .rightAmount {
            padding-bottom: 30px;
            display: block;
            font-size: 18px;
            font-weight: 500;
        }

        .line {
            display: inline-block;
            width: 1%;
            max-width: 1px;
            background: #fff;
            height: 30px;
        }

        .titleInfo {
            padding-left: 20px;
            padding-top: 18px;
            font-size: 14px;
            color: #5A5858;
            font-weight: bold;
            display: block;
        }

        .titleInfoArrow:after {
            content: " ";
            display: inline-block;
            height: 7px;
            width: 7px;
            border-width: 2px 2px 0 0;
            border-color: #c8c8cd;
            border-style: solid;
            transform: matrix(.71, .71, -.71, .71, 0, 0);
            webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);
            top: -2px;
            position: absolute;
            top: 50%;
            margin-top: -4px;
            right: 22px;
        }

        .amountAndTip {
            display: inline-block;
            width: calc(48% - 20px);
            padding-left: 20px;
        }

        .amountAndTip .amount {
            display: block;
            color: #FF7979;
            font-size: 16px;
            font-weight: bold;
        }

        .amountAndTip .tip {
            font-size: 10px;
            color: #999999;
            display: block;
        }

        #chartListInfo .leftChart {
            background: #ffffff;
            width: calc(50% - 15px);
            position: absolute;
            top: 390px;
            bottom: 10px;
            left: 10px;
            height: 267px;
        }

        #chartListInfo .rightChart {
            background: #ffffff;
            width: calc(50% - 15px);
            position: absolute;
            top: 390px;
            bottom: 10px;
            right: 10px;
            height: 267px;
        }

        .chartTitle {
            text-align: center;
            display: block;
            padding-top: 15px;
        }

        #lChartView, #rChartView {
            width: calc(100% - 20px);
            position: absolute;
            top: 45px;
            bottom: 70px;
            left: 10px;
        }

        .bottomTip {
            position: absolute;
            bottom: 10px;
            left: 10px;
            width: calc(100% - 20px);
        }

        #leftBottom, #rightBottom {
            display: inline-block;
            width: 48%;
            text-align: center;
        }

        .bottomTip .leftTip, .bottomTip .rightTip {
            display: inline-block;
            font-size: 12px;
            color: #999999;
            position: relative;
            padding-top: 0px;
        }

        .bottomTip .leftAmount, .bottomTip .rightAmount {
            display: block;
            font-size: 15px;
            font-weight: 500;
            padding-bottom: 0px;
            color: #5A5858;
        }

        #noMsg {
            position: relative;
            display: none;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }
    </style>
</head>
<body>
<div id="tagnav" class="weui-navigator weui-navigator-wrapper">
    <ul class="weui-navigator-list"></ul>
</div>
<div id="reportTitle">
    <!-- <div id="center"><span class="tip">账户余额/万元</span><span class="amount">3828,222.72</span></div>
    <div id="left"><span class="leftTip">账户余额/万元</span><span class="leftAmount">10000</span></div>
    <div class="line"></div>
    <div id="right"><span class="rightTip">上缴金额/万元</span><span class="rightAmount">18888</span></div> -->
</div>
<div id="chartSection"></div>
<div id="chartListInfo">
    <!-- <div class="leftChart">
        <span class="chartTitle">直联笔数占比</span>
        <div id="lChartView"></div>
        <div class="bottomTip">
            <div id="leftBottom"><span class="leftAmount">10000</span><span class="leftTip">直联</span></div>
            <div id="rightBottom"><span class="rightAmount">18888</span><span class="rightTip">非直联</span></div>
        </div>
    </div>
    <div class="rightChart">
        <span class="chartTitle">直联金额占比</span>
        <div id="rChartView"></div>
        <div class="bottomTip">
            <div id="leftBottom"><span class="leftAmount">10000</span><span class="leftTip">直联</span></div>
            <div id="rightBottom"><span class="rightAmount">18888</span><span class="rightTip">非直联</span></div>
        </div>
    </div> -->
</div>
<div id="noMsg">
    <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
    <span>暂无数据</span>
</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/iscroll-lite.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script src="${ctx}/js/echarts.min.js"></script>
<script>
    var dateType = sessionStorage.getItem('dateType');
    var date = sessionStorage.getItem('date');
    $(function () {
        var reportActiveCurrCode = sessionStorage.getItem('reportActiveCurrCode');
        var realCurrCode = sessionStorage.getItem('currCode');
        var navTitle = sessionStorage.getItem('navTitle');
        document.title = navTitle;
        var tabIndex = 0;
        var currCodeArray = JSON.parse(reportActiveCurrCode);
        let tabs = $('.weui-navigator-list');
        for (let i = 0; i < currCodeArray.length; i++) {
            let tabItem = currCodeArray[i];
            let currName = tabItem.currName;
            let currCode = tabItem.currCode;
            if (currCode === realCurrCode) {
                tabIndex = i;
            }
            let tab;
            if (tabItem.hasData) {
                tab = $('<li data-currCode="' + currCode + '"><a>' + currName + '</a></li>');
            } else {
                tab = $('<li class= "weui-state-disabled"><a>' + currName + '</a></li>');
            }
            tabs.append(tab);
        }
        //使用这个是为了避免滑动时出现滚动条。
        TagNav('#tagnav', {
            type: 'scrollToNext',
            curClassName: 'weui-state',
            //不能使用weui-state-active，随便写一个，以免下面的重写失效。
            index: tabIndex
        });
        //重写切换事件和选中事件
        $("#tagnav li").eq(tabIndex).addClass('weui-state-active');
        $('#tagnav').on('click', 'li',
            function (event) {
                event.preventDefault();
                $(this).addClass('weui-state-active');
                if (!$(this).hasClass('weui-state-disabled')) {
                    $(this).siblings('li').removeClass('weui-state-active');
                    requestData($(this).attr('data-currCode'));
                }
            });
        requestData(realCurrCode);
    });

    function requestData(currCode) {
        console.log('切换到-currCode=' + currCode);
        showNoMsg(false)
        var param = {'currCode': currCode, 'date': date, 'tag': 'ats', 'dateType': dateType};
        $.ajax({
            cache: true,
            type: 'POST',
            url: "<%=basePath%>/saasreport/getPaymentSettlementByOrg.do",
            data: param,
            async: true,
            dataType: 'json',
            beforeSend: function () {
                $.showLoading();
            },
            error: function () {
                $.hideLoading();
                $.toast('网络服务情况异常，请检查', "forbidden");
                showNoMsg(true);
            },
            success: function (data) {
                $.hideLoading();
                if (data.successful == true || "true" == data.successful) {
                    var dataArray = data.result.data;
                    if (dataArray.length == 3) {
                        //顶部数据
                        layoutTopTitle(dataArray[0].reportData);
                        //chart数据
                        $('#chartSection').html('');
                        $('#chartSection').append(
                            '<div id="chartView" style="width: 100%;height: 220px;background:#ffffff"></div>' +
                            '<div class="chartPadding" style="width: 100%;height: 10px;background: #F1F3F5"></div>');
                        var myChart = echarts.init(document.getElementById('chartView'));
                        var option = dataCollation(dataArray[1].reportData);
                        myChart.setOption(option);
                        //列表数据
                        layoutListInfo(dataArray[2].reportData);
                    } else {
                        // 暂无数据
                        showNoMsg(true);
                    }
                } else {
                    $.toast('网络服务情况异常，请检查', "forbidden");
                    showNoMsg(true);
                }
            }
        });
    }

    function showNoMsg(show) {
        if (show) {
            $("#reportTitle").html('');
            $("#chartSection").html('');
            $("#chartListInfo").html('');
            $("#noMsg").css('display', 'block');
            $("body").css('background', '#ffffff');
        } else {
            $("#noMsg").css('display', 'none');
            $("body").css('background', '#F1F3F5');
        }
    }

    function layoutTopTitle(reportDataOne) {
        $("#reportTitle").html('');
        let dic = reportDataOne[0];
        $("#reportTitle").append(
            '<div id="left"><span class="leftTip">付款总额/万元</span><span class="leftAmount">' + dic.totalAmount + '</span></div>' +
            '<div class="line"></div>' +
            '<div id="right"><span class="rightTip">付款笔数/笔</span><span class="rightAmount">' + dic.totalCount + '</span></div>')
    }

    function layoutListInfo(reportDataThree) {
        $('#chartListInfo').html('');
        let isDirectIndex = reportDataThree[0].isDirect === "1" ? 0 : 1;
        let unDirectIndex = isDirectIndex == 1 ? 0 : 1;
        let isDirectDic = reportDataThree[isDirectIndex];
        let unDirectDic = reportDataThree[unDirectIndex];

        $('#chartListInfo').append(
            '<div class="leftChart">' +
            '<span class="chartTitle">直联笔数占比</span>' +
            '<div id="lChartView"></div>' +
            '<div class="bottomTip">' +
            '<div id="leftBottom"><span class="leftAmount">' + isDirectDic.totalCount + '</span><span class="leftTip">直联</span></div>' +
            '<div id="rightBottom"><span class="rightAmount">' + unDirectDic.totalCount + '</span><span class="rightTip">非直联</span></div>' +
            '</div>' +
            '</div>' +
            '<div class="rightChart">' +
            '<span class="chartTitle">直联金额占比</span>' +
            '<div id="rChartView"></div>' +
            '<div class="bottomTip">' +
            '<div id="leftBottom"><span class="leftAmount">' + isDirectDic.totalAmount + '</span><span class="leftTip">直联</span></div>' +
            '<div id="rightBottom"><span class="rightAmount">' + unDirectDic.totalAmount + '</span><span class="rightTip">非直联</span></div>' +
            '</div>' +
            '</div>');
        var lChart = echarts.init(document.getElementById('lChartView'));
        var lOoption = dataCollationForLeft(isDirectDic, unDirectDic);
        lChart.setOption(lOoption);
        var rChart = echarts.init(document.getElementById('rChartView'));
        var rOoption = dataCollationForRight(isDirectDic, unDirectDic);
        rChart.setOption(rOoption);
        // for (let i = 0; i < reportDataThree.length; i++) {
        //     let dic = reportDataThree[i];
        //     $("#chartListInfo").append(
        //         '<div class="cell" style="position: relative;" data-item = ' + dic.itemId + '>' +
        //         '<span class="titleInfo">' + dic.orgName + '</span>' +
        //         '<div class="amountAndTip"><span class="amount">' + dic.totalAmount + '</span><span class="tip">账户余额</span></div>' +
        //         '<div class="amountAndTip"><span class="amount" style="color:#FFB06B">' + dic.proportion + '</span><span class="tip">占比</span></div>' +
        //         '<div style="width: 100%;height: 10px;background: #ffffff"></div>' +
        //         '<div class="cellPadding" style="width: 100%;height: 1px;background: #F1F3F5"></div>' +
        //         '</div>');
        // }
    }

    function dataCollationForLeft(isDirectDic, unDirectDic) {
        var datas = [parseInt(isDirectDic.totalCount), parseInt(unDirectDic.totalCount)];
        var option = {
            series: [
                {
                    name: '直联笔数占比',
                    type: 'pie',
                    radius: ['50%', '70%'],
                    color: ['#84DD86', '#FCE172'],
                    hoverAnimation: false,
                    label: {
                        normal: {
                            show: true,
                            position: 'center',
                            formatter: isDirectDic.countProportion,
                            textStyle: {
                                fontSize: '20',
                                fontWeight: 'bold',
                                color: '#84DD86'
                            }
                        },
                        emphasis: {
                            show: false,
                        }
                    },
                    data: datas
                }
            ]
        };
        return option;
    }

    function dataCollationForRight(isDirectDic, unDirectDic) {
        var str = isDirectDic.amountProportion.replace("%", "");
        var point = str / 100;
        option = {
            series: [
                {
                    name: '直联金额占比',
                    type: 'gauge',
                    startAngle: 180,
                    endAngle: 0,
                    data: [{value: str, color: '#FB8D8D'}],
                    itemStyle: {
                        color: "#FB8D8D",
                    },
                    splitLine: {
                        show: false,
                    },
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: false,
                        lineStyle: {
                            color: [[point, '#FB8D8D'], [1, '#FCE172']],
                            width: 10,
                        }
                    },
                    pointer: {
                        width: 4,
                    },
                    axisLabel: {
                        show: false,
                    },
                    detail: {
                        color: "#FF91BB",
                        fontSize: '20',
                        fontWeight: 'bold',
                        formatter: isDirectDic.amountProportion
                    }
                }
            ]
        };
        return option;
    }

    function comdify(n) {
        var re = /\d{1,3}(?=(\d{3})+$)/g;
        var n1 = n.replace(/^(\d+)((\.\d+)?)$/, function (s, s1, s2) {
            return s1.replace(re, "$&,") + s2;
        });
        return n1;
    }

    function dataCollation(reportDataTwo) {
        var orgNameData = [];
        var nameData = [];
        var seriesData = [];
        for (let i = 0; i < reportDataTwo.length; i++) {
            let dic = reportDataTwo[i];
            let orgName = dic.orgName;
            let len = reportDataTwo.length > 4 ? 4 : 6;
            nameData.push(orgName);
            orgNameData.push({name: orgName, icon: 'circle'})
            let series = {value: dic.totalAmount, name: orgName};
            seriesData.push(series);
        }
        var option = {
            tooltip: {
                trigger: 'item',
                // formatter: "{b}: {c}"
                formatter: function (params, ticket, callback) {
                    return params.name + ": " + comdify(params.value);
                },
            },
            legend: {
                orient: 'vertical',
                right: '5%',
                top: 'center',
                itemWidth: 8,
                itemHeight: 8,
                textStyle: {
                    fontSize: 8,
                },
                data: orgNameData,
                selectedMode: false,
                formatter: function (name) {
                    var index = nameData.indexOf(name);
                    var dic = seriesData[index];
                    return name + ' ' + comdify(dic.value);
                }
            },
            color: ['#FCE172', '#908CFF', '#FB8D8D', '#7BE5C2', '#B496F1', '#FF91BB'],
            series: [
                {
                    type: 'pie',
                    center: ["30%", "50%"],
                    radius: ['35%', '70%'],
                    avoidLabelOverlap: false,
                    label: {
                        normal: {
                            show: false,
                            position: 'center'
                        }
                    },
                    labelLine: {
                        normal: {
                            show: false
                        }
                    },
                    data: seriesData
                }
            ]
        };
        return option;
    }
</script>


</body>
</html>