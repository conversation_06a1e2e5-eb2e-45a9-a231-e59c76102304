<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>资金周报表</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style>
        #tagnav {
            position: fixed;
            top: 0;
            z-index: 999;
            background: #649CFF;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .weui-navigator-list li {
            line-height: 40px;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a {
            color: #649CFF;
            font-weight: bold;
            border-bottom: 1px solid #649CFF;
        }

        .weui-navigator-list li.weui-state-disabled a {
            color: rgba(255, 255, 255, 0.2);
            border-bottom: 1px solid #649CFF;
        }

        .weui-navigator-list li a {
            color: #ffffff;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a:after {
            background-color: transparent;
        }

        #reportTitle {
            padding-top: 10px;
            background: #649CFF;
            color: #fff;
        }

        #center {
            width: 100%;
            text-align: center;
        }

        #center .tip {
            display: block;
            padding-top: 30px;
            font-size: 12px;
        }

        #center .amount {
            display: block;
            padding-bottom: 30px;
            font-size: 24px;
            line-height: 24px;
        }

        #left, #right {
            display: inline-block;
            width: 48%;
            text-align: center;
        }

        .leftTip, .rightTip {
            display: inline-block;
            padding-top: 38px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            position: relative;
        }

        .leftTip:before, .rightTip:before {
            content: ' ';
            width: 11px;
            height: 11px;
            display: inline-block;
            border: 1px solid #ffffff;
            border-radius: 6px;
            position: absolute;
            top: 40px;
            left: -15px;
        }

        .leftTip:before {
            background: #649CFF;
        }

        .rightTip:before {
            background: #cccccc;
        }

        .leftAmount, .rightAmount {
            padding-bottom: 30px;
            display: block;
            font-size: 18px;
            font-weight: 500;
        }

        .line {
            display: inline-block;
            width: 1%;
            max-width: 1px;
            background: #fff;
            height: 30px;
        }

        .titleInfo {
            padding-left: 20px;
            padding-top: 18px;
            font-size: 17px;
            color: #5A5858;
            font-weight: bold;
            display: block;
        }

        .titleInfoArrow:after {
            content: " ";
            display: inline-block;
            height: 7px;
            width: 7px;
            border-width: 2px 2px 0 0;
            border-color: #c8c8cd;
            border-style: solid;
            transform: matrix(.71, .71, -.71, .71, 0, 0);
            webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);
            top: -2px;
            position: absolute;
            top: 50%;
            margin-top: -4px;
            right: 22px;
        }

        .amountAndTip {
            display: inline-block;
            width: calc(48% - 20px);
            padding-left: 20px;
        }

        .amountAndTip .amount {
            display: block;
            color: #FF7979;
            font-size: 16px;
            font-weight: bold;
        }

        .amountAndTip .tip {
            font-size: 10px;
            color: #999999;
            display: block;
        }

        #noMsg {
            position: relative;
            display: none;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }

        #segment {
            padding: 20px 40px;
        }

        .weui-tab-nav .weui-nav-blue.bg-blue {
            border-color: #649cff;
            color: #fff;
            background: #649cff;
        }

        .weui-tab-nav .weui-nav-blue {
            display: block;
            -webkit-box-flex: 1;
            -webkit-flex: 1;
            flex: 1;
            width: 100%;
            height: 30px;
            padding: 0;
            font-size: 14px;
            line-height: 31px;
            text-align: center;
            border: 1px solid #649cff;
            border-width: 1px 1px 1px 0;
            color: #649cff;
            white-space: nowrap;
            background: #fdfdfd;
        }
    </style>
</head>
<body>
<div id="tagnav" class="weui-navigator weui-navigator-wrapper">
    <ul class="weui-navigator-list" style="display: none"></ul>
</div>
<div id="reportTitle">
    <!-- <div id="center"><span class="tip">账户余额/万元</span><span class="amount">3828,222.72</span></div>
    <div id="left"><span class="leftTip">账户余额/万元</span><span class="leftAmount">10000</span></div>
    <div class="line"></div>
    <div id="right"><span class="rightTip">上缴金额/万元</span><span class="rightAmount">18888</span></div> -->
</div>
<div id="segment">
    <!-- <div class="weui-tab" style="height:44px;">
      <div class="weui-tab-nav">
        <a href="javascript:switchTypeCon('2');" class="weui-navbar__item weui-nav-blue"> 融资机构 </a>
        <a href="javascript:switchTypeCon('1');" class="weui-navbar__item weui-nav-blue"> 组织 </a>
      </div>
    </div> -->
</div>
<div id="chartSection"></div>
<div id="chartListInfo"></div>
<div id="noMsg">
    <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
    <span>暂无数据</span>
</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/iscroll-lite.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script src="${ctx}/js/echarts.min.js"></script>

<script>
    var type = "1";
    var typeIndex = 0;
    $(function () {

        requestData();
    });

    function requestData() {
        showNoMsg(false)
        var param = {'tag': 'ats', 'queryType': type};
        console.log('param=' + JSON.stringify(param));
        $.ajax({
            cache: true,
            type: 'POST',
            url: "${ctx}/jdGroup/getFundWeekReport.do",
            data: param,
            async: true,
            dataType: 'json',
            beforeSend: function () {
                $.showLoading();
            },
            error: function () {
                $.hideLoading();
                $.toast('网络服务情况异常，请检查', "forbidden");
                showNoMsg(true);
            },
            success: function (data) {
                $.hideLoading();
                if (data.successful === true || "true" === data.successful) {
                    var dataArray = data.result.data;
                    if (dataArray.length === 3) {
                        //顶部数据
                        layoutTopTitle(dataArray[0].reportData);
                        $('#segment').html('');
                        $('#segment').append(
                            '<div class="weui-tab" style="height:44px;">' +
                            '<div class="weui-tab-nav">' +
                            '<a href="javascript:switchTypeCon(\'1\');" class="weui-navbar__item weui-nav-blue">并表</a> ' +
                            '<a href="javascript:switchTypeCon(\'2\');" class="weui-navbar__item weui-nav-blue">非并表</a> ' +
                            '<a href="javascript:switchTypeCon(\'3\');" class="weui-navbar__item weui-nav-blue">全部</a> ' +

                            '</div>' +
                            '</div>');
                        $('#segment .weui-tab').tab({defaultIndex: typeIndex, activeClass: "bg-blue"});
                        //chart数据
                        $('#chartSection').html('');
                        $('#chartSection').append(
                            '<div id="chartView" style="width: 100%;height: 268px;"></div>' +
                            '<div class="chartPadding" style="width: 100%;height: 10px;background: #F1F3F5"></div>');
                        var myChart = echarts.init(document.getElementById('chartView'));
                        var option = dataCollation(dataArray[1].reportData);
                        myChart.setOption(option);
                        //列表数据
                        layoutListInfo(dataArray[2].reportData);
                    } else {
                        // 暂无数据
                        showNoMsg(true);
                    }
                } else {
                    $.toast('网络服务情况异常，请检查', "forbidden");
                    showNoMsg(true);
                }
            }
        });
    }

    function showNoMsg(show) {
        if (show) {
            $("#reportTitle").html('');
            $("#chartSection").html('');
            $("#chartListInfo").html('');
            $("#noMsg").css('display', 'block');
        } else {
            $("#noMsg").css('display', 'none');
        }
    }

    function layoutTopTitle(reportDataOne) {
        $("#reportTitle").html('');
        let dic = reportDataOne[0];
        $("#reportTitle").append(
            '<div id="center"><span class="tip">总余额/万元</span><span class="amount" style="padding-bottom: 0;">' + dic.openingBalance + '</span></div>' +
            '<div id="left"><span class="leftTip">可用余额/万元</span><span class="leftAmount">' + dic.availableBeginBalance + '</span></div>' +
            '<div class="line"></div>' +
            '<div id="right"><span class="rightTip">不可用余额/万元</span><span class="rightAmount">' + dic.unAvailableBeginBalance + '</span></div>');
    }

    function layoutListInfo(reportDataThree) {
        $('#chartListInfo').html('');
        for (let i = 0; i < reportDataThree.length; i++) {
            let dic = reportDataThree[i];
            $("#chartListInfo").append(
                '<div class="cell" style="position: relative;">' +
                '<span class="titleInfo">' + dic.itemName + '</span>' +
                '<div class="amountAndTip"><span class="amount">' + dic.openingBalance + '</span><span class="tip">账户余额</span></div>' +
                '<div class="amountAndTip"><span class="amount">' + dic.availableBeginBalance + '</span><span class="tip">可用余额</span></div>' +
                '<div class="amountAndTip"><span class="amount" style="color:#FFB06B">' + dic.unAvailableBeginBalance + '</span><span class="tip">不可用余额</span></div>' +
                '<div style="width: 100%;height: 10px;background: #ffffff"></div>' +
                '<div class="cellPadding" style="width: 100%;height: 1px;background: #F1F3F5"></div>' +
                '</div>');
        }


    }

    function switchTypeCon(typeCon) {
        type = typeCon;
        if (typeCon === '1') {
            typeIndex = 0
        }

        if (typeCon === '2') {
            typeIndex = 1
        }

        if (typeCon === '3') {
            typeIndex = 2
        }


        requestData();
    }

    function dataCollation(reportDataTwo) {
        var xAxisData = [];
        var seriesData = [];
        var availableBalanceData = [];
        var unAvailableBalanceData = [];
        for (let i = 0; i < reportDataTwo.length; i++) {
            let dic = reportDataTwo[i];
            let itemName = dic.itemName;
            xAxisData.push(itemName)
            availableBalanceData.push(dic.availableBeginBalance);
            unAvailableBalanceData.push(dic.unAvailableBeginBalance);
        }
        let barData1 = {
            name: '可用余额',
            type: 'bar',
            stack: 'Ad',
            color: '#649CFF',
            emphasis: {
                focus: 'series'
            },
            data: availableBalanceData
        }
        let barData2 = {
            name: '不可用余额',
            color: '#cccccc',
            type: 'bar',
            stack: 'Ad',
            emphasis: {
                focus: 'series'
            },
            data: unAvailableBalanceData
        }
        seriesData.push(barData1);
        seriesData.push(barData2);
        var option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            color: '#908CFF',
            grid: {
                top: '8%',
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true,
            },
            xAxis: [
                {
                    type: 'category',
                    data: xAxisData,
                    axisLine: {
                        lineStyle: {
                            color: '#D6D6D6',
                        }
                    },
                    axisLabel: {
                        color: '#888888',
                        fontSize: 10,
                        interval: 0,
                        rotate: 40
                    },
                    axisTick: {
                        show: false
                    },

                }
            ],
            yAxis: [
                {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#fff',
                        }
                    },
                    axisLabel: {
                        color: '#888888'
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(67,67,67,0.05)'
                        }
                    }
                }
            ],
            series: seriesData
        };
        return option;
    }
</script>


</body>
</html>

