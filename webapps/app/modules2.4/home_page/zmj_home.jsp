<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>首页</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/swiper.min.css">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

    <style>
        * {
            touch-action: pan-y;
        }

        html,
        body {
            position: relative;
            height: 100%;

        }

        body {
            background: #eee;
            font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
            font-size: 14px;
            color: #000;
            margin: 0;
            padding: 0;
        }

        .swiper-container {
            position: relative;
            bottom: 2.05rem;
            width: 90%;
            height: 44px;
            border-radius: 0.525rem;
        }

        .swiper-slide {
            width: 100%;
            max-width: 100%;
            padding-left: 0.75rem;
            border-radius: 0.525rem;
            text-align: start;
            font-size: 15px;
            background: #fff;
            line-height: 44px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .weui-cells {
            width: 90%;
            position: relative;
            bottom: 1.55rem;
            border-radius: 0.525rem;
            margin: 0 auto 3.25rem;

        }
        .icon-53 {
            margin-right: 0.625rem;
        }
        
        #datePicker span{
            line-height: 50px;
            color: #666666;
        }
        #datePicker .dateLabel{
            padding: 5px 20px;
            background: #EDF2FB;
            border-radius: 3px;
            color: #212121;
        }

        .whiteView{
            width: 80%;
            margin-left: 10%;
            background: white;
            top: calc((100% - 250px)/2);
            height: 170px;
            position: absolute;
            border-radius: 4px;
        }
        .whiteView .maskTitle{
            display: block;
            text-align: center;
            padding: 20px 10px;
            color: #333;
        }
        .whiteView .dateLabel{
            width: 40%;
            background: #EDF2FB;
            text-align: center;
            line-height: 40px;
            border-radius:5px;
            border: 0;
            font-size: 14px;
            color: #333;
        }
        #startView{
            float: left;
            margin-left: 5%;
        }
        #endView{
            float: right;
            margin-right: 5%;
        }
        .btns .btn{
            width: calc(50% - 0.5px);
            background-color: #fff9;
            line-height: 40px;
            text-align: center;
            position: absolute;
            bottom: 0;
            border-top: solid 1px #D6D6D6; 
        }
        .btns .cancel{
            left: 0;
            border-bottom-left-radius:4px;
            border-right: solid 1px #D6D6D6;
        }
        .btns .confirm{
            right: 0;
            border-bottom-right-radius:4px;
        }

        .weui-picker-container, .weui-picker-overlay{
            bottom: 0;
        }
        .weui-picker-container{
            background-color: rgba(0,0,0,0);
        }

        #noMsg {
            position: absolute;
            display: none;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 200px;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            left: -50px;
            top: 40px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            left: -32px;
            top: 135px;
            color: #999999;
            font-size: 14px;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="content" style="height:100%;overflow: auto;">
        <div style="width:100%;height:200px;background-color: #649CFF; margin-bottom: 0.625rem;">
            <div style="text-align: center; padding-top: 50px; color: #FFFFFF;">
                <span>账户余额/万元</span>
                <p id="total-amount" style="font-size: 1.625rem;">0.00</p>
            </div>
        </div>

        <div id="oneSection" style="margin: 0 auto; width:90%;height:250px;background-color: white; position: relative; bottom: 2.625rem; border-radius: 0.525rem;">
            <div id="datePicker" style="width: 100%; height:50px; text-align: center;">
                <span class='dateLabel dateStart'></span>
                <span style="padding: 5px 20px;">到</span>
                <span class='dateLabel dateEnd'></span>
            </div>
            <div id="chart" style="width: 100%; height:200px;"></div>
            <div id="noMsg">
                <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
                <span>暂无数据</span>
            </div>
        </div>
        <div class="swiper-container">
            <div class="swiper-wrapper">
            </div>
        </div>

        <div class="weui-cells">
        </div>

        <%@include file='/common/bottom_bar.jsp' %>
    </div>
    <div class="maskView" style="width: 100%;top:0;bottom: 0;position: absolute;z-index: 9999999;background-color: rgba(51, 51, 51, 0.6);display: none;">
        <div class="whiteView">
            <span class='maskTitle'>查询日期</span>
            <div id="startView" class="dateLabel"><span></span></div>
            <input id="startViewInput" type='hidden'/>
            <span style="width: 10%;text-align: center;width: 10%;display: block;float: left;line-height: 40px;">-</span>
            <div id="endView" class="dateLabel"><span></span></div>
            <input id="endViewInput" type="hidden" />
            <div style="height: 20px;"></div>
            <div class="btns">
                <div class="btn cancel">取消</div>
                <div class="btn confirm">确定</div>
            </div>
        </div>
    </div>
</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/swiper.min.js"></script>
<script src="${ctx}/js/echarts.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>

<script>

    overscroll(document.getElementsByClassName('content')[0]);
    const singlePaymentWorflowIds = "PAYMENTS_CROSSBORDERFOREIGNCURRPAY-1,PAYMENTS_OUTSIDEFOREIGNCURRPAY-1,PAYMENT_SINGLE-1,QUICKPAYAPPLY_CROSSBORDERFOREIGNCURRPAY-1,QUICKPAYAPPLY_OUTSIDEFOREIGNCURRPAY-1,QUICKPAYMENTS_APPLY_XSZK-1";
    const SINGLE_PAYMENT_COMMAND_CODE = "WFQB13";
    const NEXT_SINGLE_PAYMENT_COMMAND_CODE = "WFQB04";
    $.showLoading('加载中...')
    delegate.ready(function (paramData) {
        $.showLoading('加载中...')
        ajaxRequest('/saasmainpage/getIndexPageDataSummary.do', {tag: 'ats'}, function (data) {
            console.log(data);
            initWarningMsg(data.result.warningMessageData, paramData.result.permissionCode);
            // initChart(data.result.accountBalanceData);
            initPayments(data.result.singlePaymentData, paramData.result.permissionCode)
        });

    });

    //回退刷新
    var isPageHide = false;
    window.addEventListener('pageshow', function (e) {
        if (isPageHide) {
            window.location.reload()
        }
    });
    window.addEventListener('pagehide', function () {
        isPageHide = true;
    });


    function initWarningMsg(msgData, permissions) {

        if (permissions.indexOf(PERMISSION_CODE_WARNING_MSG) === -1) {
            $('.swiper-wrapper').append('<div class="swiper-slide"><span class="icon icon-53"></span>您没有查看预警消息的权限</div>');
            return;
        }

        if (msgData.totalNum === 0) {
            $('.swiper-wrapper').append('<div class="swiper-slide"><span class="icon icon-53"></span>暂无预警消息</div>');
        } else {

            for (let i = 0; i < msgData.data.length; i++) {
                var msg = $('<div class="swiper-slide"><span class="icon icon-53"></span>' + msgData.data[i].warningMessage + '</div>');
                $('.swiper-wrapper').append(msg);
                msg.on('click', function () {
                    window.location.href = '../warning/warning_msg.jsp?selectType=warning';
                });
            }
            let swiper = new Swiper('.swiper-container', {
                autoplay: {
                    delay: 3500,
                    stopOnLastSlide: false,
                    disableOnInteraction: false,
                    autoHeight: true //高度随内容变化
                },
                loop: true,
                direction: 'vertical'
            });
        }

    }

    function requestZMJChartData(showLoading){
        $.ajax({
            cache: true,
            type: 'POST',
            url: "<%=basePath%>/zmj/getHomeAccountBalance.do",
            data: {'startDate':startDateString,'endDate':endDateString},
            async: true,
            dataType: 'json',
            beforeSend: function () {
                if (showLoading) {
                    $.showLoading();
                }
            },
            error: function () {
                if (showLoading) {
                    $.hideLoading();
                }
                $.toast('网络服务情况异常，请检查', "forbidden");
                showNoMsg(true);
            },
            success: function (data) {
                if (showLoading) {
                    $.hideLoading();
                }
                if (data.successful == true || "true" == data.successful) {
                    initZmjChart(data.result);
                    
                } else {
                    $.toast('网络服务情况异常，请检查', "forbidden");
                    showNoMsg(true);
                }
            }
        });
    }

    function showNoMsg(show) {
        if (show) {
            $('#chart').html('');
            $("#noMsg").css('display', 'block');
        } else {
            $("#noMsg").css('display', 'none');
        }
    }


    function initZmjChart(chartData){
        if (chartData.totalAmount === null || chartData.totalAmount === undefined) {
            chartData.totalAmount = '0.00';
        }
        $('#total-amount').html(chartData.totalAmount);
        let date = [];
        let lineNames=[];
        let lineDatas=[];
        let seriesData = [];
        var jData=chartData.data;
        var colors=['#FB8D8D','#74E2AB','#649CFF'];
        var index=0;
        for(var obj in jData){
            if(jData.hasOwnProperty(obj)){
                console.log('key=' + obj +' value=' + jData[obj]);
                lineNames.push(obj);
                lineDatas.push(jData[obj]);
                var accountData=[];
                if (jData[obj].length>0) {
                    var dataArray=jData[obj];
                    for (var i = 0; i < dataArray.length; i++) {
                        if (index==0) {
                            var dateStr=dataArray[i].month;
                            date.push(dateStr);
                        }
                        var noteMoney = dataArray[i].noteMoney.replace(/,/g,'');
                        accountData.push(noteMoney);
                    }
                }
                let accountSeries = {
                    name: obj,
                    type: 'line',
                    symbol: 'circle',
                    symbolSize: 1,
                    stack: obj,
                    data: accountData,
                    itemStyle: {
                        normal: {
                            lineStyle: {
                                color: colors[index]
                            }
                        }
                    }
                };
                seriesData.push(accountSeries);
                index++;
            }
        }
        
        let myChart = echarts.init(document.getElementById('chart'));

        let option = {
            tooltip: {
                trigger: 'axis',
                confine: true
            },
            color: colors,
            legend: {
                data: lineNames,
                top: 5
            },
            grid: {
                left: '10%',
                right: '10%',
                bottom: '5%',
                top:'15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: date,
                splitLine: {
                    show: false
                },
                axisLine: {
                    lineStyle: {
                        color: '#D6D6D6',
                    }
                },
                axisLabel: {
                    color: '#888888',
                    fontSize: 10,
                },
                axisTick: {
                    show: false
                }
            },
            yAxis: {
                show: false,
                type: 'value',
                splitLine: {
                    show: false
                },
                axisLabel:{
                    show:false
                }
            },
            series: seriesData
        };
        if (date.length>6) {
            option['dataZoom']=[{
                    type: 'slider',
                    show:true,
                    startValue: date[0],
                    endValue:date[5],
                    textStyle:{
                        color: '#666666',
                        fontSize: 10,
                    },
                    left:'20%',
                    right:'20%',
                }];
            option['grid']['bottom']='20%';
            option['xAxis']['axisLabel']['interval']=1;
        }else{
            option['dataZoom']=[{show:false}];
            option['grid']['bottom']='5%';
            option['xAxis']['axisLabel']['interval']=0;
        }
        myChart.clear();
        myChart.setOption(option);
    }

    function initPayments(paymentsData, permissions) {

        let data = paymentsData.data;
        let cells = $('.weui-cells');
        cells.html('');

        let cellHeader = $('<div class="weui-cell" style="font-weight: bold">' +
            '<div class="weui-cell__bd"><p>单笔付款审批</p></div>' +
            '<div class="weui-cell__ft"></div></div>');

        cells.append(cellHeader);
        if (permissions.indexOf(PERMISSION_CODE_FLOW) === -1) {

            cells.append('<div class="weui-cell"><div class="weui-cell__hd">您没有查看审批流程的权限</div></div>');

            return;
        }

        if (data == null || data.length === 0) {
            cells.append('<div class="weui-cell"><div class="weui-cell__hd">暂无数据</div></div>');
            return;
        }
        for (let i = 0; i < data.length; i++) {
            let cell = $('<label class="weui-cell weui-check__label" for="check__no__' + data[i].itemId + '">' +
                '            <div class="weui-cell__bd">' +
                '                <p style="font-size: 17px; color: #212121; font-weight: bold">' + data[i].title + '</p>' +
                '                <div class="item_list">' +
                '                </div>' +
                '            </div>' +
                '            <div class="weui-cell__ft" style="position: absolute;top:15px;right: 10px">' +
                '                <p style="font-size: 12px">' + data[i].time + '</p>' +
                '            </div>' +
                '        </label>');

            let cellItem = cell.find('.item_list');

            let items = data[i].item;
            for (let j = 0; j < items.length; j++) {
                let item = $('<p style="font-size: 13px;color: #5A5858"><span style="width: 100px">' + items[j].key + '：</span>' + items[j].value + '</p>');
                cellItem.append(item);
            }

            cells.append(cell);

            cell.on('click', function () {
                sessionStorage.setItem("DETAIL_FLOW_PARAMS", JSON.stringify(data[i]));
                sessionStorage.setItem("TODO", TODO_TAG);
                sessionStorage.setItem("FLOWTYPEID", "ZJFK");

                window.location.href = "../flow/flowDetail.jsp";
            });

        }
    }

    //str格式yyyy-mm
    function monthDifference(str1,str2,num){
        var newYear1 = parseInt(str1.substr(0,4));
        var newYear2 = parseInt(str2.substr(0,4));
        var newMonth1 = parseInt(str1.substr(5,7));
        var newMonth2 = parseInt(str2.substr(5,7));
        var x = newYear2 - newYear1;
        newMonth2 = newMonth2+13*x;
        if(newMonth2-newMonth1<=num){         //不超过6个月
           return false;
        }else{
           return true;
        }
    }

    function getSixMonthsAgo(str1){
        var newYear1 = parseInt(str1.substr(0,4));
        var newMonth1 = parseInt(str1.substr(5,7));
        var newYear2;
        var newMonth2;
        if (newMonth1>=6) {
            newMonth2=newMonth1-6+1;
            newYear2=newYear1;
        } else {
            newYear2=newYear1-1;
            newMonth2=newMonth1+7;
        }
        return newYear2 + '-' + newMonth2;
    }


    function currentDate(){
        var now = new Date();
        var year = now.getFullYear(); //得到年份
        var month = now.getMonth();//得到月份
        // var date = now.getDate();//得到日期
        month = month + 1;
        if (month < 10) month = "0" + month;
        // if (date < 10) date = "0" + date;
        var time = "";
        // time = year + "-" + month + "-" + date;
        time = year + "-" + month;
        return time;
    }

    function showMaskView(show){
        if (show) {
            $('.maskView').css('display', 'block');
        } else {
            $('.maskView').css('display', 'none');
        }
    }

    var startDateString,endDateString;

    $(document).ready(function () {
        var currentDateStr = currentDate();
        //时间选择器 
        $('#datePicker').click(function(event) {
            console.log('时间选择器');
            showMaskView(true);
        });
        $('.maskView').click(function(event) {
            console.log('点击隐藏');
            showMaskView(false);
            event.stopPropagation();
        });
        $('.whiteView').click(function(event) {
            event.cancelBubble=true;
        });
        $('.whiteView .cancel').click(function(event) {
            showMaskView(false);
        });
        $('.whiteView .confirm').click(function(event) {
            var str1 = $("#startView span").text();
            var str2 = $("#endView span").text();
            if(!monthDifference(str1,str2,0)){
                $.toast("起始日期不能大于等于截止日期", "text");
                return false;
            }
            if(!monthDifference(str2,currentDateStr,-1)){
                $.toast("选择日期不能大于当前日期", "text");
                return false;
            }
            if(monthDifference(str1,str2,12)){
                $.toast("时间范围不能超过12个月", "text");
                return false;
            }
            showMaskView(false);
            $('span.dateStart').text($('#startView span').text());
            $('span.dateEnd').text($('#endView span').text());
            startDateString=$('#startView span').text();
            endDateString=$('#endView span').text();
            requestZMJChartData(true);
        });

        $('#startView').click(function(event) {
            $('#startViewInput').picker('open');
        });
        $('#endView').click(function(event) {
            $('#endViewInput').picker('open');
        });

        $("#startViewInput").picker({
            title:"请选择起始日期",
            cols: [
              {
                textAlign: 'center',
                values: (function () {
                    var arr = [];
                    for (var i = 0; i <30; i++) { arr.push(new Date().getFullYear()-i); }
                    return arr;
                })()
                
              },
              {
                textAlign: 'center',
                values:  (function () {
                    var arr = [];
                    for (var i = 1; i < 13; i++) { arr.push(i < 10 ? '0' + i : i); }
                    return arr;
                })(),
              }
            ],
            onClose: function(){
                var string=$("#startViewInput").val().replace(" ","-");
                $("#startView span").text(string);
            }
        });  
        $("#endViewInput").picker({
            title:"请选择截止时间",
            cols: [
              {
                textAlign: 'center',
                values: (function () {
                    var arr = [];
                    for (var i = 0; i <30; i++) { arr.push(new Date().getFullYear()-i); }
                    return arr;
                })()
                
              },
              {
                textAlign: 'center',
                values:  (function () {
                    var arr = [];
                    for (var i = 1; i < 13; i++) { arr.push(i < 10 ? '0' + i : i); }
                    return arr;
                })(),
              }
            ],
            onClose: function(){
                var string=$("#endViewInput").val().replace(" ","-");
                $("#endView span").text(string);
            }
        });  

        var preDateStr=getSixMonthsAgo(currentDateStr);
        $('#startView span').text(preDateStr);
        $('span.dateStart').text(preDateStr);
        $('#endView span').text(currentDateStr);
        $('span.dateEnd').text(currentDateStr);
        var startStr=preDateStr.replace("-"," ");
        $("#startViewInput").val(startStr);
        var endStr = currentDateStr.replace("-"," ");
        $("#endViewInput").val(endStr);

        startDateString=preDateStr;
        endDateString=currentDateStr;
        requestZMJChartData(false);
                    
    });
</script>
</body>
</html>

