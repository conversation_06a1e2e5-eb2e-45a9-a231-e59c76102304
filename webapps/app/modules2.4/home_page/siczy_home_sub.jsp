<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>四川中烟组织余额情况</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/swiper.min.css">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

    <style>
        * {
            touch-action: pan-y;
        }

        html,
        body {
            height: 100%;

        }

        body {
            background: #eee;
            font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
            font-size: 14px;
            color: #000;
            margin: 0;
            padding: 0;
        }

        .swiper-container {
            position: relative;
            bottom: 2.05rem;
            width: 90%;
            height: 44px;
            border-radius: 0.525rem;
        }

        .swiper-slide {
            width: 100%;
            max-width: 100%;
            padding-left: 0.75rem;
            border-radius: 0.525rem;
            text-align: start;
            font-size: 15px;
            background: #fff;
            line-height: 44px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .weui-cells {

            width: 90%;
            position: relative;
            bottom: 1.55rem;
            border-radius: 0.525rem;
            margin: 0 auto 3.25rem;

        }

        .icon-53 {
            margin-right: 0.625rem;
        }


    </style>
</head>
<body>
<div class="container">
    <div class="content" style="height:100%;overflow: auto;">
        <div id="title-layout">
            <div id="title-amount"
                 style="display: block;width:100%;height:200px;background-color: #649CFF;">
                <div style="text-align: center; padding-top: 50px; color: #FFFFFF;">
                    <span id="tip-name"></span>
                    <p id="total-amount" style="font-size: 1.625rem;"></p>
                </div>
            </div>

            <div id="chart"
                 style="margin: 0 auto; width:100%;height:200px;background-color: white; position: relative; top: 0.625rem; border-radius: 0rem;display: none;">
            </div>
        </div>
        <div id="body-layout" style="display: none">
            <div class="swiper-container">
                <div class="swiper-wrapper">
                </div>
            </div>
            <div class="weui-cells">
            </div>
        </div>
    </div>
</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/swiper.min.js"></script>
<script src="${ctx}/js/echarts.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>

<script>

    overscroll(document.getElementsByClassName('content')[0]);

    //回退刷新
    var isPageHide = false;
    window.addEventListener('pageshow', function (e) {
        if (isPageHide) {
            window.location.reload()
        }
    });
    window.addEventListener('pagehide', function () {
        isPageHide = true;
    });


    function initWarningMsg(msgData, permissions) {
        if (permissions.indexOf(PERMISSION_CODE_WARNING_MSG) === -1) {
            // $('.swiper-wrapper').append('<div class="swiper-slide"><span class="icon icon-53"></span>您没有查看预警消息的权限</div>');
            $('.swiper-container').remove();

            return;
        }

        if (msgData.totalNum === 0) {
            $('.swiper-wrapper').append('<div class="swiper-slide"><span class="icon icon-53"></span>暂无预警消息</div>');
        } else {

            for (let i = 0; i < msgData.data.length; i++) {
                var msg = $('<div class="swiper-slide"><span class="icon icon-53"></span>' + msgData.data[i].warningMessage + '</div>');
                $('.swiper-wrapper').append(msg);
                msg.on('click', function () {
                    window.location.href = '../warning/warning_msg.jsp?selectType=warning';
                });
            }
            let swiper = new Swiper('.swiper-container', {
                autoplay: {
                    delay: 3500,
                    stopOnLastSlide: false,
                    disableOnInteraction: false,
                    autoHeight: true //高度随内容变化
                },
                loop: true,
                direction: 'vertical'
            });
        }

    }

    function comdify(n) {
        var re = /\d{1,3}(?=(\d{3})+$)/g;
        var n1 = n.replace(/^(\d+)((\.\d+)?)$/, function (s, s1, s2) {
            return s1.replace(re, "$&,") + s2;
        });
        return n1;
    }


    function initChart(chartData) {
        $('#tip-name').text('账户余额/万元');
        $('#chart').css('display', 'block');
        if (chartData === null || chartData === undefined) {
            return;
        }
        let lineData = chartData.data;
        if (lineData === null || lineData.length === 0) {
            // 隐藏金额和图表
            $('#title-layout').hide();
            let body_layout = $('#body-layout');
            body_layout.css('position', 'relative');
            body_layout.css('top', '50px');
            return;
        }
        // 设置标题金额
        if (chartData.totalAmt === null || chartData.totalAmt === undefined) {
            chartData.totalAmt = '0.00';
        }
        $('#total-amount').html(chartData.totalAmt);

        let seriesData = [];
        let default_colors = ['#FCE172', '#908CFF', '#FB8D8D', '#7BE5C2', '#FF91BB', '#B496F1', '#74E2AB', '#649CFF', '#6E7074', '#546570', '#C4CCD3'];
        let keyData = [];
        let dateList = [];

        if (lineData.length > 0) {
            for (let i = 0; i < lineData.length; i++) {

                let itemLineName = lineData[i].lineKey;
                let itemLineData = lineData[i].lineData;
                keyData.push(itemLineName);


                let itemSeries = [];
                dateList = [];
                for (let itemData of itemLineData) {
                    itemSeries.push(itemData.noteMoney);
                    dateList.push(itemData.reportDate);
                }

                let series = {
                    name: itemLineName,
                    type: 'line',
                    symbol: 'circle',
                    symbolSize: 1,
                    stack: itemLineName,
                    data: itemSeries,
                    itemStyle: {
                        normal: {
                            lineStyle: {
                                color: default_colors[i % default_colors.length]
                            }
                        }
                    }
                }

                seriesData.push(series);

            }

        } else {
            //无顶部折线图权限
            $("#title-layout").remove();
            $('.swiper-container').css('bottom', 'unset');
            $('.swiper-container').css('margin-top', '0.5rem');
            $('.weui-cells').css('bottom', 'unset');
            $('.weui-cells').css('margin-top', '0.5rem');
            return;
        }


        let myChart = echarts.init(document.getElementById('chart'));

        let option = {
            title: {
                text: ''
            },
            tooltip: {
                trigger: 'axis',
                confine: true
            },
            color: default_colors,
            legend: {
                data: keyData,
                top: 15
            },
            grid: {
                left: '10%',
                right: '10%',
                bottom: '5%',
                top: '20%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: dateList,
                splitLine: {
                    show: false
                },
                axisLine: {
                    lineStyle: {
                        color: '#D6D6D6',
                    }
                },
                axisLabel: {
                    color: '#888888',
                    fontSize: 10,
                    interval: 0,
                },
                axisTick: {
                    show: false
                }
            },
            yAxis: {
                show: false,
                type: 'value',
                splitLine: {
                    show: false
                },
                axisLabel: {
                    show: false
                }
            },
            series: seriesData
        };

        myChart.setOption(option);
    }

    function initPayments(paymentsData, permissions) {
        let data = paymentsData.data;
        let cells = $('.weui-cells');
        cells.html('');

        let cellHeader = $('<div class="weui-cell" style="font-weight: bold">' +
            '<div class="weui-cell__bd"><p>单笔付款审批</p></div>' +
            '<div class="weui-cell__ft"></div></div>');

        cells.append(cellHeader);
        if (permissions.indexOf(PERMISSION_CODE_FLOW) === -1) {
            cells.remove();
            // cells.append('<div class="weui-cell"><div class="weui-cell__hd">您没有查看审批流程的权限</div></div>');
            return;
        }

        if (data == null || data.length === 0) {
            cells.append('<div class="weui-cell"><div class="weui-cell__hd">暂无数据</div></div>');
            return;
        }
        for (let i = 0; i < data.length; i++) {
            let cell = $('<label class="weui-cell weui-check__label" for="check__no__' + data[i].itemId + '">' +
                '            <div class="weui-cell__bd">' +
                '                <p style="font-size: 17px; color: #212121; font-weight: bold">' + data[i].title + '</p>' +
                '                <div class="item_list">' +
                '                </div>' +
                '            </div>' +
                '            <div class="weui-cell__ft" style="position: absolute;top:15px;right: 10px">' +
                '                <p style="font-size: 12px">' + data[i].time + '</p>' +
                '            </div>' +
                '        </label>');

            let cellItem = cell.find('.item_list');

            let items = data[i].item;
            for (let j = 0; j < items.length; j++) {
                let item = $('<p style="font-size: 13px;color: #5A5858"><span style="width: 100px">' + items[j].key + '：</span>' + items[j].value + '</p>');
                cellItem.append(item);
            }

            cells.append(cell);

            cell.on('click', function () {
                sessionStorage.setItem("DETAIL_FLOW_PARAMS", JSON.stringify(data[i]));
                sessionStorage.setItem("TODO", TODO_TAG);
                sessionStorage.setItem("FLOWTYPEID", "ZJFK");

                window.location.href = "../flow/flowDetail.jsp";
            });

        }
    }


    $(document).ready(function () {
        var org_id = sessionStorage.getItem("SICZY_HOME_ORG_QUERY_ID");
        ddAjaxRequestCallback('加载中', '/saasmainpage/getIndexPageDataSummary.do', {queryOrgId: org_id}, function (data) {
            initWarningMsg(data.result.warningMessageData, '${permissionCode}');
            initChart(data.result.accountBalanceData);
            initPayments(data.result.singlePaymentData, '${permissionCode}')
        });
    });
</script>


</body>
</html>

