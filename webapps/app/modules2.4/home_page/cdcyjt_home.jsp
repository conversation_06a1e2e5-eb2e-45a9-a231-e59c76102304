<%@ page import="com.fingard.app.delegate.connect.WsSender" %>
<%@ page import="com.fingard.app.delegate.framework.util.Constants" %>
<%@ page import="cn.hutool.core.util.StrUtil" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>
<%
    String orgName = String.valueOf(request.getSession().getAttribute(Constants.ORG_NAME));
    if (StrUtil.isNullOrUndefined(orgName)) {
        orgName = "";
    }
%>
<c:set var="currOrgName" value="<%=orgName%>"/>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>${currOrgName}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/swiper.min.css">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

    <style>
        * {
            touch-action: pan-y;
        }

        html,
        body {
            position: relative;
            height: 100%;

        }

        body {
            background: white;
            font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
            font-size: 14px;
            color: #000;
            margin: 0;
            padding: 0;
        }

        .swiper-container {
            position: relative;
            bottom: 2.05rem;
            width: 90%;
            height: 44px;
            border-radius: 0.525rem;
        }

        .swiper-slide {
            width: 100%;
            max-width: 100%;
            padding-left: 0.75rem;
            border-radius: 0.525rem;
            text-align: start;
            font-size: 15px;
            background: #fff;
            line-height: 44px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .weui-cells {
            width: 90%;
            position: relative;
            bottom: 1.55rem;
            border-radius: 0.525rem;
            margin: 0 auto 3.25rem;

        }


        #noMsg {
            position: absolute;
            display: none;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 200px;
        }

        #noMsg img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            left: -50px;
            top: 40px;
        }

        #noMsg span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            left: -32px;
            top: 135px;
            color: #999999;
            font-size: 14px;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="content" style="height:100%;overflow: auto;">
        <div style="width:100%;height:130px;background-color: #649CFF; margin-bottom: 0.625rem;">
            <div style="text-align: center; padding-top: 20px; color: #FFFFFF;">
                <p id="total-amount" style="font-size: 1.625rem;">0.00</p>
                <span>账户余额/万元</span>
            </div>
        </div>

        <div id="oneSection"
             style="margin: 0 auto; width:90%;background-color: white; position: relative; border-radius: 0.525rem;">
            <div id="chart" style="width: 100%;height: 300px"></div>
            <div id="lineChart" style="width: 100%;height: 350px"></div>
            <div id="noMsg">
                <img src="${ctx}/img/report/<EMAIL>" alt="暂无数据">
                <span>暂无数据</span>
            </div>
        </div>
        <div class="swiper-container" style="margin-top: 55px">
            <div class="swiper-wrapper">
            </div>
        </div>

        <div class="weui-cells">
        </div>

        <%@include file='/common/bottom_bar.jsp' %>
    </div>
</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/swiper.min.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/echarts/5.4.3/echarts.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>

<script>

    overscroll(document.getElementsByClassName('content')[0]);

    //回退刷新
    var isPageHide = false;
    window.addEventListener('pageshow', function (e) {
        if (isPageHide) {
            window.location.reload()
        }
    });
    window.addEventListener('pagehide', function () {
        isPageHide = true;
    });


    let requestData = function (showLoading) {
        $.ajax({
            cache: true,
            type: 'POST',
            url: "${ctx}/cdcyjt/getHomeData.do",
            data: {},
            async: true,
            dataType: 'json',
            beforeSend: function () {
                if (showLoading) {
                    $.showLoading();
                }
            },
            error: function () {
                if (showLoading) {
                    $.hideLoading();
                }
                $.toast('网络情况异常，请检查！', "forbidden");
                showNoMsg(true);
            },
            success: function (data) {
                if (showLoading) {
                    $.hideLoading();
                }
                if (data.success === true) {

                    $('#total-amount').html(data.result.plateData.totalAmount?data.result.plateData.totalAmount:'0.00');
                    initChart(data.result);

                } else {
                    showNoMsg(true);
                }
            }
        });
    }

    let initChart = function (data) {
        let pieChart = echarts.init(document.getElementById('chart'));
        showNoData(pieChart,data.plateData.data,'暂无账户余额数据');
        pieChart.setOption(getPieChartOption(data.plateData))
        let lineChart = echarts.init(document.getElementById('lineChart'));
        showNoData(lineChart,data.monthData,"暂无融资余额数据");
        lineChart.setOption(getLineOption(data.monthData))

        console.log(data);
    }

    let showNoData = function (myChart,data,msg){
        if (!data.length) {
            myChart.showLoading({
                text: msg,
                showSpinner: false,
                textColor: 'black',
                maskColor: 'rgba(255, 255, 255, 1)',
                fontSize: '18px'
            })
        } else {
            myChart.hideLoading()
        }
    }

    function showNoMsg(show) {
        if (show) {
            $('#chart').html('');
            $("#noMsg").css('display', 'block');
        } else {
            $("#noMsg").css('display', 'none');
        }
    }

    let default_colors = ['#FCE172', '#649CFF', '#FB8D8D', '#7BE5C2', '#FF91BB', '#B496F1', '#74E2AB', '#6E7074', '#546570', '#C4CCD3'];


    $(document).ready(function () {
        requestData(true);
    });

    let getPieChartOption = function (plateData) {

        let data = plateData.data;
        let chartData = [];
        for (let item of data) {
            let chartItem = {};
            chartItem.value = item.amount;
            chartItem.name = item.name;
            chartData.push(chartItem);
        }
        let option = {
            title: {
                text: '账户余额',
                x: 'center',
                y: 'bottom'
            },
            tooltip: {
                trigger: 'item',
                formatter: function (v) {
                    return v.name + "：" + formatAmount(v.value) + "/万元"
                },
                position: 'inside'
            },
            label: {
                formatter: function (v) {
                    return v.name + "\n" + formatAmount(v.value)
                }
            },
            color: default_colors,
            series: [
                {
                    itemStyle: {
                        borderRadius: 5,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    name: '各版块余额',
                    type: 'pie',
                    radius: '50%',
                    data: chartData,
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }
            ]
        };

        return option;
    }

    let getLineOption = function (monthData) {
        let xData = [];
        let data = [];
        for (let month of monthData) {
            xData.push(month.month);
            data.push(month.amount);
        }

        option = {
            title: {
                text: '近6月融资余额变化',
                x: 'center',
                y: 'bottom'
            },
            grid: {
                left: '10%',
                right: '10%',
                bottom: '15%',
                top: '20%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: xData,
                axisLabel: {
                    rotate: 45
                }
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    color: '#649cff',
                    data: data,
                    type: 'bar'
                }
            ],
            tooltip: {
                trigger: 'axis',
                formatter: function (v) {
                    return v[0].axisValue + "：" + formatAmount(v[0].data) + "/万元"
                }

            },
        };
        return option;
    }

    let formatAmount = function (amount) {
        if (amount === null || amount === undefined || amount === '') {
            return '0.00';
        }
        let regular = /\d{1,3}(?=(\d{3})+$)/g;
        return amount.replace(/^(\d+)((\.\d+)?)$/, function (s, s1, s2) {
            return s1.replace(regular, "$&,") + s2;
        });


    }
</script>
</body>
</html>

