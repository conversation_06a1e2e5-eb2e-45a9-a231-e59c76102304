<%@ page import="com.fingard.app.delegate.framework.util.Constants" %>
<%@page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/include.jsp" %>

<%
    String entityId = String.valueOf(request.getSession().getAttribute(Constants.ORG_ID));
    String includeSubs = String.valueOf(request.getSession().getAttribute(Constants.INCLUDE_SUBS));
%>
<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <title>组织列表</title>

    <link href="${ctx}/css/mui.min.css" rel="stylesheet">
    <link href="${ctx}/css/frozen.css" rel="stylesheet"/>
    <link href="${ctx}/css/mainstyle.css" rel="stylesheet"/>
    <link href="${ctx}/css/myscroll.css" rel="stylesheet"/>
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

    <script src="${ctx}/js/mui.min.js"></script>
    <script src="${ctx}/js/zepto.min.js"></script>
    <script src="${ctx}/js/zepto.weui.min.js"></script>
    <script src="${ctx}/js/common.js?version=${version}"></script>
    <style>

        * {
            touch-action: pan-y;
            -webkit-box-sizing: unset;
            box-sizing: unset;
        }

        .mui-scroll-wrapper {
            margin-top: 120px;
        }

        li.mui-table-view-cell > p {
            padding: 4px;
            font-size: 15px;
        }

        li.mui-table-view-cell > img {
            position: absolute;
            right: 2%;
            top: 20%;
        }

        li.mui-table-view:after {
            height: 0px;
        }

        .mui-table-view {
            margin-bottom: 50px;
        }


    </style>
</head>

<body>

<ul class="mui-table-view">
    <li class="mui-table-view-cell">
        <div class="mui-input-row mui-search">
            <input id="search-input" type="search" class="mui-input-clear" placeholder="输入组织名称">
        </div>
    </li>
    <li class="mui-table-view-cell">
        是否包含下级组织
        <div id="switch-Toggle" class="mui-switch">
            <div class="mui-switch-handle"></div>
        </div>
    </li>
</ul>
<div style="width: 100%;height: 10px; background-color: #EFEFF4"></div>
<div id="pullrefresh" class="mui-scroll-wrapper">
    <div class="mui-scroll">
        <ul class="mui-table-view" id="data-list">

        </ul>
    </div>
</div>

<%@include file="/common/bottom_bar.jsp" %>

<script>

    let currOrgId = "<%=entityId%>"

    let list = [];
    $(document).ready(function () {
        var includeSubs = '<%=includeSubs%>';
        if (includeSubs == 'true') {
            $('#switch-Toggle').addClass('mui-active');
        }

        //mui 初始化
        mui.init({
            pullRefresh: {
                container: '#pullrefresh',
                down: {
                    contentrefresh: '',
                    callback: pulldownRefresh
                }
            }
        });

        resetData();

        //下拉刷新
        function pulldownRefresh() {
            setTimeout(function () {
                resetData();
            });
        }

        //点击列表切换组织
        mui('#data-list').on('tap', 'li', function () {

            var all = $(this).parent().find('li>img');
            var curr = $(this).find('img');
            var entityId = $(this).find('p').data('href');
            ajaxRequest('${ctx}/user/switchOrg.do', {
                orgid: entityId
            }, function (data) {
                all.css('display', 'none');
                curr.css('display', 'block');
                currOrgId = data.result.entityId;
                mui.toast('切换组织成功');
                location.reload();

            });
        });

        //是否包含下级组织切换
        document.getElementById('switch-Toggle').addEventListener('toggle', function (e) {

            var isInclude = e.detail.isActive;
            ajaxRequest('${ctx}/user/isIncludeSubOrg.do', {
                isIncludeSubOrg: isInclude
            }, function (data) {
                mui.toast('切换成功！');
            })
        });

        //输入框的值改变时触发
        /*
        $("#search-input").on("input", function (e) {
            //获取input输入的值
            datafilter(e.delegateTarget.value);
        });

         */


        $('#search-input').bind('input propertychange', function (e) {
            datafilter($(this).val());
        });


        mui(".mui-icon-clear")[0].addEventListener('tap', function () {
            resetData();
        });


        // 底部tab切换
        $('.ui-list li,.ui-tiled li').click(function () {
            if ($(this).data('href')) {
                location.href = $(this).data('href');
            }
        });
        $('.ui-header .ui-btn').click(function () {
            location.href = '${ctx}/modules/system/flowTypeList.jsp';
        });

    });


    function datafilter(orgName) {
        if (orgName !== '' || orgName.length > 0) {
            var listview = $('#data-list');
            listview.html('');
            for (var i = 0; i < list.length; i++) {

                if (list[i].entityName.indexOf(orgName) !== -1) {

                    var entityName = list[i].entityName;

                    if ('${customer}' === 'ljdc') {
                        // 朗基地产项目不需要组织后的角色名称
                        if (entityName.indexOf('(') !== -1) {
                            entityName = entityName.substr(0, entityName.indexOf('('));
                        }
                    }
                    var html;
                    if (list[i].entityId === currOrgId) {
                        html = '<li class="mui-table-view-cell">' +
                            '<p data-href="' + list[i].entityId + '">' + entityName + '</p>' +
                            '<img src="${ctx}/img/selected.png"/>' +
                            '</li>';
                    } else {
                        html = '<li class="mui-table-view-cell">' +
                            '<p data-href="' + list[i].entityId + '">' + entityName + '</p>' +
                            '<img src="${ctx}/img/selected.png" style="display: none"/>' +
                            '</li>';
                    }
                    listview.append(html);
                }
            }
        } else {
            resetData();
        }
    }

    function resetData() {
        $('#data-list').html("");
        $('#search-input').val('');

        ajaxRequest('${ctx}/user/getOperateableOrg.do', {}, function (data) {

            mui('#pullrefresh').pullRefresh().endPulldownToRefresh();

            var count = data.result.totalNum;
            var listData = data.result.data;
            list = listData;
            if (count <= 0) {
                mui.toast('暂无数据');
                return;
            }

            var listview = $('#data-list');


            for (var i = 0; i < count; i++) {
                var html;

                var entityName = listData[i].entityName;

                if ('${customer}' === 'ljdc') {
                    // 朗基地产项目不需要组织后的角色名称
                    if (entityName.indexOf('(') !== -1) {
                        entityName = entityName.substr(0, entityName.indexOf('('));
                    }
                }

                if (listData[i].entityId === currOrgId) {
                    html = '<li class="mui-table-view-cell">' +
                        '<p data-href="' + listData[i].entityId + '">' + entityName + '</p>' +
                        '<img src="${ctx}/img/selected.png"/>' +
                        '</li>';
                } else {
                    html = '<li class="mui-table-view-cell">' +
                        '<p data-href="' + listData[i].entityId + '">' + entityName + '</p>' +
                        '<img src="${ctx}/img/selected.png" style="display: none"/>' +
                        '</li>';
                }
                listview.append(html);
            }
        });
    };

    function ajaxRequest(requestUrl, params, callback) {
        $.ajax({
            cache: true,
            type: 'POST',
            url: requestUrl,
            data: params,
            async: true,
            dataType: 'json',
            error: function (data) {
                mui.toast('网络连接异常');
            },
            success: function (data) {
                if (data.successful == true || 'true' == data.successful) {
                    callback(data);
                } else {
                    mui.toast('错误：' + data.message);
                }
            }
        });
    };
</script>


</body>

</html>
