<%@ page import="com.fingard.app.delegate.framework.util.Constants" %>
<%@page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/include.jsp" %>
<%
    String orgName = String.valueOf(request.getSession().getAttribute(Constants.ORG_NAME));
    String name = String.valueOf(request.getSession().getAttribute(Constants.REAL_NAME));

%>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <title>个人信息</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.1/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="card" style="width: 18rem;">
    <img src="../../img/logo.png" class="card-img-top" alt="...">
    <div class="card-body">
        <h5 class="card-title"><%=name%>></h5>
        <p class="card-text"><%=orgName%></p>
        <a onclick="go_switch()" class="btn btn-primary">切换组织</a>
    </div>
</div>
<%@include file="/common/bottom_bar.jsp" %>
<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.1/js/bootstrap.min.js"></script>
<script>
    let go_switch = function (){

        location.replace("${ctx}/modules2.4/system/organizationSwitch.jsp?selectType=oriOrg")
    }

</script>
</body>
</html>
