<%@ page import="com.fingard.app.delegate.framework.util.Constants" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>
<%
    String currentOrg = String.valueOf(request.getSession().getAttribute(Constants.ORG_NAME));
    String currentId = String.valueOf(request.getSession().getAttribute(Constants.ORG_ID));

%>
<c:set var="currentOrg" value="<%=currentOrg%>"/>
<c:set var="currentId" value="<%=currentId%>"/>

<!DOCTYPE html>
<html>

<head>

    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>组织切换</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Raleway:200,300,400,600,700">
    <link rel="stylesheet" href="${ctx}/css/demo.css?ver=3.3.1">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>
    <style>
        .hc-nav-trigger {
            left: 20px;
        }

        .hc-nav-trigger span,
        .hc-nav-trigger span::before,
        .hc-nav-trigger span::after {
            background: #fff;
        }

        body > div.weui-dialog.weui-dialog--visible > div.weui-dialog__hd > strong {
            color: gray;
        }

    </style>

    <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/knockout/3.4.2/knockout-min.js"></script>
    <script src="${ctx}/js/hc-offcanvas-nav.js?ver=3.4.1"></script>
    <script src="${ctx}/js/zepto.weui.min.js"></script>

    <script src="${ctx}/js/common.js?version=${version}"></script>
</head>

<body>

<div id="container">
    <h3 style="margin-top: 100px">当前组织</h3>
    <h1 style="margin-top: 20px; color: #333333" id="currentOrg">${currentOrg}</h1>
    <nav id="nav">
        <ul>
            <li>
                <a>组织</a>
                <ul data-bind="template: { name: 'page-template', data: {pages: pages}}"></ul>
            </li>
        </ul>
    </nav>

    <%@include file='/common/bottom_bar.jsp' %>


    <script type="text/html" id="page-template">
        <!-- ko foreach: pages -->
        <li>
            <a data-bind="click: $root.clicked">
                <span data-bind="text: title"></span>
            </a>
            <!-- ko if: pages().length > 0 -->
            <ul data-bind="template: { name: 'page-template', data: {pages: pages}}"></ul>
            <!-- /ko -->
        </li>
        <!-- /ko -->
    </script>

    <script>
        var pageDataStr = sessionStorage.getItem("DATA");
        var currentId = '${currentId}';
        (function ($) {
                var pageData = JSON.parse(pageDataStr);
                var selectName;
                var selectId;
                var selectOpTag;
                var Nav;
                var PageModel = function (page) {
                    var self = this;

                    self.id = page.id;
                    self.title = page.name;
                    self.pages = ko.observableArray();
                    self.operable = page.operable;

                    self.pages(ko.utils.arrayMap(page.pages, function (page) {
                        return new PageModel(page);
                    }));
                };

                var NavigationModel = function (pages) {
                    var self = this;

                    self.pages = ko.observableArray();

                    self.pages(ko.utils.arrayMap(pages, function (page) {
                        return new PageModel(page);
                    }));

                    self.clicked = function (data) {
                        selectName = data.title;
                        selectId = data.id;
                        selectOpTag = data.operable;
                    }.bind(self);

                };

                var switchOrg = function (orgId) {

                    $.showLoading("处理中...");
                    var url = '${ctx}/user/switchOrg.do?orgid=' + orgId;
                    var xhr = new XMLHttpRequest();
                    xhr.onreadystatechange = function () {
                        $.hideLoading();
                        if (xhr.readyState === 4) {
                            if (xhr.status === 200) {
                                if (xhr.response) {
                                    var resp = JSON.parse(xhr.response);
                                    if (resp.successful === 'true') {
                                        $.alert({
                                            title: '切换组织',
                                            text: '切换成功',
                                            onOK: function () {
                                                $('#currentOrg').html(resp.result.entityName)
                                                currentId = resp.result.entityId;
                                                selectName = undefined;

                                            }
                                        });
                                    }
                                }
                            }
                        }
                    };
                    xhr.open("get", url);
                    xhr.send(null);

                };


                ko.applyBindings(new NavigationModel(pageData));
                Nav = $('#nav').hcOffcanvasNav({
                    maxWidth: false,
                    levelTitles: true,
                    closeOnClick: true,
                    levelOpen: 'overlap', // overlap, expand, none/false
                    levelTitles: true,


                    onClose: function () {
                        if (selectName === undefined) {
                            return;
                        }

                        if (currentId === selectId) {
                            return;
                        }

                        if (selectOpTag === '0') {
                            // 切换的组织为不可操作的组织
                            $.alert({
                                title: '无法操作',
                                text: '您没有操作【' + selectName + '】的权限',
                                onOK: function () {


                                }
                            });
                        } else {
                            $.confirm({
                                title: '切换组织',
                                text: '是否确认将换组织切换为：' + selectName,
                                onOK: function () {
                                    switchOrg(selectId);
                                },
                                onCancel: function () {
                                }
                            });
                        }
                    }
                });


            }
        )(jQuery);


    </script>

</div>

</body>
</html>
