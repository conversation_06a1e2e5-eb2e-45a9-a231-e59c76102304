<%@page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/include.jsp" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <meta content="no-cache">
    <meta content="0">
    <title>计划项目明细</title>
    <link rel="stylesheet" href="../../css/weui.min.css">
    <link rel="stylesheet" href="../../css/weuix.min.css">

    <style>

        body {
            background: #e5e5e5;
        }

        .weui-cells {
            margin-top: 10px;
        }

        .note-label {
            margin-right: 40px;
            white-space: nowrap;
        }

        .note-value {
            height: auto;
            word-wrap: break-word;
            word-break: break-all;
            overflow: hidden;
        }

        .weui-cell__bd {
            min-width: 80px;
            margin-right: 10px;
        }
    </style>

</head>

<body>

<div id="template" style="width: 100%;height: 44px;">
    <ul class="collapse">
        <li onclick="goto_template_page()">
            <div class="weui-flex ">
                <p class="weui-flex__item">明细下载</p>
                <i class="icon icon-108"></i>
            </div>

        </li>
    </ul>
</div>

<div id="list">

</div>
<script src="../../js/zepto.min.js"></script>
<script src="../../js/zepto.weui.min.js"></script>
<script src="../../js/iscroll-lite.min.js"></script>
<script src="../../js/common.js?version=${version}"></script>

<script>

    let budgetItem = JSON.parse(sessionStorage.getItem("BUDGET_ITEM"));
    let budgetItemTemplateStr = sessionStorage.getItem("BUDGET_ITEM_TEMPLATE");
    let budgetItemTemplate = null;

    if (budgetItemTemplateStr !== 'undefined' && budgetItemTemplateStr !== undefined && budgetItemTemplateStr != null) {
        budgetItemTemplate = JSON.parse(budgetItemTemplateStr);
    }
    console.log(budgetItem);

    $(document).ready(function () {

        if (budgetItemTemplate == null || budgetItemTemplate.length === 0) {
            $('#template').hide();
        }

        if (budgetItem.length > 0 && Object.keys(budgetItem[0]).length === 4) {
            let list = $('#list');
            for (let i = 0; i < budgetItem.length; i++) {
                let cell = $(' <div  class="weui-cells">\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>金额/元</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>' + budgetItem[i].budgetAmount + '</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>计划项目</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>' + budgetItem[i].budgetItem + '</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>计划期间</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>' + budgetItem[i].budgetPeriod + '</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p class="note-label">备注</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p class="note-value">' + budgetItem[i].memo + '</p></div>\n' +
                    '        </div>\n' +
                    '    </div>');

                var clickable;
                $(".cell").on('touchstart', function (event) {
                    clickable = true;
                });
                $(".cell").on('touchmove', function (event) {
                    clickable = false;
                });
                $(".cell").on('click', function (event) {
                    event.preventDefault();
                    if (!clickable) {
                        return;
                    }
                    //可以钻取。
                    let itemId = budgetItem[i].itemId;
                    let budgetId = budgetItem[i].budgetItemId;
                    sessionStorage.setItem('itemId', itemId);
                    sessionStorage.setItem('budgetId', budgetId);
                    location.href = "jsjs_fund_plan_detail.jsp";


                });
                list.append(cell);

            }
        }

        if (budgetItem.length > 0 && Object.keys(budgetItem[0]).length >= 5 && Object.keys(budgetItem[0]).length < 9) {
            let list = $('#list');
            for (let i = 0; i < budgetItem.length; i++) {
                let cell = $(
                    '<div  class="weui-cells">\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>计划项目</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>' + budgetItem[i].budgetItem + '</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>调整前金额/元</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>' + budgetItem[i].beforeAdjustAmount + '</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>调整金额/元</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>' + budgetItem[i].adjustAmount + '</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>调整后金额/元</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>' + budgetItem[i].afterAdjustAmount + '</p></div>\n' +
                    '        </div>\n' +

                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>计划期间</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>' + budgetItem[i].budgetPeriod + '</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>备注</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>' + budgetItem[i].memo + '</p></div>\n' +
                    '        </div>\n' +
                    '    </div>');
                list.append(cell);

            }
        }
        if (budgetItem.length > 0 && Object.keys(budgetItem[0]).length > 9) {

            /**
             *itemParams.put("budgetSchemeName", "BUDGETSCHEMENAME");
             itemParams.put("categoryName", "CATEGORYNAME");
             itemParams.put("beginBalance", "BEGINBALANCE");
             itemParams.put("draftBeginBalance", "DRAFTSBEGINBALANCE");
             itemParams.put("currencyBeginBalance", "CURRENCYBEGINBALANCE");
             itemParams.put("endBalance", "ENDBALANCE");
             itemParams.put("regulatedEndBalance", "REGULATEDENDBALANCE");
             itemParams.put("availableEndBalance", "AVAILABLEENDBALANCE");
             itemParams.put("revenue", "REVENUE");
             itemParams.put("expenditure", "EXPENDITURE");
             * @type {jQuery|HTMLElement|*|l}
             */
            let list = $('#list');
            for (let i = 0; i < budgetItem.length; i++) {
                let cell = $(
                    '<div  class="weui-cells">\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>' + budgetItem[i].budgetSchemeName + '-' + budgetItem[i].categoryName + '</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p></p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>期初余额/元</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>' + budgetItem[i].beginBalance + '</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>期初监管余额/元</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>' + budgetItem[i].draftBeginBalance + '</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>期初可用余额/元</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>' + budgetItem[i].currencyBeginBalance + '</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>收入总额/元</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>' + budgetItem[i].revenue + '</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>支出总额/元</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>' + budgetItem[i].expenditure + '</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>期末余额/元</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>' + budgetItem[i].endBalance + '</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>期末监管余额/元</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>' + budgetItem[i].regulatedEndBalance + '</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>期末可用余额/元</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>' + budgetItem[i].availableEndBalance + '</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>备注</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>' + budgetItem[i].memo + '</p></div>\n' +
                    '        </div>\n' +
                    '    </div>');
                list.append(cell);

            }
        }

    });

    function goto_template_page() {

        location.href = 'budget_item_template.jsp';
    }

</script>


</body>

</html>
