<%@page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/include.jsp" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <meta content="no-cache">
    <meta content="0">
    <title>下拨明细</title>
    <link rel="stylesheet" href="../../css/weui.min.css">
    <link rel="stylesheet" href="../../css/weuix.min.css">

    <style>

        body {
            background: #e5e5e5;
        }
        .weui-cells {
            margin-top: 10px;
        }
        .note-label{
            margin-right: 40px;
            white-space: nowrap;
        }
        .note-value {
            height: auto;
            word-wrap:break-word;
            word-break:break-all;
            overflow: hidden;
        }
        .weui-cell__bd {
            min-width: 80px;
            margin-right: 10px;
        }
    </style>

</head>

<body>


<div id="list">

</div>
<script src="../../js/zepto.min.js"></script>
<script src="../../js/zepto.weui.min.js"></script>
<script src="../../js/iscroll-lite.min.js"></script>
<script src="../../js/common.js?version=${version}"></script>

<script>

    let budgetItem = JSON.parse(sessionStorage.getItem("FUND_DIAL_ITEM"));
    console.log(budgetItem);

    $(document).ready(function () {

        if (budgetItem.length > 0 ) {
            let list = $('#list');
            for (let i = 0; i < budgetItem.length; i++) {
                let cell = $(' <div  class="weui-cells">\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>拨入金额</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>'+budgetItem[i].amount+'</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>拨入组织</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>'+budgetItem[i].allocationInOrgName+'</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>银行</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>'+budgetItem[i].bankName+'</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>拨入账户</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>'+budgetItem[i].allocationInAccount+'</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>币种</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>'+budgetItem[i].currCode+'</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p class="note-label">用途</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p class="note-value">'+budgetItem[i].purpose+'</p></div>\n' +
                    '        </div>\n' +
                    '    </div>');
                list.append(cell);

            }
        }


    });

</script>


</body>

</html>
