<%@ page import="com.fingard.app.delegate.helper.EndpointProperties" %>
<%@ page import="cn.hutool.core.collection.CollectionUtil" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>
<%
    String openFlowCode = CollectionUtil.join(EndpointProperties.getFlowTypeList(), ",");
    String custoemrName = MyProperties.getMyPropertiesInstance().getProperty("currentCustomerName");
    if (StrUtil.isNullOrUndefined(openFlowCode)) {
        openFlowCode = StrUtil.EMPTY;
    }
%>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <meta content="no-cache">
    <meta content="0">
    <title>流程类别列表</title>
    <link rel="stylesheet" href="${ctx}/css/weui.min.css">
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css">

    <style>

        .weui-cells {
            margin-top: 0;
        }

        .weui-cell {
            padding: 15px 20px;
        }

        .weui-msgbox p {
            padding: 80% 15px;
        }

        .weui-navbar + .weui-tab__bd {
            padding-bottom: 55px;
        }

        .no-permission {
            position: relative;
            display: none;
        }

        .no-permission img {
            width: 95px;
            height: 95px;
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -50px;
            top: 50px;
        }

        .no-permission span {
            display: inline-block;
            position: absolute;
            margin-left: 50%;
            margin-top: 50%;
            left: -32px;
            top: 145px;
            color: #999999;
            font-size: 14px;
        }


    </style>
</head>

<body>

<div class="weui-tab">

    <div class="weui-navbar " style="position: fixed; top: 0;">
        <a id="todo-tab" class="weui-navbar__item weui-bar__item--on" href="#todo__list">
            待办
        </a>
        <a id="done-tab" class="weui-navbar__item" href="#done__list">
            已办
        </a>
    </div>

    <!-- 其他内容 -->
    <div class="weui-tab__bd">
        <div id="todo__list" class="weui-tab__bd-item weui-tab__bd-item--active"
             style="position: relative;margin-top: -100px;height: calc(100% + 50px);-webkit-overflow-scrolling: touch;">
            <div class="weui-pull-to-refresh__layer" style="background: #ffffff">
                <div class="weui-pull-to-refresh__arrow"></div>
                <div class="weui-pull-to-refresh__preloader"></div>
                <div class="down">下拉刷新</div>
                <div class="up">释放刷新</div>
                <div class="refresh">正在刷新</div>
            </div>
            <div class="weui-cells">


            </div>
            <div class="no-permission" style="display: none;">
                <img src="${ctx}/img/report/<EMAIL>" alt="暂无权限">
                <span>暂无权限</span>
            </div>
        </div>
        <div id="done__list" class="weui-tab__bd-item"
             style="position: relative;margin-top: -100px;height: calc(100% + 50px);-webkit-overflow-scrolling: touch;">
            <div class="weui-pull-to-refresh__layer" style="background: #ffffff">
                <div class="weui-pull-to-refresh__arrow"></div>
                <div class="weui-pull-to-refresh__preloader"></div>
                <div class="down">下拉刷新</div>
                <div class="up">释放刷新</div>
                <div class="refresh">正在刷新</div>
            </div>
            <div class="weui-cells">


            </div>
            <div class="no-permission" style="display: none;">
                <img src="${ctx}/img/report/<EMAIL>" alt="暂无权限">
                <span>暂无权限</span>
            </div>
        </div>
    </div>
</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/iscroll-lite.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>

<script>

    let openFLowCode = '<%=openFlowCode%>'.split(',');
    let customerName = '<%=custoemrName%>';
    sessionStorage.setItem("customerName", customerName);
    $(document).ready(function () {


        let todoTag = sessionStorage.getItem("TODO");
        if (todoTag == null || todoTag == undefined) {
            todoTag = TODO_TAG;
        }

        var isPageHide = false;
        window.addEventListener('pageshow', function () {
            if (isPageHide) {
                window.location.reload();
            }
        });
        window.addEventListener('pagehide', function () {
            isPageHide = true;
        });
        let todo_list = $('#todo__list');
        let done_list = $('#done__list');

        let todo_tab = $('#todo-tab');
        let done_tab = $('#done-tab');

        if (todoTag == DONE_TAG) {
            $('#todo-tab').removeClass('weui-bar__item--on')
            $('#done-tab').addClass('weui-bar__item--on')
            todo_list.removeClass('weui-tab__bd-item--active');
            done_list.addClass('weui-tab__bd-item--active');

        }
        initData(function () {

        }, todoTag);
        todo_list.pullToRefresh().on('pull-to-refresh', function (done) {
            initData(function () {
            }, TODO_TAG);
        });
        done_list.pullToRefresh().on('pull-to-refresh', function (done) {
            initData(function () {
            }, DONE_TAG);
        });

        todo_tab.on('click', function () {

            initData(function () {
            }, TODO_TAG);

            todoTag = TODO_TAG;
            sessionStorage.setItem("TODO", todoTag);

        });

        done_tab.on('click', function () {
            initData(function () {
            }, DONE_TAG);

            todoTag = DONE_TAG;
            sessionStorage.setItem("TODO", todoTag);

        });


        $(document.body).on('touchmove', function (e) {
            e.preventDefault();
        })

    });


    let toPage = function (cellData) {
        console.log(cellData);
        sessionStorage.setItem('SUB_FLOW_PARAMS', JSON.stringify(cellData));
        sessionStorage.setItem('ZJFK', 'false');
        sessionStorage.setItem("TODO", cellData.dealState);
        sessionStorage.setItem("FLOWTYPEID", cellData.flowTypeId);

        if (cellData.totalNum == 0) {
            $.toast("该流程暂无待办事项", "cancel");
        } else {
            if (cellData.flowTypeId !== 'ZJFK') {
                location.href = 'flowList.jsp';
            } else {
                location.href = 'flowListSingle.jsp';

            }
        }


    };

    let initData = function (afterSuccess, tag) {
        delegate.ready(function (paramsData) {
            if (paramsData.result.permissionCode.indexOf(PERMISSION_CODE_FLOW) === -1) {
                $('.no-permission').css('display', 'block');
            } else {
                $.showLoading("加载中...")
                let params = {};
                params.dealState = tag;
                ajaxRequest('/saasflow/getFullFlowTypeList.do', params, function (data) {
                    let cells_todo, cells_done;

                    if (tag == TODO_TAG) {
                        cells_todo = $('#todo__list').find('.weui-cells');
                        $('#todo__list').pullToRefreshDone();
                        cells_todo.html('');
                        let todo_list = data.result.todoList;
                        if (data.message === '查询记录为空' || todo_list.length === 0) {
                            cells_todo.append('<div class="weui-msgbox" style="height: 50%; line-height: 50%">\n' +
                                '    <p>\n' +
                                '        <i class="weui-icon-info-circle"></i>查询记录为空\n' +
                                '    </p>\n' +
                                '</div>');
                        }

                        if (todo_list.length === 0) {
                            return;
                        }


                        for (let i = 0; i < todo_list.length; i++) {
                            let cellData = todo_list[i];
                            let cell;
                            let obj = JSON.stringify(cellData);
                            if (openFLowCode.indexOf(cellData.flowTypeId) !== -1) {
                                let flowDesc;
                                if (cellData.totalNum === '0') {
                                    flowDesc = "暂无待办事项";
                                } else {
                                    flowDesc = '您有' + cellData.totalNum + '笔' + cellData.flowTypeName + '，请及时处理';
                                }
                                let img = cellData.totalNum === '0' ? (iconImg[cellData.flowTypeId] === undefined ? iconImg.ZHSQ : iconImg[cellData.flowTypeId]) : (iconImgPoint[cellData.flowTypeId] === undefined ? iconImgPoint.ZHSQ : iconImgPoint[cellData.flowTypeId]);
                                cell = $("<a class='weui-cell weui-cell_access' href='javascript:toPage(" + obj + ");'>" +
                                    '<div class="weui-cell__hd"><img src="${ctx}/img/czdn/flow/' + img + '" style="width: 50px;height: 50px; margin-right: 10px;">' +
                                    '</div>' +
                                    '    <div class="weui-cell__bd">' +
                                    '      <h4>' + cellData.flowTypeName + '</h4>' +
                                    '      <p style="color: #BDBDBD; font-size: 13px" >' + flowDesc + '</p>' +

                                    '    </div>' +
                                    '<div class="weui-cell__ft"></div>' +
                                    '  </a>');


                                cells_todo.append(cell);
                            }
                        }


                    } else {
                        cells_done = $('#done__list').find('.weui-cells');
                        $('#done__list').pullToRefreshDone();
                        cells_done.html('');
                        let done_list = data.result.doneList;
                        if (data.message === '查询记录为空' || done_list.length === 0) {
                            cells_done.append('<div class="weui-msgbox" style="height: 50%; line-height: 50%">\n' +
                                '    <p>\n' +
                                '        <i class="weui-icon-info-circle"></i>查询记录为空\n' +
                                '    </p>\n' +
                                '</div>');
                        }
                        if (done_list.length === 0) {
                            return;
                        }


                        for (let i = 0; i < done_list.length; i++) {
                            let cellData = done_list[i];
                            let cell;
                            let obj = JSON.stringify(cellData);
                            if (openFLowCode.indexOf(cellData.flowTypeId) !== -1) {
                                let img = iconImgDoneFlow[cellData.flowTypeId]===undefined?iconImgDoneFlow.ZHSQ:iconImgDoneFlow[cellData.flowTypeId];
                                cell = $("<a class='weui-cell weui-cell_access' href='javascript:toPage(" + obj + ");'>" +
                                    '<div class="weui-cell__hd"><img src="${ctx}/img/czdn/flow/' + img + '" style="width: 40px;height: 40px; margin-right: 10px;">' +
                                    '</div>' +

                                    '    <div class="weui-cell__bd">' +
                                    '      <h4>' + cellData.flowTypeName + '</h4>' +
                                    '    </div>' +
                                    '    <div class="weui-cell__ft">' +
                                    '<span style="margin-left: 5px;">' + cellData.totalNum + '</span>' +
                                    '</div>' +
                                    '  </a>');

                                cells_done.append(cell);
                            }
                        }

                    }

                    if (afterSuccess !== undefined && afterSuccess != null) {
                        afterSuccess();
                    }
                });
            }

        })
    }
</script>


</body>

</html>
