<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>业务排程明细</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

    <style>
        html,
        body {
        }

        .weui-media-box__title {
            font-size: 1rem;
            font-weight: bold;
        }

        body {
            background: #eee;
            font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
            font-size: 14px;
            color: #000;
            margin: 0;
            padding: 0;
        }

        .weui-cells {
            margin-top: 0;
            margin-bottom: 4.25em;
            font-size: 15px;
        }

        .weui-media-box__desc {
            color: #0d0d0d;
            line-height: 1.8em;
        }

        .weui-navbar__item.weui-bar__item_on {
            background-color: white;
            color: #0d0d0d;
            font-size: 0.8rem;
        }

        .query_dialog {
            border-radius: 5px;
            border-color: #c1c1c1;
            top: 10%;
            left: 5%;
            position: absolute;
            background-color: whitesmoke;
            width: 80%;
            z-index: 2;
            padding: 20px;
        }

        .weui-btn {
            font-size: 0.65rem;
        }

    </style>
</head>
<body>

<div class="container" style="margin-top: 50px">
    <div class="content" style="height:100%;overflow: auto;-webkit-overflow-scrolling: touch;">
        <div class="weui-pull-to-refresh__layer">
            <div class='weui-pull-to-refresh__arrow'></div>
            <div class='weui-pull-to-refresh__preloader'></div>
            <div class="down">下拉刷新</div>
            <div class="up">释放刷新</div>
            <div class="refresh">正在刷新</div>
        </div>
        <div class="weui-cells">


        </div>
    </div>
</div>

<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>

<script>


    const pageSize = 20;
    var onTop = true;

    window.onscroll = function () {
        var scrollT = document.documentElement.scrollTop || document.body.scrollTop; //滚动条的垂直偏移
        console.log(scrollT)
        onTop = (scrollT === 0);

    };

    let getData = function () {

        currentPageNum = 2;
        let url = '/jsjd/getFundPlanBusinessPlanDetail.do';
        let params = {};
        params.pageNum = 1;
        params.pageSize = pageSize;
        params.itemId = sessionStorage.getItem("itemId");
        params.budgetId = sessionStorage.getItem("budgetId");

        ajaxRequest(url, params, function (response) {
            console.log(response);
            content.pullToRefreshDone();

            let cells = $('.weui-cells');
            cells.html('');
            if (response.result.totalNum === 0) {
                // 没有预警数据
                cells.append(
                    '<div class="weui-msgbox" style="position: fixed; line-height: 50%">' +
                    '    <p>' +
                    '        <i class="weui-icon-info-circle"></i>暂无数据' +
                    '    </p>' +
                    '</div>');
            } else {

                let data = response.result.data;
                let refreshHeader = '<div class="weui-pull-to-refresh__layer">\n' +
                    '                <div class=\'weui-pull-to-refresh__arrow\'></div>\n' +
                    '                <div class=\'weui-pull-to-refresh__preloader\'></div>\n' +
                    '                <div class="down">下拉刷新</div>\n' +
                    '                <div class="up">释放刷新</div>\n' +
                    '                <div class="refresh">正在刷新</div>\n' +
                    '            </div>';
                // cells.append(refreshHeader);
                for (let i = 0; i < data.length; i++) {


                    // let cell = $(' <div class="weui-cell">\n' +
                    //     '        <div class="weui-cell__bd">\n' +
                    //     '            <p class="msg-title">' + data[i].amount + '</p>\n' +
                    //     '            <p class="msg-content">' + data[i].materialName + '</p>\n' +
                    //     '        </div>\n' +
                    //     '    </div>');

                    let cell = $('<div role="option" class="weui-media-box weui-media-box_text">' +
                        '<strong class="weui-media-box__title">' + data[i].amount + '（元）</strong>' +
                        ' <p class="weui-media-box__desc">供应商/客户：' + data[i].supplier + '</p>' +
                        ' <p class="weui-media-box__desc">现金金额：' + data[i].cashAmount + '</p>' +
                        ' <p class="weui-media-box__desc">承兑金额：' + data[i].draftAmount + '</p>' +
                        ' <p class="weui-media-box__desc">币种：' + data[i].currCode + '</p>' +
                        ' <p class="weui-media-box__desc">收支方向：' + data[i].moneyWay + '</p>' +
                        '</div>')

                    cells.append(cell);


                }

            }

            let currDataSize = $('.weui-cells').find('.weui-cell').length;
            if (currDataSize >= pageSize) {
                cells.append('<div class="weui-panel__ft">\n' +
                    '            <a href="javascript:getMore();" class="weui-cell weui-cell_access weui-cell_link" style="text-align: center">\n' +
                    '                <div class="weui-cell__bd" id="getmore">查看更多<i id="loading" class="weui-loading"></i></div>\n' +
                    '            </a>\n' +
                    '        </div>');

                $('#loading').hide();
            }
        })


    };

    let click = true;
    let currentPageNum = 2;

    let getMore = function () {

        $('#no-more-tip').remove();
        $('#loading').show();

        let url = '/jsjd/getFundPlanBusinessPlanDetail.do';
        let params = {};
        params.pageNum = 1;
        params.pageSize = pageSize;
        params.itemId = sessionStorage.getItem("itemId");
        params.budgetId = sessionStorage.getItem("budgetId");
        currentPageNum++;

        ajaxRequest(url, params, function (response) {
            $('#loading').hide();

            console.log(response);
            let more = $('.weui-panel__ft');
            if (response.result.totalNum === 0) {
                currentPageNum--;
                $('.weui-cells').append('<div id="no-more-tip" class="weui-loadmore weui-loadmore_line">\n' +
                    '    <span class="weui-loadmore__tips">暂无更多数据</span>\n' +
                    '</div>\n');
            } else {

                let data = response.result.data;
                for (let i = 0; i < data.length; i++) {


                    let cell = $('<div role="option" class="weui-media-box weui-media-box_text">' +
                        '<strong class="weui-media-box__title">' + data[i].amount + '（元）</strong>' +
                        ' <p class="weui-media-box__desc">供应商/客户：' + data[i].supplier + '</p>' +
                        ' <p class="weui-media-box__desc">现金金额：' + data[i].cashAmount + '</p>' +
                        ' <p class="weui-media-box__desc">承兑金额：' + data[i].draftAmount + '</p>' +
                        ' <p class="weui-media-box__desc">币种：' + data[i].currCode + '</p>' +
                        ' <p class="weui-media-box__desc">收支方向：' + data[i].moneyWay + '</p>' +
                        '</div>')

                    more.before(cell);
                }

            }

        })


    };

    let content = $('.content');

    let canRefresh = true;

    $(document).ready(function () {
        console.log("has ready");


        getData();

        content.on('touchstart', function () {
            canRefresh = true;
        });

        content.on('touchmove', function () {
            canRefresh = false;
        });

        content.on('touchend', function () {
            canRefresh = true;
        });
        content.pullToRefresh({
            distance: 50,
            onRefresh: function () {
                if (onTop) {
                    $('.weui-msgbox').hide();

                    setTimeout(function () {
                        getData();


                    }, 500);
                }
            }
        });
    });


    overscroll(document.getElementsByClassName('content')[0]);


</script>


</body>
</html>

