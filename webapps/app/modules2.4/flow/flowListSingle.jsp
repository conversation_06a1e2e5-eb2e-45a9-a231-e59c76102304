<%@page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/include.jsp" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <meta content="no-cache">
    <meta content="0">
    <title>流程概要信息</title>
    <link rel="stylesheet" href="../../css/weui.min.css">
    <link rel="stylesheet" href="../../css/weuix.min.css">

    <style>

        body {
            background-color: #F2F4F6;
        }

        .weui-dialog {
            max-height: 400px;
            overflow: auto;
        }
        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a {
            color: #000;
            font-weight: bold;
        }

        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a:after {
            background-color: transparent;
        }

        .weui-navigator-list li a {
            line-height: 50px;
            font-size: 15px;
        }

        .weui-cell {
            padding: 15px 15px;
        }

        .weui-msgbox p {
            padding: 80% 15px;
        }

        /*批量审批区域样式*/
        .weui-footer_fixed-bottom {
            bottom: 0;
            background-color: white;
            z-index: 9;
        }


        .weui-agree__checkbox {
            width: 15px;
            height: 15px;
        }
        .weui-agree__text {
            font-size: 18px;
        }
        .weui-agree__checkbox:checked:before{
            font-size: 15px;
        }

        .weui-agree {
            text-align: left;
            text-align: -moz-left;
            text-align: -webkit-left;
            padding: .5em 25px;
        }
        .bg-blue {
            background-color: #54D6AD;
        }

        .bg-red {
            background-color: #FF7979;
        }


        .weui-agree__checkbox:checked:before {
            color: #54D6AD;
        }

        .weui-cells_checkbox .weui-check:checked+.weui-icon-checked:before {
            color: #54D6AD;
        }

    </style>
</head>
<body>

<div id="tagnav" class="weui-navigator weui-navigator-wrapper" style="position: fixed; top: 0; background-color: white; margin-bottom: 10px;z-index: 999;">
    <ul class="weui-navigator-list">

    </ul>

</div>

<div id="container"  style="overflow: scroll;-webkit-overflow-scrolling: touch;">
    <div class="weui-pull-to-refresh__layer" style="background: #ffffff">
        <div class="weui-pull-to-refresh__arrow"></div>
        <div class="weui-pull-to-refresh__preloader"></div>
        <div class="down">下拉刷新</div>
        <div class="up">释放刷新</div>
        <div class="refresh">正在刷新</div>
    </div>
    <div class="weui-cells weui-cells_checkbox" style="font-size: 15px">
    </div>
</div>

<!--批量审批区域html 开始-->
<div id="weuiAgree-bottom" class="weui-footer weui-footer_fixed-bottom" style="display: none">

    <label for="weuiAgree" class="weui-agree">
        <input onchange="selectAll()" id="weuiAgree" class="weui-agree__checkbox" type="checkbox">
        <span class="weui-agree__text">
            已选(<span id="weui-agree__text_tip">0</span>)
            </span>
        <p id="amount-summary" style="color: #FF7979"></p>
    </label>
    <p class="weui-footer__links" style="margin-top: 10px; margin-bottom: 10px">

        <button id="batch" onclick="javascript:openBatch();" class="weui-btn weui-btn_inline weui-btn_default">批量
        </button>

        <button id="batch-agree" onclick="javascript:beforeSendBatchApprove(true,function() {
                if('${customer}'==='leading'){
                location.href = 'leading/refresh.jsp';
                }else {
                initData(currentTypeId)
                }        });"
                class="weui-btn weui-btn_inline bg-blue">同意
        </button>
        <button id="batch-refuse" onclick="javascript:beforeSendBatchApprove(false,function() {
                if('${customer}'==='leading'){
                location.href = 'leading/refresh.jsp';
                }else {
                initData(currentTypeId)
                }
        });"
                class="weui-btn weui-btn_inline bg-red">拒绝
        </button>
    </p>
</div>
<!--批量审批区域html结束-->

<script src="../../js/zepto.min.js"></script>
<script src="../../js/zepto.weui.min.js"></script>
<script src="../../js/iscroll-lite.min.js"></script>
<script src="../../js/common.js?version=${version}"></script>
<script src="../../js/batchApprove.js?version=${version}"></script>

<script>

    // 声明为单笔付款组织页面的批量
    singlePaymentTag = true;
    var siczyFlowMsgCheck = '<%=siczyMsgCheck%>';

    let customerName = sessionStorage.getItem('customerName');
    let currentTypeId = 0;
    let cellDataStr = sessionStorage.getItem('SUB_FLOW_PARAMS');
    let TODO = sessionStorage.getItem('TODO');
    let cellData = JSON.parse(cellDataStr);
    let subFLowType = cellData.subFlowType;
    let commandCode = cellData.commandCode;
    let workflowIds = cellData.workflowIds;
    let currentPage = 1;
    let isPullToRefresh = true;

    $('weuiAgree-bottom').attr('display','block');

    let onTabSelect = function (typeId) {
        isPullToRefresh = true;
        if (currentTypeId === typeId) {
            return;
        }
        currentTypeId = typeId;

        currentPage = 1;
        container.destroyInfinite();

        loading = false;
        container.infinite().on("infinite", function() {
            if(loading) return;
            loading = true;
            currentPage++;
            isPullToRefresh = false;
            initData(currentTypeId,function () {
                loading = false;
            })
        });
        initData(typeId);
        container.scrollTop(0);
    };

    let initData = function (currentTypeId, postFunc) {

        hideBatchButtons(true);
        if(TODO == 2 || (customerName=='cqyz' && cellData.flowTypeId=='ZJFK')){
            //已办
            container.css({'margin-top': '-10px','height':'calc(100% + 10px )'});
            $("#weuiAgree-bottom").css('display','none');
        }
        let params = {};
        params.commandCode = commandCode;
        params.workflowIds = workflowIds;
        params.workflowType = currentTypeId;
        // params.pageNum = 1;
        params.pageNum = currentPage;
        params.pageSize = 20;
        params.dealState=TODO;

        $.showLoading("加载中...")
        ajaxRequest('/saasflow/getSinglePaymentByOrg.do', params, function (resp) {
            container.pullToRefreshDone();
            console.log(resp);
            let data = resp.result.data;
            let cells = $('.weui-cells');
            // cells.html('');
            if(isPullToRefresh){
                cells.html('');
            }


            if (resp.message === '查询记录为空'|| data.length == 0){
                container.destroyInfinite();
                if(isPullToRefresh){
                    footer.css('display','none')
                    cells.append('<div class="weui-msgbox" style="position: fixed; line-height: 50%">\n' +
                        '    <p>\n' +
                        '        <i class="weui-icon-info-circle"></i>' + resp.message + '\n' +
                        '    </p>\n' +
                        '</div>');
                    return;
                }else{
                    cells.append('<div class="weui-loadmore weui-loadmore_line">\n' +
                        '    <span class="weui-loadmore__tips">暂无更多数据</span>\n' +
                        '</div>\n')
                    return;
                }

            }
            for (let i = 0; i < data.length; i++) {
                let amounts = data[i].totalAmount.split(';');
                let amountsHtml = '';

                if (amounts !== null && amounts !== undefined && amounts.length > 0) {

                    for (let k = 0; k < amounts.length - 1; k++) {
                        amountsHtml += '<p style="color: #FF7979; font-size: 13px;margin-top: 10px">' + amounts[k] + '</p>';
                    }
                }

                let timeId =Math.random().toString(36).substr(2);

                let cell = $('<label class="weui-cell weui-check__label" for="check__no__' + timeId + '">' +
                    '            <div  class="weui-cell__hd check_cell">' +
                    '                <input onchange="checkBoxChange()" data-amount="'+data[i].totalAmount+'" type="checkbox" data-itemid="' + data[i].itemId + '" data-workflowid="' + data[i].workflowId + '" class="weui-check" name="checkbox_' + data[i].itemId + '" id="check__no__' + timeId + '">' +
                    '                <i class="weui-icon-checked"></i>' +
                    '            </div>' +
                    '            <div class="weui-cell__bd weui-cell_access">' +
                    '                <h4>' + data[i].orgName + '</h4>' +
                    amountsHtml +
                    // '                <p class="item_list">' + data[i].totalAmount + '</p>' +
                    '            </div>' +
                    '        </label>');

                if( /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ) {

                    cell.on('touchstart', function () {

                        is_click = true;


                    });

                    cell.on('touchmove', function () {

                        is_click = false;
                    });

                    cell.on('touchend', function () {
                        if (is_click) {
                            // 当多选框不可见时才可以点击
                            if ($('.weui-check').attr('disabled') === 'disabled') {
                                sessionStorage.setItem('ZJFK', 'true');
                                sessionStorage.setItem('workflowIds', data[i].workflowId);
                                sessionStorage.setItem('orgId', data[i].orgId);
                                sessionStorage.setItem('workflowType', data[i].workflowType);
                                sessionStorage.setItem('url', '/saasflow/getSinglePaymentSimple.do');
                                location.href = 'flowList.jsp';
                            }
                        }
                    });
                }else {

                    cell.on('click', function () {
                        if (is_click) {
                            // 当多选框不可见时才可以点击
                            if ($('.weui-check').attr('disabled') === 'disabled') {
                                sessionStorage.setItem('ZJFK', 'true');
                                sessionStorage.setItem('workflowIds', data[i].workflowId);
                                sessionStorage.setItem('orgId', data[i].orgId);
                                sessionStorage.setItem('workflowType', data[i].workflowType);
                                sessionStorage.setItem('url', '/saasflow/getSinglePaymentSimple.do');
                                location.href = 'flowList.jsp';
                            }

                        }
                    });
                }

                cells.append(cell);
                $('.weui-footer').css('display','block');

                hideBatchButtons(true);
                if(TODO == 2 || (customerName=='cqyz' && cellData.flowTypeId=='ZJFK')){
                    //已办
                    container.css({'margin-top': '-10px','height':'calc(100% + 10px )'});
                    $("#weuiAgree-bottom").css('display','none');
                }
                if (postFunc !== null && postFunc !== undefined) {
                    postFunc();
                }
            }
        })
    };

    let is_click = true;
    let footerHeight = 66;//footer的高度。


    $(document).ready(function () {
        var isPageHide = false;
        window.addEventListener('pageshow', function () {
            if (isPageHide) {
                window.location.reload();
            }
        });
        window.addEventListener('pagehide', function () {
            isPageHide = true;
        });
        initData(currentTypeId, function () {
            // $('#container').pullToRefreshDone();
        });
        let tagnav =$('#tagnav');
        let container =$('#container');
        let footer = $('.weui-footer');
        let tabs = $('.weui-navigator-list');

        let marginTop=-50;
        let topAndBottom = marginTop + footerHeight;

        footer.css('display','none');

        if (subFLowType.length > 1) {
            for (let i = 0; i < subFLowType.length; i++) {
                let tab = $('<li><a href="javascript:onTabSelect(\'' + subFLowType[i].typeId + '\');">' + subFLowType[i].typeName + '</a></li>');
                tabs.append(tab);

            }

            // 初始化tab
            TagNav('#tagnav', {
                type: 'scrollToNext',
                curClassName: 'weui-state-active',
                index: 0
            });
            tagnav.css('display','block');
            marginTop =marginTop + parseInt(tagnav.css('height'));
        } else {
            tabs.hide();
            tagnav.css('display','none');
            marginTop= -50
        }

        topAndBottom =marginTop + footerHeight;
        container.css({'margin-top':marginTop + 'px','margin-bottom':footerHeight + 'px','height':'calc(100% - ' + topAndBottom + 'px )'});
        container.pullToRefresh({
            onRefresh: function () {
                footer.css('display','none')
                isPullToRefresh = true;
                setTimeout(function () {
                    currentPage = 1;
                    initData(currentTypeId, function () {
                        // container.pullToRefreshDone();
                        // setTimeout(function () {
                        //     footer.css('display','block')
                        // },500)

                    });
                }, 50);
            },
            onPull: function (percent) {
            },
            distance: 20
        });
        var loading = false;
        container.infinite().on("infinite", function() {
            if(loading) return;
            loading = true;
            currentPage++;
            isPullToRefresh = false;
            initData(currentTypeId,function () {
                loading = false;
            })
        });

        // $(document.body).on('touchmove', function (e) {
        //     e.preventDefault();
        // })

    });


</script>


</body>
</html>

