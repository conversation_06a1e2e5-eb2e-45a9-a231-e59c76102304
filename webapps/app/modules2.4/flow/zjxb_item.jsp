<%@page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/include.jsp" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <meta content="no-cache">
    <meta content="0">
    <title>资金下拨申请明细</title>
    <link rel="stylesheet" href="../../css/weui.min.css">
    <link rel="stylesheet" href="../../css/weuix.min.css">

    <style>

        body {
            background: #efeff4;
        }
        .weui-cells {
            margin-top: 10px;
        }
        .note-label{
            margin-right: 40px;
            white-space: nowrap;
        }
        .note-value {
            height: auto;
            word-wrap:break-word;
            word-break:break-all;
            overflow: hidden;
        }
        .weui-cell__bd {
            min-width: 80px;
            margin-right: 10px;
        }

    </style>

</head>

<body>


<div id="list">

</div>
<script src="../../js/zepto.min.js"></script>
<script src="../../js/zepto.weui.min.js"></script>
<script src="../../js/iscroll-lite.min.js"></script>
<script src="../../js/common.js?version=${version}"></script>

<script>

    let budgetItem = JSON.parse(sessionStorage.getItem("ZJXB_ITEM"));
    console.log(budgetItem);

    $(document).ready(function () {

        if (budgetItem.length > 0 ) {
            let list = $('#list');
            for (let i = 0; i < budgetItem.length; i++) {
                let cell = $(' <div  class="weui-cells">\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>申请金额/元</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>'+budgetItem[i].applyAmount+'</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>申请类型</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>'+budgetItem[i].applyType+'</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>拨入账号</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>'+budgetItem[i].inBankAccount+'</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>拨出账号</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>'+budgetItem[i].outBankAccount+'</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p>保留天数</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p>'+budgetItem[i].reserveKeepDays+'</p></div>\n' +
                    '        </div>\n' +
                    '        <div class="weui-cell">\n' +
                    '            <div class="weui-cell__bd"><p class="note-label">用途</p></div>\n' +
                    '            <div  class="weui_cell_ft"><p class="note-value">'+budgetItem[i].purpose+'</p></div>\n' +
                    '        </div>\n' +
                    '    </div>');
                list.append(cell);

            }
        }


    });

</script>


</body>

</html>
