<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="/common/include.jsp" %>
<%--审批中转--%>
<%
    String taskId = request.getParameter("taskId");
    if (StrUtil.isEmpty(taskId)) {
        // 泸州老窖
        taskId = request.getParameter("requestid");
    }
%>
<c:set var="taskId" value="<%=taskId%>"/>

<html>
<head>

    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">

    <title>加载单据</title>

    <link rel="stylesheet" href="../../css/weui.min.css">
    <link rel="stylesheet" href="../../css/jquery-weui.min.css">
    <style>
        body {
            padding: 20px;
        }
    </style>
</head>

<body>
<div id="content" style="display: none;">
    <div class="weui-msg" id="s2">
        <div class="weui-msg__icon-area"><i class="weui-icon-warn  weui-icon_msg"></i></div>
        <div class="weui-msg__text-area">
            <h2 class="weui-msg__title">单据查询异常</h2>
            <p class="weui-msg__desc">
            </p>
        </div>
    </div>

    <p class="weui-btn-area">
        <button id="return_list" class="weui-btn weui-btn_primary">返回</button>
    </p>
</div>
<%--<button id="btn" class="weui-btn weui-btn_primary">获取token</button>--%>


<script src="${ctx}/js/jquery.min.js"></script>
<script src="${ctx}/js/jquery-weui.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script src="https://lf1-cdn-tos.bytegoofy.com/goofy/lark/op/h5-js-sdk-1.5.19.js"></script>
<script src="https://open-doc.welink.huaweicloud.com/docs/jsapi/2.0.10/hwh5-cloudonline.js"></script>

<script>

    $(document).ready(function () {

        getToken();

        var return_list_btn = $('#return_list');


        return_list_btn.on('click', function () {
            if ('${customer}' === 'corpwx') {
                document.addEventListener('WeixinJSBridgeReady', function () {
                    WeixinJSBridge.call('closeWindow');
                }, false);
                WeixinJSBridge.call('closeWindow');
            } else if ('${customer}' === 'feishu') {
                window.h5sdk.ready(() => { // ready方法不需要每次都调用
                    tt.closeWindow({
                        fail(res) {
                            console.log(`closeWindow fail: ${JSON.stringify(res)}`);
                        }
                    });
                });
            } else if ('${customer}' === 'welink') {
                HWH5.close().catch(function (error) {
                    console.log('关闭webview异常', error);
                });
            } else {
                history.back();
            }

        });
    });


    function getToken() {
        $.showLoading("加载中...")
        ajaxRequestWithFailCallback('/saasflow/getFlowParams.do', {taskId: '${taskId}'}, function (response) {
            let result = response.result;
            let dealState = result.dealState;

            var params = {};
            params.commandCode = result.transCode;
            params.itemId = result.itemId;
            params.workflowId = result.workflowId;
            params.workflowType = result.workflowType;
            sessionStorage.setItem("DETAIL_FLOW_PARAMS", JSON.stringify(params));
            var approveUrl = '${ctx}/modules2.4/flow/flowDetailUnified.jsp?' +
                'dealState=' + dealState +
                '&flowType=' + result.flowType;
            if ('${customer}' === 'zjdc') {
                // 中交地产个性化页面
                approveUrl = '${ctx}/modules2.4/flow/flowDetailSpec.jsp?' +
                    'dealState=' + dealState +
                    '&flowType=' + result.flowType;
            }
            if ('${customer}' === 'szgdjt' || '${customerCode}' === 'szgdjt') {
                // 苏州轨道交通
                approveUrl = '${ctx}/modules2.4/flow/flowDetailUnifiedSzgdjt.jsp?' +
                    'dealState=' + dealState +
                    '&flowType=' + result.flowType;
            }
            window.location.replace(approveUrl);

        }, function (data) {

            if ('${customer}' === 'qdyl') {
                // 青岛饮料单据查询失败则跳转待办列表页面
                window.location.replace('${ctx}/modules2.4/flow/flowTypeList.jsp?selectType=flow');
                return;
            }

            $('#content').css('display', 'block');
            $('.weui-msg__desc').html(data.message)
        })


    }

</script>

</body>
</html>