<%@page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/include.jsp" %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <meta content="no-cache">
    <meta content="0">
    <title>附件列表</title>
    <link rel="stylesheet" href="../../css/weui.min.css">
    <link rel="stylesheet" href="../../css/weuix.min.css">

    <style>

        * {
            touch-action: pan-y;
        }

        body {
            background-color: #F2F4F6;
        }

        .weui-cell__ft {
            position: absolute;
            top: 10px;
            right: 15px;
        }

    </style>
</head>
<body>

<div class="weui-cells">

</div>
<script src="../../js/zepto.min.js"></script>
<script src="../../js/zepto.weui.min.js"></script>
<script src="../../js/common.js?version=${version}"></script>

<script>

    let dataStr = sessionStorage.getItem("accessories");
    let data = JSON.parse(dataStr);
    $(document).ready(function () {

        let cells = $('.weui-cells');

        for (let cellData of data) {
            let cell = $('<div class="weui-cell">' +
                '<div class="weui-media-box__bd">\n' +
                '        <h4 class="weui-media-box__title">' + cellData.fileName + '(' + cellData.size + ')</h4>\n' +
                '        <p class="weui-media-box__desc">' + cellData.creator + '</p>\n' +
                '        <p class="weui-cell__ft">' + cellData.createTime + '</p>\n' +
                '      </div>' +
                '</div>');
            cells.append(cell);

            cell.on('click', function () {
                location.href = cellData.url;
            })

        }
    });
</script>

</body>
</html>
