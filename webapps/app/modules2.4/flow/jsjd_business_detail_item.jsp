<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file='/common/include.jsp' %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>业务付款明细</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

    <style>
        html,
        body {
        }

        .weui-media-box__title {
            font-size: 1rem;
            font-weight: bold;
        }

        body {
            background: #eee;
            font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
            font-size: 14px;
            color: #000;
            margin: 0;
            padding: 0;
        }

        .weui-cells {
            margin-top: 0;
            margin-bottom: 4.25em;
            font-size: 15px;
        }

        .weui-media-box__desc {
            color: #0d0d0d;
            line-height: 1.8em;
        }

        .weui-navbar__item.weui-bar__item_on {
            background-color: white;
            color: #0d0d0d;
            font-size: 0.8rem;
        }

        .query_dialog {
            border-radius: 5px;
            border-color: #c1c1c1;
            top: 10%;
            left: 5%;
            position: absolute;
            background-color: whitesmoke;
            width: 80%;
            z-index: 2;
            padding: 20px;
        }

        .weui-btn {
            font-size: 0.65rem;
        }

    </style>
</head>
<body>
<div class="weui-navbar">
    <div onclick="alert_query()" role="tab" aria-selected="true" aria-controls="panel1" id="tab1"
         class="weui-navbar__item weui-bar__item_on" wah-hotarea="click">
        查询
    </div>
</div>

<div class="container" style="margin-top: 50px">
    <div class="content" style="height:100%;overflow: auto;-webkit-overflow-scrolling: touch;">
        <div class="weui-pull-to-refresh__layer">
            <div class='weui-pull-to-refresh__arrow'></div>
            <div class='weui-pull-to-refresh__preloader'></div>
            <div class="down">下拉刷新</div>
            <div class="up">释放刷新</div>
            <div class="refresh">正在刷新</div>
        </div>
        <div class="weui-cells">


        </div>
    </div>
</div>

<div class="query_dialog" hidden>
    <label for="js_input1" class="weui-cell weui-cell_active">
        <div class="weui-cell__hd"><span class="weui-label">物料名称</span></div>
        <div class="weui-cell__bd">
            <input id="js_input1" class="weui-input" placeholder="请输入物料名称">
        </div>
    </label>
    <label for="js_input2" class="weui-cell weui-cell_active">
        <div class="weui-cell__hd"><span class="weui-label">起始金额</span></div>
        <div class="weui-cell__bd">
            <input id="js_input2" class="weui-input" placeholder="起始金额">
        </div>
    </label>
    <label for="js_input3" class="weui-cell weui-cell_active">
        <div class="weui-cell__hd"><span class="weui-label">截止金额</span></div>
        <div class="weui-cell__bd">
            <input id="js_input3" class="weui-input" placeholder="截止金额">
        </div>
    </label>
    <div class="weui-form__opr-area" style="padding-left: 20px;padding-right: 20px;margin-top: 10px">
        <button class="weui-btn weui-btn_primary" type="button" id="confirm-query-btn" wah-hotarea="click">确定</button>
        <button class="weui-btn weui-btn_primary" type="button" id="reset-query-btn" wah-hotarea="click">重置选项</button>
        <button class="weui-btn weui-btn_default" type="button" id="cancel-query-btn" wah-hotarea="click">取消</button>
    </div>

</div>
<script src="${ctx}/js/zepto.min.js"></script>
<script src="${ctx}/js/zepto.weui.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>

<script>


    const pageSize = 20;
    var onTop = true;
    $('#reset-query-btn').on('click', function () {
        $('#js_input1').val('');
        $('#js_input2').val('');
        $('#js_input3').val('');
        getData();
        $(".query_dialog").hide();

    })
    $('#cancel-query-btn').on('click', function () {
        $(".query_dialog").hide();
    })

    $('#confirm-query-btn').on('click', function () {
        let mtName = $('#js_input1').val();
        let amountStart = $('#js_input2').val();
        let amountEnd = $('#js_input3').val();
        console.log("name:" + mtName + "; amountStart:" + amountStart + "; amountEnd:" + amountEnd)
        $(".query_dialog").hide();
        getData();
    })
    window.onscroll = function () {
        var scrollT = document.documentElement.scrollTop || document.body.scrollTop; //滚动条的垂直偏移
        console.log(scrollT)

        onTop = (scrollT === 0);

    };

    let alert_query = function () {
        // alert(1)
        $(".query_dialog").show();
    }

    let getData = function () {

        currentPageNum = 2;
        let url = '/jsjd/getBusinessPaymentDetail.do';
        let params = {};
        params.pageNum = 1;
        params.pageSize = pageSize;
        params.itemId = sessionStorage.getItem("itemId");
        params.materialName = $('#js_input1').val();
        params.amountFrom = $('#js_input2').val();
        params.amountTo = $('#js_input3').val();

        ajaxRequest(url, params, function (response) {
            console.log(response);
            content.pullToRefreshDone();

            let cells = $('.weui-cells');
            cells.html('');
            if (response.result.totalNum === 0) {
                // 没有预警数据
                cells.append(
                    '<div class="weui-msgbox" style="position: fixed; line-height: 50%">' +
                    '    <p>' +
                    '        <i class="weui-icon-info-circle"></i>暂无数据' +
                    '    </p>' +
                    '</div>');
            } else {

                let data = response.result.data;
                let refreshHeader = '<div class="weui-pull-to-refresh__layer">\n' +
                    '                <div class=\'weui-pull-to-refresh__arrow\'></div>\n' +
                    '                <div class=\'weui-pull-to-refresh__preloader\'></div>\n' +
                    '                <div class="down">下拉刷新</div>\n' +
                    '                <div class="up">释放刷新</div>\n' +
                    '                <div class="refresh">正在刷新</div>\n' +
                    '            </div>';
                // cells.append(refreshHeader);
                for (let i = 0; i < data.length; i++) {


                    // let cell = $(' <div class="weui-cell">\n' +
                    //     '        <div class="weui-cell__bd">\n' +
                    //     '            <p class="msg-title">' + data[i].amount + '</p>\n' +
                    //     '            <p class="msg-content">' + data[i].materialName + '</p>\n' +
                    //     '        </div>\n' +
                    //     '    </div>');

                    let cell = $('<div role="option" class="weui-media-box weui-media-box_text">' +
                        '<strong class="weui-media-box__title">' + data[i].amount + '（元）</strong>' +
                        ' <p class="weui-media-box__desc">物料名称：' + data[i].materialName + '</p>' +
                        ' <p class="weui-media-box__desc">物料编码：' + data[i].materialNo + '</p>' +
                        ' <p class="weui-media-box__desc">物料数量：' + data[i].materialCount + '</p>' +
                        ' <p class="weui-media-box__desc">订单单价：' + data[i].price + '</p>' +
                        ' <p class="weui-media-box__desc">款项分类名称：' + data[i].categoryName + '</p>' +
                        ' <p class="weui-media-box__desc">付款明细应付金额：' + data[i].detailAmount + '</p>' +
                        ' <p class="weui-media-box__desc">本笔明细应付金额：' + data[i].amount + '</p>' +
                        '</div>')


                    cell.on('touchstart', function () {

                        click = true;
                    });

                    cell.on('touchmove', function () {
                        click = false;
                    });

                    cell.on('touchend', function () {
                        if (click) {
                            console.log($(this).data('warningid'));
                            dealMsg($(this).data('warningid'), $(this).data('read'));
                        }
                    });
                    cells.append(cell);


                }

            }

            let currDataSize = $('.weui-cells').find('.weui-cell').length;
            if (currDataSize >= pageSize) {
                cells.append('<div class="weui-panel__ft">\n' +
                    '            <a href="javascript:getMore();" class="weui-cell weui-cell_access weui-cell_link" style="text-align: center">\n' +
                    '                <div class="weui-cell__bd" id="getmore">查看更多<i id="loading" class="weui-loading"></i></div>\n' +
                    '            </a>\n' +
                    '        </div>');

                $('#loading').hide();
            }
        })


    };

    let click = true;
    let currentPageNum = 2;

    let getMore = function () {

        $('#no-more-tip').remove();
        $('#loading').show();

        let url = '/jsjd/getBusinessPaymentDetail.do';
        let params = {};
        params.pageNum = currentPageNum;
        params.pageSize = pageSize;
        params.itemId = sessionStorage.getItem("itemId");
        params.materialName = $('#js_input1').val();
        params.amountFrom = $('#js_input2').val();
        params.amountTo = $('#js_input3').val();
        currentPageNum++;

        ajaxRequest(url, params, function (response) {
            $('#loading').hide();

            console.log(response);
            let more = $('.weui-panel__ft');
            if (response.result.totalNum === 0) {
                currentPageNum--;
                $('.weui-cells').append('<div id="no-more-tip" class="weui-loadmore weui-loadmore_line">\n' +
                    '    <span class="weui-loadmore__tips">暂无更多数据</span>\n' +
                    '</div>\n');
            } else {

                let data = response.result.data;
                for (let i = 0; i < data.length; i++) {


                    // let cell = $(' <div class="weui-cell">\n' +
                    //     '        <div class="weui-cell__bd">\n' +
                    //     '            <p class="msg-title">' + data[i].amount + '</p>\n' +
                    //     '            <p class="msg-content">' + data[i].materialName + '</p>\n' +
                    //     '        </div>\n' +
                    //     '    </div>');
                    let cell = $('<div role="option" class="weui-media-box weui-media-box_text">' +
                        '<strong class="weui-media-box__title">' + data[i].amount + '（元）</strong>' +
                        ' <p class="weui-media-box__desc">物料名称：' + data[i].materialName + '</p>' +
                        ' <p class="weui-media-box__desc">物料编码：' + data[i].materialNo + '</p>' +
                        ' <p class="weui-media-box__desc">物料数量：' + data[i].materialCount + '</p>' +
                        ' <p class="weui-media-box__desc">订单单价：' + data[i].price + '</p>' +
                        ' <p class="weui-media-box__desc">款项分类名称：' + data[i].categoryName + '</p>' +
                        ' <p class="weui-media-box__desc">付款明细应付金额：' + data[i].detailAmount + '</p>' +
                        ' <p class="weui-media-box__desc">本笔明细应付金额：' + data[i].amount + '</p>' +
                        '</div>')
                    cell.on('touchstart', function () {

                        click = true;
                    });

                    cell.on('touchmove', function () {
                        click = false;
                    });

                    cell.on('touchend', function () {
                        if (click) {
                            console.log($(this).data('warningid'));

                            dealMsg($(this).data('warningid'), $(this).data('read'));

                        }
                    });
                    more.before(cell);
                }

            }

        })


    };

    let content = $('.content');

    let canRefresh = true;

    $(document).ready(function () {
        console.log("has ready");


        getData();

        content.on('touchstart', function () {
            canRefresh = true;
        });

        content.on('touchmove', function () {
            canRefresh = false;
        });

        content.on('touchend', function () {
            canRefresh = true;
        });
        content.pullToRefresh({
            distance: 50,
            onRefresh: function () {
                if (onTop) {
                    $('.weui-msgbox').hide();

                    setTimeout(function () {
                        getData();


                    }, 500);
                }
            }
        });
    });


    overscroll(document.getElementsByClassName('content')[0]);


</script>


</body>
</html>

