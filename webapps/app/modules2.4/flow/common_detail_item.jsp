<%@page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/include.jsp" %>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <meta content="no-cache">
    <meta content="0">
    <title>明细</title>
    <link rel="stylesheet" href="../../css/weui.min.css">
    <link rel="stylesheet" href="../../css/weuix.min.css">

    <style>

        body {
            background: #e5e5e5;
        }
        .weui-cells {
            margin-top: 10px;
            font-size: 15px;
        }
        .note-label{
            margin-right: 40px;
            white-space: nowrap;
        }
        .note-value {
            height: auto;
            word-wrap:break-word;
            word-break:break-all;
            overflow: hidden;
        }
        .weui-cell__hd{
            color: #5A5858;
            width: 120px;
            margin-right: 10px;
        }
    </style>

</head>

<body>


<div id="list">

</div>
<script src="../../js/zepto.min.js"></script>
<script src="../../js/zepto.weui.min.js"></script>
<script src="../../js/iscroll-lite.min.js"></script>
<script src="../../js/common.js?version=${version}"></script>

<script>

    let detailItemData = JSON.parse(sessionStorage.getItem("DETAIL_ITEM_DATA"));
    console.log(detailItemData);

    $(document).ready(function () {

        if (detailItemData.length > 0 ) {
            let list = $('#list');
            for (let kvListItem of detailItemData) {
                let html = '';
                for(let kvItem of kvListItem){

                    // html +=  '        <div class="weui-cell">\n' +
                    //     '            <div class="weui-cell__bd"><p>'+kvItem.key+'</p></div>\n' +
                    //     '            <div  class="weui_cell_ft"><p>'+kvItem.value+'</p></div>\n' +
                    //     '        </div>\n';
                    html+='<div class="weui-cell">'+
                        ' <div class="weui-cell__hd"><label class="weui-label">'+kvItem.key+'</label></div>'+
                        '<div class="weui-cell__bd">'+
                        '<p>'+kvItem.value+'</p>'+
                        '</div>'+
                        '</div>';
                }
                let cell = $(' <div  class="weui-cells">\n' +
                    html+
                    '    </div>');
                list.append(cell);

            }
        }


    });

</script>
</body>

</html>
