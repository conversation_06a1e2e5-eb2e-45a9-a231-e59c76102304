<%@page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/include.jsp" %>
<%
    String customerName = MyProperties.getMyPropertiesInstance().getProperty("currentCustomerName");
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <meta content="no-cache">
    <meta content="0">
    <title></title>
    <link rel="stylesheet" href="../../css/weui.min.css">
    <link rel="stylesheet" href="../../css/weuix.min.css">

    <style>

        * {
            touch-action: pan-y;
        }

        body {
            background-color: #F2F4F6;
        }

        .header {
            color: #ffffff;
            width: 100%;
            background: #649CFF;
            text-align: center;
            padding-top: 30px;
            padding-bottom: 30px;
        }

        .weui-dialog {
            max-height: 400px;
            overflow: auto;
        }

        .weui-cells {
            margin-top: 0px;
            padding: 10px 0;
        }

        .weui-cell {
            padding: 5px 15px;
        }

        .weui-cell:before {
            display: none;
        }

        .weui-cells:before, .weui-cells:after {
            display: none;
        }

        .approve {
            margin-top: 20px;
            text-align: center;
            padding: 10px 20px;
        }

        .weui-btn_primary {
            background-color: #54D6AD;

        }

        .timeline .recent {
            color: #54D6AD;
        }

        .weui-btn_warn {
            background-color: #FF7979;
        }

        .timeline-item-color {
            background-color: #54D6AD;
        }

        .timeline-item-tail {
            background-color: #54D6AD;
        }
    </style>
</head>
<body>

<div class="container" style="display: none">

    <div class="header" style="margin-bottom: 10px; font-size: 15px">
        <p style="font-size: 12px" id="note-money-tip">金额(元)</p>
        <p style="font-size: 26px; font-weight: bold" id="note-money"></p>
        <p style="font-size: 20px;" id="cn-note-money"></p>
    </div>

    <div class="content" style="font-size: 15px">

        <div id="account-header" style="margin-bottom: 10px">
            <div class="weui-cells" style="font-size: 15px">
                <div class="weui-cell">
                    <div class="weui-cell__bd" style="max-width: 80%"><p
                            style="text-align: left;color: #5A5858">
                        申请组织</p></div>
                    <div class="weui-cell__bd" style="color: #0d0d0d;text-align: right" id="apply-org">-</div>
                </div>
                <div class="weui-cell" id="apply-reason-cell">
                    <div class="weui-cell__bd" style="max-width:  80%">
                        <p style=" text-align: left;color: #5A5858">
                            申请原因</p></div>
                    <div class="weui-cell__bd" style="color: #0d0d0d;text-align: right" id="apply-reason">-</div>
                </div>
            </div>

        </div>
        <div id="change-tip1" style="background-color: white; padding: 10px 15px">
            <div class="weui-cell weui-cell__bd" style="background: #F2F4F6"><p>变更前</p></div>
        </div>
        <div class="weui-cells" id="detail-list">

        </div>
        <div id="change-tip2" style="background-color: white; padding: 10px 15px">
            <div class="weui-cell weui-cell__bd" style="background: #F2F4F6"><p>变更后</p></div>
        </div>
        <div class="weui-cells" style="font-size: 15px" id="change-detail-list">

        </div>
    </div>
    <div class="timeline" style="background-color: white; margin-top: 10px; font-size: 15px">
        <ul id="workflow-line">
            <li class="timeline-item">
                <div class="timeline-item-color timeline-item-head-first">
                    <i class="timeline-item-checked   weui-icon-success-no-circle"></i>
                </div>
                <div class="timeline-item-tail"></div>
                <div class="timeline-item-content">
                    <h3 id="flowTypeName">单笔付款申请</h3>
                    <img id="workflow-state" src=""
                         style="width: 80px; position: absolute;top: 10px;right: 10px">
                </div>
            </li>


        </ul>

    </div>

    <div class="approve">
        <a href="javascript:beforeSendApprove(true);" class="weui-btn weui-btn_primary">同意</a>
        <a href="javascript:beforeSendApprove(false);" class="weui-btn weui-btn_warn">拒绝</a>
    </div>
</div>


<script src="../../js/zepto.min.js"></script>
<script src="../../js/zepto.weui.min.js"></script>
<script src="../../js/common.js?version=${version}"></script>

<script>

    var siczyFlowMsgCheck = '<%=siczyMsgCheck%>';

    let customerName = '<%=customerName%>';
    let preParams = JSON.parse(sessionStorage.getItem('DETAIL_FLOW_PARAMS'));
    let requestUrl = '/saasflow/getFlowDetailInfo.do';
    let workflowRequestUrl = '/saasflow/getWorkflowHistory.do';
    let todo_tag = sessionStorage.getItem("TODO");

    let changeDetailList = $('#change-detail-list');
    changeDetailList.hide();

    let changeTip1 = $('#change-tip1');
    let changeTip2 = $('#change-tip2');
    changeTip1.hide();
    changeTip2.hide();

    let accountApplyHeader = $('#account-header');
    accountApplyHeader.hide();


    let approveButtons = $('.approve');
    approveButtons.hide();

    let workflowState = $('#workflow-state');
    workflowState.hide();

    $('.container').attr('display', 'block');


    let initDetailData = function (afterSuccess) {
        let params = {};
        params.commandCode = preParams.commandCode;
        params.itemId = preParams.itemId;
        params.workflowId = preParams.workflowId;
        params.workflowType = preParams.workflowType;
        params.dealState = todo_tag;

        $.showLoading("加载中...");
        ajaxRequest(requestUrl, params, function (resp) {

            console.log(resp);


            if (sessionStorage.getItem("TODO") === '1') {

                if (customerName != 'cqyz') {
                    // 重庆怡置单据详情不需要审批按钮
                    approveButtons.show();
                } else {
                    // 客户是cqyz
                    if (preParams.commandCode == 'WFQS52') {
                        approveButtons.hide();
                    } else {
                        approveButtons.show();
                    }

                }
                workflowState.hide();

            } else {
                workflowState.show();
            }

            // --- 循环开始 ---
            let detail_list = $('#detail-list');
            detail_list.html('');
            for (let i = 0; i < resp.result.data.length; i++) {
                let data = resp.result.data[i];
                if (data.noteMoney != null && data.noteMoney != '' && data.noteMoney != undefined) {
                    $('#note-money').html(data.noteMoney);
                    $('#cn-note-money').html(data.cnNoteMoney);
                    $('#note-money-tip').html(data.noteMoneyTip);

                } else {
                    $('.header').hide();
                    if (data.orgName || data.applyReason) {
                        accountApplyHeader.show();
                    }

                    if (data.orgName) {
                        accountApplyHeader.find('#apply-org').html(data.orgName);
                    }
                    if (data.applyReason) {
                        accountApplyHeader.find('#apply-reason').html(data.applyReason);
                    }

                }


                if (data.hasChange) {
                    // 账户申请中的变更
                    changeDetailList.show();
                    changeTip1.show();
                    changeTip2.show();
                    let changeDetailData = data.changeItem;

                    for (let i = 0; i < changeDetailData.length; i++) {

                        let valueColor = changeDetailData[i].color?changeDetailData[i].color:'#0d0d0d';

                        let cell = '<div class="weui-cell">' +
                            '    <div class="weui-cell__bd" style="max-width: 80%;">' +
                            '      <p style="text-align: left;color: #5A5858">' + changeDetailData[i].key + '</p>' +
                            '    </div>' +
                            '    <div class="weui-cell__bd" style="color: '+valueColor+';text-align: right">' + changeDetailData[i].value + '</div>' +
                            '  </div>';

                        changeDetailList.append(cell);


                    }

                }

                let detailData = data.item;

                for (let i = 0; i < detailData.length; i++) {

                    let color = '#000000';
                    let keyWeight = 'normal';
                    if (detailData[i].key === '计划项目') {
                        color = '#ff0000';
                    }
                    if(detailData[i].tag==='title'){
                        keyWeight = 'bold'
                    }

                    let cell = '<div class="weui-cell" style="font-size: 15px">' +
                        '    <div class="weui-cell__bd" style="max-width:80%;">' +
                        '      <p style="text-align: left; color: #5A5858; font-weight: '+keyWeight+'">' + detailData[i].key + '</p>' +
                        '    </div>' +
                        '    <div class="weui-cell__bd" style="color: ' + color + '; text-align: right" >' + detailData[i].value + '</div>' +
                        '  </div>';

                    detail_list.append(cell);


                }
                // 资金下拨申请
                if (data.hasAllocateApplyItem) {

                    itemWork(detail_list, "ZJXB_ITEM", data.allocateApplyItem, '下拨申请', "zjxb_item.jsp");

                }

                if (data.hasBudgetItem) {

                    accountApplyHeader.find('#apply-reason-cell').hide();
                    fund_plan_itemWork(detail_list, "BUDGET_ITEM", data.budgetItem, "BUDGET_ITEM_TEMPLATE", data.budgetTemplate, '计划项目', "budget_item.jsp");
                }

                if (data.hasCollectionItem) {
                    itemWork(detail_list, "COLLECTION_ITEM", data.collectionItem, '上划申请', "collection_item.jsp");
                }

                if (data.hasFundDial) {
                    itemWork(detail_list, "FUND_DIAL_ITEM", data.fundDialItem, '资金下拨', "fund_dial_item.jsp");
                }

                if (data.hasFundTransfer) {
                    itemWork(detail_list, "FUND_TRANSFER_ITEM", data.fundTransferItem, '资金调拨', "fund_transfer_item.jsp");
                }

                if (data.hasBatchItem) {
                    itemWork(detail_list, "BATCH_ITEM", data.batchItem, '批量付款', "batch_payment_item.jsp");
                }

                if (data.detailItemList !== null && data.detailItemList !== undefined && data.detailItemList.length > 0) {
                    go_to_common_detail_list_page(detail_list, data.detailItemList);

                } else if (data.hasDetailItem) {
                    if (data.flowTypeId == 'YFKSQ') {
                        go_to_customer_detail_page(detail_list, data, data.detailItemName)
                    } else {
                        go_to_common_detail_page(detail_list, data.detailItem, data.detailItemName);
                    }
                }


                if (data.hasAccessory) {
                    accessoriesWork(detail_list, data.accessoryItem);
                }

                if (afterSuccess !== null && afterSuccess !== undefined) {
                    afterSuccess();
                }
                if (i < resp.result.data.length - 1) {
                    detail_list.append('<div style="width: 100%; height: 5px; background-color: whitesmoke"></div>');
                }
            }
            //---循环结束
            initWorkflowLine();


        })
    };

    let go_to_common_detail_list_page = function (parentLayout, detailItemList) {
        for (let detailItem of detailItemList) {
            let cell = $('<div class="weui-cell" style="font-size: 15px">' +
                '    <div class="weui-cell__bd" style="max-width: 80%">' +
                '      <p style="text-align: left; color: #5A5858">' + detailItem.detailItemName + '</p>' +
                '    </div>' +
                '    <div class="weui-cell__bd" style="color: #54D6AD; text-align: right">明细</div>' +
                '  </div>');
            parentLayout.append(cell);

            cell.on('click', function () {

                console.log(detailItem.detailItem);
                sessionStorage.setItem("DETAIL_ITEM_DATA", JSON.stringify(detailItem.detailItem));
                window.location.href = "common_detail_item.jsp";
            });
        }
    }

    let go_to_customer_detail_page = function (parentLayout, data, name) {
        let cell = $('<div class="weui-cell" style="font-size: 15px">' +
            '    <div class="weui-cell__bd" style="max-width: 80%">' +
            '      <p style="text-align: left; color: #5A5858">' + name + '</p>' +
            '    </div>' +
            '    <div class="weui-cell__bd" style="color: #54D6AD;text-align: right"">明细</div>' +
            '  </div>');
        parentLayout.append(cell);

        cell.on('click', function () {

            console.log(data);
            sessionStorage.setItem("itemId", data.itemId);
            location.href = 'jsjd_business_detail_item.jsp';
        });

    }
    let go_to_common_detail_page = function (parentLayout, data, name) {
        let cell = $('<div class="weui-cell" style="font-size: 15px">' +
            '    <div class="weui-cell__bd" style="max-width: 80%">' +
            '      <p style="text-align: left; color: #5A5858">' + name + '</p>' +
            '    </div>' +
            '    <div class="weui-cell__bd" style="color: #54D6AD;text-align: right">明细</div>' +
            '  </div>');
        parentLayout.append(cell);

        cell.on('click', function () {

            console.log(data);
            sessionStorage.setItem("DETAIL_ITEM_DATA", JSON.stringify(data));
            window.location.href = "common_detail_item.jsp";
        });
    }
    let itemWork = function (parent, dataKey, data, name, pageUrl) {
        let cell = $('<div class="weui-cell" style="font-size: 15px">' +
            '    <div class="weui-cell__bd" style="max-width: 80%">' +
            '      <p style="text-align: left; color: #5A5858">' + name + '</p>' +
            '    </div>' +
            '    <div class="weui-cell__bd" style="color: #54D6AD; text-align: right">明细</div>' +
            '  </div>');
        parent.append(cell);

        cell.on('click', function () {

            console.log(data);
            sessionStorage.setItem(dataKey, JSON.stringify(data));
            window.location.href = pageUrl;
        });
    };

    let fund_plan_itemWork = function (parent, dataKey, data, templateKey, templateData, name, pageUrl) {
        let cell = $('<div class="weui-cell" style="font-size: 15px">' +
            '    <div class="weui-cell__bd" style="max-width: 80%">' +
            '      <p style="text-align: left; color: #5A5858">' + name + '</p>' +
            '    </div>' +
            '    <div class="weui-cell__bd" style="color: #54D6AD; text-align: right">明细</div>' +
            '  </div>');
        parent.append(cell);

        cell.on('click', function () {

            console.log(data);
            sessionStorage.setItem(dataKey, JSON.stringify(data));
            sessionStorage.setItem(templateKey, JSON.stringify(templateData));
            window.location.href = pageUrl;
        });
    };

    let accessoriesWork = function (parent, data) {
        let cell = $('<div class="weui-cell" style="font-size: 15px">' +
            '    <div class="weui-cell__bd" style="max-width: 80%">' +
            '      <p style="text-align: left; color: #5A5858">附件</p>' +
            '    </div>' +
            '    <div class="weui-cell__bd" style="color: #54D6AD">查看</div>' +
            '  </div>');
        parent.append(cell);
        cell.on('click', function () {
            console.log(data);
            sessionStorage.setItem("accessories", JSON.stringify(data));
            window.location.href = "accessoryList.jsp";

        });

    };

    let initWorkflowLine = function () {
        let params = {};
        params.workflowId = preParams.workflowId;
        params.itemId = preParams.itemId;
        $.showLoading("加载中...");
        ajaxRequest(workflowRequestUrl, params, function (resp) {
            console.log(resp);

            let data = resp.result.data;
            $('#flowTypeName').html(resp.result.workflowName);
            document.title = (resp.result.workflowName ? resp.result.workflowName : "单据") + "详情";

            let timeline = $('#workflow-line');
            for (let i = 0; i < data.length; i++) {
                let line = $('<li class="timeline-item">\n' +
                    '                <div class="timeline-item-color timeline-item-head"></div>\n' +
                    '                <div class="timeline-item-tail"></div>\n' +
                    '                <div class="timeline-item-content">\n' +
                    '                    <h4>' + data[i].opUsername + '<span class="recent"> ' + data[i].actionName + '</span></h4>\n' +
                    '                    <p style="position: absolute;right: 1px; top: 0; text-align: right;width: 120px">' + data[i].actionTime + '</p>\n' +
                    '                    <p>' + data[i].actionMemo + '</p>\n' +
                    '                </div>\n' +
                    '            </li>');

                if (i === data.length - 1) {
                    line.find('.timeline-item-tail').addClass('hide');
                }

                timeline.append(line);
            }
            $('.container').show();

            if (resp.result.approvalState == '2') {
                workflowState.attr("src", "../../img/czdn/flow/tag_access.png");
            } else if (resp.result.approvalState == '3') {
                workflowState.attr("src", "../../img/czdn/flow/tag_refuse.png");
            }

        })

    };

    $(document).ready(function () {
        $('.container').hide();

        initDetailData();
    });


    let beforeSendApprove = function (agree) {
        if (siczyFlowMsgCheck == 'true') {
            checkPayState(agree, function (payTag, passCode) {
                checkMsgCode(function () {
                    sendApprove(agree, payTag, passCode);
                });
            });

        } else {
            // sendApprove(agree);
            checkPayState(agree, function (payTag, passCode) {
                sendApprove(agree, payTag, passCode);
            });
        }


    }


    let sendApprove = function (agree, payTag, passCode) {


        let title;
        let defaultHint;

        if (agree) {

            title = "请输入审批意见";
            defaultHint = "同意";
        } else {
            title = "请输入拒绝理由";
            defaultHint = "拒绝";
        }

        let url = '/saasflow/sendApproval.do';


        $.prompt({
            title: title,
            text: '',
            input: defaultHint,
            empty: true, // 是否允许为空
            onOK: function (input) {

                if (input === '') {
                    $.toast(title.substr(3, 4) + "不得为空", "cancel");
                    return;
                }


                let params = {};
                params.dealMsg = input;
                params.dealType = agree ? 1 : 2;
                params.flowTypeId = sessionStorage.getItem("FLOWTYPEID");
                params.workflowId = preParams.workflowId;
                params.itemId = preParams.itemId;
                params.payTag = payTag;
                params.passCode = passCode;
                params.simplePassCode = 1;
                $.showLoading("处理中...");
                ajaxRequest(url, params, function (data) {
                    $.alert(data.result.data[0].returnMessage, "审批结果", function () {

                        setTimeout(function () {

                            if ('${customer}' === 'leading') {
                                location.href = 'leading/refresh.jsp';
                            } else {
                                history.back();
                            }
                        }, 500);
                    });
                })


            },
            onCancel: function () {
                //点击取消
                $.toast("取消操作", "cancel");
            }
        });
    }


</script>


</body>
</html>
