<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title></title>
    <!-- 最新版本的 Bootstrap 核心 CSS 文件 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"
          crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">

    <style>
        .accordion-button:not(.collapsed) {
            color: #4d7ec0;
        }

        .amount-title {
            font-size: 18px;
            font-weight: bold;
        }

        .amount-sub-title {
            margin-left: 20px;
            color: orangered;
            font-weight: bold;
        }

        .container {
            max-width: 100%;
        }

        .header {
            background-color: #4d7ec0;
            color: white;
            padding: 20px;
            margin: 0;
        }

    </style>
</head>
<body>

<div class="container text-center header">
    <div class="row align-items-start">
        <div id="total-amount" class="col amount-title">
            0.00
        </div>
    </div>
    <div class="row align-items-start">
        <div class="col">
            资金总额/万元
        </div>
    </div>
    <div style="margin-top: 10px">
        <div class="row align-items-center">
            <div id="merge-total-amount" class="col amount-title">
                0.00
            </div>
            <div id="un-merge-total-amount" class="col amount-title">
                0.00
            </div>
        </div>
        <div class="row align-items-center">
            <div class="col">
                并表小计/万元
            </div>
            <div class="col">
                非并表小计/万元
            </div>
        </div>
    </div>
</div>
<div class="accordion-item" style="margin-top: 20px">
    <h2 class="accordion-header" id="headingOne">
        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne"
                aria-expanded="true" aria-controls="collapseOne">
            自有资金总额/小计(万元)<span id="free-fund-sum" class="amount-sub-title"></span>
        </button>

    </h2>
    <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne"
         data-bs-parent="#accordionExample">
        <div class="accordion-body">
            <div class="accordion-item">
                <h2 class="accordion-header" id="headingOneSub">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapseOneSub" aria-expanded="true" aria-controls="collapseOne">
                        按区域明细/人民币(万元)
                    </button>
                </h2>
                <div id="collapseOneSub" class="accordion-collapse collapse show" aria-labelledby="headingOne"
                     data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <ul id="area-free-list" class="list-group">
                        </ul>
                    </div>
                </div>
            </div>
            <div class="accordion-item" style="margin-top: 0">
                <h2 class="accordion-header" id="headingTwoSub">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapseTwoSub" aria-expanded="true" aria-controls="collapseOne">
                        按银行明细/人民币(万元)
                    </button>
                </h2>
                <div id="collapseTwoSub" class="accordion-collapse collapse show" aria-labelledby="headingOne"
                     data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <ul id="bank-free-list" class="list-group">
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="accordion-item">
    <h2 class="accordion-header" id="headingGgzz">
        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseGgzzOne"
                aria-expanded="true" aria-controls="collapseGgzzOne">
            共管资金总额/小计(万元)<span id="share-fund-sum" class="amount-sub-title"></span>
        </button>

    </h2>
    <div id="collapseGgzzOne" class="accordion-collapse collapse show" aria-labelledby="headingOne"
         data-bs-parent="#accordionExample">
        <div class="accordion-body">
            <div class="accordion-item">
                <h2 class="accordion-header" id="headingGgzzSub">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapseGgzzSub" aria-expanded="true" aria-controls="collapseOne">
                        按区域明细/人民币(万元)
                    </button>
                </h2>
                <div id="collapseGgzzSub" class="accordion-collapse collapse show" aria-labelledby="headingOne"
                     data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <ul id="area-share-list" class="list-group">
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="accordion-item">
    <h2 class="accordion-header" id="headingMjzz">
        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseMjzz"
                aria-expanded="true" aria-controls="collapseMjzz">
            募集资金总额/小计(万元)<span id="raise-fund-sum" class="amount-sub-title"></span>
        </button>

    </h2>
    <div id="collapseMjzz" class="accordion-collapse collapse show" aria-labelledby="headingOne"
         data-bs-parent="#accordionExample">
        <div class="accordion-body">
            <div class="accordion-item">
                <h2 class="accordion-header" id="headingMjzzOneSub">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapseMjzzOneSub" aria-expanded="true"
                            aria-controls="collapseMjzzOneSub">
                        人民币明细（TOP15银行）/人民币(万元)
                    </button>
                </h2>
                <div id="collapseMjzzOneSub" class="accordion-collapse collapse show" aria-labelledby="headingOne"
                     data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <ul id="cny-raise-list" class="list-group">
                        </ul>
                    </div>
                </div>
            </div>
            <div class="accordion-item" style="margin-top: 0">
                <h2 class="accordion-header" id="headingMjzzTwoSub">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapseMjzzTwoSub" aria-expanded="true"
                            aria-controls="collapseMjzzTwoSub">
                        港币明细（TOP15银行）/港币(万元)
                    </button>
                </h2>
                <div id="collapseMjzzTwoSub" class="accordion-collapse collapse show"
                     aria-labelledby="headingMjzzTwoSub"
                     data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <ul id="hkd-raise-list" class="list-group">
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"
        crossorigin="anonymous"></script>
<script src="../js/longhu_app_util.js"></script>
<script>
    $(document).ready(function () {

        let query_date = getQueryVariable("queryDate");
        let tenant_id = getQueryVariable("tenantId");
        document.title = query_date + '资金实时余额信息'
        getData({
            queryDate: query_date,
            tenantId: tenant_id
        });
    });

    let getData = function (params) {
        simple_ajax('/jkzh/getQywxAccountBalance.do', params, function (data) {
            console.log(data);
            let result = data.result;
            setSumData(result);
            setListData(result);


        }, function () {
            $('body').html('<div class="text-center" style="margin-top: 100px">\n' +
                '  <img src="../img/report/<EMAIL>" class="rounded" style="width: 100px; height: 100px" alt="...">\n' +
                '<p style="color: #c9c9c9">暂无数据</p>' +
                '</div>');
        })
    }

    let setSumData = function (result) {
        $("#total-amount").html(result.totalAmount)
        $("#un-merge-total-amount").html(result.unMergeFundSum)
        $("#merge-total-amount").html(result.mergeFundSum)
        $("#free-fund-sum").html(result.freeFundSum)
        $("#raise-fund-sum").html(result.raiseFundSum)
        $("#share-fund-sum").html(result.shareFundSum)
    }

    let setListData = function (result) {
        let areaFreeList = $('#area-free-list');
        let bankFreeList = $('#bank-free-list');
        let areaShareList = $('#area-share-list');
        let cnyRaiseList = $('#cny-raise-list');
        let hkdRaiseList = $('#hkd-raise-list');
        areaFreeList.html('');
        bankFreeList.html('');
        areaShareList.html('');
        cnyRaiseList.html('');
        hkdRaiseList.html('');
        for (let item of result.areaFreeFundDetailList) {
            let html = '  <li class="list-group-item d-flex justify-content-between align-items-center">\n' +
                item.name +
                '    <span class="amount-sub-title">' + item.amount + '</span>\n' +
                '  </li>';
            areaFreeList.append(html);
        }

        for (let item of result.bankFreeFundDetailList) {
            let html = '  <li class="list-group-item d-flex justify-content-between align-items-center">\n' +
                item.name +
                '    <span class="amount-sub-title">' + item.amount + '</span>\n' +
                '  </li>';
            bankFreeList.append(html);
        }
        if (result.bankFreeFundDetailList.length > 0) {
            bankFreeList.append('  <li class="list-group-item d-flex justify-content-between align-items-center">\n' +
                '其他银行合计' +
                '    <span class="amount-sub-title">' + result.otherFundSum + '</span>\n' +
                '  </li>')
        }


        for (let item of result.areaShareFundDetailList) {
            let html = '  <li class="list-group-item d-flex justify-content-between align-items-center">\n' +
                item.name +
                '    <span class="amount-sub-title">' + item.amount + '</span>\n' +
                '  </li>';
            areaShareList.append(html);
        }

        for (let item of result.cnyRaiseFundDetailList) {
            let html = '  <li class="list-group-item d-flex justify-content-between align-items-center">\n' +
                item.name +
                '    <span class="amount-sub-title">' + item.amount + '</span>\n' +
                '  </li>';
            cnyRaiseList.append(html);
        }

        for (let item of result.hkdRaiseFundDetailList) {
            let html = '  <li class="list-group-item d-flex justify-content-between align-items-center">\n' +
                item.name +
                '    <span class="amount-sub-title">' + item.amount + '</span>\n' +
                '  </li>';
            hkdRaiseList.append(html);
        }


    }


</script>
</body>
</html>