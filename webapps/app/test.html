<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="initial-scale=1">
    <title>test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"
          crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">

    <style>
        th,td {
            min-width: 100px;
        }

    </style>
</head>
<body>
<table class="table table-bordered">
    <thead>
    <tr id="header">

    </tr>
    <tr id="header-sub">

    </tr>
    </thead>
    <tbody id="body">
    </tbody>
</table>

<!--js-->
<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"
        crossorigin="anonymous"></script>
<script src="../js/longhu_app_util.js"></script>
<script>
    $(document).ready(function () {

        let colHeader = ['2022第一季度','2022第二季度','2022第三季度','2022第四季度','2023第一季度']
        let rows = [
            {
                name : '计划项目1',
                '2022第一季度':'100',
                '2022第二季度':'200',
                '2022第三季度':'300',
                '2022第四季度':'400',
                '2023第一季度':'500',
            },
            {
                name : '计划项目2',
                '2022第一季度':'110',
                '2022第二季度':'210',
                '2022第三季度':'310',
                '2022第四季度':'410',
                '2023第一季度':'510',
            }
        ]
        let rows2 = [
            {
                name : '计划项目1',
                '2022第一季度':'101',
                '2022第二季度':'201',
                '2022第三季度':'301',
                '2022第四季度':'401',
                '2023第一季度':'501',
            },
            {
                name : '计划项目2',
                '2022第一季度':'111',
                '2022第二季度':'211',
                '2022第三季度':'311',
                '2022第四季度':'411',
                '2023第一季度':'511',
            }
        ]

        $('#header').append('<th class="align-middle" scope="col" rowspan="2">计划项目</th>')
        for(let header of colHeader){
            let html = '<th scope="col" colspan="2">'+header+'</th>';
            $('#header').append(html);
            $('#header-sub').append('<th>计划值</th><th>实际值</th>');

        }
        for(let i=0;i<rows.length;i++){
            let tdHtml = '<th scope="row">'+rows[i].name+'</th>';
            for(let header of colHeader){
                tdHtml += '<td>'+rows[i][header]+'</td><td>'+rows2[i][header]+'</td>'
            }
            let rowHtml = '<tr>'+tdHtml+'</tr>'
            $('#body').append(rowHtml);

        }

    });



</script>
</body>
</html>