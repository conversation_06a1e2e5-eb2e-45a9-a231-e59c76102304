<%@ page import="cn.hutool.core.util.ObjectUtil" %>

<%@ page import="cn.hutool.core.util.StrUtil" %>
<%@ page import="com.fingard.app.delegate.beans.OpenResult" %>
<%@ page import="com.fingard.app.delegate.framework.util.Constants" %>
<%@ page import="com.fingard.app.delegate.controller.third.CorpWechatHelper" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="/common/include.jsp" %>


<%
    String code = request.getParameter("code");

    if (StrUtil.isEmpty(code)) {
        code = String.valueOf(request.getSession().getAttribute("code"));
    }
    String errMsg;
    if (StrUtil.isNotEmpty(code)) {
        String userId = CorpWechatHelper.getUserId(code);
        if (StrUtil.isNotEmpty(userId)) {
            OpenResult login = CorpWechatHelper.login(request, userId);
            if (ObjectUtil.isNotNull(login) && Boolean.TRUE.toString().equalsIgnoreCase(login.getSuccessful())) {

                String previousPage = String.valueOf(request.getSession().getAttribute(Constants.PREVIOUS_PAGE));
                previousPage = "null".equals(previousPage) ? "" : previousPage;

//                response.sendRedirect(basePath + "/modules/flow/flowTypeList.jsp?pmDealState=1");
                response.sendRedirect(basePath + previousPage);
                errMsg = StrUtil.EMPTY;
            } else {
                errMsg = "用户无法登录，请检查资金管理系统中是否存在用户: " + userId;
            }

        } else {
            errMsg = "用户信息获取失败";
        }
    } else {
        errMsg = "企业微信授权码[code]获取失败";
    }

%>
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">

    <title>登录失败</title>
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

    <script src="${ctx}/js/jquery-1.7.2.min.js"></script>
</head>

<body>
<div class="weui-msg" style="display:block;" id="s2">
    <div class="weui-msg__icon-area"><i class="weui-icon-warn  weui-icon_msg"></i></div>
    <div class="weui-msg__text-area">
        <h2 class="weui-msg__title">登录失败</h2>
        <p class="weui-msg__desc"><%=errMsg%>
        </p>
    </div>
</div>

<p class="weui-btn-area">
    <button id="return_list" class="weui-btn weui-btn_primary">退出</button>
</p>

<script>
    $(document).ready(function () {
        var return_list_btn = $('#return_list');
        return_list_btn.on('click', function () {
            document.addEventListener('WeixinJSBridgeReady', function () {
                WeixinJSBridge.call('closeWindow');
            }, false);
            WeixinJSBridge.call('closeWindow');
        });
    });

</script>

</body>

</html>

