<%@ page import="com.fingard.app.delegate.beans.OpenResult" %>

<%@ page import="com.fingard.app.delegate.controller.third.CorpWechatHelper" %>
<%@ page import="com.fingard.app.delegate.framework.util.MD5" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@include file="/common/include.jsp" %>
<%--金科地产 蓝凌OA--%>
<%

    String userCode = request.getParameter("user");
    String token = request.getParameter("token");
    String taskId = request.getParameter("taskId");
    String key = "jinke2019";
    String errMsg = "";

    String sign = MD5.getMD5ofStr(userCode + key);
    if (StrUtil.equalsIgnoreCase(sign, token)) {
        OpenResult result = CorpWechatHelper.login(request, userCode);
        if (StrUtil.equals(result.getSuccessful(), Boolean.TRUE.toString())) {
            response.sendRedirect(basePath + "/modules2.4/flow/trans.jsp?taskId=" + taskId + "&userCode=" + userCode);

        } else {

            errMsg = "登录失败，请检查资金系统中是否存在用户：【" + userCode + "】";
        }
    } else {
        errMsg = "无此用户或无授权，请联系信息管理员";
    }
%>

<c:set var="errMsg" value="<%=errMsg%>"/>

<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">

    <title>登录失败</title>
    <link rel="stylesheet" href="${ctx}/css/weui.min.css">
    <link rel="stylesheet" href="${ctx}/css/jquery-weui.min.css">

</head>

<body>
<div class="weui-msg" style="display:block;" id="s2">
    <div class="weui-msg__icon-area"><i class="weui-icon-warn  weui-icon_msg"></i></div>
    <div class="weui-msg__text-area">
        <h2 class="weui-msg__title">登录失败</h2>
        <p class="weui-msg__desc">${errMsg}</p>
    </div>
</div>

<p class="weui-btn-area">
    <button id="exit" class="weui-btn weui-btn_primary">退出</button>
</p>
<script src="${ctx}/js/jquery.min.js"></script>
<script src="${ctx}/js/jquery-weui.min.js"></script>
<script src="//g.alicdn.com/dingding/dingtalk-jsapi/2.6.41/dingtalk.open.js"></script>
<script>


    $(document).ready(function () {
        $('#exit').on('click', function () {
            // 金科为钉钉环境，所以退出时使用钉钉的退出
            dd.biz.navigation.close({
                onSuccess: function (result) {
                },
                onFail: function (err) {
                }
            })
        });
    });

</script>

</body>

</html>

