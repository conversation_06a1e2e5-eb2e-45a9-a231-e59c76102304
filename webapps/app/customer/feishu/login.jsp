<%@ page import="com.fingard.app.delegate.framework.util.Constants" %>
<%@page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/include.jsp" %>
<%
    String appId = MyProperties.getMyPropertiesInstance().getProperty("feishu.appId");
    Object attribute = request.getSession().getAttribute(Constants.PREVIOUS_PAGE);
    String previousUrl = attribute == null ? (basePath + "/modules2.4/home_page/home.jsp?selectType=home") : (basePath + attribute);
%>
<c:set var="appId" value="<%=appId%>"/>
<c:set var="previousUrl" value="<%=previousUrl%>"/>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>

    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        .hide {
            display: none;
        }
    </style>
</head>
<body>
<div id="result" class="card text-center hide" style="margin: 40px 20px;">
    <div class="card-header">
        登陆认证结果
    </div>
    <div class="card-body">
        <h5 class="card-title">登陆失败</h5>
        <p class="card-text" id="content"></p>
        <a onclick="closePage()" class="btn btn-primary">退出</a>
    </div>
    <div class="card-footer text-muted">
        @资金管理系统App x 飞书
    </div>
</div>
<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="../../js/longhu_app_util.js"></script>
<script src="https://lf1-cdn-tos.bytegoofy.com/goofy/lark/op/h5-js-sdk-1.5.19.js"></script>

<script>
    $(document).ready(function () {
        try {
            window.h5sdk.ready(() => { // ready方法不需要每次都调用
                tt.requestAuthCode({
                    appId: '${appId}',
                    success: (info) => {
                        let params = {};
                        params.authCode = info.code;
                        requestWithFailCallback("/feishu/getUserId.do", params, function (resp) {
                            let userId = resp.result;
                            if (userId !== '' && userId !== null && userId !== undefined) {

                                showNewLoading();
                                requestWithFailCallback("/feishu/login.do", {userId: userId}, function (loginResult) {
                                    location.replace('${previousUrl}')
                                }, function () {
                                    onAtsVerifyFail();
                                })
                            } else {
                                // 从飞书接口获取用户信息失败
                                onOperationFail("获取用户信息失败");
                            }

                        }, function () {
                            // 从钉钉接口获取用户信息失败
                            onOperationFail("获取用户信息失败");
                        })
                    },
                    fail: (error) => {
                        onOperationFail("获取飞书授权码失败，请检查配置是否正确");
                        hideNewLoading();
                    }
                });
            });


        } catch (e) {
            onOperationFail("飞书接口调用失败，请在飞书打开链接");

        }


    });

    function closePage() {
        tt.closeWindow({
            fail(res) {
                console.log('关闭失败');
            }
        });
    }

    function onOperationFail(content) {
        $('#result').removeClass('hide');
        $('#content').html(content)
    }

    function onAtsVerifyFail() {
        $('#result').removeClass('hide');
        $('#content').html('您没有资金系统审批权限，请联系业务管理员开通')
    }

    function requestWithFailCallback(requestUrl, params, callback, fail) {

        let url = getContextPath() + requestUrl;
        let thisMethod = 'POST';
        if (params.method) {
            thisMethod = params.method;
        }
        $.ajax({
            // 是否使用缓存
            cache: true,
            // http请求方式
            type: thisMethod,
            // 请求url
            url: url,
            // 请求参数
            data: params,
            // 是否异步请求
            async: true,
            // 返回数据格式
            dataType: 'json',
            // 请求失败回调
            error: function (e) {
                hideNewLoading();


            },
            // 请求成功回调
            success: function (data) {
                hideNewLoading();
                if (data.successful === true || 'true' === data.successful) {
                    callback(data);
                } else {
                    fail();
                }
            }
        });
    }

</script>
</body>
</html>
