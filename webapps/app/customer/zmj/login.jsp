<%@ page import="cn.hutool.core.util.StrUtil" %>
<%@ page import="com.fingard.app.delegate.framework.util.MD5" %>
<%@page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/include.jsp" %>


<%

    String userCode = request.getParameter("userCode");
    String timestamp = request.getParameter("timestamp");
    String sign = request.getParameter("sign");

    String errPage = null;
    String previousPage = null;

    boolean err = false;
    if (!StrUtil.equalsIgnoreCase(sign, MD5.getMD5ofStr(userCode + timestamp))) {
        err = true;
        errPage = basePath + "/customer/zmj/error_login.jsp?errMsg=用户参数错误";

    } else {
        previousPage = basePath + "/modules2.4/home_page/zmj_home.jsp?selectType=zmjHome";
    }

%>
<c:set var="ssoUserCode" value="<%=userCode%>"/>
<c:set var="err" value="<%=err%>"/>

<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8"/>
    <meta content="no-cache">
    <meta content="0">
    <title>登录</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css">
    <link rel="stylesheet" href="${ctx}/css/jquery-weui.min.css">


    <style>
        body {
            padding: 20px;
        }
    </style>
</head>

<body>
<div class="logo" style=" width: 100%; height: 200px; margin-top: 30px; text-align: center;">

    <img src="../../img/logo.png" style="width: 120px; height: 120px; margin: 0 auto;"/>
</div>
<div class="weui-cells_form" style="margin-top: 10px;">
    <div class="weui-cell">
        <input class="weui-input" type="text" id="username" placeholder="用户名" value="${ssoUserCode}"
               disabled="disabled"/>
    </div>
    <div class="weui-cell">
        <input class="weui-input" type="password" id="password" placeholder="密码"/>
    </div>

</div>
<button class="weui-btn weui-btn_primary" id="login-btn" style="width: 100%; margin-top: 30px; text-align: center;">
    登录
</button>


<script src="${ctx}/js/jquery.min.js"></script>
<script src="${ctx}/js/jquery-weui.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script src="${ctx}/js/base64.js"></script>

<script>

    $.toast.prototype.defaults.duration = 1500;

    $(document).ready(function () {

        if ('${err}' === 'true') {
            window.location.href = '<%=errPage%>';
        }
        init();
        $('#login-btn').on('click', function () {

            var username = $('#username').val();
            var password = $('#password').val();
            if (username === '') {
                $.toast("用户名不得为空！", "forbidden");
                return;
            }
            if (password === '') {
                $.toast("密码不得为空！", "forbidden");
                return;
            }
            login(username, password);
        });

    });

    var init = function () {

        // 清空验证码
        var h_username = sessionStorage.getItem("USER_NAME");
        var h_password = sessionStorage.getItem("USER_PSD");
        $('#username').val('${ssoUserCode}');
        $('#password').val(h_password);


    };

    var login = function (uname, upsd) {
        var params = {};
        params[base64encode("username")] = base64encode(utf16to8(uname));
        params[base64encode("password")] = base64encode(utf16to8(upsd));
        params.loginTag = true;
        $.showLoading("登录中...");
        ajaxRequest("/user/login.do", params, function (data) {
            // 请求成功
            sessionStorage.setItem("USER_NAME", uname);
            sessionStorage.setItem("USER_PSD", upsd);
            $.toast(data.message);
            window.location.href = '<%=previousPage%>';
        })
    };


</script>
</body>

</html>
