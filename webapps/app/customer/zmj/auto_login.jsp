<%@ page import="cn.hutool.core.util.ObjectUtil" %>

<%@ page import="cn.hutool.crypto.SecureUtil" %>
<%@ page import="cn.hutool.crypto.symmetric.DES" %>
<%@ page import="com.fingard.app.delegate.beans.OpenResult" %>
<%@ page import="com.fingard.app.delegate.controller.third.CorpWechatHelper" %>
<%@ page import="com.fingard.app.delegate.framework.util.Constants" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@include file="/common/include.jsp" %>

<%--自动登录--%>
<%

    String userCode = String.valueOf(request.getSession().getAttribute(Constants.SSO_USER_ID));
    String taskId = String.valueOf(request.getSession().getAttribute("taskId"));
    String errMsg;

    String key = "ZMJ_ATS_2020";
    DES des = SecureUtil.des(key.getBytes());
    userCode = des.decryptStr(userCode);


    if (StrUtil.isNotEmpty(userCode)) {
        OpenResult login = CorpWechatHelper.login(request, userCode);
        if (ObjectUtil.isNotNull(login) && Boolean.TRUE.toString().equalsIgnoreCase(login.getSuccessful())) {
            String previousPage = String.valueOf(request.getSession().getAttribute(Constants.PREVIOUS_PAGE));
            previousPage = "null".equals(previousPage) ? "" : previousPage;

            if (StrUtil.isNullOrUndefined(previousPage) || StrUtil.isEmpty(previousPage)) {
                previousPage = basePath + "/modules2.4/flow/trans.jsp?taskId=" + taskId;
            }
            response.sendRedirect(basePath + previousPage);
            errMsg = StrUtil.EMPTY;
        } else {
            request.getSession().removeAttribute(Constants.SSO_USER_ID);
            errMsg = "用户【" + userCode + "】无法登录，请联系管理员检查用户状态或权限是否正常! ";
        }

    } else {
        request.getSession().removeAttribute(Constants.SSO_USER_ID);
        errMsg = "用户信息解密失败";
    }

%>
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">

    <title>登录失败</title>
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

    <script src="${ctx}/js/jquery-1.7.2.min.js"></script>
</head>

<body>
<div class="weui-msg" style="display:block;" id="s2">
    <div class="weui-msg__icon-area"><i class="weui-icon-warn  weui-icon_msg"></i></div>
    <div class="weui-msg__text-area">
        <h2 class="weui-msg__title">登录失败</h2>
        <p class="weui-msg__desc"><%=errMsg%>
        </p>
    </div>
</div>

<p class="weui-btn-area">
    <button id="return_list" class="weui-btn weui-btn_primary">退出</button>
</p>

<script>


    $(document).ready(function () {
        var return_list_btn = $('#return_list');
        return_list_btn.on('click', function () {
            try {
                document.addEventListener('WeixinJSBridgeReady', function () {
                    WeixinJSBridge.call('closeWindow');
                }, false);
                WeixinJSBridge.call('closeWindow');
            } catch (e) {
                // 非（企业）微信环境时返回上一页面
                history.back();
            }
        });
    });

</script>

</body>

</html>

