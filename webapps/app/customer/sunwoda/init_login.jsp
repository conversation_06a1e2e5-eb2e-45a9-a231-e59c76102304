<%@ page import="java.net.URLEncoder" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="/common/include.jsp" %>

<%--web.xml中loginPage配置这个页面，企业微信授权重定向--%>
<%

    String ssoWebUrl = MyProperties.getMyPropertiesInstance().getProperty("sunwoda.ssoWebUrl");
    String baseUrl = MyProperties.getMyPropertiesInstance().getProperty("app.gateway");
    String token = MyProperties.getMyPropertiesInstance().getProperty("sunwoda.token");
    String loginPage = baseUrl + "/customer/" + customer + "/login.jsp";
    loginPage = URLEncoder.encode(loginPage, "UTF-8");
//    String ticket = JsjdApiHelper.authorize();
%>

<c:set var="ssoWebUrl" value="<%=ssoWebUrl%>"/>
<c:set var="loginPage" value="<%=loginPage%>"/>
<c:set var="token" value="<%=token%>"/>


<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">

    <title>加载中</title>

</head>

<body>

<script>

    window.location.href = '${ssoWebUrl}?token=${token}&url=${loginPage}';
</script>

</body>

</html>

