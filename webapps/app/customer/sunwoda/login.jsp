<%@ page import="com.fingard.app.delegate.oalogin.ENCustomer" %>
<%@ page import="com.fingard.app.delegate.oalogin.IThirdSysUserDecrypt" %>
<%@ page import="cn.hutool.json.JSONObject" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="/common/include.jsp" %>

<%--企业微信标准对接--%>
<%
    String errMsg = "";
    IThirdSysUserDecrypt strategy = ENCustomer.getUserNameStrategy(customer);
    JSONObject result = strategy.decrypt(request, response);
    if (result.getBool("success")) {
        String userCode = result.getStr("data");
        String verifyPage = "/customer/sunwoda/verify_login.jsp?userCode=" + userCode;
        response.sendRedirect(basePath + verifyPage);
    } else {
        errMsg = result.getStr("message");
    }

%>

<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">

    <title>登录失败</title>
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

    <script src="${ctx}/js/jquery-1.7.2.min.js"></script>
</head>

<body>
<div class="weui-msg" style="display:block;" id="s2">
    <div class="weui-msg__icon-area"><i class="weui-icon-warn  weui-icon_msg"></i></div>
    <div class="weui-msg__text-area">
        <h2 class="weui-msg__title">登录失败</h2>
        <p class="weui-msg__desc"><%=errMsg%>
        </p>
    </div>
</div>

<p class="weui-btn-area">
    <button id="return_list" class="weui-btn weui-btn_primary">退出</button>
</p>

<script>
    $(document).ready(function () {

        var return_list_btn = $('#return_list');
        return_list_btn.on('click', function () {
            document.addEventListener('WeixinJSBridgeReady', function () {
                WeixinJSBridge.call('closeWindow');
            }, false);
            WeixinJSBridge.call('closeWindow');
        });
    });

</script>

</body>

</html>

