<%@ page import="java.net.URLEncoder" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="/common/include.jsp" %>

<%--企业微信扫码登录--%>
<%
    String appId = MyProperties.getMyPropertiesInstance().getProperty("qywx.corpId");
    String agentId = MyProperties.getMyPropertiesInstance().getProperty("qywx.agentId");
    String baseUrl = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+request.getContextPath();
    String loginPage = baseUrl + "/customer/"+customer+"/login.jsp";
    loginPage = URLEncoder.encode(loginPage, "UTF-8");
%>

<c:set var="appId" value="<%=appId%>"/>
<c:set var="loginPage" value="<%=loginPage%>"/>
<c:set var="agentId" value="<%=agentId%>"/>




<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">

    <title>企业微信扫码</title>

</head>
<body>
<script>
    var url = 'https://open.work.weixin.qq.com/wwopen/sso/qrConnect?appid=${appId}&agentid=${agentId}&redirect_uri=${loginPage}&state=STATE';
    window.location.href = url;
</script>

</body>

</html>

