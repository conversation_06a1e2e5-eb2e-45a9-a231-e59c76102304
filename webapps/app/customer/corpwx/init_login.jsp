<%@ page import="java.net.URLEncoder" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="/common/include.jsp" %>

<%--web.xml中loginPage配置这个页面，企业微信授权重定向--%>
<%

    String appId = MyProperties.getMyPropertiesInstance().getProperty("qywx.corpId");
    String baseUrl = MyProperties.getMyPropertiesInstance().getProperty("app.gateway");
    String loginPage = baseUrl + "/customer/"+customer+"/login.jsp";
    loginPage = URLEncoder.encode(loginPage, "UTF-8");
%>

<c:set var="appId" value="<%=appId%>"/>
<c:set var="loginPage" value="<%=loginPage%>"/>


<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">

    <title>加载中</title>

</head>

<body>

<script>

    window.location.href = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${loginPage}&response_type=code&scope=snsapi_base&state=ATS#wechat_redirect';
</script>

</body>

</html>

