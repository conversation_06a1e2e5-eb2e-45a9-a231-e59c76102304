<%@ page import="cn.hutool.core.util.ObjectUtil" %>

<%@ page import="com.fingard.app.delegate.beans.OpenResult" %>
<%@ page import="com.fingard.app.delegate.framework.util.Constants" %>
<%@ page import="com.fingard.app.delegate.controller.third.CorpWechatHelper" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@include file="/common/include.jsp" %>


<%
    String code = request.getParameter("userCode");
    if (StrUtil.isEmpty(code)) {
        // ssoUserCode：存储在session中的userCode，其他页面作为第一链接时，从session中获取该值
        code = String.valueOf(request.getSession().getAttribute(Constants.SSO_USER_ID));
    }
    String errMsg;
    if (StrUtil.isNotEmpty(code)) {
        System.out.println("userId=" + code);
        OpenResult login = CorpWechatHelper.login(request, code);
        if (ObjectUtil.isNotNull(login) && Boolean.TRUE.toString().equalsIgnoreCase(login.getSuccessful())) {

            String previousPage = String.valueOf(request.getSession().getAttribute(Constants.PREVIOUS_PAGE));
            previousPage = "null".equals(previousPage) ? "" : previousPage;

//                response.sendRedirect(basePath + "/modules/flow/flowTypeList.jsp?pmDealState=1");
            if (StrUtil.isNullOrUndefined(previousPage) || StrUtil.isEmpty(previousPage)) {
                previousPage = "/modules2.4/flow/flowTypeList.jsp?selectType=flow";
            }
            response.sendRedirect(basePath + previousPage);
            errMsg = StrUtil.EMPTY;
        } else {
            errMsg = "用户无法登录，请检查资金管理系统中是否存在用户: " + code;
        }
    } else {
        errMsg = "用户名不得为空";
    }

%>
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">

    <title>登录结果</title>
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

    <script src="${ctx}/js/jquery-1.7.2.min.js"></script>
</head>

<body>
<div class="weui-msg" style="display:block;" id="s2">
    <div class="weui-msg__icon-area"><i class="weui-icon-warn  weui-icon_msg"></i></div>
    <div class="weui-msg__text-area">
        <h2 class="weui-msg__title">登录失败</h2>
        <p class="weui-msg__desc"><%=errMsg%>
        </p>
    </div>
</div>

<p class="weui-btn-area">
    <button id="return_list" class="weui-btn weui-btn_primary">退出</button>
</p>

<script>


    $(document).ready(function () {
        var return_list_btn = $('#return_list');
        return_list_btn.on('click', function () {
            try {
                document.addEventListener('WeixinJSBridgeReady', function () {
                    WeixinJSBridge.call('closeWindow');
                }, false);
                WeixinJSBridge.call('closeWindow');
            } catch (e) {
                // 非（企业）微信环境时返回上一页面
                history.back();
            }
        });
    });

</script>

</body>

</html>

