<%@ page import="java.net.URLEncoder" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="/common/include.jsp" %>

<%--web.xml中loginPage配置这个页面，授权重定向--%>
<%

    String authUrl = MyProperties.getMyPropertiesInstance().getProperty("twgf.newSsoUrl");
    String clientId = MyProperties.getMyPropertiesInstance().getProperty("twgf.clientId");
    String baseUrl = MyProperties.getMyPropertiesInstance().getProperty("app.gateway");
    String loginPage = baseUrl + "/customer/" + customer + "/login.jsp";
    loginPage = URLEncoder.encode(loginPage, "UTF-8");
//    String ticket = JsjdApiHelper.authorize();
%>

<c:set var="authUrl" value="<%=authUrl%>"/>
<c:set var="loginPage" value="<%=loginPage%>"/>
<c:set var="clientId" value="<%=clientId%>"/>
<%--<c:set var="ticket" value="<%=ticket%>"/>--%>


<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">

    <title>加载中</title>

</head>

<body>

<script>

    <%--window.location.href = '${authUrl}/oauthapi/v2/authorize?redirect_uri=${loginPage}&state=ATSAPP&client_id=${clientId}&response_type=code';--%>
    window.location.href = '${authUrl}/oauthapi/v2/authorize?response_type=code&appid=${clientId}&autologin=true&redirect_uri=${loginPage}&scope=user_info&state=ATSAPP';
</script>

</body>

</html>

