<%@ page import="cn.hutool.core.util.ObjectUtil" %>

<%@ page import="cn.hutool.json.JSONObject" %>
<%@ page import="com.fingard.app.delegate.beans.OpenResult" %>
<%@ page import="com.fingard.app.delegate.controller.third.CorpWechatHelper" %>
<%@ page import="com.fingard.app.delegate.oalogin.ENCustomer" %>
<%@ page import="com.fingard.app.delegate.oalogin.IThirdSysUserDecrypt" %>
<%@ page import="com.google.gson.JsonObject" %>
<%@ page import="com.fingard.app.delegate.controller.SysParamController" %>
<%@ page import="com.fingard.app.delegate.helper.EndpointProperties" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@include file="/common/include.jsp" %>

<%--主业务标准OA认证登录--%>
<%
    String errMsg;

    try {
        IThirdSysUserDecrypt strategy = ENCustomer.getUserNameStrategy(customer);
        JSONObject result = strategy.decrypt(request, response);
        if (result != null) {
            if (result.getBool("success")) {
                String userCode = result.getStr("data");

                OpenResult login = CorpWechatHelper.login(request, userCode);
                if (ObjectUtil.isNotNull(login) && Boolean.TRUE.toString().equalsIgnoreCase(login.getSuccessful())) {

                    if (StrUtil.equalsIgnoreCase(customer, "yangzj") && request.getParameter("userId") != null) {
                        // 扬子江查询待办数量
                        JsonObject jsonObject = new JsonObject();
                        jsonObject.addProperty("count", SysParamController.getFlowTodoCount(request));
                        response.setContentType("application/json");
                        response.getWriter().print(jsonObject);
                        return;
                    }
                    String previousPage = String.valueOf(request.getSession().getAttribute(Constants.PREVIOUS_PAGE));
                    if (previousPage == null || StrUtil.isNullOrUndefined(previousPage)) {
                        previousPage = MyProperties.getMyPropertiesInstance().getProperty("home.page");
                    }
                    if (previousPage == null || StrUtil.isNullOrUndefined(previousPage)) {
                        previousPage = "/modules2.4/home_page/home.jsp?selectType=home";
                    }
                    if (StrUtil.isEmpty(request.getParameter("taskId"))) {
                        previousPage = "/modules2.4/home_page/home.jsp?selectType=home";
                    }
                    response.sendRedirect(basePath + previousPage);
                    errMsg = StrUtil.EMPTY;

                } else {// loginFail
                    errMsg = login.getMessage();
                }
            } else { // result.success==false
                errMsg = result.getStr("message");
            }
        } else { // result==null
            errMsg = "加载中...";
        }
    } catch (Exception e) {
        errMsg = "数据异常";

    }


%>
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">

    <title>登录失败</title>
    <link rel="stylesheet" href="../../css/weui.min.css"/>
    <link rel="stylesheet" href="../../css/weuix.min.css"/>

    <script src="../../js/jquery-1.7.2.min.js"></script>
</head>

<body>
<div class="weui-msg" style="display:block;" id="s2">
    <div class="weui-msg__icon-area"><i class="weui-icon-warn  weui-icon_msg"></i></div>
    <div class="weui-msg__text-area">
        <h2 class="weui-msg__title">登录失败</h2>
        <p class="weui-msg__desc"><%=errMsg%>
        </p>
    </div>
</div>

<p class="weui-btn-area">
    <button id="return_list" class="weui-btn weui-btn_primary">退出</button>
</p>

<script>


    $(document).ready(function () {
        var return_list_btn = $('#return_list');
        return_list_btn.on('click', function () {
            try {
                document.addEventListener('WeixinJSBridgeReady', function () {
                    WeixinJSBridge.call('closeWindow');
                }, false);
                WeixinJSBridge.call('closeWindow');
            } catch (e) {
                // 非（企业）微信环境时返回上一页面
                history.back();
            }
        });
    });

</script>

</body>

</html>

