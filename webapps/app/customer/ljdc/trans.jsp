<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="/common/include.jsp" %>
<%--审批中转--%>
<%
    String taskId = request.getParameter("taskId");
%>
<c:set var="taskId" value="<%=taskId%>"/>

<html>
<head>

    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">

    <title>加载单据</title>

    <link rel="stylesheet" href="${ctx}/css/weui.min.css">
    <link rel="stylesheet" href="${ctx}/css/jquery-weui.min.css">
    <style>
        body {
            padding: 20px;
        }
    </style>
</head>

<body>
<div id="content" style="display: none;">
    <div class="weui-msg" id="s2">
        <div class="weui-msg__icon-area"><i class="weui-icon-warn  weui-icon_msg"></i></div>
        <div class="weui-msg__text-area">
            <h2 class="weui-msg__title">单据不存在</h2>
            <p class="weui-msg__desc">
            </p>
        </div>
    </div>

    <p class="weui-btn-area">
        <button id="return_list" class="weui-btn weui-btn_primary">返回</button>
    </p>
</div>
<%--<button id="btn" class="weui-btn weui-btn_primary">获取token</button>--%>


<script src="${ctx}/js/jquery.min.js"></script>
<script src="${ctx}/js/jquery-weui.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>
<script>

    $(document).ready(function () {

        getToken();

        var return_list_btn = $('#return_list');
        return_list_btn.on('click', function () {
            try {
                document.addEventListener('WeixinJSBridgeReady', function () {
                    WeixinJSBridge.call('closeWindow');
                }, false);
                WeixinJSBridge.call('closeWindow');
            } catch (e) {
                // 非（企业）微信环境时返回上一页面
                history.back();
            }
        });
    });


    function getToken() {
        $.showLoading("加载中...")
        ajaxRequestWithFailCallback('/saasflow/getFlowParams.do', {taskId: '${taskId}'}, function (response) {
            let result = response.result;
            let dealState = result.dealState;

            var params = {};
            params.commandCode = result.transCode;
            params.itemId = result.itemId;
            params.workflowId = result.workflowId;
            params.workflowType = result.workflowType;
            sessionStorage.setItem("DETAIL_FLOW_PARAMS", JSON.stringify(params));
            window.location.href = '${ctx}/modules2.4/flow/flowDetailUnified.jsp?' +
                'dealState=' + dealState +
                '&flowType=' + result.flowType;

        }, function () {
            $('#content').css('display', 'block');
            $('.weui-msg__desc').html('单据[${taskId}]查询异常：' + data.message);
        })


    }

</script>

</body>
</html>
