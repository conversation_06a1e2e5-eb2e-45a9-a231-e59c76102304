<%@page import="com.fingard.app.delegate.controller.third.WechatBizController" %>
<%@page import="net.sf.json.JSONObject" %>
<%@page import="org.apache.commons.lang.StringUtils" %>
<%@ page import="com.fingard.app.delegate.framework.util.Constants" %>
<%@page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/include.jsp" %>
<%

	String code = String.valueOf(request.getSession().getAttribute("code"));
	String previousPage = String.valueOf(request.getSession().getAttribute(Constants.PREVIOUS_PAGE));
	previousPage = "null".equals(previousPage) ? "" : previousPage;
	JSONObject json = WechatBizController.getOpenId(code);
	String openid = "";
	String accessToken = "";
	String tmpOpenid = "";
	System.out.println(json == null ? "OPENID获取失败" : "OPENID获取成功");
	if (json != null) {
		if (json.get("errcode") == null) {
			openid = json.getString("openid");
			tmpOpenid = openid;
			accessToken = json.getString("access_token");
			request.getSession().setAttribute("openid", openid);
		} else {
			System.out.println("ERR-CODE: " + json.get("errcode"));
		}
	}

	if (StringUtils.isEmpty(openid)) {
		openid = String.valueOf(request.getSession().getAttribute("openid"));
	}

	openid = String.valueOf(request.getSession().getAttribute("openid"));
%>
<!DOCTYPE html>
<html lang="en">
<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
	<meta charset="utf-8"/>
	<title>浙江省交通投资集团统一结算平台微信服务</title><!-- 交投资金管理系统 -->

	<meta name="description" content="中台管理系统|登录"/>
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0"/>

	<script type="text/javascript">
        if (top != self) {
            parent.window.location.href = "${ctx}" + "<%=request.getSession().getServletContext().getInitParameter("loginPage")%>";
        }
	</script>
	<!-- bootstrap & fontawesome -->
	<link rel="stylesheet" href="${ctx}/admin/assets/css/bootstrap.css"/>
	<link rel="stylesheet" href="${ctx}/admin/assets/css/font-awesome.css"/>

	<!-- text fonts -->
	<link rel="stylesheet" href="${ctx}/admin/assets/css/ace-fonts.css"/>

	<!-- ace styles -->
	<link rel="stylesheet" href="${ctx}/admin/assets/css/ace.css"/>

	<!--[if lte IE 9]>
	<link rel="stylesheet" href="${ctx}/admin/assets/css/ace-part2.css"/>
	<![endif]-->
	<link rel="stylesheet" href="${ctx}/admin/assets/css/ace-rtl.css"/>

	<!--[if lte IE 9]>
	<link rel="stylesheet" href="${ctx}/admin/assets/css/ace-ie.css"/>
	<![endif]-->

	<!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->

	<!--[if lt IE 9]>
	<script src="${ctx}/admin/assets/js/html5shiv.js"></script>
	<script src="${ctx}/admin/assets/js/respond.js"></script>
	<![endif]-->
	<script src='${ctx}/admin/assets/js/base64.js'></script>
	<script src='${ctx}/js/mui.min.js'></script>
	<style>
		* {
			overflow: hidden;
		}
	</style>
</head>

<body class="login-layout light-login">
<div class="main-container">
	<div class="main-content">
		<div class="row">
			<div class="col-sm-10 col-sm-offset-1" style="margin-top:10%;">
				<div class="login-container">
					<div class="center">
						<h1>
							<!-- <i class="ace-icon fa fa-leaf green"></i> -->
							<span class="gray" id="id-text2">浙江省交通投资集团<br/>统一结算平台微信服务</span>
							<br/>
						</h1>
					</div>

					<div class="space-6"></div>

					<div class="position-relative">
						<div id="login-box" class="login-box visible widget-box no-border">
							<div class="widget-body">
								<div class="widget-main">
									<h4 class="header blue lighter bigger">
										<!-- <i class="ace-icon fa fa-coffee green"></i> -->
										请输入您的信息
									</h4>

									<div class="space-6"></div>
									<form id="login-form" method="post">
										<fieldset>
											<label class="block clearfix">
												<span class="block input-icon input-icon-right">
													<input id="txt_user" type="text" class="form-control"
														   name="username" placeholder="用户名"/>
													<i class="ace-icon fa fa-user"></i>
												</span>
											</label>

											<label class="block clearfix">
												<span class="block input-icon input-icon-right">
													<input id="txt_pwd" type="password" class="form-control"
														   name="password" placeholder="密码"/>
													<i class="ace-icon fa fa-lock"></i>
												</span>
											</label>

											<div class="space"></div>

											<div class="clearfix">
												<label class="inline">
													<input id="cb_rememberme" type="checkbox" class="ace"/>
													<span class="lbl"> 记住密码</span>
												</label>

												<button id="login" type="button"
														class="width-35 pull-right btn btn-sm btn-primary">
													<i class="ace-icon fa fa-key"></i>
													<span class="bigger-110">登录</span>
												</button>
											</div>

											<div class="space-4"></div>
										</fieldset>
									</form>
								</div><!-- /.widget-main -->
							</div><!-- /.widget-body -->
						</div><!-- /.login-box -->
					</div><!-- /.position-relative -->
				</div>
				<div class="login-container">
					<div class="center">
						<br/><br/>
						<h5 class="blue" id="id-company-text">&copy; 浙江省交通投资集团财务有限责任公司</h5>
					</div>
				</div>
			</div><!-- /.col -->
		</div><!-- /.row -->
	</div><!-- /.main-content -->
</div><!-- /.main-container -->

<!-- basic scripts -->

<!--[if !IE]> -->
<script type="text/javascript">
    window.jQuery || document.write("<script src='${ctx}/admin/assets/js/jquery.js'>" + "<" + "/script>");
    document.write("<script src='${ctx}/js/jquery.base64.js'>" + "<" + "/script>");
</script>

<!-- <![endif]-->

<!--[if IE]>
<script type="text/javascript">
	window.jQuery || document.write("<script src='${ctx}/admin/assets/js/jquery1x.js'>" + "<" + "/script>");
</script>
<![endif]-->
<script type="text/javascript">
    if ('ontouchstart' in document.documentElement) document.write("<script src='${ctx}/admin/assets/js/jquery.mobile.custom.js'>" + "<" + "/script>");
</script>

<!-- inline scripts related to this page -->
<script type="text/javascript">
    var openid = '<%=openid%>';
    var accessToken = '<%=accessToken%>';
    var pre_page;
    if (localStorage.getItem("openid") == 'null' || localStorage.getItem("openid") == null) {
        localStorage.setItem('openid', '<%=tmpOpenid%>');
    }

    localStorage.setItem('prePage', '<%=previousPage%>');


    if (openid == null || openid == 'null' || openid == '') {
        openid = localStorage.getItem("openid");
    }
    if (pre_page == null || pre_page == 'null' || pre_page == '') {
        pre_page = localStorage.getItem("prePage");
    }

    jQuery(function ($) {

        $("#login").click(function (event) {
            if ($("#cb_rememberme").is(":checked")) {
                localStorage.setItem("it", true);
                localStorage.setItem("un", compileStr($("#txt_user").val()));
                localStorage.setItem("pd", compileStr($("#txt_pwd").val()));
            } else {
                localStorage.setItem("it", false);
                localStorage.removeItem("un");
                localStorage.removeItem("pd");
            }
            dologin(false);
        });

    });

    if (localStorage.getItem("it") == true || localStorage.getItem("it") == "true") {
        $("#txt_user").val(uncompileStr(localStorage.getItem("un")));
        $("#txt_pwd").val(uncompileStr(localStorage.getItem("pd")));
        $("#cb_rememberme").attr("checked", true);
        //此注释为微信平台【退出】按钮所设置，屏蔽自动登陆的功能
        /* if(localStorage.getItem("un") && localStorage.getItem("pd")){
            dologin(false);
        } */
    }


    function dologin(auto) {

        var username = $("#txt_user").val();
        var userpsd = $("#txt_pwd").val();
        if (username == '' || userpsd == '') {
            alert('用户名或密码不得为空！');
            return;
        }
        var params = {};
        params[base64encode(utf16to8("username"))] = base64encode(utf16to8($("#txt_user").val()));
        params[base64encode(utf16to8("password"))] = base64encode(utf16to8($("#txt_pwd").val()));

        params['openid'] = openid;

        //var params = $("#login-form").serialize();
        $.ajax({
            cache: false,
            type: "POST",
            url: "${ctx}/user/login.do",
            data: params,
            async: true,
            dataType: 'json',
            error: function (jqXHR, textStatus, errorThrown) {
            },
            success: function (data) {
                if (!data.successful || data.successful == "false" && !data.message == '还未绑定微信，请先绑定') {
                    alert(data.msg ? data.msg : data.message);
                    return;
                }
                if (data.message == '还未绑定微信，请先绑定') {

                    var bindConfirm = confirm(data.message + '!注意：绑定后只能通过该微信账号登陆系统，确定继续？');
                    if (bindConfirm == true) {
                        var username = $("#txt_user").val();
                        ajaxRequest('${ctx}/user/bindWechatAccount.do', {
                            username: username,
                            openid: openid
                        }, function (data) {
                            alert(data.message);
                            return;
                        });
                    } else {
                        return;
                    }

                } else if (data.successful == 'true') {
                    //var page = '<%=previousPage%>';
                    var page = pre_page;
                    page = page ? ("${ctx}" + page) : "${ctx}/modules/report/reportTypeList.jsp";
                    window.location.href = page;
                } else {
                    alert('错误信息：' + data.message);
                    return;
                }
            }
        });
    }


    function compileStr(code) { //对字符串进行加密
        var c = String.fromCharCode(code.charCodeAt(0) + code.length);
        for (var i = 1; i < code.length; i++) {
            c += String.fromCharCode(code.charCodeAt(i)
                + code.charCodeAt(i - 1));
        }
        return escape(c);
    }

    //字符串进行解密
    function uncompileStr(code) {
        if (!code) {
            return "";
        }
        code = unescape(code);
        var c = String.fromCharCode(code.charCodeAt(0) - code.length);
        for (var i = 1; i < code.length; i++) {
            c += String.fromCharCode(code.charCodeAt(i) - c.charCodeAt(i - 1));
        }
        return c;
    }


    var ajaxRequest = function (requestUrl, params, callback) {
        $.ajax({
            // 是否使用缓存
            cache: true,
            // http请求方式
            type: 'POST',
            // 请求url
            url: requestUrl,
            // 请求参数
            data: params,
            // 是否异步请求
            async: true,
            // 返回数据格式
            dataType: 'json',
            // 请求失败回调
            error: function (data) {
                alert('网络连接异常');
            },
            // 请求成功回调
            success: function (data) {
                if (data.successful == true || 'true' == data.successful) {
                    callback(data);
                } else {
                    alert('错误：' + data.message);
                }
            }
        });
    }

    var overscroll = function (els) {
        for (var i = 0; i < els.length; ++i) {
            var el = els[i];
            el.addEventListener('touchstart', function () {
                var top = this.scrollTop
                    , totalScroll = this.scrollHeight
                    , currentScroll = top + this.offsetHeight;
                //If we're at the top or the bottom of the containers
                //scroll, push up or down one pixel.
                //
                //this prevents the scroll from "passing through" to
                //the body.
                if (top === 0) {
                    this.scrollTop = 1;
                } else if (currentScroll === totalScroll) {
                    this.scrollTop = top - 1;
                }
            });
            el.addEventListener('touchmove', function (evt) {
                //if the content is actually scrollable, i.e. the content is long enough
                //that scrolling can occur
                if (this.offsetHeight < this.scrollHeight)
                    evt._isScroller = true;
            });
        }
    };

    //禁止body的滚动事件
    document.body.addEventListener('touchmove', function (evt) {
        //In this case, the default behavior is scrolling the body, which
        //would result in an overflow.  Since we don't want that, we preventDefault.
        if (!evt._isScroller) {
            evt.preventDefault();
        }
    });

    //给class为.scroll的元素加上自定义的滚动事件
    overscroll(document.querySelectorAll('.scroll'));
</script>
</body>
</html>
