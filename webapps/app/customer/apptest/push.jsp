<%@page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/include.jsp" %>
<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>Msg Push Test</title>
    <link href="${ctx}/css/bootstrap.min.css" rel="stylesheet"/>
    <style type="text/css">
        h3 {
            text-align: center;
        }
    </style>
</head>

<body>

<div class="container">
    <h3>Msg Push Test</h3>
    <div class="form-horizontal" role="form">
        <div class="form-group">
            <label for="userName" class="col-sm-3 control-label">Username:</label>
            <div class="col-sm-6">
                <input type="text" class="form-control" id="userName" placeholder="judy" value="judy">
            </div>
        </div>
        <div class="form-group">
            <label for="title" class="col-sm-3 control-label">Title:</label>
            <div class="col-sm-6">
                <input type="text" class="form-control" id="title" placeholder="This is a msg title!"
                       value="This is a msg title!">
            </div>
        </div>
        <div class="form-group">
            <label for="content" class="col-sm-3 control-label">content:</label>
            <div class="col-sm-6">
                <input type="text" class="form-control" id="content" placeholder="This is a msg content!"
                       value="This is a msg content!">
            </div>
        </div>

        <div class="form-group">
            <label for="todoCount" class="col-sm-3 control-label">Todo Count:</label>
            <div class="col-sm-6">
                <input type="text" class="form-control" id="todoCount" placeholder="2" value="2">
            </div>
        </div>

    </div>
    <div class="form-group">
        <div class="col-sm-offset-3 col-sm-6">
            <button type="button" id="send" class="btn btn-default">send</button>
        </div>
    </div>
</div>
<script type="text/javascript" src="${ctx}/js/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/js/bootstrap.min.js"></script>
<script type="text/javascript" src="${ctx}/js/base64.js"></script>

<script>
    $(document).ready(function () {
        console.log("start @ " + getNowFormatDate())
        $('#send').on('click', function () {
            console.log("click send");
            sendMsg();

        });
    })

    var getNowFormatDate = function () {
        var date = new Date();
        var seperator1 = "-";
        var seperator2 = ":";
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        return date.getFullYear() + seperator1 + month + seperator1 + strDate +
            " " + date.getHours() + seperator2 + date.getMinutes() +
            seperator2 + date.getSeconds();
    }

    var randomNum = function (minNum, maxNum) {
        switch (arguments.length) {
            case 1:
                return parseInt(Math.random() * minNum + 1, 10);
            case 2:
                return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10);
            default:
                return 0;
        }
    }

    var sendRequest = function (url, params, successCallback) {

        $.ajax({
            // 是否使用缓存
            cache: true,
            // http请求方式
            type: 'POST',
            // 请求url
            url: url,
            // 请求参数
            data: params,
            // 是否异步请求
            async: true,
            // 返回数据格式
            dataType: 'json',
            // 请求失败回调
            error: function (data) {
                alert("Connected Failed!")
            },
            // 请求成功回调
            success: function (data) {
                successCallback(data);
            }
        });
    }

    var sendMsg = function () {
        var user = $('#userName').val();
        var title = $('#title').val();
        var description = $('#content').val();
        var todoCount = $('#todoCount').val();
        var occurrenceTime = getNowFormatDate();
        var username = base64encode('fingard');
        var password = base64encode('fingard123');
        var id = randomNum(0, 99999999);

        console.log(user + ";" + title + ";" +
            description + ";" + todoCount + ";" +
            occurrenceTime + ";" + username + ";" +
            password + ";" + id + ";");

        var url = "${ctx}/appMsgServlet";
        var params = {};
        params.user = base64encode(user);
        params.title = title;
        params.description = description;
        params.todoCount = todoCount;
        params.occurrenceTime = occurrenceTime;
        params.username = username;
        params.password = password;
        params.id = id;

        sendRequest(url, params, function (data) {
            console.log(data);
            if (data.successful) {
                alert("推送成功");
            } else {
                alert("推送失敗：" + data.message);
            }
        })

    }
</script>
</body>

</html>
