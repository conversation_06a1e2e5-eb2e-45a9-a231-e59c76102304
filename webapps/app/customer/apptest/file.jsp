<%@page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/include.jsp" %>

<!DOCTYPE html>
<html>
<head>
	<meta charset='utf-8'>
	<meta http-equiv='X-UA-Compatible' content='IE=edge'>
	<title>
		file upload test
	</title>
	<meta name='viewport' content='width=device-width, initial-scale=1'>
</head>
<body>
<form id="uploadForm" enctype="multipart/form-data">
	<input id="files" type="file" name="files" multiple/>
	<input id="fileName" type="text" name="fileName" value="testfile"/>
	<button onclick="clickUpload()" id="upload" type="button">
		upload
	</button>
</form>
<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.7.1/jquery.min.js">
</script>
<script>
	let clickUpload = function() {
		$.ajax({
			url: '${ctx}/collection/upload.do',
			type: 'POST',
			cache: false,
			data: new FormData($('#uploadForm')[0]),
			processData: false,
			contentType: false
		}).done(function(res) {}).fail(function(res) {});
	}
</script>
</body>

</html>