<%@ page import="cn.hutool.core.util.ObjectUtil" %>
<%@ page import="com.fingard.app.delegate.beans.OpenResult" %>
<%@ page import="com.fingard.app.delegate.controller.third.CorpWechatHelper" %>
<%@ page import="com.fingard.app.delegate.oalogin.ENCustomer" %>
<%@ page import="com.fingard.app.delegate.helper.EndpointProperties" %>
<%@ page import="com.fingard.app.delegate.oalogin.IThirdSysUserDecrypt" %>
<%@ page import="cn.hutool.json.JSONObject" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@include file="/common/include.jsp" %>


<%

    IThirdSysUserDecrypt userNameStrategy = ENCustomer.getUserNameStrategy(EndpointProperties.getCustomerName());
    JSONObject decrypt = userNameStrategy.decrypt(request, response);

    String errMsg;
    String data = decrypt.getStr("data");

    if (StrUtil.isNotEmpty(data)) {

        OpenResult login = CorpWechatHelper.login(request, data);
        if (ObjectUtil.isNotNull(login) && Boolean.TRUE.toString().equalsIgnoreCase(login.getSuccessful())) {

            String indexPage = "/longhu/fund_index.jsp";

            response.sendRedirect(basePath + indexPage);
            errMsg = StrUtil.EMPTY;
        } else {
            errMsg = "抱歉，您暂没权限，请在权限自助门户申请权限";
        }

    } else {
        errMsg =  "抱歉，您暂没权限，请在权限自助门户申请权限";
    }

%>
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">

    <title>登录提示</title>
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

    <script src="${ctx}/js/jquery-1.7.2.min.js"></script>
</head>

<body>
<div class="weui-msg" style="display:block;" id="s2">
    <div class="weui-msg__icon-area"><i class="weui-icon-cancel  weui-icon_msg"></i></div>
    <div class="weui-msg__text-area">
        <h2 class="weui-msg__title">提示</h2>
        <p class="weui-msg__desc"><%=errMsg%>
        </p>
    </div>
</div>

<p class="weui-btn-area">
    <button id="return_list" class="weui-btn weui-btn_primary">退出</button>
</p>

<script>


    $(document).ready(function () {
        var return_list_btn = $('#return_list');
        return_list_btn.on('click', function () {
            try {
                document.addEventListener('WeixinJSBridgeReady', function () {
                    WeixinJSBridge.call('closeWindow');
                }, false);
                WeixinJSBridge.call('closeWindow');
            } catch (e) {
                // 非（企业）微信环境时返回上一页面
                history.back();
            }
        });
    });

</script>

</body>

</html>

