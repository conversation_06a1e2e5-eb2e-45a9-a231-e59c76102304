<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>

    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css">
    <link rel="stylesheet" href="${ctx}/css/jquery-weui.min.css">

    <style>
        body {
            padding: 20px;
        }
    </style>
</head>
<body>
<div style="width: 100%;height: 300px; text-align: center">
    <img id="avatar" style="width: 200px;height: 200px;" src=""/>
    <p id="name"></p>
</div>

<button id="btn" class="weui-btn weui-btn_primary">点击登录</button>

<script src="${ctx}/js/jquery.min.js"></script>
<script src="${ctx}/js/jquery-weui.min.js"></script>
<script src="//g.alicdn.com/dingding/dingtalk-jsapi/2.6.41/dingtalk.open.js"></script>

<script src="${ctx}/js/common.js?version=${version}"></script>

<script>
    $(document).ready(function () {

        $('#btn').on('click', function () {

            delegate.ready(function (data) {
                dd.runtime.permission.requestAuthCode({
                    corpId: data.result.ddCorpId,
                    onSuccess: function (result) {
                        var params = {};
                        params.authCode = result.code;
                        ajaxRequest("/sysParam/getUserInfo.do", params, function (resp) {
                            alert(JSON.stringify(resp));
                            $('#avatar').attr('src', resp.result.avatar);
                            $('#name').html(resp.result.name);
                            ajaxRequest("/sysParam/login.do", {userId: resp.result.jobnumber}, function (loginResult) {
                                alert(JSON.stringify(loginResult));
                                window.location.href = data.result.previousPage;
                            })
                        })
                    },
                    onFail: function (err) {
                        alert(JSON.stringify(err));
                    }
                })
            })
        })
    });
</script>
</body>
</html>
