<%@ page import="com.fingard.app.delegate.framework.util.Constants" %>
<%@ page import="com.fingard.app.delegate.helper.EndpointProperties" %>
<%@page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/include.jsp" %>
<%
    String corpId = EndpointProperties.getDdCorpId();
    Object attribute = request.getSession().getAttribute(Constants.PREVIOUS_PAGE);
    String previousUrl = attribute == null ? (basePath + "/modules2.4/home_page/home.jsp?selectType=home") : (basePath + attribute);

%>
<c:set var="ddCorpId" value="<%=corpId%>"/>
<c:set var="previousUrl" value="<%=previousUrl%>"/>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>

    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css">
    <link rel="stylesheet" href="${ctx}/css/jquery-weui.min.css">
</head>
<body>
<script src="${ctx}/js/jquery.min.js"></script>
<script src="${ctx}/js/jquery-weui.min.js"></script>
<script src="//g.alicdn.com/dingding/dingtalk-jsapi/2.6.41/dingtalk.open.js"></script>


<script src="${ctx}/js/common.js?version=${version}"></script>

<script>
    $(document).ready(function () {

        let ddUserId;
        if (dd.env.platform !== "notInDingTalk") {
            $.showLoading();
            dd.runtime.permission.requestAuthCode({
                corpId: '${ddCorpId}',
                onSuccess: function (result) {
                    var params = {};
                    params.authCode = result.code;
                    ddAjaxRequestWithFailCallback("/dd/getUserId.do", params, function (resp) {
                        ddUserId = resp.result;
                        if (ddUserId !== '' && ddUserId !== null && ddUserId !== undefined) {
                            $.showLoading();
                            ddAjaxRequestWithFailCallback("/dd/login.do", {userId: ddUserId}, function (loginResult) {
                                window.location.href = '${previousUrl}';
                                // location.replace('../../modules2.4/report/standard/new_fund_summary_bank_balance_financing.jsp?selectType=zllHome')
                                // location.replace('../../modules2.4/home_page/home.jsp?selectType=home')
                            }, function () {
                                onAtsVerifyFail();
                            })
                        } else {
                            // 从钉钉接口获取用户信息失败
                            onOperationFail("获取用户信息失败");
                        }

                    }, function () {
                        // 从钉钉接口获取用户信息失败
                        onOperationFail("获取用户信息失败");
                    })
                },
                onFail: function (err) {
                    onOperationFail("获取钉钉授权码失败，请检查配置是否正确");
                    $.hideLoading();
                }
            })
        } else {
            $.toast("请在钉钉中打开链接", "cancel");
        }

    });

    function onOperationFail(content) {
        $.alert({
            title: '操作失败',
            text: content,
            onOK: function () {
                dd.biz.navigation.close({
                    onSuccess: function (result) {
                    },
                    onFail: function (err) {
                    }
                })

            }
        });
    }

    function onAtsVerifyFail() {
        $.alert({
            title: '权限验证提示',
            text: '您没有资金系统审批权限，请联系业务管理员开通',
            onOK: function () {
                dd.biz.navigation.close({
                    onSuccess: function (result) {
                    },
                    onFail: function (err) {
                    }
                })
            }
        });
    }

    function ddAjaxRequestWithFailCallback(requestUrl, params, callback, fail) {

        let url = getContextPath() + requestUrl;
        let thisMethod = 'POST';
        if (params.method) {
            thisMethod = params.method;
        }
        $.ajax({
            // 是否使用缓存
            cache: true,
            // http请求方式
            type: thisMethod,
            // 请求url
            url: url,
            // 请求参数
            data: params,
            // 是否异步请求
            async: true,
            // 返回数据格式
            dataType: 'json',
            // 请求失败回调
            error: function (e) {
                $.hideLoading();
                $.toast(e.message, "forbidden");


            },
            // 请求成功回调
            success: function (data) {
                $.hideLoading();
                if (data.successful === true || 'true' === data.successful) {
                    callback(data);
                } else {
                    fail();
                }
            }
        });
    }

</script>
</body>
</html>
