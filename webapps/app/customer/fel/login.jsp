<%@ page import="com.fingard.app.delegate.framework.util.Constants" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="/common/include.jsp" %>

<%

    String currentUsername = (String) request.getSession().getAttribute(Constants.SSO_USER_ID);
    String baseUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath();
    String redirectUrl = baseUrl + "/customer/fel/login.jsp";
    String previousUrl = basePath + request.getSession().getAttribute(Constants.PREVIOUS_PAGE);
    if (StrUtil.isNullOrUndefined(previousUrl)) {
        previousUrl = basePath + "/modules2.4/home_page/home.jsp?selectType=home";
    }
%>
<c:set var="redirectUrl" value="<%=redirectUrl%>"/>
<c:set var="previousUrl" value="<%=previousUrl%>"/>
<c:set var="currentUserName" value="<%=currentUsername%>"/>

<html>
<head>
    <title>远东租赁</title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css">
    <link rel="stylesheet" href="${ctx}/css/jquery-weui.min.css">
</head>
<body>
<script src="${ctx}/js/jquery.min.js"></script>
<script src="${ctx}/js/jquery-weui.min.js"></script>
<script src="${ctx}/js/jquery.cookie.min.js"></script>
<script src="${ctx}/js/common.js?version=${version}"></script>

<script>

    $(document).ready(function () {


        check_token();


    });

    let check_token = function () {

        if ('${currentUserName}' === 'null' || '${currentUserName}' === null) {
            <%--location.href = "http://ha.fehorizon.com:40006/femsp/uaa/login.html?redirectURL=${redirectUrl}";--%>
        } else {

            // 登录成功
            $.showLoading();
            ddAjaxRequestWithFailCallback("/dd/login.do", {userId: '${currentUserName}'}, function (loginResult) {
                window.location.href = '${previousUrl}';
            }, function () {
                $.toast('登录失败', "cancel");
            })

        }
    };

    function ajaxRequest(requestUrl, params, callback) {

        $.ajax({
            // 是否使用缓存
            cache: true,
            // http请求方式
            type: 'POST',
            // 请求url
            url: url,
            // 请求参数
            data: params,
            // 是否异步请求
            async: true,
            // 返回数据格式
            dataType: 'json',
            // 请求失败回调
            error: function (e) {
                $.hideLoading();
                if (e.message === '已经完成') {
                    alert("请求完成");
                    return;
                }
                $.toast(e.message, "forbidden");

            },
            // 请求成功回调
            success: function (data) {
                $.hideLoading();
                callback(data);

            }
        });
    }


</script>
</body>
</html>
