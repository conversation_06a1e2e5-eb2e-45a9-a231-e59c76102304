<%@ page import="cn.hutool.core.util.ObjectUtil" %>

<%@ page import="cn.hutool.json.JSONObject" %>
<%@ page import="com.fingard.app.delegate.beans.OpenResult" %>
<%@ page import="com.fingard.app.delegate.controller.third.CorpWechatHelper" %>
<%@ page import="com.fingard.app.delegate.oalogin.ENCustomer" %>
<%@ page import="com.fingard.app.delegate.oalogin.IThirdSysUserDecrypt" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@include file="/common/include.jsp" %>

<%--常德城投-主业务登录--%>
<%

    String errMsg;
    IThirdSysUserDecrypt strategy = ENCustomer.getUserNameStrategy(customer);
    JSONObject result = strategy.decrypt(request,response);
    if (result.getBool("success")) {
        String userCode = result.getStr("data");

        OpenResult login = CorpWechatHelper.login(request, userCode);
        if (ObjectUtil.isNotNull(login) && Boolean.TRUE.toString().equalsIgnoreCase(login.getSuccessful())) {
            String previousPage = "/modules2.4/home_page/home.jsp?selectType=home";
            response.sendRedirect(basePath + previousPage);
            errMsg = StrUtil.EMPTY;

        } else {
            errMsg = login.getMessage();
        }
    } else {
        errMsg = result.getStr("message");
    }


%>
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">

    <title>登录失败</title>
    <link rel="stylesheet" href="../../css/weui.min.css"/>
    <link rel="stylesheet" href="../../css/weuix.min.css"/>

    <script src="../../js/jquery-1.7.2.min.js"></script>
</head>

<body>
<div class="weui-msg" style="display:block;" id="s2">
    <div class="weui-msg__icon-area"><i class="weui-icon-warn  weui-icon_msg"></i></div>
    <div class="weui-msg__text-area">
        <h2 class="weui-msg__title">登录失败</h2>
        <p class="weui-msg__desc"><%=errMsg%>
        </p>
    </div>
</div>

<p class="weui-btn-area">
    <button id="return_list" class="weui-btn weui-btn_primary">退出</button>
</p>

<script>


    $(document).ready(function () {
        var return_list_btn = $('#return_list');
        return_list_btn.on('click', function () {
            try {
                document.addEventListener('WeixinJSBridgeReady', function () {
                    WeixinJSBridge.call('closeWindow');
                }, false);
                WeixinJSBridge.call('closeWindow');
            } catch (e) {
                // 非（企业）微信环境时返回上一页面
                history.back();
            }
        });
    });

</script>

</body>

</html>

