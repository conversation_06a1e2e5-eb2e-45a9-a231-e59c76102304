<%@ page import="cn.hutool.core.util.StrUtil" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@include file="/common/include.jsp" %>
<%
    String errMsg = request.getParameter("errMsg");
    if (StrUtil.isEmpty(errMsg)) {
        errMsg = "暂无权限访问，请联系系统管理员";
    }
%>

<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">

    <title>登录失败</title>
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

</head>

<body>
<div class="weui-msg" style="display:block;" id="s2">
    <div class="weui-msg__icon-area"><i class="weui-icon-warn  weui-icon_msg"></i></div>
    <div class="weui-msg__text-area">
        <h2 class="weui-msg__title">操作失败</h2>
        <p class="weui-msg__desc"><%=errMsg%>
        </p>
    </div>
</div>

<p class="weui-btn-area">
    <button id="return_list" class="weui-btn weui-btn_primary">退出</button>
</p>

<script src="${ctx}/js/jquery.min.js"></script>
<script src="${ctx}/js/jquery-weui.min.js"></script>
<script src="${ctx}/js/noah_connect_bridge.js"></script>
<script>
    $(document).ready(function () {
        var return_list_btn = $('#return_list');
        return_list_btn.on('click', function () {


            //todo 对方app退出接口
        });
    });

</script>

</body>

</html>


