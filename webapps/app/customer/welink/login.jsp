<%@ page import="com.fingard.app.delegate.framework.util.Constants" %>
<%@page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/include.jsp" %>
<%
    String appId = MyProperties.getMyPropertiesInstance().getProperty("welink.appId");
    Object attribute = request.getSession().getAttribute(Constants.PREVIOUS_PAGE);
    String previousUrl = attribute == null ? (basePath + "/modules2.4/home_page/home.jsp?selectType=home") : (basePath + attribute);

%>
<c:set var="appId" value="<%=appId%>"/>
<c:set var="previousUrl" value="<%=previousUrl%>"/>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>

    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        .hide {
            display: none;
        }
    </style>
</head>
<body>
<div id="result" class="card text-center hide" style="margin: 40px 20px;">
    <div class="card-body">
        <h5 class="card-title">登陆失败</h5>
        <p class="card-text" id="content"></p>
        <a onclick="closePage()" class="btn btn-primary">退出</a>
    </div>
</div>
<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="../../js/longhu_app_util.js"></script>
<script src="https://open-doc.welink.huaweicloud.com/docs/jsapi/2.0.10/hwh5-cloudonline.js"></script>
<script src="https://unpkg.com/vconsole/dist/vconsole.min.js"></script>

<script>
    window.vConsole = new window.VConsole();
    $(document).ready(function () {
        try {

            HWH5.getAuthCode().then(function (info) {
                let params = {};
                params.code = info.code;
                requestWithFailCallback("/user/ssoLogin.do", params, function (resp) {
                    console.log(resp);
                    location.replace('${previousUrl}')
                }, function () {
                    // 从钉钉接口获取用户信息失败
                    onOperationFail("获取用户信息失败");
                })
            }).catch(function (error) {
                console.log('获取异常', error);
                onOperationFail("获取welink授权码失败，请检查配置是否正确");
                hideNewLoading();
            });

        } catch (e) {
            alert(e);
        }


    });

    function closePage() {
        HWH5.close().catch(function (error) {
            console.log('关闭webview异常', error);
        });
    }

    function onOperationFail(content) {
        $('#result').removeClass('hide');
        $('#content').html(content)
    }

    function onAtsVerifyFail() {
        $('#result').removeClass('hide');
        $('#content').html('您没有资金系统审批权限，请联系业务管理员开通')
    }

    function requestWithFailCallback(requestUrl, params, callback, fail) {

        let url = getContextPath() + requestUrl;
        let thisMethod = 'POST';
        if (params.method) {
            thisMethod = params.method;
        }
        $.ajax({
            // 是否使用缓存
            cache: true,
            // http请求方式
            type: thisMethod,
            // 请求url
            url: url,
            // 请求参数
            data: params,
            // 是否异步请求
            async: true,
            // 返回数据格式
            dataType: 'json',
            // 请求失败回调
            error: function (e) {
                hideNewLoading();


            },
            // 请求成功回调
            success: function (data) {
                hideNewLoading();
                if (data.successful === true || 'true' === data.successful) {
                    callback(data);
                } else {
                    fail();
                }
            }
        });
    }

</script>
</body>
</html>
