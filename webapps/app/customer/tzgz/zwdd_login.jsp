<%@ page import="com.fingard.app.delegate.framework.util.Constants" %>
<%@ page import="com.fingard.app.delegate.helper.EndpointProperties" %>
<%@page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@include file="/common/include.jsp" %>
<%
    String corpId = EndpointProperties.getDdCorpId();
    String previousUrl = basePath + request.getSession().getAttribute(Constants.PREVIOUS_PAGE);
    if (StrUtil.isNullOrUndefined(previousUrl)) {
        previousUrl = basePath + "/modules2.4/home_page/home.jsp?selectType=home";
    }
%>
<c:set var="ddCorpId" value="<%=corpId%>"/>
<c:set var="previousUrl" value="<%=previousUrl%>"/>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>

    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel="stylesheet" href="${ctx}/css/weui.min.css">
    <link rel="stylesheet" href="${ctx}/css/jquery-weui.min.css">
</head>
<body>
<script src="${ctx}/js/jquery.min.js"></script>
<script src="${ctx}/js/jquery-weui.min.js"></script>
<script src="https://g.alicdn.com/gdt/jsapi/1.5.2/index.js"></script>
<script src="https://g.alicdn.com/gdt/jsapi-adapter-dingtalk-pc/1.2.10/index.js"></script>

<script src="${ctx}/js/common.js?version=${version}"></script>

<script>
    $(document).ready(function () {

        let ddUserId;

        try {
            dd.ready(() => {

                dd.getAuthCode({
                    corpId: ""
                }).then(res => {
                    console.log(res);

                    var params = {};
                    params.authCode = res.code;
                    ddAjaxRequestWithFailCallback("/zwdd/getUserId.do", params, function (resp) {
                        // alert(JSON.stringify(resp));
                        ddUserId = resp.result.account;

                        sessionStorage.setItem('_user_id', resp.result.accountId);
                        zwdd_upload();
                        ddAjaxRequestWithFailCallback("/zwdd/login.do", {userId: ddUserId}, function (loginResult) {
                            window.location.href = '${ctx}/modules2.4/home_page/home.jsp?selectType=home';
                        }, function () {
                            onAtsVerifyFail();
                        })
                    }, function (msg) {
                        // 从钉钉接口获取用户信息失败
                        onOperationFail(msg);
                    })
                }).catch(err => {
                })

            });
        } catch (e) {
            alert('请在政务钉钉中打开');

            console.log("请在政务钉钉中打开")
        }


    });

    function onOperationFail(content) {
        $.alert({
            title: '操作失败',
            text: content,
            onOK: function () {
                dd.biz.navigation.close({
                    onSuccess: function (result) {
                    },
                    onFail: function (err) {
                    }
                })

            }
        });
    }

    function onAtsVerifyFail() {
        $.alert({
            title: '权限验证提示',
            text: '您没有资金系统审批权限，请联系业务管理员开通',
            onOK: function () {
                dd.biz.navigation.close({
                    onSuccess: function (result) {
                    },
                    onFail: function (err) {
                    }
                })
            }
        });
    }



    function ddAjaxRequestWithFailCallback(requestUrl, params, callback, fail) {
        $.showLoading('用户校验中');

        let url = getContextPath() + requestUrl;
        let thisMethod = 'POST';
        if (params.method) {
            thisMethod = params.method;
        }
        $.ajax({
            // 是否使用缓存
            cache: true,
            // http请求方式
            type: thisMethod,
            // 请求url
            url: url,
            // 请求参数
            data: params,
            // 是否异步请求
            async: true,
            // 返回数据格式
            dataType: 'json',
            // 请求失败回调
            error: function (e) {
                $.hideLoading();
                $.toast(e.message, "forbidden");


            },
            // 请求成功回调
            success: function (data) {
                $.hideLoading();
                if (data.successful === true || 'true' === data.successful) {
                    callback(data);
                } else {
                    fail(data.message);
                }
            }
        });
    }

    function zwdd_upload() {
        var pageName = window.document.title;
        var pageUrl = getPathWithoutParam();
        var pageId = hex_md5(pageUrl);
        (function (w, d, s, q, i) {
            w[q] = w[q] || [];
            var f = d.getElementsByTagName(s)[0], j = d.createElement(s);
            j.async = true;
            j.id = 'beacon-aplus';
            j.src = 'https://alidt.alicdn.com/alilog/mlog/aplus_cloud.js';
            f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'aplus_queue');

        aplus_queue.push({
            action: 'aplus.setMetaInfo',
            arguments: ['aplus-rhost-v', 'alog.zjzwfw.gov.cn']
        });
        aplus_queue.push({
            action: 'aplus.setMetaInfo',
            arguments: ['aplus-rhost-g', 'alog.zjzwfw.gov.cn']
        });
        aplus_queue.push({
            action: "aplus.setMetaInfo",
            arguments: ["_user_id", sessionStorage.getItem("_user_id")]
        });
        aplus_queue.push({
            'action': 'aplus.sendPV',
            'arguments': [{
                is_auto: false
            }, {
                // 当前你的应用信息，此两行请勿修改
                sapp_id: '4180',
                sapp_name: 'ZJGL_APP',
                // 自定义PV参数key-value键值对（只能是这种平铺的json，不能做多层嵌套），如：
                page_id: pageId,
                page_name: pageName,
                page_url: pageUrl
            }]
        });


        var u = navigator.userAgent
        var isAndroid = u.indexOf('Android') > -1
        var isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)

        aplus_queue.push({
            action: 'aplus.setMetaInfo',
            arguments: ['appId', isAndroid ? '28302650' : isIOS ? '28328447' : '47130293']
        });


    }



</script>
</body>
</html>
