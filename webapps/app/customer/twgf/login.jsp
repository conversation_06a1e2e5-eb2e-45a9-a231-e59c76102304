<%@ page import="cn.hutool.core.util.ObjectUtil" %>

<%@ page import="com.fingard.app.delegate.beans.OpenResult" %>
<%@ page import="com.fingard.app.delegate.controller.third.CorpWechatHelper" %>
<%@ page import="com.fingard.app.delegate.controller.third.WtWebServiceHelper" %>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@include file="/common/include.jsp" %>
<%--通威股份--%>
<%
    String ticket = request.getParameter("ticket");
    String errMsg;

    if (StrUtil.isNotEmpty(ticket)) {
        String userId = WtWebServiceHelper.getUserId(ticket);

        if (!userId.startsWith("!@#")) {

            if (StrUtil.isNotEmpty(userId)) {
                OpenResult login = CorpWechatHelper.login(request, userId);
                if (ObjectUtil.isNotNull(login) && Boolean.TRUE.toString().equalsIgnoreCase(login.getSuccessful())) {
                    response.sendRedirect(basePath + "/modules2.4/flow/twgf/flowTypeList.jsp?selectType=flow");
                    errMsg = StrUtil.EMPTY;
                } else {
                    errMsg = "用户无法登录，请检查资金管理系统中是否存在用户: " + userId;
                }
            } else {
                errMsg = "用户信息获取失败";
            }
        } else {
            errMsg = WtWebServiceHelper.getErrMsgFromUserId(userId);
        }
    } else {
        errMsg = "未获取到i通威凭证(ticket)";
    }

%>
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">

    <title>登录失败</title>
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

    <script src="${ctx}/js/jquery-1.7.2.min.js"></script>
</head>

<body>
<div class="weui-msg" style="display:block;" id="s2">
    <div class="weui-msg__icon-area"><i class="weui-icon-warn  weui-icon_msg"></i></div>
    <div class="weui-msg__text-area">
        <h2 class="weui-msg__title">登录失败</h2>
        <p class="weui-msg__desc"><%=errMsg%>
        </p>
    </div>
</div>

<p class="weui-btn-area">
    <button id="return_list" class="weui-btn weui-btn_primary">退出</button>
</p>

<script>
    $(document).ready(function () {
        var return_list_btn = $('#return_list');
        return_list_btn.on('click', function () {
            document.addEventListener('WeixinJSBridgeReady', function () {
                WeixinJSBridge.call('closeWindow');
            }, false);
            WeixinJSBridge.call('closeWindow');
        });
    });

</script>

</body>

</html>

