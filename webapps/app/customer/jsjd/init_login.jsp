<%@ page import="java.net.URLEncoder" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="/common/include.jsp" %>

<%--web.xml中loginPage配置这个页面，企业微信授权重定向--%>
<%

    String bpmUrl = MyProperties.getMyPropertiesInstance().getProperty("jsjd.ssoUrl");
    String clientId = MyProperties.getMyPropertiesInstance().getProperty("jsjd.clientId");
    String baseUrl = MyProperties.getMyPropertiesInstance().getProperty("app.gateway");
    String loginPage = baseUrl + "/customer/" + customer + "/login.jsp";
    loginPage = URLEncoder.encode(loginPage, "UTF-8");
//    String ticket = JsjdApiHelper.authorize();
%>

<c:set var="bpmUrl" value="<%=bpmUrl%>"/>
<c:set var="loginPage" value="<%=loginPage%>"/>
<c:set var="clientId" value="<%=clientId%>"/>
<%--<c:set var="ticket" value="<%=ticket%>"/>--%>


<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">

    <title>加载中</title>

</head>

<body>

<script>

    window.location.href = '${bpmUrl}/esc-sso/oauth2.0/authorize?client_id=${clientId}&redirect_uri=${loginPage}&response_type=code';
</script>

</body>

</html>

