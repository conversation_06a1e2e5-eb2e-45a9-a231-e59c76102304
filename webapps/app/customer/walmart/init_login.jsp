<%@ page import="java.net.URLEncoder" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="/common/include.jsp" %>

<%--web.xml中loginPage配置这个页面，企业微信授权重定向--%>
<%

    String authUrl = MyProperties.getMyPropertiesInstance().getProperty("walmart.oauthUrl");
    String baseUrl = MyProperties.getMyPropertiesInstance().getProperty("app.gateway");
    String appId = MyProperties.getMyPropertiesInstance().getProperty("walmart.appId");
    String loginPage = baseUrl + "/customer/" + customer + "/login.jsp?appId=" + appId;
//    String ssoUrl = "https://flysky-qa.walmartmobile.cn/portal/#/login?launch=MTAwMA==&target="+loginPage;
    String ssoUrl = authUrl + "?launch=MTAwMA==&target=" + loginPage;
%>

<c:set var="ssoUrl" value="<%=ssoUrl%>"/>
<c:set var="loginPage" value="<%=loginPage%>"/>


<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">

    <title>加载中</title>

</head>

<body>
<script src="${ctx}/js/jquery-1.7.2.min.js"></script>
<script src="${ctx}/js/longhu_app_util.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/3.1.9-1/crypto-js.js"></script>
<script>

    let code = getQueryVariable("code");
    if (code !== '') {
        location.replace('${loginPage}&code=' + code)
    } else {
        window.location.href = '${ssoUrl}';
    }
</script>

</body>

</html>

