<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="/common/include.jsp" %>
<%
    Object attribute = request.getSession().getAttribute(Constants.PREVIOUS_PAGE);
    String previousUrl = attribute == null ? (basePath + "/modules2.4/home_page/home.jsp?selectType=home") : (basePath + attribute);
%>
<c:set var="previousUrl" value="<%=previousUrl%>"/>
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">

    <title>登录中...</title>
    <link rel="stylesheet" href="${ctx}/css/weui.min.css"/>
    <link rel="stylesheet" href="${ctx}/css/weuix.min.css"/>

    <script src="${ctx}/js/jquery-1.7.2.min.js"></script>
    <script src="${ctx}/js/longhu_app_util.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/3.1.9-1/crypto-js.js"></script>
</head>

<body>
<div class="weui-msg" style="display:none;" id="s2">
    <div class="weui-msg__icon-area"><i class="weui-icon-warn  weui-icon_msg"></i></div>
    <div class="weui-msg__text-area">
        <h2 class="weui-msg__title">登录失败</h2>
        <p class="weui-msg__desc"></p>
    </div>
</div>
<script>
    $(document).ready(function () {
        let code = getQueryVariable("code");
        let appId = getQueryVariable("appId");
        console.log('code=' + code + "; appId=" + appId);
        let tokenStr = decrypt(decodeURIComponent(code.replace(/%20/g, '+')));
        console.log('token=' + tokenStr)

        simple_ajax('/user/ssoLogin.do', {
            token: tokenStr
        }, function (data) {
            location.replace('${previousUrl}')
        }, function (msg){
            $('#s2').css('display', 'block');
            $('.weui-msg__desc').html(msg);
            document.title = '登陆失败';
        })


    });

    function decrypt(word, keyStr) {
        console.log(word + ';' + keyStr)
        keyStr = keyStr || 'bf960145b2a1338f';
        console.log(keyStr)
        let key = CryptoJS.enc.Utf8.parse(keyStr)
        var decrypt = CryptoJS.AES.decrypt(word, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7
        })
        return CryptoJS.enc.Utf8.stringify(decrypt).toString();

    }

</script>

</body>

</html>

