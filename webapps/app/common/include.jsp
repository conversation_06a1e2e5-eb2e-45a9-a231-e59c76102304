<%@ page import="cn.hutool.core.util.StrUtil" %>
<%@ page import="com.fingard.app.delegate.framework.util.MyProperties" %>
<%@ page import="com.fingard.app.delegate.framework.util.Constants" %>
<%@ page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib prefix="my" uri="/WEB-INF/my.tld" %>
<%
    String customer = MyProperties.getMyPropertiesInstance().getProperty("currentCustomerName");
    String customerCode = MyProperties.getMyPropertiesInstance().getProperty("customerCode");
    String version = MyProperties.getMyPropertiesInstance().getProperty("appDelegateVersion");
    String basePath = request.getContextPath();
    Boolean virtualSwitch = MyProperties.getMyPropertiesInstance().getProperty3("virtualSwitch", false);
    if (virtualSwitch) {
        String virtualPath = MyProperties.getMyPropertiesInstance().getProperty("virtualPath");
        virtualPath = StrUtil.isEmpty(virtualPath) ? StrUtil.EMPTY : virtualPath;
        basePath = virtualPath + basePath;
    }
    response.setHeader("Cache-Control", "no-store");
    response.setHeader("Pragma", "no-cache");
    response.setDateHeader("Expires", 0);

    String permissionCode = String.valueOf(request.getSession().getAttribute(Constants.AUTHORITY));
    String savedUserId = String.valueOf(request.getSession().getAttribute(Constants.USER));
    String savedUserName = String.valueOf(request.getSession().getAttribute(Constants.REAL_NAME));
    Boolean siczyMsgCheck = MyProperties.getMyPropertiesInstance().getProperty3("flow.msgCheck", false);
    String endpoint = MyProperties.getMyPropertiesInstance().getProperty("ats.endpoint");
    String atsVersion = "2.0";
    if(StrUtil.containsAny(endpoint,"EXTERNALSERV")){
        atsVersion = "3.0";
    }

//    boolean waterMakerOpen = MyProperties.getMyPropertiesInstance().getProperty3("waterMaker.open");

%>
<c:set var="ctx" value="<%=basePath%>"/>
<c:set var="customer" value="<%=customer%>"/>
<c:set var="permissionCode" value="<%=permissionCode%>"/>
<c:set var="userId" value="<%=savedUserId%>"/>
<c:set var="realUserName" value="<%=savedUserName%>"/>
<c:set var="atsVersion" value="<%=atsVersion%>"/>
<c:set var="customerCode" value="<%=customerCode%>"/>
<c:set var="version" value="<%=version%>"/>
<%--<c:set var="waterMakerOpen" value="<%=waterMakerOpen%>"/>--%>


<%--
<script src='https://wpk-gate.zjzwfw.gov.cn/static/wpk-jssdk.1.0.2/wpkReporter.js' crossorigin='true'></script>
<script src='${ctx}/js/md5.js'></script>
<script src='${ctx}/js/water-marker.js'></script>
<script>

    /*try {
        const config = {
            bid: 'ZJGL_APP_zzdpro',
            signkey: '1234567890abcdef',
            gateway: 'https://wpk-gate.zjzwfw.gov.cn'
        };
        const wpk = new wpkReporter(config);
        wpk.installAll();
        window._wpk = wpk;
    } catch (err) {
        console.error('WpkReporter init fail', err);
    }*/

</script>
--%>

