<%@ page import="com.fingard.app.delegate.framework.util.Constants" %>
<%@ page import="java.util.Set" %>
<%@ page import="com.fingard.app.delegate.controller.SysParamController" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%

    String menuCustomer = MyProperties.getMyPropertiesInstance().getProperty("currentCustomerName");
    Object authority = request.getSession().getAttribute(Constants.AUTHORITY);
    boolean hasWarningMsgPermission = true;
    if (authority instanceof Set) {
        Set<String> set = (Set<String>) authority;
        hasWarningMsgPermission = set.contains("WPSS01");

    }
    if (authority instanceof String) {
        String string = (String) authority;
        hasWarningMsgPermission = string.contains("WPSS01");

    }

    int flowTodoCount = SysParamController.getFlowTodoCount(request);

%>
<c:set var="hasWarningMsgPermission" value="<%=hasWarningMsgPermission%>"/>
<c:set var="flowTodoCount" value="<%=flowTodoCount%>"/>
<c:set var="menu_customer" value="<%=menuCustomer%>"/>

<style>

    .weui-tabbar__label {
        font-size: 12px;
        line-height: 16px;
    }

    .weui-tabbar__item.weui-bar__item--on .weui-tabbar__label {
        color: #649CFF;
    }

    .weui-tabbar__item {
        padding: 5px 0;
    }

    .tab_icon {
        display: block;
        background-position: center;
        background-size: 28px 28px;
        width: 28px;
        height: 28px;
        margin-left: auto;
        margin-right: auto;
        background-repeat: no-repeat;
    }

    .tab_icon_1 {
        background-image: url('${ctx}/img/Group <EMAIL>');
    }

    .tab_icon_2 {
        background-image: url('${ctx}/img/Group <EMAIL>');
    }

    .tab_icon_3 {
        background-image: url('${ctx}/img/Group <EMAIL>');
    }

    .tab_icon_4 {
        background-image: url('${ctx}/img/Group <EMAIL>');
    }

    .tab_icon_5 {
        background-image: url('${ctx}/img/<EMAIL>');
    }

    .tab_icon_6 {
        background-image: url('${ctx}/img/org_ori.png');
    }

    .tab_icon_7 {
        background-image: url('${ctx}/img/mine.png');
    }

    .tab_icon_1_selected {
        background-image: url('${ctx}/img/Group <EMAIL>');
    }

    .tab_icon_2_selected {
        background-image: url('${ctx}/img/Group <EMAIL>');
    }

    .tab_icon_3_selected {
        background-image: url('${ctx}/img/Group <EMAIL>');
    }

    .tab_icon_4_selected {
        background-image: url('${ctx}/img/Group <EMAIL>');
    }

    .tab_icon_5_selected {
        background-image: url('${ctx}/img/<EMAIL>');
    }

    .tab_icon_6_selected {
        background-image: url('${ctx}/img/org_ori_select.png');
    }
    .tab_icon_7_selected {
        background-image: url('${ctx}/img/mine_2.png');
    }
</style>
<div class="weui-tab tab-bottom" style="height:49px;z-index: 999;">
    <div class="weui-tabbar">
        <my:hp menu="home">

            <a  onclick=clickTab($(this)) data-url="${ctx}/modules2.4/home_page/home.jsp?selectType=home"
               class="weui-tabbar__item <c:if test="${param.selectType=='home' || empty param.selectType}">weui-bar__item--on</c:if>">
                <c:if test="${param.selectType=='home' || empty param.selectType}">
                    <i class="tab_icon tab_icon_1_selected"></i>
                </c:if>
                <c:if test="${param.selectType!='home' || empty param.selectType}">
                    <i class="tab_icon tab_icon_1"></i>
                </c:if>
                <p class="weui-tabbar__label">首页</p>
            </a>

        </my:hp>
        <my:hp menu="cdcyjtHome">
            <a  onclick=clickTab($(this)) data-url="${ctx}/modules2.4/home_page/cdcyjt_home.jsp?selectType=cdcyjtHome"
                class="weui-tabbar__item <c:if test="${param.selectType=='home' || empty param.selectType}">weui-bar__item--on</c:if>">
                <c:if test="${param.selectType=='cdcyjtHome' || empty param.selectType}">
                    <i class="tab_icon tab_icon_1_selected"></i>
                </c:if>
                <c:if test="${param.selectType!='cdcyjtHome' || empty param.selectType}">
                    <i class="tab_icon tab_icon_1"></i>
                </c:if>
                <p class="weui-tabbar__label">首页</p>
            </a>

        </my:hp>
        <my:hp menu="siczyHome">

            <a onclick=clickTab($(this)) data-url="${ctx}/modules2.4/report/standard/SCZYBankBalance.jsp?selectType=siczyHome"
               class="weui-tabbar__item <c:if test="${param.selectType=='siczyHome' || empty param.selectType}">weui-bar__item--on</c:if>">
                <c:if test="${param.selectType=='siczyHome' || empty param.selectType}">
                    <i class="tab_icon tab_icon_1_selected"></i>
                </c:if>
                <c:if test="${param.selectType!='siczyHome' || empty param.selectType}">
                    <i class="tab_icon tab_icon_1"></i>
                </c:if>
                <p class="weui-tabbar__label">首页</p>
            </a>

        </my:hp>
        <my:hp menu="zllHome">

            <a onclick=clickTab($(this)) data-url="${ctx}/modules2.4/report/standard/new_fund_summary_bank_balance_financing.jsp?selectType=zllHome"
               class="weui-tabbar__item <c:if test="${param.selectType=='zllHome' || empty param.selectType}">weui-bar__item--on</c:if>">
                <c:if test="${param.selectType=='zllHome' || empty param.selectType}">
                    <i class="tab_icon tab_icon_1_selected"></i>
                </c:if>
                <c:if test="${param.selectType!='zllHome' || empty param.selectType}">
                    <i class="tab_icon tab_icon_1"></i>
                </c:if>
                <p class="weui-tabbar__label">首页</p>
            </a>

        </my:hp>
        <my:hp menu="zmjHome">

            <a onclick=clickTab($(this)) data-url="${ctx}/modules2.4/home_page/zmj_home.jsp?selectType=zmjHome"
               class="weui-tabbar__item <c:if test="${param.selectType=='zmjHome' || empty param.selectType}">weui-bar__item--on</c:if>">
                <c:if test="${param.selectType=='zmjHome' || empty param.selectType}">
                    <i class="tab_icon tab_icon_1_selected"></i>
                </c:if>
                <c:if test="${param.selectType!='zmjHome' || empty param.selectType}">
                    <i class="tab_icon tab_icon_1"></i>
                </c:if>
                <p class="weui-tabbar__label">首页</p>
            </a>

        </my:hp>
        <my:hp menu="flow">

            <a onclick=clickTab($(this)) data-url="${ctx}/modules2.4/flow/flowTypeList.jsp?selectType=flow"
               class="weui-tabbar__item <c:if test="${param.selectType=='flow' || empty param.selectType}">weui-bar__item--on</c:if>">
                <c:if test="${flowTodoCount!=0}">
                    <span class="weui-badge"
                          style="position: absolute;top: -.4em;">${flowTodoCount}</span>
                </c:if>
                <c:if test="${param.selectType=='flow' || empty param.selectType}">
                    <i class="tab_icon tab_icon_2_selected"></i>
                </c:if>
                <c:if test="${param.selectType!='flow' || empty param.selectType}">
                    <i class="tab_icon tab_icon_2"></i>
                </c:if>
                <p class="weui-tabbar__label">待办</p>
            </a> </my:hp>

        <my:hp menu="twFlow">

            <a onclick=clickTab($(this)) data-url="${ctx}/modules2.4/flow/twgf/flowTypeList.jsp?selectType=flow"
               class="weui-tabbar__item <c:if test="${param.selectType=='flow' || empty param.selectType}">weui-bar__item--on</c:if>">
                <c:if test="${flowTodoCount!=0}">
                    <span class="weui-badge"
                          style="position: absolute;top: -.4em;">${flowTodoCount}</span>
                </c:if>
                <c:if test="${param.selectType=='flow' || empty param.selectType}">
                    <i class="tab_icon tab_icon_2_selected"></i>
                </c:if>
                <c:if test="${param.selectType!='flow' || empty param.selectType}">
                    <i class="tab_icon tab_icon_2"></i>
                </c:if>
                <p class="weui-tabbar__label">待办</p>
            </a> </my:hp>

        <my:hp menu="report">

            <a onclick=clickTab($(this)) data-url="${ctx}/modules2.4/report/originalReportList.jsp?selectType=report"
               class="weui-tabbar__item <c:if test="${param.selectType=='report' || empty param.selectType}">weui-bar__item--on</c:if>">
                <c:if test="${param.selectType=='report' || empty param.selectType}">
                    <i class="tab_icon tab_icon_3_selected"></i>
                </c:if>
                <c:if test="${param.selectType!='report' || empty param.selectType}">
                    <i class="tab_icon tab_icon_3"></i>
                </c:if>
                <p class="weui-tabbar__label">统计分析</p>
            </a> </my:hp>

        <my:hp menu="warning">
            <c:if test="${hasWarningMsgPermission=='true'}">

                <a onclick=clickTab($(this)) data-url="${ctx}/modules2.4/warning/warning_msg.jsp?selectType=warning"
                   class="weui-tabbar__item <c:if test="${param.selectType=='warning' || empty param.selectType}">weui-bar__item--on</c:if>">
                    <c:if test="${param.selectType=='warning' || empty param.selectType}">
                        <i class="tab_icon tab_icon_4_selected"></i>
                    </c:if>
                    <c:if test="${param.selectType!='warning' || empty param.selectType}">
                        <i class="tab_icon tab_icon_4"></i>
                    </c:if>
                    <p class="weui-tabbar__label">消息提醒</p>
                </a></c:if>
        </my:hp>

        <my:hp menu="org">

            <a onclick=clickTab($(this)) data-url="${ctx}/modules2.4/system/orgTrans.jsp?selectType=org"
               class="weui-tabbar__item <c:if test="${param.selectType=='org' || empty param.selectType}">weui-bar__item--on</c:if>">
                <c:if test="${param.selectType=='org' || empty param.selectType}">
                    <i class="tab_icon tab_icon_5_selected"></i>
                </c:if>
                <c:if test="${param.selectType!='org' || empty param.selectType}">
                    <i class="tab_icon tab_icon_5"></i>
                </c:if>
                <p class="weui-tabbar__label">组织</p>
            </a>
        </my:hp>

        <my:hp menu="oriOrg">

            <a onclick=clickTab($(this)) data-url="${ctx}/modules2.4/system/organizationSwitch.jsp?selectType=oriOrg"
               class="weui-tabbar__item <c:if test="${param.selectType=='oriOrg' || empty param.selectType}">weui-bar__item--on</c:if>">
                <c:if test="${param.selectType=='oriOrg' || empty param.selectType}">
                    <i class="tab_icon tab_icon_6_selected"></i>
                </c:if>
                <c:if test="${param.selectType!='oriOrg' || empty param.selectType}">
                    <i class="tab_icon tab_icon_6"></i>
                </c:if>
                <p class="weui-tabbar__label">组织</p>
            </a>
        </my:hp>

        <my:hp menu="mine">

            <a onclick=clickTab($(this)) data-url="${ctx}/modules2.4/system/mine.jsp?selectType=mine"
               class="weui-tabbar__item <c:if test="${param.selectType=='mine' || empty param.selectType}">weui-bar__item--on</c:if>">
                <c:if test="${param.selectType=='mine' || empty param.selectType}">
                    <i class="tab_icon tab_icon_7_selected"></i>
                </c:if>
                <c:if test="${param.selectType!='mine' || empty param.selectType}">
                    <i class="tab_icon tab_icon_7"></i>
                </c:if>
                <p class="weui-tabbar__label">我的</p>
            </a>
        </my:hp>
    </div>
</div>

<script>
    function clickTab(obj){
        console.log(obj);
        location.replace(obj.data('url'))
    }
</script>
