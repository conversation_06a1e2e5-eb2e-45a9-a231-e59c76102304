<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <parent>
        <groupId>com.fingard.ats.delegate</groupId>
        <artifactId>app-delegate-master</artifactId>
        <version>1.0.0</version>
    </parent>
    <artifactId>app-delegate-EE</artifactId>
    <version>2.6.202410.1</version>
    <name>app-delegate-EE</name>
    <packaging>war</packaging>
    <dependencies>
        <dependency>
            <groupId>com.fingard.ats.delegate</groupId>
            <artifactId>app-delegate-server</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>xercesImpl</artifactId>
                    <groupId>xerces</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>axis</groupId>
            <artifactId>axis</artifactId>
            <version>1.4</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-discovery</groupId>
                    <artifactId>commons-discovery</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>axis</groupId>
            <artifactId>axis-wsdl4j</artifactId>
            <version>1.5.1</version>
        </dependency>

        <dependency>
            <groupId>apache-xfire</groupId>
            <artifactId>xfire</artifactId>
            <version>2.6</version>
        </dependency>
        <dependency>
            <groupId>org.apache.ws.commons.schema</groupId>
            <artifactId>XmlSchema</artifactId>
            <version>1.4.2</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.thoughtworks.xstream/xstream -->
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.20</version>
        </dependency>


        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-lgpl</artifactId>
            <version>1.8.5</version>
        </dependency>


        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>5.3.1</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>xpp3</groupId>
            <artifactId>xpp3</artifactId>
            <version>1.1.4c</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.auxilii.msgparser/msgparser -->
        <dependency>
            <groupId>com.auxilii.msgparser</groupId>
            <artifactId>msgparser</artifactId>
            <version>1.1.15</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--<dependency>
            <groupId>com.fingard.app.sso</groupId>
            <artifactId>taobao-dingtalk</artifactId>
            <version>1.0.1</version>
        </dependency>-->

        <!-- https://mvnrepository.com/artifact/org.jasig.cas.client/cas-client-core -->
        <dependency>
            <groupId>org.jasig.cas.client</groupId>
            <artifactId>cas-client-core</artifactId>
            <version>3.1.11</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.fingard.app.sso</groupId>
            <artifactId>ekp-sso</artifactId>
            <version>1.0.0</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.fingard.app.sso</groupId>-->
<!--            <artifactId>zwdd</artifactId>-->
<!--            <version>2.0.0</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.9.9</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-lang3 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.4</version>
        </dependency>

        <dependency>
            <groupId>com.fingard.app.sso</groupId>
            <artifactId>fel-sso</artifactId>
            <version>1.0.0</version>
        </dependency>


        <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-databind -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.9.10</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>3.6.0</version>
        </dependency>


        <!-- https://mvnrepository.com/artifact/com.github.axet/java-unrar -->
        <dependency>
            <groupId>com.github.axet</groupId>
            <artifactId>java-unrar</artifactId>
            <version>1.7.0-5</version>
        </dependency>


        <!-- https://mvnrepository.com/artifact/com.mashape.unirest/unirest-java -->
        <dependency>
            <groupId>com.mashape.unirest</groupId>
            <artifactId>unirest-java</artifactId>
            <version>1.4.9</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>commons-codec</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.6.0</version>
        </dependency>

    </dependencies>
</project>
