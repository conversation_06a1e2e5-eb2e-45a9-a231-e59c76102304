<html>
<head>
    <title>单据详情</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- 最新版本的 Bootstrap 核心 CSS 文件 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"
          crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">


    <style>
        .card {
            border-radius: 2px;
            box-shadow: 0 0 8px 0 rgba(16, 48, 94, 0.08);
        }

        .row > * {
            width: auto;
        }

        .note-label, .detail-label {
            width: 80px;
            color: #666;
            font-size: 13px;
            font-weight: 400;
            word-wrap: break-word;
        }

        .form-control-plaintext {
            color: #333333;
            max-width: 65%;
            word-wrap: break-word;
        }

        .accordion-button {
            font-weight: bold;
            color: #333333;
            font-size: 14px;
        }

        .hide {
            display: none;
        }

        .detail-label {
            min-width: 100px;
            width: 100px;
            margin-right: 10px;

        }

        .title-icon {
            margin-right: 10px;
        }
    </style>


</head>

<div style="margin: 15px">
    <div class="accordion-item">
        <h2 class="accordion-header" id="base-info">

            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#base-info-data"
                    aria-expanded="true" aria-controls="collapseOne">
                <i class="bi-file-text title-icon"></i>基础信息
            </button>
        </h2>
        <div id="base-info-data" class="accordion-collapse collapse show" aria-labelledby="base-info">
            <div class="card-body">
                <ul class="list-group list-group-flush" id="base-data">

                </ul>
            </div>
        </div>
    </div>
</div>

<div class="hide" style="margin: 15px;display: none" id="change-card">
    <div class="accordion-item">
        <h2 class="accordion-header" id="change-info">
            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#change-info-data"
                    aria-expanded="true" aria-controls="collapseOne">
                <i class="bi-file-text title-icon"></i>变更信息
            </button>
        </h2>
        <div id="change-info-data" class="accordion-collapse collapse show" aria-labelledby="change-info">
            <div class="card-body">
                <ul class="list-group list-group-flush" id="change-data">

                </ul>
            </div>
        </div>
    </div>
</div>

<div class="card" style="margin: 15px;display: none">
    <h2 class="accordion-header" id="other-info">
        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#other-info-data"
                aria-expanded="true" aria-controls="collapseOne">
            <i class="bi-file-text title-icon"></i>其他信息
        </button>
    </h2>
    <div id="other-info-data" class="accordion-collapse collapse show" aria-labelledby="other-info">
        <div class="card-body">
            <table class="table">
                <thead>
                <tr style="background-color: #F0F7FC">
                    <th scope="col">审批人</th>
                    <th scope="col">节点</th>
                    <th scope="col">意见</th>
                </tr>
                </thead>
                <tbody id="flow-history-data">

                </tbody>
            </table>
        </div>
    </div>
</div>


<div style="margin: 15px;display: none">
    <div class="accordion-item">
        <h2 class="accordion-header" id="detail-info">
            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#detail-info-data"
                    aria-expanded="true" aria-controls="collapseOne">
                <i class="bi-file-text title-icon"></i>明细信息
            </button>
        </h2>
        <div id="detail-info-data" class="accordion-collapse collapse show" aria-labelledby="detail-info">
            <ul class="list-group list-group-flush" id="detail-data" style="padding: 10px">

            </ul>
        </div>
    </div>
</div>

<div style="margin: 15px;display: none">
    <div class="accordion-item">
        <h2 class="accordion-header" id="accessory-info">
            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#accessory-info-data"
                    aria-expanded="true" aria-controls="collapseOne">
                <i class="bi-file-text title-icon"></i>附件信息
            </button>
        </h2>
        <div id="accessory-info-data" class="accordion-collapse collapse show" aria-labelledby="accessory-info">
            <ol id="accessory-data" class="list-group list-group-numbered">

            </ol>
        </div>
    </div>
</div>
</body>


<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"
        crossorigin="anonymous"></script>
<script src="../js/longhu_app_util.js"></script>
<script>

    $(document).ready(function () {
        get_base_info_data();
        //get_workflow_history();
    });
    let get_base_info_data = function () {

        let params = {};
        params.urid = getQueryVariable("bpmid")

        simple_ajax('/zjdc/queryNoteInfo.do', params, function (data) {
            let base_info_data = data.result.baseInfoData;

            set_base_info_data(base_info_data);
            /* set_detail_info_data(base_info_data);
             set_accessory_info_data(base_info_data);*/

            console.log(data);
        })
    }

    let get_workflow_history = function () {
        let params = {};
        params.workflowId = preParams.workflowId;
        params.itemId = preParams.itemId;
        params.tag = 'ats';
        simple_ajax('${ctxPath}/saasflow/getWorkflowHistory.do', params, function (data) {

            set_flow_history_data(data.result.data)
        })
    }

    let set_flow_history_data = function (data) {
        let $flowHistoryData = $('#flow-history-data');

        for (let item of data) {
            let html = '<tr>' +
                '<td>' + item.opUsername + '</td>' +
                '<td>' + item.actionName + '</td>' +
                '<td>' + item.actionMemo + '</td>';
            $flowHistoryData.append(html);

        }
    }

    let set_base_info_data = function (base_info_data) {
        let $baseInfoData = $('#base-data');
        $baseInfoData.html('');

        for (let item_list of base_info_data) {

            let list_html_item = '<div class="card mb-3">' +
                '<ul class="list-group list-group-flush">';
            for (let item of item_list) {
                list_html_item += '<li class="list-group-item"><span class="detail-label">' + item.key + '</span>' + item.val + '</li>';
            }
            list_html_item += '</ul></div>'
            $baseInfoData.append(list_html_item);
        }
    }

    let set_detail_info_data = function (data) {
        let $detailDataInfo = $('#detail-data');

        if (data.hasBudgetItem && data.budgetItemType === 3) {
            // 资金计划申请
            let budget_item_data = data.budgetItem;
            for (let item of budget_item_data) {
                let list_html_item = '<div class="card mb-3">' +
                    '<ul class="list-group list-group-flush">' +
                    '<li class="list-group-item"><span class="detail-label">计划项目:</span>' + item.budgetItem + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">计划金额:</span>' + item.budgetAmount + '</li>' +
                    '<li class="list-group-item"><span class="detail-label"> 计划周期:</span>' + item.budgetPeriod + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">备注:</span>' + item.memo + '</li>' +
                    '</ul>' +
                    '</div>';
                $detailDataInfo.append(list_html_item);
            }

        }

        if (data.hasBudgetItem && data.budgetItemType === 5) {
            // 计划调整申请
            let budget_item_data = data.budgetItem;
            for (let item of budget_item_data) {
                let list_html_item = '<div class="card mb-3">' +
                    '<ul class="list-group list-group-flush">' +
                    '<li class="list-group-item"><span class="detail-label">计划项目:</span>' + item.budgetItem + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">计划值:</span>' + item.beforeAdjustAmount + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">调整值:</span>' + item.adjustAmount + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">调整后:</span>' + item.afterAdjustAmount + '</li>' +
                    '<li class="list-group-item"><span class="detail-label"> 计划周期:</span>' + item.budgetPeriod + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">备注:</span>' + item.memo + '</li>' +
                    '</ul>' +
                    '</div>';
                $detailDataInfo.append(list_html_item);
            }
        }

        if (data.hasFundTransfer) {
            // 资金调拨申请
            let item_data = data.fundTransferItem;
            for (let item of item_data) {
                let list_html_item = '<div class="card mb-3">' +
                    '<ul class="list-group list-group-flush">' +
                    '<li class="list-group-item"><span class="detail-label">交易类型:</span>' + item.tradeType + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">结算方式:</span>' + item.settlementMethod + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">调拨金额:</span>' + item.applyAmount + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">拨出组织:</span>' + item.outOrgName + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">拨出银行:</span>' + item.outBanKName + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">拨出账户:</span>' + item.outAccountNumber + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">拨出账户性质:</span>' + item.outAccountType + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">拨入组织:</span>' + item.inOrgName + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">拨入银行:</span>' + item.inBankName + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">拨入账户:</span>' + item.inAccountNumber + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">拨入账户性质:</span>' + item.inAccountType + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">申请拨入日期:</span>' + item.transferDate + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">资金用途:</span>' + item.purpose + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">备注:</span>' + item.transferMemo + '</li>' +
                    '</ul>' +
                    '</div>';
                $detailDataInfo.append(list_html_item);
            }
        }

        if (data.hasCollectionItem) {
            // 资金上划申去
            let item_data = data.collectionItem;
            for (let item of item_data) {
                let list_html_item = '<div class="card mb-3">' +
                    '<ul class="list-group list-group-flush">' +
                    '<li class="list-group-item"><span class="detail-label">划出金额:</span>' + item.amount + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">划出组织:</span>' + item.collectionOutOrgName + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">银行:</span>' + item.collectionOutBank + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">划出账户:</span>' + item.collectionOutAccount + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">币种:</span>' + item.currCode + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">用途:</span>' + item.purpose + '</li>' +
                    '</ul>' +
                    '</div>';
                $detailDataInfo.append(list_html_item);
            }
        }

        if (data.hasFundDial) {
            // 资金主动下拨
            let item_data = data.fundDialItem;
            for (let item of item_data) {
                let list_html_item = '<div class="card mb-3">' +
                    '<ul class="list-group list-group-flush">' +
                    '<li class="list-group-item"><span class="detail-label">下拨金额:</span>' + item.amount + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">拨入组织:</span>' + item.allocationInOrgName + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">拨入账户:</span>' + item.allocationInAccount + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">银行:</span>' + item.bankName + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">币种:</span>' + item.currCode + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">用途:</span>' + item.purpose + '</li>' +
                    '</ul>' +
                    '</div>';
                $detailDataInfo.append(list_html_item);
            }
        }

        if (data.hasAllocateApplyItem) {
            // 资金下拨申请
            let item_data = data.allocateApplyItem;
            for (let item of item_data) {
                let list_html_item = '<div class="card mb-3">' +
                    '<ul class="list-group list-group-flush">' +
                    '<li class="list-group-item"><span class="detail-label">申请金额:</span>' + item.applyAmount + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">申请类型:</span>' + item.applyType + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">用途:</span>' + item.purpose + '</li>' +
                    '<li class="list-group-item"><span class="detail-label">保留天数:</span>' + item.reserveKeepDays + '</li>' +
                    '</ul>' +
                    '</div>';
                $detailDataInfo.append(list_html_item);
            }
        }

        if (data.hasChange) {
            // 账户变更申请
            $('#change-card').removeClass('hide');
            let $changeDataInfo = $('#change-data');
            let change_data = data.changeItem;
            for (let item of change_data) {
                let item_html = '<li class="list-group-item">' +
                    '<div class="row">' +
                    '<label class="note-label col-sm-2 col-form-label">' + item.key + '</label>' +
                    '<p class="form-control-plaintext">' + item.value + '</p>' +
                    '</div>' +
                    '</li>'
                $changeDataInfo.append(item_html);
            }

        }
    }

    let set_accessory_info_data = function (data) {
        if (data.hasAccessory) {
            let accessory_list = data.accessoryItem;
            let $accessoryList = $('#accessory-data');
            for (let item of accessory_list) {

                let html = $('<li class="list-group-item">' + item.fileName + '</li>');

                $accessoryList.append(html);
                html.on('click', function () {
                    location.href = item.url;
                });
            }
        }
    }
</script>
</body>
</html>
