<%@ page import="com.fingard.app.delegate.framework.util.Constants" %>
<%@ page import="com.fingard.app.delegate.framework.util.MD5" %><%--
  Created by IntelliJ IDEA.
  User: sylvia
  Date: 2021/12/7
  Time: 9:23
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<%
    String ctxPath = request.getContextPath();
    String currentUser = String.valueOf(request.getSession().getAttribute(Constants.USER));
    String md5ofStr = MD5.getMD5ofStr(currentUser+"LH");
%>
<c:set var="ctxPath" value="<%=ctxPath%>"/>
<c:set var="currentUser" value="<%=md5ofStr%>"/>

<html>
<head>
    <title>新建调拨单</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker3.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/select2/4.1.0-rc.0/css/select2.min.css" rel="stylesheet">

    <style>

        .hide {
            display: none;
        }

        .card {
            margin-top: 10px;
            border: 1px solid rgba(46, 46, 46, .125);
        }

        .form-control:disabled, .form-control[readonly] {
            background-color: #FFFFFF;
        }

    </style>
</head>
<body>
<ul class="nav nav-tabs">

    <li class="nav-item">
        <a class="nav-link active">新增</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="fund_index.jsp">待办</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="fund_index_data.jsp">查看</a>
    </li>
</ul>
<nav class="navbar navbar-expand-lg navbar-light bg-light">
    <a class="navbar-brand" href="#">新建调拨任务</a>
    <div>
        <button id="save-btn" class="btn btn-primary" type="button">保存</button>
        <button id="submit-btn" class="btn btn-primary hide" type="button">发起任务</button>
    </div>

</nav>

<div class="root-div" style="padding: 15px">

    <!--基础信息-->
    <div class="input-group mb-3">
       
        <label for="transfer-base-apply-org">申请组织</label>
        <select class="form-control transfer-base-apply-org" id="transfer-base-apply-org" style="width: 100%">

        </select>
    </div>
    <div class="input-group mb-3">
        <div class="input-group-prepend">
            <span class="input-group-text">¥</span>
        </div>
        <input value="0.00" readonly type="number" class="form-control" id="apply-total-amount-input" aria-label="调拨金额"
               placeholder="">
        <div class="input-group-append">
            <span class="input-group-text">元</span>
        </div>
    </div>
    <div class="input-group mb-3">
        <div class="input-group-prepend"><span class="input-group-text">调拨类型 </span></div>
        <label for="transfer-base-type"></label>
        <input data-id="WLDB01" value='跨区域调拨' class="form-control transfer-base-type" id="transfer-base-type" disabled>
    </div>
    <div class='input-group mb-3 date' id='datetimepicker1'>
        <div class="input-group-prepend">
                    <span class="input-group-text">
                        调拨日期
                    </span>
        </div>
        <label for="transfer-date"></label>
        <input readonly id="transfer-date" type='text' class="form-control"/>
        <span class="input-group-addon">
            <span class="glyphicon glyphicon-calendar"></span>
        </span>
    </div>

    <!--调拨区域明细-->
    <nav class="navbar navbar-expand-lg navbar-light bg-light out-area-nav" data-toggle="collapse" href="#transfer-area-out-details-container">
        <a class="navbar-brand" >拨出地区明细<span style="font-size: 14px">(点击可展开/收起)</span></a>
    </nav>
    <div class="transfer-area-out-details-container" id="transfer-area-out-details-container">

    </div>
    <button id="add-transfer-out-area-detail-btn" class="btn btn-primary btn-block" style="margin-top: 15px">+拨出地区明细
    </button>

    <nav class="navbar navbar-expand-lg navbar-light bg-light in-area-nav" style="margin-top: 15px" data-toggle="collapse" href="#transfer-area-in-details-container">
        <a class="navbar-brand" >拨入地区明细<span style="font-size: 14px">(点击可展开/收起)</span></a>
    </nav>
    <div class="transfer-area-in-details-container show" id="transfer-area-in-details-container">

    </div>
    <button id="add-transfer-in-area-detail-btn" class="btn btn-success btn-block" style="margin-top: 15px">+拨入地区明细
    </button>


</div><!-- root div -->


<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/select2/4.1.0-rc.0/js/select2.full.min.js"></script>

<script src="../js/jquery.md5.js"></script>
<script src="../js/longhu_app_util.js"></script>

<script>

    let transfer_tag = sessionStorage.getItem("transfer_tag");
    let transfer_id = sessionStorage.getItem("transfer_id");
    let can_edit_out_area = '${param.canEditOutArea}';
    let can_edit_in_area = '${param.canEditInArea}';


    $(document).ready(function () {

        $('#datetimepicker1 input').datepicker({
            format: 'yyyy-mm-dd',
        });
        get_area_data();
        get_operable_org();
        if (can_edit_out_area === 'false') {
            $('#add-transfer-out-area-detail-btn').hide();
        }
        if (can_edit_in_area === 'false') {
            $('#add-transfer-in-area-detail-btn').hide();
        }


        if (transfer_tag === 'edit') {
            // 编辑时才查询单据信息
            $('.page-nav').addClass('hide');
            $('#submit-btn').removeClass('hide');
            // get_base_info_data();
        }


    });

    let area_options_html = '';
    let get_area_data = function () {

        lh_ajax("${ctxPath}/api/longhu/getArea.do", {
            cUser:'${currentUser}',
        }, function (data) {

            let areas = data.result.data;
            for (let area of areas) {
                area_options_html += '<option value="' + area.areaId + '">' + area.areaName + '</option>'
            }
            console.log(data);
        })
    }

    let get_operable_org = function () {

        lh_ajax('${ctxPath}/user/getOperateableOrg.do', { cUser:'${currentUser}',}, function (data) {
            hideLoading();
            let entities = data.result.data;
            let entity_options = '';
            for (let entity of entities) {
                let entity_name = entity.fullName;
                if (entity_name.indexOf('(') !== -1) {
                    entity_name = entity_name.substr(0, entity_name.indexOf('('));
                }
                entity_options += '<option value="' + entity.entityId + '">' + entity_name + '</option>'
            }

            $('.transfer-base-apply-org').html(entity_options);

            let apply_org_select = $('#transfer-base-apply-org');
            apply_org_select.select2();

        })
    }
    // 获取当前单据信息以编辑,
    let get_base_info_data = function () {

        lh_ajax("${ctxPath}/api/longhu/getTransferNoteDetail.do", {
            id: transfer_id,
            approveState: 1,
            cUser:'${currentUser}',
        }, function (data) {

            console.log(data);
            set_base_info(data.result.baseInfo);
            set_area_info(data.result.transferAreas);
        })
    }

    let save_data = function (params) {

        confirm({
            title: '确认提交？',
            info: '保存后APP端无法修改，请确认填写内容无误',
            onOk: function () {
                lh_ajax("${ctxPath}/api/longhu/addEditTransferBaseInfo.do", {
                    // id: 'transfer_id',
                    jsonParams: JSON.stringify(params),
                    cUser:'${currentUser}',
                }, function (data) {

                    onlyConfirm({
                        title: '保存结果',
                        info: data.result.msg,
                        onOk: function () {
                            if (data.result.success === true) {
                                window.location.reload();
                            }
                        }
                    })
                })
            }
        })
    }

    let set_base_info = function (baseInfo) {
        let amount_input = $('#apply-total-amount-input');
        $('#transfer-base-apply-org').val(baseInfo.entityId);
        amount_input.val(baseInfo.amount);
        $('#transfer-date').val(baseInfo.date);
        // $('#transfer-base-type').val(baseInfo.transferType);
    }

    let set_area_info = function (areaInfo) {

        $('.out-area-nav').append('<span class="badge badge-primary">' + areaInfo.outTransferDetails.length + '</span>')
        $('.in-area-nav').append('<span class="badge badge-success">' + areaInfo.inTransferDetails.length + '</span>')

        for (let item of areaInfo.outTransferDetails) {
            let html = $('<div class="card card-body">' +
                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend"><span class="input-group-text">编号</span></div>' +
                '<input type="text" value="' + item.no + '" class="form-control transfer-out-no">' +
                '</div>' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend"><span class="input-group-text">拨出地区</span></div>' +
                '<select type="text" value="' + item.outAreaName + '" class="form-control transfer-out-area">' +
                area_options_html +
                '</select>' +
                '</div>' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend"><span class="input-group-text">拨出计息地区</span></div>' +
                '<select type="text" value="' + item.outInterestAreaName + '" class="form-control transfer-out-interest-area">' +
                area_options_html +
                '</select>' +
                '</div>' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend"><span class="input-group-text">¥</span></div>' +
                '<input type="number" value="' + item.amount + '" class="form-control transfer-out-amount">' +
                '<div class="input-group-append"><span class="input-group-text">元</span></div>' +
                '</div>' +
                '</div>');

            html.find('select').select2();
            html.find('.select2').css('width','100%')
            html.find('.transfer-out-area').val(item.inAreaId).trigger("change");
            html.find('.transfer-out-interest-area').val(item.inInterestAreaId).trigger("change");


            $('.transfer-area-out-details-container').append(html);

        }

        for (let item of areaInfo.inTransferDetails) {
            let html = $('<div class="card card-body">' +
                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend"><span class="input-group-text">编号</span></div>' +
                '<input type="text" value="' + item.no + '" class="form-control transfer-in-no">' +
                '</div>' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend"><span class="input-group-text">拨入地区</span></div>' +
                '<select value="' + item.inAreaName + '" class="form-control transfer-in-area">' +
                area_options_html +
                '</select>' +
                '</div>' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend"><span class="input-group-text">拨入计息地区</span></div>' +
                '<select value="' + item.inInterestAreaName + '" class="form-control transfer-in-interest-area">' +
                area_options_html +
                '</select>' +
                '</div>' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend"><span class="input-group-text">¥</span></div>' +
                '<input type="number" value="' + item.amount + '" class="form-control transfer-in-amount">' +
                '<div class="input-group-append"><span class="input-group-text">元</span></div>' +
                '</div>' +
                '</div>');


            html.find('select').select2();
            html.find('.select2').css('width','100%')
            html.find('.transfer-in-area').val(item.inAreaId).trigger("change");
            html.find('.transfer-in-interest-area').val(item.inInterestAreaId).trigger("change");

            $('.transfer-area-in-details-container').append(html);

            html.find('.transfer-in-amount').live('input propertychange', function () {
                console.log($(this).val())
            })

        }
    }


    $('#save-btn').on('click', function () {

        let outCards = $('.transfer-area-out-details-container').children('.card');
        let inCards = $('.transfer-area-in-details-container').children('.card');

        let params = {};

        let no_contain_empty = false;
        let amount_contain_empty = false;
        let amount_negative = false;
        let outDetails = [];
        for (let cardItem of outCards) {
            let card = $(cardItem);
            let detail_no = card.find('.transfer-out-no').val();
            let transfer_area_id = card.find('.transfer-out-area').val();
            let transfer_area_Name = card.find('.transfer-out-area option:selected').html();

            let transfer_interest_area_id = card.find('.transfer-out-interest-area').val();
            let transfer_interest_area_Name = card.find('.transfer-out-interest-area option:selected').html();

            let transfer_amount = card.find('.transfer-out-amount').val();
            console.log(detail_no + "," + transfer_area_id + "," + transfer_interest_area_id + "," + transfer_amount);
            let detail = {};
            detail.no = detail_no;
            detail.areaId = transfer_area_id;
            detail.areaName = transfer_area_Name;

            detail.areaInterestId = transfer_interest_area_id;
            detail.areaInterestName = transfer_interest_area_Name;

            detail.amount = transfer_amount;
            outDetails.push(detail);
            if (detail_no.length === 0) {
                no_contain_empty = true;
            }
            if (transfer_amount.length === 0) {
                amount_contain_empty = true;
            }
            if(parseFloat(transfer_amount)<=0){
                amount_negative = true;
            }

        }

        let inDetails = [];

        for (let cardItem of inCards) {
            let card = $(cardItem);
            let detail_no = card.find('.transfer-in-no').val();
            let transfer_area_id = card.find('.transfer-in-area').val();
            let transfer_area_Name = card.find('.transfer-in-area option:selected').html();
            let transfer_interest_area_id = card.find('.transfer-in-interest-area').val();
            let transfer_interest_area_Name = card.find('.transfer-in-interest-area option:selected').html();
            let transfer_amount = card.find('.transfer-in-amount').val();
            console.log(detail_no + "," + transfer_area_id + "," + transfer_interest_area_id + "," + transfer_amount);
            let detail = {};
            detail.no = detail_no;
            detail.areaId = transfer_area_id;
            detail.areaName = transfer_area_Name;
            detail.areaInterestName = transfer_interest_area_Name;
            detail.areaInterestId = transfer_interest_area_id;
            detail.amount = transfer_amount;
            inDetails.push(detail);
            if (detail_no.length === 0) {
                no_contain_empty = true;
            }
            if (transfer_amount.length === 0) {
                amount_contain_empty = true;
            }
            if(parseFloat(transfer_amount)<=0){
                amount_negative = true;
            }
        }

        params.outDetails = outDetails;
        params.inDetails = inDetails;

        params.applyOrgId = $('#transfer-base-apply-org').val();
        params.totalAmt = $('#apply-total-amount-input').val();
        params.applyType = $('#transfer-base-type').data('id');
        params.date = $('#transfer-date').val();

        console.log(params);

        if (no_contain_empty === true) {
            toast("存在为空的序号，请检查");
            return;
        }

        if (amount_contain_empty === true) {
            toast("存在为空的调拨金额，请检查");
            return;
        }
        if(amount_negative===true){
            toast('存在≤0的调拨金额，请检查');
            return;

        }

        if (params.totalAmt === '') {
            toast("调拨金额不得为空");
            return;
        }
        if (params.date === '') {
            toast("调拨日期不得为空");
            return;
        }
        if (params.outDetails.length === 0) {
            toast("还未增加任何拨出地区明细")
            return;
        }

        if (params.inDetails.length === 0) {
            toast("还未增加任何拨入地区明细")
            return;
        }

        if (get_out_amount_sum() !== get_in_amount_sum()) {
            toast('拨入总额与拨出总额不等，请检查')
            return;
        }

        if (check_seq_no(outDetails) === false) {
            toast('拨出明细中存在重复序号')
            return;
        }

        if (check_seq_no(inDetails) === false) {
            toast('拨入明细中存在重复序号')
            return;
        }

        save_data(params);

    })

    let check_seq_no = function (details) {
        let tmp = {};
        for (let detail of details) {
            if (tmp[detail.no] === undefined) {
                tmp[detail.no] = [1];
            } else {
                tmp[detail.no].push(1);
            }
        }

        for (let key in tmp) {
            if (tmp[key].length > 1) {
                return false;
            }
        }

        return true;
    }


    let area_detail_out_no = 1;
    let out_areas = [];
    let out_interest_areas = [];

    $('#add-transfer-out-area-detail-btn').on('click', function () {

        let html = $('<div class="card card-body">' +
            '<div class="input-group mb-3">' +
            '<div class="input-group-prepend"><span class="input-group-text">编号</span></div>' +
            '<input onkeyup="on_no_key_up(this)" type="text" value="'+area_detail_out_no+'" class="form-control transfer-out-no" >' +
            '</div>' +

            '<div class="mb-3">' +
            '<label class="input-group-prepend">拨出地区</label>' +
            '<select class="form-control transfer-out-area">' +
            area_options_html +
            '</select>' +
            '</div>' +

            '<div class=" mb-3">' +
            '<label class="input-group-prepend">拨出计息地区</label>' +
            '<select type="text" value="" class="form-control transfer-out-interest-area">' +
            area_options_html +
            '</select>' +
            '</div>' +

            '<div class="input-group mb-3">' +
            '<div class="input-group-prepend"><span class="input-group-text">¥</span></div>' +
            '<input oninput="on_out_amount_input()" type="number" value="" class="form-control transfer-out-amount">' +
            '<div class="input-group-append"><span class="input-group-text">元</span></div>' +
            '</div>' +
            '<button onclick="delete_self_parent($(this),1)" class="btn btn-danger btn-block">删除</button>' +

            '</div>' +
            '</div>');

        html.find('select').select2();
        html.find('.select2').css('width','100%')
        let out_area_container = $('.transfer-area-out-details-container');
        out_area_container.append(html);


        area_detail_out_no++;
    });

    let area_detail_in_no = 1;

    $('#add-transfer-in-area-detail-btn').on('click', function () {

        let html = $('<div class="card card-body">' +
            '<div class="input-group mb-3">' +
            '<div class="input-group-prepend"><span class="input-group-text">编号</span></div>' +
            '<input onkeyup="on_no_key_up(this)" type="text" value="'+area_detail_in_no+'" class="form-control transfer-in-no">' +
            '</div>' +

            '<div class="input-group mb-3">' +
            '<label class="input-group-prepend">拨入地区</label>' +
            '<select type="text" value="" class="form-control transfer-in-area">' +
            area_options_html +
            '</select>' +
            '</div>' +

            '<div class="input-group mb-3">' +
            '<label class="input-group-prepend">拨入计息地区</label>' +
            '<select type="text" value="" class="form-control transfer-in-interest-area">' +
            area_options_html +
            '</select>' +
            '</div>' +

            '<div class="input-group mb-3">' +
            '<div class="input-group-prepend"><span class="input-group-text">¥</span></div>' +
            '<input type="number" value="" class="form-control transfer-in-amount">' +
            '<div class="input-group-append"><span class="input-group-text">元</span></div>' +
            '</div>' +

            '<button onclick="delete_self_parent($(this),1)" class="btn btn-danger btn-block">删除</button>' +

            '</div>' +
            '</div>');

        html.find('select').select2();
        html.find('.select2').css('width','100%')
        let in_area_container = $('.transfer-area-in-details-container');
        in_area_container.append(html);
        in_area_container.addClass('show');

        area_detail_in_no++;
    });

    let get_out_amount_sum = function () {
        let sum = 0;
        $('.transfer-out-amount').each(function () {
            sum += parseFloat($(this).val());
        })

        return sum;
    }

    let get_in_amount_sum = function () {
        let sum = 0;
        $('.transfer-in-amount').each(function () {
            sum += parseFloat($(this).val());
        })

        return sum;
    }

    let on_out_amount_input = function () {
        let sum = 0;
        $('.transfer-out-amount').each(function () {
            sum += parseFloat($(this).val());
        })
        $('#apply-total-amount-input').val(sum);
    }

    let delete_self_parent = function (btn_obj, type) {
        confirmDelete(function () {
            if (type === 1) {
                area_detail_out_no--;
            }
            if (type === 2) {
                area_detail_in_no--;
            }
            btn_obj.parent().remove()
            on_out_amount_input();

        });
    }

    let confirmDelete = function (deleteFunc) {
        deleteFunc();
    }


</script>
</body>
</html>
