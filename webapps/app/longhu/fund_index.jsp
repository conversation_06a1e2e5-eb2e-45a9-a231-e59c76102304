<%@ page import="com.fingard.app.delegate.framework.util.Constants" %>
<%@ page import="com.fingard.app.delegate.framework.util.MD5" %><%--
  Created by IntelliJ IDEA.
  User: sylvia
  Date: 2021/12/7
  Time: 9:23
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<%
    String ctxPath = request.getContextPath();
    String currentUser = String.valueOf(request.getSession().getAttribute(Constants.USER));
    String md5ofStr = MD5.getMD5ofStr(currentUser + "LH");
%>
<c:set var="ctxPath" value="<%=ctxPath%>"/>
<c:set var="currentUser" value="<%=md5ofStr%>"/>

<html>
<head>
    <title>资金调拨处理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- 最新版本的 Bootstrap 核心 CSS 文件 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/css/bootstrap.min.css"
          integrity="sha384-zCbKRCUGaJDkqS1kPbPd7TveP5iyJE0EjAuZQTgFLD2ylzuqKfdKlfG/eSrtxUkn" crossorigin="anonymous">

    <style>

        .hide {
            display: none;
        }

        .input-group-text {
            background-color: #FFFFFF;
        }

        .badge {
            margin-right: 10px;
        }

        .divider {
            width: 100%;
            height: 5px;
            background-color: #f8f9fa;
        }

        .input-group-text {
            border: 1px solid #FFFFFF;
        }

        .transfer_note_item {
            position: relative;
            left: 50px;
            top: 5px;
        }

        .transfer_note_item .transfer_note_item_date {
            position: absolute;
            top: 0;
            right: 5%;
        }

        .transfer_note_item {
            width: 90%;
        }

        .input-group-prepend {
            position: absolute;
            top: 35%;
        }

    </style>
</head>
<body>

<ul class="nav nav-tabs">
    <li class="nav-item">
        <a class="nav-link" href="fund_add_new_transfer_note.jsp">新增</a>
    </li>
    <li class="nav-item">
        <a class="nav-link active">待办</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="fund_index_data.jsp">查看</a>
    </li>
</ul>
<nav class="navbar navbar-expand-lg navbar-light bg-light">
    <a class="navbar-brand" href="#">调拨任务处理</a>
    <div class="btn-group" role="group" aria-label="Button group with nested dropdown">

        <div class="btn-group" role="group">
            <button id="btnGroupDrop1" type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown"
                    aria-expanded="false">
                操作
            </button>
            <div class="dropdown-menu" aria-labelledby="btnGroupDrop1">
                <a class="dropdown-item" href="javascript:op_note(1)">删除</a>
                <a class="dropdown-item" href="javascript:op_note(2)">作废</a>
                <a class="dropdown-item" href="javascript:op_note(3)">发起任务</a>
                <a class="dropdown-item" href="javascript:op_note(4)">撤销任务</a>
                <a class="dropdown-item" href="javascript:op_note(5)">驳回任务</a>
            </div>
        </div>
        <button data-toggle="collapse" href="#query-container" class="btn btn-success">筛选</button>
        <a class="btn btn-primary" href="switch_org.jsp">切换组织</a>

    </div>
</nav>

<div style="padding: 20px" class="collapse" id="query-container">
    <input id="query-content" class="form-control mr-sm-2" type="search" placeholder="输入申请单号" aria-label="申请单号">

    <div style="margin-top: 15px;" id="approve-state-condition">
        <div class="form-check form-check-inline">
            <input class="form-check-input" type="checkbox" id="inlineCheckbox1" value="1">
            <label class="form-check-label" for="inlineCheckbox1">未审批</label>
        </div>
        <div class="form-check form-check-inline">
            <input class="form-check-input" type="checkbox" id="inlineCheckbox2" value="2">
            <label class="form-check-label" for="inlineCheckbox2">已审批</label>
        </div>
        <div class="form-check form-check-inline">
            <input class="form-check-input" type="checkbox" id="inlineCheckbox3" value="3">
            <label class="form-check-label" for="inlineCheckbox3">已拒绝</label>
        </div>
        <div class="form-check form-check-inline">
            <input class="form-check-input" type="checkbox" id="inlineCheckbox4" value="4">
            <label class="form-check-label" for="inlineCheckbox4">审批中</label>
        </div>
    </div>

    <div style="margin-top: 15px;" id="transfer-state-condition">
        <div class="form-check form-check-inline">
            <input class="form-check-input" type="checkbox" id="inlineCheckboxA" value="1">
            <label class="form-check-label" for="inlineCheckboxA">未调度</label>
        </div>
        <div class="form-check form-check-inline">
            <input class="form-check-input" type="checkbox" id="inlineCheckboxB" value="2">
            <label class="form-check-label" for="inlineCheckboxB">调度成功</label>
        </div>
        <div class="form-check form-check-inline">
            <input class="form-check-input" type="checkbox" id="inlineCheckboxC" value="3">
            <label class="form-check-label" for="inlineCheckboxC">调度失败</label>
        </div>
        <div class="form-check form-check-inline">
            <input class="form-check-input" type="checkbox" id="inlineCheckboxD" value="4">
            <label class="form-check-label" for="inlineCheckboxD">调度中</label>
        </div>
        <div class="form-check form-check-inline">
            <input class="form-check-input" type="checkbox" id="inlineCheckboxE" value="5">
            <label class="form-check-label" for="inlineCheckboxE">部分调度</label>
        </div>
    </div>

    <button id="submit-query-btn" class="btn btn-outline-success my-2 my-sm-0" type="submit">查询</button>

</div>

<div class="transfer-list-container" style="padding: 15px">

</div>

<div class="card text-center hide no-data-holder">
    <div class="card-body">
        <h5 class="card-title">暂无数据</h5>
        <p class="card-text">当前组织或条件下暂无待处理的单据</p>
    </div>
</div>

<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-fQybjgWLrvvRgtW6bFlB7jaZrFsaBXjsOMm/tB9LTS58ONXgqbR9W8oWht/amnpF"
        crossorigin="anonymous"></script>
<script src="../js/jquery.md5.js"></script>
<script src="../js/longhu_app_util.js"></script>

<script>
    sessionStorage.removeItem('transfer_id');
    sessionStorage.removeItem('transfer_tag');


    $(document).ready(function () {

        get_data({});
    });

    let get_data = function (params) {
        params.cUser = '${currentUser}';
        $('#query-container').removeClass('show');
        lh_ajax('${ctxPath}/api/longhu/getTransferNoteList.do', params, function (data) {
            if (data.result.data.length === 0) {
                $('.transfer-list-container').html('');
                $('.no-data-holder').removeClass('hide');
            } else {
                set_list(data.result.data);
            }
        })

    }


    let op_note = function (type) {
        let tips = ['删除', '作废', '提交', '撤销', '驳回'];


        let url = '';
        if (type === 1) {
            url = '/api/longhu/delete.do';
        }
        if (type === 2) {
            url = '/api/longhu/cancel.do';
        }
        if (type === 3) {
            url = '/api/longhu/submitTask.do';
        }
        if (type === 4) {
            url = '/api/longhu/revertTask.do';
        }
        if (type === 5) {
            url = '/api/longhu/rejectTask.do';
        }

        let checked_items = get_checked_list_item();
        if (checked_items.length === 0) {
            toast('你还未选择单据');
            return;
        }
        if (checked_items.length > 1) {
            toast('请选择一个单据进行操作，你选择了' + checked_items.length + '个');
            return;
        }

        confirm({
            title: '确认' + tips[type - 1] + '？',
            info: '正在' + tips[type - 1] + '单据号为【' + checked_items[0].noteCode + '】的单据',
            onOk: function () {
                lh_ajax('${ctxPath}' + url, {id: checked_items[0].id, cUser: '${currentUser}',}, function (data) {
                    onlyConfirm({
                        title: '操作提示',
                        info: data.result.msg,
                        onOk: function () {
                            window.location.reload();
                        }
                    })
                })
            }
        })


    }
    let set_list = function (list_data) {
        $('.transfer-list-container').html('');
        for (let item of list_data) {
            let list_item_html = $(
                '<div class="list-group-item" data-notecode="' + item.noteCode + '" data-id="' + item.id + '" data-approve="' + item.approveState + '" data-transfer="' + item.transferState + '">' +

                '<span class="input-group-prepend">' +
                '<div class="input-group-text"><input id="check_box_' + item.id + '" type="checkbox" aria-label="Checkbox for following text input">' +
                '</div></span>' +
                '<div  class="transfer_note_item">' +
                '<div class="">' +
                '<h6><span class="badge badge-secondary">金额</span>' + item.amount + '</h6>' +
                '<small class="transfer_note_item_date">' + item.date + '</small></div>' +
                '<h6><span class="badge badge-secondary">拨出地区</span>' + item.outAreaName + '</h6>' +
                '<h6><span class="badge badge-secondary">拨入地区</span>' + item.inAreaName + '</h6>' +
                '<h6 data-id="ssss"><span class="badge badge-secondary">单据号</span>' + item.noteCode + '</h6>' +
                '<p class="badge badge-info">' + item.approveStateName + '</p>' +
                '<p class="badge badge-primary">' + item.transferStateName + '</p></div></div>');

            list_item_html.find('.transfer_note_item').on('click', function () {
                let approve_state = $(this).parent().data('approve');
                sessionStorage.setItem("transfer_id", item.id);
                sessionStorage.setItem("approve_state", approve_state);
                sessionStorage.setItem('can_edit_out_area', item.canEditOutArea);
                sessionStorage.setItem('can_edit_in_area', item.canEditInArea);
                sessionStorage.setItem('only_read', !item.canSubmit);

                location.href = 'fund_detail.jsp';

            })

            $('.transfer-list-container').append(list_item_html);
        }

    }

    let get_checked_list_item = function () {
        let checked_item_list = [];
        $.each($('.list-group-item input'), function () {
            if ($(this).prop('checked') === true) {
                let item_obj = $(this).parent().parent().parent();
                let item = {id: item_obj.data('id'), noteCode: item_obj.data('notecode')};
                checked_item_list.push(item);
            }
        });
        console.log(checked_item_list);
        return checked_item_list;
    }

    $('#submit-query-btn').on('click', function () {

        let query_content = $('#query-content').val();

        let approve_state = [];
        let transfer_state = [];
        $.each($('#approve-state-condition .form-check-input'), function () {
            if ($(this).prop('checked') === true) {
                approve_state.push($(this).val());
            }
        });
        $.each($('#transfer-state-condition .form-check-input'), function () {
            if ($(this).prop('checked') === true) {
                transfer_state.push($(this).val());
            }
        });
        console.log(approve_state);
        console.log(transfer_state);
        console.log(query_content);

        get_data({
            approveState: approve_state.join(','),
            transferState: transfer_state.join(','),
            noteCode: query_content,
        })


    })


</script>
</body>
</html>
