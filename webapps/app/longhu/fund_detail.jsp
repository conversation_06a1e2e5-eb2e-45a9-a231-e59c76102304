<%@ page import="com.fingard.app.delegate.framework.util.Constants" %>
<%@ page import="com.fingard.app.delegate.framework.util.MD5" %><%--
  Created by IntelliJ IDEA.
  User: sylvia
  Date: 2021/12/7
  Time: 9:23
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<%
    String ctxPath = request.getContextPath();
    String currentUser = String.valueOf(request.getSession().getAttribute(Constants.USER));
    String md5ofStr = MD5.getMD5ofStr(currentUser + "LH");
%>
<c:set var="ctxPath" value="<%=ctxPath%>"/>
<c:set var="currentUser" value="<%=md5ofStr%>"/>

<html>
<head>
    <title>调拨单详情</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/select2/4.1.0-rc.0/css/select2.min.css" rel="stylesheet">
    <style>

        .card {
            margin-top: 10px;
            border: 1px solid rgba(46, 46, 46, .125);
        }

        .card p {
            margin-bottom: 0;
        }

        .card-body {
            padding: 0.5em 1.5em;
        }

        .hide {
            display: none;
        }

        .block-divider {
            width: 100%;
            height: 10px;
            background-color: #FFFFFF;
        }

    </style>
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-light bg-light">
    <a class="navbar-brand" href="#">详细信息<span style="margin-left: 10px" class="badge badge-primary"
                                               id="approve_state_badge"></span></a>
    <div class="approve-block hide">
        <button onclick="on_approve_btn_click()" id="approve-btn" class="btn btn-primary" type="button">提交任务</button>
        <button onclick="on_refuse_btn_click()" id="refuse-btn" class="btn btn-danger" type="button">驳回任务</button>
    </div>

</nav>


<div class="root-div" style="padding: 15px">
    <!--基础信息-->
    <div class="input-group mb-3">
        <div class="input-group-prepend">
            <span class="input-group-text" id="basic-addon1">申请组织</span>
        </div>
        <input readonly type="text" class="form-control transfer-base-apply-org" id="transfer-base-apply-org"
               aria-label="Username" aria-describedby="basic-addon1">
    </div>
    <div class="input-group mb-3">
        <div class="input-group-prepend">
            <span class="input-group-text">¥</span>
        </div>
        <input readonly type="text" class="form-control" id="apply_total_amount_input" aria-label="调拨金额"
               placeholder="输入调拨金额">
        <div class="input-group-append">
            <span class="input-group-text">元</span>
        </div>
    </div>
    <div class="input-group mb-3">
        <div class="input-group-prepend">
                    <span class="input-group-text">
                        调拨类型
                    </span>
        </div>
        <label for="transfer-base-type"></label>
        <input value=" 跨区域调拨" class="form-control transfer-base-type" id="transfer-base-type" readonly/>
    </div>
    <div class='input-group mb-3 date' id='datetimepicker1'>
        <div class="input-group-prepend">
                    <span class="input-group-text">
                        调拨日期
                    </span>
        </div>
        <label for="transfer-date"></label>
        <input readonly id="transfer-date" type='text' class="form-control"/>
    </div>

    <nav class="navbar navbar-expand-lg navbar-light bg-light area-nav" data-toggle="collapse" href="#areas">
        <a class="navbar-brand">调拨区域明细<span style="font-size: 14px">(点击可展开/收起)</span></a>
    </nav>
    <div class="show" id="areas">

    </div>
    <div class="block-divider"></div>

    <!--拨出需求-->
    <nav class="navbar navbar-expand-lg navbar-light bg-light out-org-nav" data-toggle="collapse" href="#transfer-out-details-container-show">
        <a class="navbar-brand" >拨出<span
                style="font-size: 14px">(点击可展开/收起)</span></a>
    </nav>
    <div class="show" id="transfer-out-details-container-show">

    </div>
    <div class="transfer-out-details-container">

    </div>
    <button onclick="add_one_transfer_out_org_card()" id="add-transfer-out-detail-btn"
            class="btn btn-primary btn-block hide"
            style="margin-top: 15px">+拨出组织
    </button>

    <div class="block-divider"></div>
    <!--拨入需求-->
    <nav class="navbar navbar-expand-lg navbar-light bg-light in-org-nav" data-toggle="collapse" href="#transfer-in-details-container-show">
        <a class="navbar-brand">拨入<span
                style="font-size: 14px">(点击可展开/收起)</span></a>
    </nav>
    <div id="transfer-in-details-container-show" class="show"></div>

    <div class="transfer-in-details-container">

    </div>
    <button id="add-transfer-in-detail-btn" onclick="add_one_transfer_in_org_card()"
            class="btn btn-primary btn-success btn-block hide" style="margin-top: 15px">+拨入组织
    </button>

    <div class="block-divider"></div>

    <nav class="navbar navbar-expand-lg navbar-light bg-light dispatcher-info-nav" data-toggle="collapse" href="#transfer-dispatch-detail-container">
        <a class="navbar-brand">调度信息<span
                style="font-size: 14px">(点击可展开/收起)</span></a>
    </nav>

    <div class="list-group list-group-flush show" id="transfer-dispatch-detail-container">

    </div>


</div><!-- root div -->


<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/select2/4.1.0-rc.0/js/select2.full.min.js"></script>

<script src="../js/jquery.md5.js"></script>
<script src="../js/longhu_app_util.js"></script>
<script>

    let approve_state = sessionStorage.getItem("approve_state");
    let transfer_id = sessionStorage.getItem("transfer_id");
    let can_edit_out_area = sessionStorage.getItem('can_edit_out_area');
    let can_edit_in_area = sessionStorage.getItem('can_edit_in_area');
    let only_read = sessionStorage.getItem('only_read');


    let outAreaOptionsHtml = '';
    let outInterestAreaOptionsHtml = '';
    let inAreaOptionsHtml = '';
    let inInterestAreaOptionsHtml = '';
    let current_out_area_id = '';
    let current_in_area_id = '';

    let default_out_org_options_html = '';
    let default_in_org_options_html = '';

    let default_out_interest_org_options_html = '';
    let default_in_interest_org_options_html = '';

    let default_out_bank_options_html = '';
    let default_in_bank_options_html = '';

    let default_out_bank_account_options_html = '';
    let default_in_bank_account_options_html = '';

    let default_trade_mode_options_html = '';
    let default_settlement_method_option_html = '';

    let approve_state_enum = {
        '1': '未审批',
        '2': '已审批',
        '3': '已拒绝',
        '4': '审批中',

    }


    $(document).ready(function () {

        $('#approve_state_badge').html(approve_state_enum[approve_state])
        get_detail_data();
        check_approve_state();
        get_trade_mode_options();

    });


    let check_approve_state = function () {
        if (approve_state === '4' && only_read !== 'true') {
            // 审批中才有新增拨入、拨出组织按钮、通过、拒绝按钮
            if (can_edit_out_area === 'true') {
                $('#add-transfer-out-detail-btn').removeClass('hide')
            }
            if (can_edit_in_area === 'true') {
                $('#add-transfer-in-detail-btn').removeClass('hide');

            }
            $('.approve-block').removeClass('hide');

        }
    }

    let get_detail_data = function () {
        lh_ajax("${ctxPath}/api/longhu/getTransferNoteDetail.do", {
            id: transfer_id,
            approveState: approve_state,
            cUser: '${currentUser}',
        }, function (data) {

            console.log(data);

            set_base_info(data.result.baseInfo);
            set_area_info(data.result.transferAreas);
            set_org_info(data.result.transferOrganizations);
            set_transfer_dispatch_info(data.result.transferDispatchInfos);

        })
    }

    let set_transfer_dispatch_info = function (list) {
        let $dispatcher = $('.dispatcher-info-nav');
        if (list.length > 0) {
            $dispatcher.append('<span class="badge badge-secondary">' + list.length + '</span>')
            let container = $('#transfer-dispatch-detail-container');
            for (let item of list) {
                let html = '<li class="list-group-item list-group-item-action">' +
                    '<p class="mb-1"><span class="badge badge-light" style="margin-right: 15px">拨出组织</span>' + item.outOrg + '</p>' +
                    '<p class="mb-1"><span class="badge badge-light" style="margin-right: 15px">拨出账户</span>' + item.outAccount + '</p>' +
                    '<p class="mb-1"><span class="badge badge-light" style="margin-right: 15px">拨入组织</span>' + item.inOrg + '</p>' +
                    '<p class="mb-1"><span class="badge badge-light" style="margin-right: 15px">拨入账户</span>' + item.inAccount + '</p>' +
                    '<p class="mb-1"><span class="badge badge-light" style="margin-right: 15px">调度金额</span>' + item.amount + '</p>' +
                    '<p class="mb-1"><span class="badge badge-light" style="margin-right: 15px">交易类型</span>' + item.tradeMode + '</p>' +
                    '<p class="mb-1"><span class="badge badge-light" style="margin-right: 15px">结算方式</span>' + item.settlementMethod + '</p>' +
                    '<p class="mb-1"><span class="badge badge-light" style="margin-right: 15px">调度状态</span>' + item.state + '</p>' +
                    '<p class="mb-1"><span class="badge badge-light" style="margin-right: 15px">调度信息</span>' + item.info + '</p>' +
                    '</li>'
                container.append(html);
            }
        } else {
            $dispatcher.addClass('hide');
        }
    }

    let total_apply_amount = 0;
    let set_base_info = function (baseInfo) {
        $('#transfer-base-apply-org').val(baseInfo.orgName);
        $('#apply_total_amount_input').val(baseInfo.amount);
        $('#transfer-date').val(baseInfo.date);
        // $('#transfer-base-type').val(baseInfo.transferType);

        let reg = new RegExp(",", "g");//g,表示全部替换。
        total_apply_amount = baseInfo.amount.replace(reg, "");
    }


    let set_area_info = function (areas) {
        // 新增一个组织时
        // 拨出组织
        get_org_options2(areas.outTransferDetails[0].outAreaId, function (list) {
            default_out_org_options_html = get_options_html(list);
            if (list.length === 0) {
                return;
            }
            get_bank_options2(list[0].id, function (bank_list) {
                default_out_bank_options_html = get_options_html(bank_list);
                if (bank_list.length === 0) {
                    return;
                }
                get_account_options2(bank_list[0].id, list[0].id, function (account_list) {
                    default_out_bank_account_options_html = get_options_html(account_list);
                })
            })
        });
        // 拨入组织
        get_org_options2(areas.inTransferDetails[0].inAreaId, function (list) {
            default_in_org_options_html = get_options_html(list);
            if (list.length === 0) {
                return;
            }
            get_bank_options2(list[0].id, function (bank_list) {
                default_in_bank_options_html = get_options_html(bank_list);
                if (bank_list.length === 0) {
                    return;
                }
                get_account_options2(bank_list[0].id, list[0].id, function (account_list) {
                    default_in_bank_account_options_html = get_options_html(account_list);
                })
            })
        });

        // 拨出计息组织
        get_org_options2(areas.outTransferDetails[0].outInterestAreaId, function (list) {
            default_out_interest_org_options_html = get_options_html(list);
        });
        // 拨入计息组织
        get_org_options2(areas.inTransferDetails[0].inInterestAreaId, function (list) {
            default_in_interest_org_options_html = get_options_html(list);
        });

        get_trade_mode_options(function (mode_list) {
            default_trade_mode_options_html = get_options_html(mode_list);
            get_settlement_method_options(mode_list[0].id, function (method_list) {
                default_settlement_method_option_html = get_options_html(method_list);
            })
        })


        let areas_count = areas.outTransferDetails.length + areas.inTransferDetails.length;
        let $area = $('.area-nav');
        if (areas_count > 0) {
            $area.append('<span class="badge badge-secondary">' + areas_count + '</span>')
        } else {
            $area.addClass('hide');
        }
        for (let out_item of areas.outTransferDetails) {
            outAreaOptionsHtml += '<option value="' + out_item.outAreaId + '">' + out_item.outAreaName + '</option>';
            outInterestAreaOptionsHtml += '<option value="' + out_item.outInterestAreaId + '">' + out_item.outInterestAreaName + '</option>';
            let html =
                '<div class="card card-body">' +
                '<p class="card-text"><span class="badge badge-primary" style="margin-right: 15px">NO.' + out_item.no + '</span></p>' +
                '<p class="card-text">' +
                '<span class="badge badge-primary" style="margin-right: 15px">金额</span>' + out_item.amount + '</p>' +
                '<p class="card-text">' +
                '<span class="badge badge-primary" style="margin-right: 15px">拨出地区</span>' + out_item.outAreaName + '</p>' +
                '<p class="card-text">' +
                '<span class="badge badge-primary" style="margin-right: 15px">拨出计息地区</span>' + out_item.outInterestAreaName + '</p></div>';
            $('#areas').append(html);
        }
        for (let in_item of areas.inTransferDetails) {
            inAreaOptionsHtml += '<option value="' + in_item.inAreaId + '">' + in_item.inAreaName + '</option>';
            inInterestAreaOptionsHtml += '<option value="' + in_item.inInterestAreaId + '">' + in_item.inInterestAreaName + '</option>';
            let html =
                '<div class="card card-body">' +
                '<p class="card-text"><span class="badge badge-success" style="margin-right: 15px">NO.' + in_item.no + '</span></p>' +
                '<p class="card-text">' +
                '<span class="badge badge-success" style="margin-right: 15px">金额</span>' + in_item.amount + '</p>' +
                '<p class="card-text">' +
                '<span class="badge badge-success" style="margin-right: 15px">拨入地区</span>' + in_item.inAreaName + '</p>' +
                '<p class="card-text">' +
                '<span class="badge badge-success" style="margin-right: 15px">拨入计息地区</span>' + in_item.inInterestAreaName + '</p></div>';
            $('#areas').append(html);
        }


    }

    let set_org_info = function (orgInfo) {
        let previous_out_org_count = orgInfo.outTransferOrgDetails.length;
        let previous_in_org_count = orgInfo.inTransferOrgDetails.length;

        transfer_out_org_no = previous_out_org_count + 1;
        transfer_in_org_no = previous_in_org_count + 1;
        let $out_nav = $('.out-org-nav');
        let $in_nav = $('.in-org-nav');
        if (previous_out_org_count > 0) {
            $out_nav.append('<span class="badge badge-primary">' + previous_out_org_count + '</span>')
        } else {
            $out_nav.addClass('hide');
        }
        if (previous_in_org_count > 0) {
            $in_nav.append('<span class="badge badge-success">' + previous_in_org_count + '</span>')
        } else {
            $in_nav.addClass('hide');
        }


        for (let org of orgInfo.outTransferOrgDetails) {
            let transferOutHtml = $(
                '<div class="card card-body">' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend">' +
                '<span class="input-group-text">序号</span></div>' +
                '<input class="form-control" value="' + org.id + '" readonly/>' +
                '</div>' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend">' +
                '<span class="input-group-text">拨出地区</span></div>' +
                '<input class="form-control" value="' + org.area + '" readonly/>' +
                '</div>' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend"><span class="input-group-text">拨出计息地区</span></div>' +
                '<input class="form-control" value="' + org.interestArea + '" readonly/>' +
                '</div>' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend">' +
                '<span class="input-group-text">拨出组织</span></div>' +
                '<input class="form-control" value="' + org.org + '" readonly/></div>' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend">' +
                '<span class="input-group-text">拨出计息组织</span></div>' +
                '<input class="form-control" value="' + org.interestOrg + '" readonly/>' +
                '</div>' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend"><span class="input-group-text">拨出银行</span></div>' +
                '<input class="form-control" value="' + org.bank + '" readonly/></div>' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend">' +
                '<span class="input-group-text">拨出账户</span></div>' +
                '<input class="form-control " value="' + org.bankAccount + '" readonly/>' +
                '</div>' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend">' +
                '<span class="input-group-text">拨入单位类型</span></div>' +
                '<input readonly type="text" class="form-control" value="内部单位" aria-label="transfer-out-unit-type">' +

                '</div><div class="input-group mb-3">' +
                '<div class="input-group-prepend">' +
                '<span class="input-group-text">交易类型</span></div>' +
                '<input readonly class="form-control" value="' + org.tradeMode + '" /></div>' +
                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend">' +
                '<span class="input-group-text">结算方式</span></div>' +
                '<input readonly class="form-control" value="' + org.settlementMethod + '"></div>' +
                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend">' +
                '<span class="input-group-text">¥</span></div>' +
                '<input readonly type="text" class="form-control all-out-amount"  value="' + org.amount + '">' +
                '<div class="input-group-append"><span class="input-group-text">元</span></div>' +

                '</div><div class="input-group mb-3">' +
                '<div class="input-group-prepend">' +
                '<span class="input-group-text">资金用途</span></div>' +
                '<input readonly type="text" class="form-control" value="' + org.purpose + '"></div>' +
                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend">' +
                '<span class="input-group-text">备注</span></div>' +
                '<input readonly type="text" class="form-control" value="' + org.memo + '"></div>' +
                '</div>');

            $('#transfer-out-details-container-show').append(transferOutHtml);
        }
        for (let org of orgInfo.inTransferOrgDetails) {
            let transferOutHtml = $(
                '<div class="card card-body">' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend">' +
                '<span class="input-group-text">序号</span></div>' +
                '<input class="form-control" value="' + org.id + '" readonly/>' +
                '</div>' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend">' +
                '<span class="input-group-text">拨入地区</span></div>' +
                '<input class="form-control" value="' + org.area + '" readonly/>' +
                '</div>' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend"><span class="input-group-text">拨入计息地区</span></div>' +
                '<input class="form-control" value="' + org.interestArea + '" readonly/>' +
                '</div>' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend">' +
                '<span class="input-group-text">拨入组织</span></div>' +
                '<input class="form-control" value="' + org.org + '" readonly/></div>' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend">' +
                '<span class="input-group-text">拨入计息组织</span></div>' +
                '<input class="form-control" value="' + org.interestOrg + '" readonly/>' +
                '</div>' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend"><span class="input-group-text">拨出银行</span></div>' +
                '<input class="form-control" value="' + org.bank + '" readonly/></div>' +

                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend">' +
                '<span class="input-group-text">拨出账户</span></div>' +
                '<input class="form-control " value="' + org.bankAccount + '" readonly/>' +
                '</div>' +
                '<div class="input-group mb-3">' +
                '<div class="input-group-prepend">' +
                '<span class="input-group-text">¥</span></div>' +
                '<input readonly type="text" class="form-control all-in-amount"  value="' + org.amount + '">' +
                '<div class="input-group-append"><span class="input-group-text">元</span></div>');

            $('#transfer-in-details-container-show').append(transferOutHtml);
        }

    }


    $('#add-transfer-area-btn').on('click', function () {

        location.href = 'fund_set_transfer_area_details.jsp';
    });


    let transfer_out_org_no = 1;
    let add_one_transfer_out_org_card = function () {
        let transferOutHtml = $(
            '<div class="card card-body">' +
            '<div class="input-group mb-3">' +
            '<div class="input-group-prepend">' +
            '<span class="input-group-text">顺序号</span></div>' +
            '<input onkeyup="on_no_key_up(this)" type="number" value="' + transfer_out_org_no + '" id="transfer-out-no' + transfer_out_org_no + '" class="form-control transfer-out-no" placeholder="序号" aria-label="no"></div>' +

            '<div class="mb-3">' +
            '<label for="transfer-out-area-data' + transfer_out_org_no + '">拨出地区</label>' +
            '<select onchange="on_area_select($(this),1,false)" class="form-control transfer-out-area-data" id="transfer-out-area-data' + transfer_out_org_no + '">' +
            outAreaOptionsHtml +
            '</select></div>' +

            '<div class="mb-3">' +
            '<label for="transfer-out-area-interest-data' + transfer_out_org_no + '">拨出计息地区</label>' +
            '<select onchange="on_area_select($(this),1,true)"  class="form-control transfer-out-area-interest-data" id="transfer-out-area-interest-data' + transfer_out_org_no + '">' +
            outInterestAreaOptionsHtml +
            '</select></div>' +

            '<div class="mb-3">' +
            '<label for="transfer-out-org-data' + transfer_out_org_no + '">拨出组织</label>' +
            '<select onchange="on_org_select($(this),1)"  class="form-control transfer-out-org-data" id="transfer-out-org-data' + transfer_out_org_no + '">' +
            default_out_org_options_html +
            '</select>' +
            '</div>' +

            '<div class="mb-3">' +
            '<label for="transfer-out-org-interest-data' + transfer_out_org_no + '">拨出计息组织</label>' +
            '<select class="form-control transfer-out-org-interest-data" id="transfer-out-org-interest-data' + transfer_out_org_no + '">' +
            default_out_interest_org_options_html +
            '</select></div>' +

            '<div class="mb-3">' +
            '<label for="transfer-out-bank-data' + transfer_out_org_no + '">拨出银行</label>' +
            '<select onchange="on_bank_select($(this),1)" class="form-control transfer-out-bank-data" id="transfer-out-bank-data' + transfer_out_org_no + '">' +
            default_out_bank_options_html +
            '</select></div>' +

            '<div class="mb-3">' +
            '<label for="transfer-out-bank-account-data' + transfer_out_org_no + '">拨出账户' +
            '</label><select class="form-control transfer-out-bank-account-data" id="transfer-out-bank-account-data' + transfer_out_org_no + '">' +
            default_out_bank_account_options_html +
            '</select></div>' +

            '<div class="input-group mb-3">' +
            '<div class="input-group-prepend">' +
            '<span class="input-group-text">拨入单位类型</span></div>' +
            '<input  readonly value="内部单位" type="text" id="transfer-out-unit-type" class="form-control" placeholder="输入拨入单位类型" aria-label="transfer-out-unit-type">' +
            '</div>' +

            '<div class="mb-3">' +
            '<label for="transfer-out-trade-mode-data' + transfer_out_org_no + '">交易类型</label>' +
            '<select onchange="on_mode_change($(this));" class="form-control transfer-out-trade-mode-data" id="transfer-out-trade-mode-data' + transfer_out_org_no + '">' +
            default_trade_mode_options_html +
            '</select></div>' +

            '<div class="mb-3">' +
            '<label for="transfer-out-settlement-method-data' + transfer_out_org_no + '">结算方式</label>' +
            '<select value="" class="form-control transfer-out-settlement-method-data" id="transfer-out-settlement-method-data' + transfer_out_org_no + '">' +
            default_settlement_method_option_html +
            '</select></div>' +
            '<div class="input-group mb-3">' +
            '<div class="input-group-prepend">' +
            '<span class="input-group-text">¥</span></div>' +
            '<input type="number" class="form-control transfer-out-amount all-out-amount"  aria-label="拨出金额" placeholder="输入拨出金额">' +
            '<div class="input-group-append"><span class="input-group-text">元</span></div>' +

            '</div><div class="input-group mb-3">' +
            '<div class="input-group-prepend">' +
            '<span class="input-group-text">资金用途</span></div>' +
            '<input value="调拨" type="text" class="form-control transfer-out-purpose" placeholder="输入资金用途"></div>' +
            '<div class="input-group mb-3">' +
            '<div class="input-group-prepend">' +
            '<span class="input-group-text">备注</span></div>' +
            '<input type="text" class="form-control transfer-out-memo" placeholder="输入备注" aria-label="transfer-out-memo"></div>' +
            '<button type="button" onclick="delete_transfer_org_card($(this),1)" class="btn btn-danger">删除</button>' +
            '</div>');

        transferOutHtml.find('select').select2();
        transferOutHtml.find('.select2').css('width','100%')
        transferOutHtml.find('.transfer-out-settlement-method-data').val('');

        $('.transfer-out-details-container').append(transferOutHtml);

        transfer_out_org_no++;
    }

    let transfer_in_org_no = 1;
    let add_one_transfer_in_org_card = function () {


        let transfer_in_card_html = $(
            '<div class="card card-body">' +
            '<div class="input-group mb-3">' +
            '<div class="input-group-prepend"><span class="input-group-text">顺序号</span></div>' +
            '<input onkeyup="on_no_key_up(this)" type="number" value="' + transfer_in_org_no + '" id="transfer-in-no" class="form-control transfer-in-no" placeholder="序号" aria-label="no"></div>' +

            '<div class="mb-3">' +
            '<label for="transfer-in-area-data' + transfer_in_org_no + '">拨入地区</label>' +
            '<select onchange="on_area_select($(this),2,false)" class="form-control transfer-in-area-data" id="transfer-in-area-data' + transfer_in_org_no + '">' +
            inAreaOptionsHtml +
            '</select>' +

            '<div class="mb-3">' +
            '<label for="transfer-in-area-interest-data' + transfer_in_org_no + '">拨入计息地区</label>' +
            '<select onchange="on_area_select($(this),2,true)" class="form-control transfer-in-area-interest-data" id="transfer-in-area-interest-data' + transfer_in_org_no + '">' +
            inInterestAreaOptionsHtml +
            '</select>' +

            '<div class="mb-3">' +
            '<label for="transfer-in-org-data' + transfer_in_org_no + '">拨入组织</label>' +
            '<select onchange="on_org_select($(this),2)"  class="form-control transfer-in-org-data" id="transfer-in-org-data' + transfer_in_org_no + '">' +
            default_in_org_options_html +
            '</select></div>' +

            '<div class="mb-3">' +
            '<label for="transfer-in-org-interest-data' + transfer_in_org_no + '">拨入计息组织</label>' +
            '<select class="form-control transfer-in-org-interest-data" id="transfer-in-org-interest-data' + transfer_in_org_no + '">' +
            default_in_interest_org_options_html +
            '</select></div>' +

            '<div class="mb-3">' +
            '<label for="transfer-in-bank-data' + transfer_in_org_no + '">拨入银行</label>' +
            '<select onchange="on_bank_select($(this),2)" class="form-control transfer-in-bank-data" id="transfer-in-bank-data' + transfer_in_org_no + '">' +
            default_in_bank_options_html +
            '</select></div>' +

            '<div class="mb-3">' +
            '<label for="transfer-in-bank-account-data' + transfer_in_org_no + '">拨入账户</label>' +
            '<select class="form-control transfer-in-bank-account-data" id="transfer-in-bank-account-data' + transfer_in_org_no + '">' +
            default_in_bank_account_options_html +
            '</select></div>' +

            '<div class="input-group mb-3">' +
            '<div class="input-group-prepend"><span class="input-group-text">¥</span></div>' +
            '<input type="number" class="form-control transfer-in-amount all-in-amount" aria-label="拨入金额" placeholder="输入拨入金额">' +
            '<div class="input-group-append"><span class="input-group-text">元</span></div></div>' +
            '<button type="button" onclick="delete_transfer_org_card($(this),2)" class="btn btn-danger">删除</button>' +

            '</div>');
        transfer_in_card_html.find('select').select2();
        transfer_in_card_html.find('.select2').css('width','100%');

        $('.transfer-in-details-container').append(transfer_in_card_html);
        transfer_in_org_no++;
    }

    let delete_transfer_org_card = function (cardChild, type) {

        cardChild.parent().remove();


        // 1-拨出，2-拨入
        if (type === 1) {
            transfer_out_org_no--;
        }
        if (type === 2) {
            transfer_in_org_no--;
        }
    }

    let on_area_select = function (obj, type, interest) {

        // get_org_options(obj.val(), type, false, obj, interest, true);
        get_org_options2(obj.val(), function (org_list) {
            let options = get_options_html(org_list);
            let selector;
            if (type === 1) {
                if (interest) {
                    selector = '.transfer-out-org-interest-data';
                } else {
                    selector = '.transfer-out-org-data';
                }
            }
            if (type === 2) {
                if (interest) {
                    selector = '.transfer-in-org-interest-data';
                } else {
                    selector = '.transfer-in-org-data';
                }
            }
            obj.parent().parent().find(selector).html(options);

            if (interest) {
                // 计息组织不继续查询银行
                return;
            }
            get_bank_options2(org_list[0].id, function (bank_list) {
                let options = get_options_html(bank_list);
                let selector = (type === 1) ? '.transfer-out-bank-data' : '.transfer-in-bank-data';
                obj.parent().parent().find(selector).html(options);

                get_account_options2(bank_list[0].id, org_list[0].id, function (account_list) {
                    let options = get_options_html(account_list);
                    let selector = (type === 1) ? '.transfer-out-bank-account-data' : '.transfer-in-bank-account-data';
                    obj.parent().parent().find(selector).html(options);
                })
            })
        });
    }

    let on_org_select = function (obj, type) {
        get_bank_options2(obj.val(), function (bank_list) {
            let options = get_options_html(bank_list);
            let selector = (type === 1) ? '.transfer-out-bank-data' : '.transfer-in-bank-data';
            obj.parent().parent().find(selector).html(options);

            get_account_options2(bank_list[0].id, obj.val(), function (account_list) {
                let options = get_options_html(account_list);
                let selector = (type === 1) ? '.transfer-out-bank-account-data' : '.transfer-in-bank-account-data';
                obj.parent().parent().find(selector).html(options);
            })
        })
    }
    let on_bank_select = function (obj, type) {
        let org_selector = type === 1 ? '.transfer-out-org-data' : '.transfer-in-org-data';
        let org_id = obj.parent().parent().find(org_selector).val();
        get_account_options2(obj.val(), org_id, function (account_list) {
            let options = get_options_html(account_list);
            let selector = (type === 1) ? '.transfer-out-bank-account-data' : '.transfer-in-bank-account-data';
            obj.parent().parent().find(selector).html(options);
        })
    }

    let on_mode_change = function (obj) {
        get_settlement_method_options(obj.val(), function (method_list) {
            let options = get_options_html(method_list);
            let selector = '.transfer-out-settlement-method-data';
            obj.parent().parent().find(selector).html(options);
        })
    }


    let get_org_options2 = function (query_id, after_get_data) {
        lh_ajax("${ctxPath}/api/longhu/getOrganization.do", {
            areaId: query_id,
            cUser: '${currentUser}',
        }, function (data) {
            console.log(data);
            if (typeof after_get_data === 'function') {
                after_get_data(data.result.data);
            }
        })
    }

    let get_bank_options2 = function (query_id, after_get_data) {
        lh_ajax("${ctxPath}/api/longhu/getBank.do", {
            orgId: query_id,
            cUser: '${currentUser}',
        }, function (data) {
            console.log(data);
            if (typeof after_get_data === 'function') {
                after_get_data(data.result.data);
            }
        })
    }

    let get_account_options2 = function (query_id, org_id, after_get_data) {
        lh_ajax("${ctxPath}/api/longhu/getAccount.do", {
            orgId: org_id,
            bankId: query_id,
            cUser: '${currentUser}',
        }, function (data) {
            console.log(data);
            if (typeof after_get_data === 'function') {
                after_get_data(data.result.data);
            }
        })
    }

    let get_trade_mode_options = function (after_get_data) {
        lh_ajax("${ctxPath}/api/longhu/getTradeMode.do", {
            cUser: '${currentUser}',
        }, function (data) {
            console.log(data);
            if (typeof after_get_data === 'function') {
                after_get_data(data.result.data);
            }
        })
    }

    let get_settlement_method_options = function (query_id, after_get_data) {
        lh_ajax("${ctxPath}/api/longhu/getSettlementMethod.do", {
            id: query_id,
            cUser: '${currentUser}',
        }, function (data) {
            console.log(data);
            if (typeof after_get_data === 'function') {
                after_get_data(data.result.data);
            }
        })
    }

    let get_options_html = function (list) {
        let options_html = '';
        for (let item of list) {
            options_html += '<option value="' + item.id + '">' + item.name + '</option>'
        }

        return options_html;
    }

    let get_added_orgs = function () {
        let out_list = [];
        let in_list = [];
        $('.transfer-out-details-container .card').each(function () {
            let out_no = $(this).find('.transfer-out-no').val();
            let out_area_id = $(this).find('.transfer-out-area-data').val();
            let out_interest_area_id = $(this).find('.transfer-out-area-interest-data').val();
            let out_org_id = $(this).find('.transfer-out-org-data').val();
            let out_interest_org_id = $(this).find('.transfer-out-org-interest-data').val();
            let out_bank_id = $(this).find('.transfer-out-bank-data').val();
            let out_account_id = $(this).find('.transfer-out-bank-account-data').val();
            let out_trade_mode_id = $(this).find('.transfer-out-trade-mode-data').val();
            let out_settlement_method_id = $(this).find('.transfer-out-settlement-method-data').val();
            let out_amount = $(this).find('.transfer-out-amount').val();
            let out_purpose = $(this).find('.transfer-out-purpose').val();
            let out_memo = $(this).find('.transfer-out-memo').val();
            let out_in_unit_type = '1';

            out_list.push({
                no: out_no,
                areaId: out_area_id,
                interestAreaId: out_interest_area_id,
                orgId: out_org_id,
                interestOrgId: out_interest_org_id,
                bankId: out_bank_id,
                bankAccountId: out_account_id,
                tradeModeId: out_trade_mode_id,
                settlementMethodId: out_settlement_method_id,
                amount: out_amount,
                purpose: out_purpose,
                memo: out_memo,
                unitType: out_in_unit_type,
            })
        })

        $('.transfer-in-details-container .card').each(function () {
            let in_no = $(this).find('.transfer-in-no').val();
            let in_area_id = $(this).find('.transfer-in-area-data').val();
            let in_interest_area_id = $(this).find('.transfer-in-area-interest-data').val();
            let in_org_id = $(this).find('.transfer-in-org-data').val();
            let in_interest_org_id = $(this).find('.transfer-in-org-interest-data').val();
            let in_bank_id = $(this).find('.transfer-in-bank-data').val();
            let in_account_id = $(this).find('.transfer-in-bank-account-data').val();
            let amount = $(this).find('.transfer-in-amount').val();

            in_list.push({
                no: in_no,
                areaId: in_area_id,
                interestAreaId: in_interest_area_id,
                orgId: in_org_id,
                interestOrgId: in_interest_org_id,
                bankId: in_bank_id,
                bankAccountId: in_account_id,
                amount: amount,
            })
        })
        console.log('out-list:')
        console.log(out_list);
        console.log('in-list:')

        console.log(in_list);

        return {
            outList: out_list,
            inList: in_list
        };

    }


    let request_approve = function (params) {
        confirm({
            title: '确认提交？',
            info: '提交后APP端无法修改，请确认填写内容无误',
            onOk: function () {
                lh_ajax("${ctxPath}/api/longhu/approve.do", {
                    id: transfer_id,
                    approveMsg: '默认提交',
                    jsonParams: JSON.stringify(params),
                    cUser: '${currentUser}',
                }, function (data) {

                    onlyConfirm({
                        title: '操作结果',
                        info: data.result.msg,
                        onOk: function () {
                            history.go(-1);
                        }
                    })
                })
            }
        })


    }

    let on_approve_btn_click = function () {
        let added_orgs = get_added_orgs();

        if (check_seq_no(added_orgs.inList) === false) {
            toast('拨入组织中存在重复序号');
            return;
        }

        if (check_seq_no(added_orgs.outList) === false) {
            toast('拨出组织中存在重复序号');
            return;
        }

        if (check_amount_negative(added_orgs.inList) === true) {
            toast('拨入的金额中存在≤0的金额');
            return;
        }
        if (check_amount_negative(added_orgs.outList) === true) {
            toast('拨出的金额中存在≤0的金额');
            return;
        }

        let out_field_check_result = check_field_negative(added_orgs.outList, true);
        let in_field_check_result = check_field_negative(added_orgs.inList, false);

        if (out_field_check_result.empty === true) {
            toast('拨出' + out_field_check_result.name + '为空，请检查');
            return;
        }
        if (in_field_check_result.empty === true) {
            toast('拨入' + in_field_check_result.name + '为空，请检查');
            return;
        }

        let in_summary_amount = get_summary_amount('.all-in-amount');
        let out_summary_amount = get_summary_amount('.all-out-amount');
        let total_amount = parseFloat(total_apply_amount);

        if (in_summary_amount > total_amount) {
            toast('拨入总金额不能大于调拨金额');
            return;
        }
        if (out_summary_amount > total_amount) {
            toast('拨出总金额不能大于调拨金额');
            return;
        }

        if (in_summary_amount < total_amount || out_summary_amount < total_amount) {
            confirm({
                title: '提示',
                info: '拨出/入总金额与调拨金额不等，是否继续提交？',
                onOk: function () {
                    request_approve(added_orgs);
                }
            })
        } else {
            request_approve(added_orgs);
        }


    }


    let on_refuse_btn_click = function () {

        confirm({
            title: '确认驳回？',
            info: '正在驳回当前单据',
            onOk: function () {
                lh_ajax('${ctxPath}/api/longhu/rejectTask.do', {
                    id: transfer_id,
                    cUser: '${currentUser}',
                }, function (data) {
                    onlyConfirm({
                        title: '操作提示',
                        info: data.result.msg,
                        onOk: function () {
                            window.location.href = 'fund_index.jsp';
                        }
                    })
                })
            }
        })
    }

    let check_seq_no = function (details) {
        let tmp = {};
        for (let detail of details) {
            if (tmp[detail.no] === undefined) {
                tmp[detail.no] = [1];
            } else {
                tmp[detail.no].push(1);
            }
        }

        for (let key in tmp) {
            if (tmp[key].length > 1) {
                return false;
            }
        }

        return true;
    }

    let check_amount_negative = function (details) {

        for (let detail of details) {
            if (detail.amount === '' || parseFloat(detail.amount) <= 0) {
                return true;
            }
        }

        return false;
    }

    let get_summary_amount = function (selector) {
        let sum = 0;

        $(selector).each(function () {
            let amount = $(this).val();
            if (amount !== '' && amount !== null) {
                sum += parseFloat(amount);
            }
        });

        return sum;
    }

    let check_field_negative = function (details, out) {

        for (let detail of details) {
            if (detail.no === '') {
                return {name: '序号', empty: true};
            }

            if (detail.areaId === '' || detail.areaId === null) {
                return {name: '地区', empty: true};
            }
            if (detail.interestAreaId === '' || detail.interestAreaId === null) {
                return {name: '计息地区', empty: true};
            }
            if (detail.orgId === '' || detail.orgId === null) {
                return {name: '组织', empty: true};
            }
            if (detail.interestOrgId === '' || detail.interestOrgId === null) {
                return {name: '计息组织', empty: true};
            }
            if (detail.bankId === '' || detail.bankId === null) {
                return {name: '银行', empty: true};
            }
            if (detail.accountId === '' || detail.accountId === null) {
                return {name: '账户', empty: true};
            }

            if (out) {
                if (detail.purpose === '') {
                    return {name: '资金用途', empty: true};
                }

                if (detail.settlementMethodId === '' || detail.settlementMethodId === null) {
                    return {name: '结算方式', empty: true};

                }
            }
        }

        return {name: '', empty: false}
    }

</script>
</body>
</html>
