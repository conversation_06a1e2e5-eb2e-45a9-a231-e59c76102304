<%@ page import="com.fingard.app.delegate.framework.util.Constants" %><%--
  Created by IntelliJ IDEA.
  User: sylvia
  Date: 2021/12/22
  Time: 9:42
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<%
    String ctxPath = request.getContextPath();
    String userOrg = String.valueOf(request.getSession().getAttribute(Constants.ORG_ID));
%>
<c:set var="ctxPath" value="<%=ctxPath%>"/>
<c:set var="userOrg" value="<%=userOrg%>"/>

<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>切换组织</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">

</head>
<body>
<div class="input-group" style="padding: 15px;">
    <span class="input-group-text" id="basic-addon1" style="display: none">组织名称</span>
    <input oninput="on_input($(this))" type="text" class="form-control" placeholder="组织名称" aria-label="Username"
           aria-describedby="basic-addon1">
</div>
<div style="padding: 15px;">
    <ul class="list-group">

    </ul>
</div>
<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="../js/jquery.md5.js"></script>
<script src="../js/longhu_app_util.js"></script>
<script>

    let current_org_id = '${userOrg}';
    let list = [];
    $(document).ready(function () {

        get_data();
    })

    let on_input = function (input) {
        console.log(input.val());
        filter_data(input.val())
    }

    let filter_data = function (input_val) {
        let list_group = $('.list-group');
        list_group.html('');
        for (let item of list) {
            let entity_name = item.fullName;
            if (entity_name.indexOf(input_val) === -1) {
                continue;
            }
            let html;
            if (item.entityId === current_org_id) {
                html = $('<li class="list-group-item active" data-entityid="' + item.entityId + '">' + entity_name + '</li>');
            } else {
                html = $('<li class="list-group-item" data-entityid="' + item.entityId + '">' + entity_name + '</li>');
            }

            html.on('click', function () {
                switch_org(item.entityId);
            })

            list_group.append(html);
        }
    }
    let get_data = function () {
        $('input').val('');
        lh_ajax('${ctxPath}/user/getOperateableOrg.do', {}, function (data) {
            console.log(data);
            if (data.result.data.length > 0) {
                let list_group = $('.list-group');
                list_group.html('');
                list = data.result.data;
                for (let item of list) {
                    let entity_name = item.fullName;

                    let html;
                    if (item.entityId === current_org_id) {
                        html = $('<li class="list-group-item active" data-entityid="' + item.entityId + '">' + entity_name + '</li>');
                    } else {
                        html = $('<li class="list-group-item" data-entityid="' + item.entityId + '">' + entity_name + '</li>');
                    }

                    html.on('click', function () {
                        switch_org(item.entityId);
                    })

                    list_group.append(html);
                }
            }
        })
    }

    let switch_org = function (org_id) {
        lh_ajax('${ctxPath}/user/switchOrg.do', {
            orgid: org_id,
        }, function (data) {
            current_org_id = data.result.entityId;

            onlyConfirm({
                title: '操作提示',
                info: '切换成功',
                onOk: function () {

                    get_data();
                    history.go(-1);

                }
            })

        })
    }

</script>
</body>
</html>
