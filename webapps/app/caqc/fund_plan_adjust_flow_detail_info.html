<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="GB2312">
    <meta name="viewport" content="width=device-width">
    <title>资金计划调整</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"
          crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">

    <style>
        body, .row, h4, a {
            font-size: 1.5rem;

        }

        .hide-after::after {
            width: 0;
        }

        .accordion-button::after {
            position: absolute;
            left: 1%;
            display: block;
        }

        .accordion-button {
            padding: 0.5em 1em;
            font-size: 1.5rem;
        }

        .accordion-button:not(.collapsed) {
            background-color: white;
            box-shadow: none;
        }

        .btn-primary {
            color: #0d0d0d;
            background-color: rgb(91, 155, 213);
            border-color: rgb(91, 155, 213);
            font-size: 1.5rem;
        }

        .btn-danger {
            color: #0d0d0d;
            background-color: rgb(248, 203, 173);
            border-color: rgb(248, 203, 173);
            font-size: 1.5rem;


        }

        .btn-secondary {
            font-size: 1.5rem;
        }

        .navbar {
            background-color: #F1F3F5;
            height: 10vh;
            width: 100%;
            bottom: 0;
            position: fixed;
            z-index: 2;
            padding: 20px;

        }

        .parent-container {
            display: block;
            /*background-color: yellow;*/
            width: 860px;
            min-height: 500vh;
            height: 2200px;
        }
        .header {
            text-align: center;
            padding: 10px;
            /*width: 100%;*/
        }

        .wrapper {
            margin-right: 10px;
            /*background-color: #0d0d0d;*/
            width: 100%;
            height: 85vh;
            overflow: hidden;
        }

        .p-3 {
            white-space: nowrap;
        }


    </style>
</head>
<body>
<div class="navbar" style="padding: 10px;font-size: 1.8rem">
    <a class="navbar-brand" href="#">审批</a>

    <div class="btn-group" role="group" id="approve-block">

        <a class="btn btn-primary" onclick="on_approve_btn_click('FUND_PLAN_ADJUST')">同意</a>
        <a class="btn btn-danger" onclick="on_refuse_btn_click('FUND_PLAN_ADJUST')">拒绝</a>

    </div>
</div>
<div class="wrapper">

    <div class="parent-container">

        <div class="content">

            <div class="header">
                <div style="text-align: center"><p id="title-name" style="font-size: 2rem"></p></div>
                <div style="text-align: center"><p id="title-period" style="font-size: 1.5rem"></p></div>
                <div style="text-align: end"><p>单位：万元</p></div>

            </div>

            <div class="container">
                <div class="row" style="width: 860px; background-color: rgb(189,215,238); color: #0d0d0d">
                    <div class="col-3 p-3">
                        项目
                    </div>
                    <div class="col-3 p-3" style="text-align: end">
                        现金
                    </div>
                    <div class="col-3 p-3" style="text-align: end">
                        承兑
                    </div>
                    <div class="col-3 p-3" style="text-align: end">
                        合计
                    </div>
                </div>
            </div>


        </div>

        <div id="err-info-area" style="padding: 10px; display: none">
            <div style="text-align: center"><h4 id="err-info"></h4></div>
        </div>
    </div>
</div>
<!--js-->
<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"
        crossorigin="anonymous"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/better-scroll/2.5.1/better-scroll.min.js"></script>
<!--<script src="https://cdn.bootcdn.net/ajax/libs/better-scroll/2.5.1/better-scroll.min.js"></script>-->
<script src="../js/longhu_app_util.js"></script>
<script src="../js/caqc_approve.js"></script>

<script>
    let _id = getQueryVariable("workItemId");

    let bs;
    $(document).ready(function () {
        let wrapper = document.querySelector('.wrapper');
        // BetterScroll.use(Zoom);

        bs = BetterScroll.createBScroll(".wrapper", {
            click: true,
            probeType: 3,
            freeScroll: true,
            scrollX: true,  // 横向可滑动，默认为false
            scrollY: true,  // 纵向可滑动，默认为true
            zoom: {
                start: 0.4,
                min: 0.45,
                max: 2.0
            }
        });
        get_data(_id);





    });

    let get_data = function (_id) {

        simple_ajax('/caqc/getDetailInfo.do', {id: _id, type: 'FUND_PLAN_ADJUST'}, function (data) {
            console.log(data);

            $('#title-name').html(data.result.title.orgName);
            $('#title-period').html(data.result.title.period + '月度资金平衡计划');
            set_data(data.result.data);
        }, function (msg) {

            $('#err-info-area').css('display', 'block');
            $('#err-info').html(msg);
        })

        bs.refresh();

    }

    let set_data = function (list) {

        for (let item of list) {

            let subParentHtml = '';
            for (let subItem of item.subBudgetItems) {

                let subSubParentHtml = '<table class="table"><tbody>';

                for (let subSubItem of subItem.subBudgetItems) {
                    subSubParentHtml +=

                        '<div class="container">\n' +
                        '    <div class="row" style="width: 805px">\n' +
                        '        <div class="col-3 bg-light p-3">\n' +
                        '            ' + subSubItem.itemName + '\n' +
                        '        </div>\n' +
                        '        <div class="col-3 bg-light p-3" style="text-align: end">\n' +
                        '            ' + subSubItem.cashAmount + '\n' +
                        '        </div>\n' +
                        '        <div class="col-3 bg-light p-3" style="text-align: end">\n' +
                        '            ' + subSubItem.draftAmount + '\n' +
                        '        </div>\n' +
                        '        <div class="col-3 bg-light p-3" style="text-align: end">\n' +
                        '            ' + subSubItem.amount + '\n' +
                        '        </div>\n' +
                        '    </div>\n' +
                        '</div>';
                }
                subSubParentHtml += '</tbody></table>';

                let hideAfterClass = '';
                if (subItem.subBudgetItems.length === 0) {
                    hideAfterClass = 'hide-after'
                }

                subParentHtml += '<div class="accordion accordion-flush" id="parent-' + subItem.itemId + '">\n' +
                    '  <div class="accordion-item">\n' +
                    '    <h2 class="accordion-header" id="flush-heading' + subItem.itemId + '">\n' +
                    '      <button class="accordion-button ' + hideAfterClass + ' collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapse' + subItem.itemId + '" aria-expanded="false" aria-controls="flush-collapse' + subItem.itemId + '">\n' +
                    '       <div class="container">\n' +
                    '<div class="row" style="width: 800px">\n' +
                    '    <div class="col-3 bg-light p-3">\n' +
                    subItem.itemName +
                    '    </div>\n' +
                    '    <div class="col-3 bg-light p-3" style="text-align: end">\n' +
                    subItem.cashAmount +
                    '    </div>\n' +
                    '    <div class="col-3 bg-light p-3" style="text-align: end">\n' +
                    subItem.draftAmount +
                    '    </div>\n' +
                    '    <div class="col-3 bg-light p-3" style="text-align: end">\n' +
                    subItem.amount +
                    '    </div>\n' +
                    '  </div>' +
                    '       </div>' +
                    '      </button>\n' +
                    '    </h2>\n' +
                    '    <div id="flush-collapse' + subItem.itemId + '" class="accordion-collapse collapse" aria-labelledby="flush-heading' + subItem.itemId + '" data-bs-parent="parent-' + subItem.itemId + '">\n' +
                    '      <div class="accordion-body"><ul class="list-group list-group-flush">' + subSubParentHtml + '</ul></div>\n' +
                    '    </div>\n' +
                    '  </div>'
            }

            let hasSub = item.subBudgetItems.length !== 0;
            let subHtmlContainer = '';
            if (hasSub) {
                subHtmlContainer = '    <div id="flush-collapse' + item.itemId + '" class="accordion-collapse collapse" aria-labelledby="flush-heading' + item.itemId + '" data-bs-parent="parent-' + item.itemId + '">\n' +
                    '      <div class="accordion-body">' + subParentHtml + '</div>\n' +
                    '    </div>\n';
            }


            let parentHtml = $('<div class="accordion accordion-flush" id="parent-' + item.itemId + '">\n' +
                '  <div class="accordion-item">\n' +
                '    <h2 class="accordion-header" id="flush-heading' + item.itemId + '">\n' +
                '      <button onclick="need_refresh()" " class="accordion-button collapsed"  type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapse' + item.itemId + '" aria-expanded="false" aria-controls="flush-collapse' + item.itemId + '">\n' +
                '       <div class="container">\n' +
                '<div class="row" style="width: 820px">\n' +
                '    <div class="col-3 bg-light p-3" >\n' +
                item.itemName +
                '    </div>\n' +
                '    <div class="col-3 bg-light p-3" style="text-align: end">\n' +
                item.cashAmount +
                '    </div>\n' +
                '    <div class="col-3 bg-light p-3" style="text-align: end">\n' +
                item.draftAmount +
                '    </div>\n' +
                '    <div class="col-3 bg-light p-3" style="text-align: end">\n' +
                item.amount +
                '    </div>\n' +
                '  </div>' +
                '                </div>' +
                '      </button>\n' +
                '    </h2>\n' +
                subHtmlContainer +
                '  </div>');
            if (item.subBudgetItems.length === 0) {
                parentHtml.find('.accordion-button').addClass('hide-after');
            }
            $('.content').append(parentHtml);
        }
    }

    let need_refresh = function (){
        bs.refresh();
        console.log(bs);
    }


</script>
</body>
</html>