<!DOCTYPE html>
<html LANG="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>授信申请</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"
          crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">

    <style>
        body, .row, h4, a {
            font-size: 1.5rem;

        }

        .hide {
            display: none;
        }

        .btn-primary {
            color: #0d0d0d;
            background-color: rgb(91, 155, 213);
            border-color: rgb(91, 155, 213);
            font-size: 1.5rem;
        }

        .btn-danger {
            color: #0d0d0d;
            background-color: rgb(248, 203, 173);
            border-color: rgb(248, 203, 173);
            font-size: 1.5rem;


        }
    </style>
</head>
<body>

<div class="parent-container">
    <div class="navbar" style="padding: 10px;font-size: 1.8rem">
        <a class="navbar-brand" href="#">审批</a>

        <div class="btn-group" role="group" id="approve-block">

            <a class="btn btn-primary" onclick="on_approve_btn_click('FUND_PLAN_ADJUST')">同意</a>
            <a class="btn btn-danger" onclick="on_refuse_btn_click('FUND_PLAN_ADJUST')">拒绝</a>

        </div>
    </div>
    <div class="content">


        <div style="padding: 10px">
            <ul id="field-list" class="list-group list-group-flush">
            </ul>
        </div>

    </div>

    <div id="err-info-area" style="padding: 10px; display: none">
        <div style="text-align: center"><h4 id="err-info"></h4></div>
    </div>
</div>


<!--js-->
<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"
        crossorigin="anonymous"></script>
<script src="../js/longhu_app_util.js"></script>
<script src="../js/caqc_approve.js"></script>
<script>
    let _id = getQueryVariable("workItemId");

    $(document).ready(function () {

        get_data(_id);

    });

    let get_data = function (_id) {

        simple_ajax('/caqc/getDetailInfo.do', {id: _id, type: 'CREDIT'}, function (data) {
            console.log(data);
            set_data(data.result.data);
        }, function (msg) {

            $('#err-info-area').css('display', 'block');
            $('#err-info').html(msg);
        })
    }

    let set_data = function (list) {

        for (let item of list) {

            let html = '<li class="list-group-item">\n' +
                '        <div class="row">\n' +
                '            <div class="col-6">\n' +
                '                ' + item.key + '\n' +
                '            </div>\n' +
                '            <div class="col">\n' +
                '                ' + item.value + '\n' +
                '            </div>\n' +
                '        </div>\n' +
                '    </li>';
            $('#field-list').append(html);

        }
    }


</script>
</body>
</html>