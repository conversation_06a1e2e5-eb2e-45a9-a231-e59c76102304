.zjzl-report .date-picker-wrap {
	background: #fff;
	display: -webkit-box;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	padding-top: 14px;
	padding-bottom: 4px;
	border-bottom: 1px solid #cbcbcb;
}
.zjzl-report .date-picker-wrap div {
	-webkit-box-flex: 1;
	text-align: center;
}
.zjzl-report .date-picker-wrap button {
	height: 30px;
	line-height: 30px;
	padding: 0px;
	width: 104px;
	border-radius: 15px;
	font-size: 13px;
}
.zjzl-report .start-date button {
	background: #3A94CA;
	color: #fff;
	border: 1px solid #3A94CA;
}
.zjzl-report .end-date button {
	border: 1px solid #3A94CA;
	color: #3A94CA;             
}
.zjzl-report .date-picker-wrap p {
	height: 24px;
	line-height: 24px;
	color: #565656;
}
.zjzl-report .input-report,
.zjzl-report .output-report {
	background: #fff;
	padding-top: 22px;
	padding: 22px 20px 0 20px;
}
.zjzl-report .input-report-title,
.zjzl-report .output-report-title {
	height: 16px;
	line-height: 16px;
}
.zjzl-report .input-report-title,
.zjzl-report .output-report-title {
	display: -webkit-box;
	-webkit-box-pack: center;
}
.zjzl-report .input-report-title span,
.zjzl-report .output-report-title span {
	display: block;
}
.zjzl-report .input-report-title span a,
.zjzl-report .output-report-title span a {
	color: #22a380;
}
.zjzl-report .input-report-title span:nth-of-type(1),
.zjzl-report .output-report-title span:nth-of-type(1) {
	color: #333;
}
.zjzl-report .input-report-title span:nth-of-type(2),
.zjzl-report .output-report-title span:nth-of-type(2) {
	-webkit-box-flex: 1;
	color: #DF6A6E;
	font-size: 18px;
	padding-left: 6px;
}
.zjzl-report .input-report-title span:nth-of-type(3),
.zjzl-report .input-report-title span:nth-of-type(4),
.zjzl-report .output-report-title span:nth-of-type(3),
.zjzl-report .output-report-title span:nth-of-type(4) {
	width: 75px;
	text-align: right;
}
.zjzl-report .output-report {
	margin-top: 15px;
}
.zjzl-report .total-amount {
	display: -webkit-box;
	margin-top: 15px;
	background: #fff;
	height: 98px;
	-webkit-box-align: center;
	-webkit-box-pack: center;
}
.zjzl-report .total-amount div {
	-webkit-box-flex: 1;
	text-align: center;
}
.zjzl-report .total-amount span {
	color: #EBA448;
	font-size: 16px;
	padding: 0 6px;
}
.zjzl-report .input-chart,
.zjzl-report .output-chart  {
	width: 100%;
	height: 170px;
}
.zjzl-report ~ .mui-popup {
	top: 40%;
}