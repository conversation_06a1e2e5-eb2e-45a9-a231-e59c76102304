@charset "UTF-8";
/**
 * 页面消息提示
 */
.ui-tips-news-wrap {
  margin: 20px 15px;
  text-align: center; }

.ui-tips-news {
  background: #383939;
  position: relative;
  height: 40px;
  line-height: 40px;
  display: -webkit-inline-box;
  -webkit-box-align: center;
  padding-right: 25px;
  border-radius: 5px;
  font-size: 14px;
  color: #fff;
  padding-left: 15px; }
  .ui-tips-news .ui-avatar-tiled, .ui-tips-news .ui-tips-news-thumb, .ui-tips-news i {
    display: block;
    margin-left: -5px;
    margin-right: 10px; }
  .ui-tips-news .ui-tips-news-thumb {
    width: 30px;
    height: 30px;
    position: relative; }
    .ui-tips-news .ui-tips-news-thumb > span {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      background-repeat: no-repeat;
      -webkit-background-size: cover; }
  .ui-tips-news div {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    -webkit-box-flex: 1;
    height: inherit; }

.ui-tips-news:after {
  font-family: "iconfont" !important;
  font-size: 32px;
  line-height: 44px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  display: block;
  color: rgba(0, 0, 0, 0.5);
  color: #c7c7c7;
  content: "\e600";
  position: absolute;
  right: 15px;
  top: 50%;
  margin-top: -22px;
  margin-right: -10px; }
  @media (max-width: 320px) {
    .ui-tips-news:after {
      right: 10px; } }

.ui-tips-news .ui-reddot, .ui-tips-news .ui-badge-num {
  margin-left: 10px;
  margin-right: 5px; }
