@charset "UTF-8";
/**
 * 选项卡
 */
.ui-tab {
  width: 100%;
  overflow: hidden; }

.ui-tab-nav {
  width: 100%;
  background-color: #fff;
  display: box;
  display: -webkit-box;
  font-size: 16px;
  height: 45px;
  box-sizing: border-box; }

.ui-tab-content {
  display: -webkit-box; }

.ui-tab-content > li {
  -webkit-box-flex: 1;
  width: 100%; }

.ui-tab-nav li {
  height: 45px;
  line-height: 45px;
  min-width: 70px;
  box-flex: 1;
  -webkit-box-flex: 1;
  text-align: center;
  color: #777;
  box-sizing: border-box;
  border-bottom: 2px solid transparent;
  width: 100%; }

.ui-tab-nav li.current {
  color: #00a5e0;
  border-bottom: 2px #00a5e0 solid; }

.ui-tab-nav li:active {
  opacity: .8; }

/*# sourceMappingURL=tab.css.map */
