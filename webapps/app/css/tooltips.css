@charset "UTF-8";
.ui-tooltips {
  width: 100%;
  position: relative;
  z-index: 99;
  overflow: hidden;
  box-sizing: border-box; }

.ui-tooltips-cnt {
  background-color: #fff;
  line-height: 44px;
  height: 44px;
  padding-left: 10px;
  padding-right: 30px;
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis; }
  .ui-tooltips-cnt .ui-icon-close:before {
    font-size: 40px;
    color: rgba(0, 0, 0, 0.2);
    margin-left: -10px;
    position: absolute;
    right: 0;
    top: 0; }

.ui-tooltips-warn .ui-tooltips-cnt {
  background-color: rgba(255, 242, 183, 0.95);
  color: #000; }

.ui-tooltips-warn:active .ui-tooltips-cnt {
  background-color: #e1d498; }

.ui-tooltips-guide .ui-tooltips-cnt {
  color: #00a5e0;
  background-color: rgba(205, 242, 255, 0.95); }
  .ui-tooltips-guide .ui-tooltips-cnt .ui-icon-close:before {
    color: rgba(0, 165, 224, 0.2); }

.ui-tooltips-guide:active .ui-tooltips-cnt {
  background-color: #b5dbe8; }

.ui-tooltips-cnt-link:after {
  font-family: "iconfont" !important;
  font-size: 32px;
  line-height: 44px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  display: block;
  color: rgba(0, 0, 0, 0.5);
  color: #c7c7c7;
  content: "";
  position: absolute;
  right: 15px;
  top: 50%;
  margin-top: -22px;
  margin-right: -10px;
  color: rgba(0, 0, 0, 0.5); }
  @media (max-width: 320px) {
    .ui-tooltips-cnt-link:after {
      right: 10px; } }

.ui-tooltips-guide .ui-tooltips-cnt-link:after {
  color: #00aeef; }

.ui-tooltips-warn i {
  display: inline-block;
  margin-right: 4px;
  margin-left: -4px;
  width: 32px;
  height: 1px;
  vertical-align: top; }

.ui-tooltips-warn i:before {
  font-family: "iconfont" !important;
  font-size: 32px;
  line-height: 44px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  display: block;
  color: rgba(0, 0, 0, 0.5);
  content: "";
  color: #f76249; }

/*# sourceMappingURL=tooltips.css.map */
