.ui-selector header {
  padding: 6px 10px;
  color: #a6a6a6;
  overflow: hidden; }

.ui-selector header h3 {
  float: left; }

.ui-selector-content {
  background: #fff; }

.ui-selector-item p {
  margin-left: 10px;
  -webkit-box-flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis; }

.ui-selector-item .ui-txt-info {
  margin: 0 10px;
  font-size: 12px; }

.ui-selector-item .ui-list-link li:after {
  display: none; }

.ui-selector-item h3:before {
  content: '';
  display: block;
  width: 0;
  height: 0;
  border-left: 6px solid;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  color: #a6a6a6;
  position: absolute;
  left: 25px;
  top: 15px;
  -webkit-transition: all 0.2s; }

.ui-selector-item.active h3:before {
  -webkit-transform: rotate(90deg); }

.ui-selector-item.active h3 {
  border: none;
  background-image: none; }

.ui-selector-item.active ul {
  display: block; }

.ui-selector-item ul {
  display: none; }

.ui-selector-item h3 {
  display: -webkit-box;
  font-size: 16px;
  padding-left: 54px;
  line-height: 44px;
  height: 44px;
  position: relative; }

/*# sourceMappingURL=selector.css.map */
