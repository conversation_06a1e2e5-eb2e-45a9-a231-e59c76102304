@charset "UTF-8";
.ui-poptips {
  width: 100%;
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 999;
  padding: 0px 10px;
  box-sizing: border-box; }

.ui-poptips-cnt {
  background-color: rgba(0, 0, 0, 0.6);
  line-height: 40px;
  height: 40px;
  color: #fff;
  font-size: 16px;
  text-align: center;
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis; }
  .ui-poptips-cnt i {
    display: inline-block;
    width: 32px;
    height: 1px;
    vertical-align: top; }
    .ui-poptips-cnt i:before {
      font-family: "iconfont" !important;
      font-size: 32px;
      line-height: 44px;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -webkit-text-stroke-width: 0.2px;
      display: block;
      color: rgba(0, 0, 0, 0.5);
      margin-right: 2px;
      margin-left: 4px;
      color: #fff;
      line-height: 40px; }

.ui-poptips-info i:before {
  content: ""; }

.ui-poptips-success i:before {
  content: ""; }

.ui-poptips-warn i:before {
  content: ""; }

/*# sourceMappingURL=poptips.css.map */
