.ui-label {
  display: inline-block;
  position: relative;
  line-height: 30px;
  height: 30px;
  padding: 0 15px;
  border: 1px solid #cacccd;
  border-radius: 15px; }
  @media screen and (-webkit-min-device-pixel-ratio: 2) {
    .ui-label {
      position: relative;
      border: 0; }
      .ui-label:before {
        content: "";
        width: 200%;
        height: 200%;
        position: absolute;
        top: 0;
        left: 0;
        border: 1px solid #cacccd;
        -webkit-transform: scale(0.5);
        -webkit-transform-origin: 0 0;
        padding: 1px;
        -webkit-box-sizing: border-box;
        border-radius: 30px;
        pointer-events: none; } }
  .ui-label:active {
    background-color: #f3f2f2; }

.ui-label-list {
  margin: 0 10px; }
  .ui-label-list .ui-label {
    margin: 0 10px 10px 0; }

.ui-label-s {
  font-size: 11px;
  line-height: 13px;
  display: inline-block;
  position: relative;
  padding: 0 1px;
  color: #ff7f0d;
  border: 1px solid #ff7f0d;
  border-radius: 2px; }
  @media screen and (-webkit-min-device-pixel-ratio: 2) {
    .ui-label-s {
      position: relative;
      border: 0; }
      .ui-label-s:before {
        content: "";
        width: 200%;
        height: 200%;
        position: absolute;
        top: 0;
        left: 0;
        border: 1px solid #ff7f0d;
        -webkit-transform: scale(0.5);
        -webkit-transform-origin: 0 0;
        padding: 1px;
        -webkit-box-sizing: border-box;
        border-radius: 4px;
        pointer-events: none; } }
  .ui-label-s:active {
    background-color: #f3f2f2; }
  .ui-label-s:after {
    content: "";
    position: absolute;
    top: -5px;
    bottom: -5px;
    left: -5px;
    right: -5px; }

/*# sourceMappingURL=label.css.map */
