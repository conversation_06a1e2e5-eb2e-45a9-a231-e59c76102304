@charset "UTF-8";
/**
* 红点提醒
*/
.ui-reddot, .ui-reddot-border, .ui-reddot-s {
  position: relative;
  display: inline-block;
  line-height: 22px;
  padding: 0 6px; }
  .ui-reddot:after, .ui-reddot-border:after, .ui-reddot-s:after {
    content: '';
    position: absolute;
    display: block;
    width: 8px;
    height: 8px;
    background-color: #f74c31;
    border-radius: 5px;
    right: -3px;
    top: -3px;
    background-clip: padding-box; }

.ui-reddot-static {
  display: block;
  width: 8px;
  height: 8px;
  padding: 0; }
  .ui-reddot-static:after {
    top: 0;
    right: 0; }

/* 带白边的 */
.ui-reddot-border:before {
  content: '';
  position: absolute;
  display: block;
  width: 8px;
  height: 8px;
  background-color: #fff;
  border-radius: 5px;
  right: -4px;
  top: -4px;
  background-clip: padding-box;
  padding: 1px; }

/* 小号的 */
.ui-reddot-s:after {
  width: 6px;
  height: 6px;
  top: -5px;
  right: -5px; }

/*# sourceMappingURL=reddot.css.map */
