.ui-searchbar-wrap {
  display: -webkit-box;
  -webkit-box-pack: center;
  -webkit-box-align: center;
  background-color: #ebeced;
  height: 44px; }
  .ui-searchbar-wrap button {
    margin-right: 10px; }
  .ui-searchbar-wrap .ui-searchbar-cancel {
    color: #00a5e0;
    font-size: 16px;
    padding: 4px 8px; }
  .ui-searchbar-wrap .ui-searchbar-input, .ui-searchbar-wrap button, .ui-searchbar-wrap .ui-icon-close {
    display: none; }
  .ui-searchbar-wrap.focus {
    -webkit-box-pack: start; }
    .ui-searchbar-wrap.focus .ui-searchbar-input, .ui-searchbar-wrap.focus button, .ui-searchbar-wrap.focus .ui-icon-close {
      display: block; }
    .ui-searchbar-wrap.focus .ui-searchbar-text {
      display: none; }

.ui-searchbar {
  border-radius: 5px;
  margin: 0 10px;
  background: #fff;
  height: 30px;
  line-height: 30px;
  position: relative;
  padding-left: 4px;
  -webkit-box-flex: 1;
  display: -webkit-box;
  -webkit-box-pack: center;
  -webkit-box-align: center;
  color: #bbb;
  font-size: 14px;
  width: 100%; }
  .ui-searchbar input {
    -webkit-appearance: none;
    border: none;
    background: none;
    color: #000;
    width: 100%;
    padding: 4px 0; }
  .ui-searchbar .ui-icon-search {
    line-height: 30px; }
  .ui-searchbar .ui-icon-close {
    line-height: 30px; }

.ui-searchbar-input {
  -webkit-box-flex: 1; }

@media screen and (-webkit-min-device-pixel-ratio: 2) {
  .ui-searchbar.ui-border-radius:before {
    border-radius: 10px; } }

/*# sourceMappingURL=searchbar.css.map */
