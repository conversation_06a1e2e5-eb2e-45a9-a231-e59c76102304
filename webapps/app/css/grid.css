@charset "UTF-8";
/* 三等分 */
.ui-grid, .ui-grid-trisect, .ui-grid-halve {
  padding-left: 15px;
  padding-right: 10px;
  overflow: hidden;
  padding-top: 10px; }
  @media (max-width: 320px) {
    .ui-grid, .ui-grid-trisect, .ui-grid-halve {
      padding-left: 10px;
      padding-right: 5px; } }
  .ui-grid li, .ui-grid-trisect li, .ui-grid-halve li {
    padding-right: 5px;
    padding-bottom: 10px;
    float: left;
    position: relative;
    -webkit-box-sizing: border-box; }

.ui-grid-trisect > li {
  width: 33.3333%; }

.ui-grid-trisect-img {
  padding-top: 149.47%; }

.ui-grid-trisect h4 {
  position: relative;
  margin: 7px 0 3px; }

.ui-grid-trisect h4 span {
  display: inline-block;
  margin-left: 12px;
  color: #777; }

/* 二等分 */
.ui-grid-halve > li {
  width: 50%; }

.ui-grid-halve-img {
  padding-top: 55.17%; }

.ui-grid-trisect-img, .ui-grid-halve-img {
  position: relative;
  width: 100%; }
  .ui-grid-trisect-img > span, .ui-grid-halve-img > span {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    background-repeat: no-repeat;
    -webkit-background-size: cover; }
  .ui-grid-trisect-img img, .ui-grid-halve-img img {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0; }
  .ui-grid-trisect-img.active, .ui-grid-halve-img.active {
    opacity: .5; }

.ui-row {
  display: block;
  overflow: hidden; }

.ui-col {
  float: left;
  box-sizing: border-box;
  width: 100%; }

.ui-col-10 {
  width: 10%; }

.ui-col-20 {
  width: 20%; }

.ui-col-25 {
  width: 25%; }

.ui-col-33 {
  width: 33.3333%; }

.ui-col-50 {
  width: 50%; }

.ui-col-67 {
  width: 66.6666%; }

.ui-col-75 {
  width: 75%; }

.ui-col-80 {
  width: 80%; }

.ui-col-90 {
  width: 90%; }

.ui-row-flex {
  display: -webkit-box;
  width: 100%;
  -webkit-box-sizing: border-box; }
  .ui-row-flex .ui-col {
    float: none;
    -webkit-box-flex: 1;
    width: 0; }
  .ui-row-flex .ui-col-2 {
    -webkit-box-flex: 2; }
  .ui-row-flex .ui-col-3 {
    -webkit-box-flex: 3; }
  .ui-row-flex .ui-col-4 {
    -webkit-box-flex: 4; }

.ui-row-flex-ver {
  -webkit-box-orient: vertical; }
  .ui-row-flex-ver .ui-col {
    width: 100%;
    height: 0; }

/*# sourceMappingURL=grid.css.map */
