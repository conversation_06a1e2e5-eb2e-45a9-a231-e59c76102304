@charset "UTF-8";
.ui-form {
  background-color: #fff; }

.ui-form-item-order.active {
  background-color: #e5e6e7; }

/* 表单输入项 */
.ui-form-item {
  position: relative;
  font-size: 16px;
  height: 44px;
  line-height: 44px;
  padding-right: 15px;
  padding-left: 15px; }
  .ui-form-item label:not(.ui-switch):not(.ui-checkbox):not(.ui-radio) {
    width: 95px;
    position: absolute;
    text-align: left;
    box-sizing: border-box; }
  .ui-form-item input,
  .ui-form-item textarea {
    width: 100%;
    box-sizing: border-box;
    -webkit-appearance: none;
    border: 0;
    background: none;
    padding-left: 95px; }
  .ui-form-item input[type="checkbox"], .ui-form-item input[type="radio"] {
    padding-left: 0; }
  .ui-form-item .ui-icon-close {
    position: absolute;
    top: 0;
    right: 6px; }
    @media (max-width: 320px) {
      .ui-form-item .ui-icon-close {
        right: 1px; } }
  @media (max-width: 320px) {
    .ui-form-item {
      padding-left: 10px;
      padding-right: 10px; } }

.ui-form-item-textarea {
  height: 65px; }

.ui-form-item-textarea label {
  vertical-align: top; }

.ui-form-item-textarea textarea {
  margin-top: 15px;
  border: none; }

.ui-form-item-textarea textarea:focus {
  outline: none; }

.ui-form-item-link > li:after {
  font-family: "iconfont" !important;
  font-size: 32px;
  line-height: 44px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  display: block;
  color: rgba(0, 0, 0, 0.5);
  color: #c7c7c7;
  content: "";
  position: absolute;
  right: 15px;
  top: 50%;
  margin-top: -22px;
  margin-right: -10px; }
  @media (max-width: 320px) {
    .ui-form-item-link > li:after {
      right: 10px; } }

.ui-form-item-l label,
.ui-form-item-r button {
  color: #00a5e0;
  text-align: center; }

.ui-form-item-r .ui-icon-close {
  right: 125px; }

.ui-form-item-l input:not([type="checkbox"]):not([type="radio"]) {
  padding-left: 115px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box; }

.ui-form-item-r {
  padding-right: 0; }

.ui-form-item-r input:not([type="checkbox"]):not([type="radio"]) {
  padding-left: 0;
  padding-right: 150px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box; }

.ui-form-item-r button {
  width: 110px;
  height: 44px;
  position: absolute;
  top: 0;
  right: 0; }

.ui-form-item-r button.disabled {
  color: #bbb; }

.ui-form-item-r button:not(.disabled):active {
  background-color: #e5e6e7; }

.ui-form-item-pure input,
.ui-form-item-pure textarea {
  padding-left: 0; }

/* 表单展示项 */
.ui-form-item-show label {
  color: #777; }

.ui-form-item-link:after {
  font-family: "iconfont" !important;
  font-size: 32px;
  line-height: 44px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  display: block;
  color: rgba(0, 0, 0, 0.5);
  color: #c7c7c7;
  content: "";
  position: absolute;
  right: 15px;
  top: 50%;
  margin-top: -22px;
  margin-right: -10px; }
  @media (max-width: 320px) {
    .ui-form-item-link:after {
      right: 10px; } }

.ui-form-item-checkbox,
.ui-form-item-radio,
.ui-form-item-switch {
  display: -webkit-box;
  -webkit-box-align: center; }

/*# sourceMappingURL=form.css.map */
