@charset "UTF-8";
/**
 * 未读数通知
 */
.ui-badge, .ui-badge-muted, .ui-badge-num, .ui-badge-corner, .ui-badge-cornernum {
  display: inline-block;
  text-align: center;
  background: #f74c31;
  color: #fff;
  font-size: 11px;
  height: 16px;
  line-height: 16px;
  -webkit-border-radius: 8px;
  padding: 0 6px;
  background-clip: padding-box; }

/* 浅色的 */
.ui-badge-muted {
  background: #b6cae0; }

.ui-badge-num {
  height: 19px;
  line-height: 20px;
  font-size: 12px;
  min-width: 19px;
  -webkit-border-radius: 10px; }

.ui-badge-wrap {
  position: relative;
  text-align: center; }

.ui-badge-corner {
  position: absolute;
  border: 2px #fff solid;
  height: 20px;
  line-height: 20px;
  top: -4px;
  right: -9px; }

.ui-badge-cornernum {
  position: absolute;
  top: -4px;
  right: -9px;
  height: 19px;
  line-height: 19px;
  font-size: 12px;
  min-width: 19px;
  -webkit-border-radius: 10px;
  top: -5px;
  right: -5px; }

/*# sourceMappingURL=badge.css.map */
