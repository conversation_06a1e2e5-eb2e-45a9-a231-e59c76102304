.ui-actionsheet {
  position: fixed;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  z-index: 9999;
  opacity: 0;
  pointer-events: none;
  display: -webkit-box;
  -webkit-box-orient: horizontal;
  -webkit-box-pack: center;
  -webkit-box-align: end;
  background: rgba(0, 0, 0, 0.4); }
  .ui-actionsheet.show {
    pointer-events: inherit;
    opacity: 1; }
    .ui-actionsheet.show .ui-actionsheet-cnt {
      -webkit-transform: translateY(0);
      -webkit-transition-delay: 0.3s; }

.ui-actionsheet-cnt {
  font-size: 21px;
  position: fixed;
  bottom: 0;
  padding: 0 8px;
  width: 100%;
  box-sizing: border-box;
  text-align: center;
  -webkit-transform: translateY(100%);
  -webkit-transition-property: all;
  -webkit-transition-timing-function: ease-out;
  -webkit-transition-duration: 0.3s; }

.ui-actionsheet button, .ui-actionsheet h4 {
  background: rgba(255, 255, 255, 0.84);
  display: block;
  width: 100%;
  color: #0079ff;
  box-sizing: border-box; }

.ui-actionsheet button {
  line-height: 44px;
  height: 44px; }

.ui-actionsheet h4 {
  line-height: 24px;
  padding-left: 20px;
  padding-right: 20px;
  padding-top: 10px;
  padding-bottom: 10px;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px; }

.ui-actionsheet button:not(:last-child) {
  border-top: 1px #e0e0e0 solid; }

.ui-actionsheet button:last-child {
  margin: 8px 0;
  border-radius: 3px; }

.ui-actionsheet button:nth-last-child(2) {
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px; }

.ui-actionsheet button:active {
  opacity: 0.84; }

.ui-actionsheet h4 {
  font-size: 13px;
  color: #8a8a8a; }

.ui-actionsheet .ui-actionsheet-del {
  color: #fd472b; }

@media screen and (-webkit-min-device-pixel-ratio: 2) {
  .ui-actionsheet button:not(:last-child) {
    border: 0;
    background-position: left top;
    background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0.5, transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0));
    background-repeat: repeat-x;
    -webkit-background-size: 100% 1px; } }

/*# sourceMappingURL=actionsheet.css.map */
