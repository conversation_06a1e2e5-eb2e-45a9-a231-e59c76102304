body {
	margin: 0;
	padding: 0;
}

.header {
	height: 110px;
	background: #2489CE;
	background-repeat: no-repeat;
	background-position-x: center;
	background-size: cover;
	text-align: center;
}

.header p {
	line-height: 200px;
	color: #FFFFFF;
	font-size: 38px;
}

.header img {
	width: 60px;
	height: 60px;
	position: absolute;
	top: 90px;
	left: 100px;
	background: transparent;
}

.content .cheque-img {
	background: #FFFFFF;
	margin: 0 auto;
	width: 80%;
	height: 300px;
	border: solid;
	border-width: 1px;
	border-radius: 10px;
	text-align: center;
	color: #777777;
	position: absolute;
	top: 80px;
	left: 10%;
	font-family: "΢���ź�";
}

.content .cheque-img p {
	line-height: 200px;
	font-size: 40px;
}

.content .cheque-img a {
	width: 66px;
	height: 66px;
}

.content .cheque-img .h-icon {
	background: #0088BB;
	position: absolute;
	top: 100px;
	left: 100px;
}

.content .cheque-img .hr {
	width: 180px;
	height: 1px;
	background: #777777;
	position: absolute;
	top: 150px;
	left: 300px;
}

.content .cheque-img #h-title {
	position: absolute;
	top: 0px;
	left: 300px;
}

.content .cheque-img #h-info {
	position: absolute;
	top: 100px;
	left: 300px;
}

.content .cheque-img #h-amount {
	position: absolute;
	top: 120px;
	left: 550px;
	font-size: 45px;
	color: #F7C942;
}

.content img {
	position: absolute;
	top: 420px;
	left: 45%;
	width: 110px;
	height: 110px;
	background-color: #cc9527;
}

.content .content-title {
	width: 100%;
	height: 100px;
	/*background-color: #22AADD;*/
	margin-top: 350px;
}

.content .content-title p,span{
	font-size: 40px;
	line-height: 100px;
	
}
.content .content-title p{
	margin-left: 110px;
}


.content .listview {
	/*background: #22AADD;*/
	width: 100%;
	height: 600px;
	margin-bottom: 300px;
}
.content .listview .list-group .list-group-item{
	height: 220px;
	/*background: #C1E2B3;*/
}
.content .listview .list-group .list-group-item p{
	font-size: 40px;
	
}
.content .listview .list-group .list-group-item .item-doc{
	position: absolute;
	top: 30px;
	left: 300px;
}
.content .listview .list-group .list-group-item .item-poc{
	position: absolute;
	top: 90px;
	left: 300px;
}

.content .listview .list-group .list-group-item .item-amount{
	position: absolute;
	top: 60px;
	left: 680px;
}
.content .listview .list-group .list-group-item .item-icon{
	background: turquoise;
	position: absolute;
	top: 60px;
	left: 110px;
}

#btns {
	position: fixed;
	bottom: 0;
	background: rgba(244,244,244,0.9);
	width: 100%;
	height: 210px;
	/*background-color: #22AADD;*/
}
#btns >div{
	width: 300px;
	height: 100px;
	line-height: 100px;
	/*background: #2AABD2;*/
	font-size: 40px;
	border-width: 1px;
	border-radius: 15px;
	margin: 10px;
	text-align: center;
	
}

#btns #btn-add {
	position: absolute;
	top: 55px;
	left: 150px;
	color: #0088BB;
	background: transparent;
	border: solid 1px #0088BB;
}

#btns #btn-sub {
	color: #FFFFFF;
	border: solid 1px #0088BB;
	background-color: #0088BB;
	position: absolute;
	top: 55px;
	right: 150px;
}
