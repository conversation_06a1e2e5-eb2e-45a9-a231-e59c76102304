@charset "UTF-8";
/**
 * 出错页面
 */
.ui-notice {
  width: 100%;
  height: 100%;
  z-index: 99;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-box-pack: center;
  -webkit-box-align: center;
  position: absolute;
  text-align: center; }

.ui-notice > i {
  display: block;
  margin-bottom: 20px; }
  .ui-notice > i:before {
    font-family: "iconfont" !important;
    font-size: 32px;
    line-height: 44px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -webkit-text-stroke-width: 0.2px;
    display: block;
    color: rgba(0, 0, 0, 0.5);
    content: "";
    font-size: 100px;
    line-height: 100px;
    color: rgba(0, 0, 0, 0.3); }

.ui-notice p {
  font-size: 16px;
  line-height: 20px;
  color: #bbb;
  text-align: center;
  padding: 0 15px; }

.ui-notice-btn {
  width: 100%;
  -webkit-box-sizing: border-box;
  padding: 50px 15px 15px; }

.ui-notice-btn button {
  margin: 10px 0px; }

/*# sourceMappingURL=notice.css.map */
