/* Datewheel overlay */
.dw {
    position: absolute;
    top: 5%;
    left: 0;
    z-index: 1001;
    color: #000;
    font-family: arial, verdana, sans-serif;
    font-size: 12px;
    text-shadow: none;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -ms-touch-action: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.dwi {
    position: static;
    margin: 5px;
    display: inline-block;
}
.dwwr {
    min-width: 170px;
    zoom: 1;
    padding: 0 10px;
    text-align: center;
}
/* Datewheel overlay background */
.dw-persp, .dwo {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1001;
}
.dwo {
    background: #000;
    opacity: .7;
    filter: Alpha(Opacity=70);
}
/* Bubble positionings */
.dw-bubble .dw {
    margin: 20px 0;
}
.dw-bubble .dw-arrw {
    position: absolute;
    left: 0;
    width: 100%;
}
.dw-bubble-top .dw-arrw {
    bottom: -36px;
}
.dw-bubble-bottom .dw-arrw {
    top: -36px;
}
.dw-bubble .dw-arrw-i {
    margin: 0 30px;
    position: relative;
    height: 36px;
}
.dw-bubble .dw-arr {
    display: block;
}
.dw-arr {
    display: none;
    position: absolute;
    left: 0;
    width: 0;
    height: 0;
    border-width: 18px 18px;
    border-style: solid;
    margin-left: -18px;
}
.dw-bubble-bottom .dw-arr {
    top: 0;
    border-color: transparent transparent #fff transparent;
}
.dw-bubble-top .dw-arr {
    bottom: 0;
    border-color: #fff transparent transparent transparent;
}
/* Datewheel wheel container wrapper */
.dwc {
    float: none;
    margin: 0 2px 5px 2px;
    padding-top: 30px;
    display: inline-block;
}
.dwcc {
    clear: both;
}
/* Datewheel label */
.dwl {
    text-align: center;
    line-height: 30px;
    height: 30px;
    white-space: nowrap;
    position: absolute;
    top: -30px;
    width: 100%;
}
/* Datewheel value */
.dwv {
    padding: 10px 0;
    border-bottom: 1px solid #000;
}
/* Datewheel wheel container */
.dwrc {
    -webkit-border-radius: 3px;
    border-radius: 3px;
}
.dwwc {
    margin: 0;
    padding: 0 2px;
    position: relative;
    background: #000;
    zoom: 1;
}
/* Datewheel wheels */
.dwwl {
    margin: 4px 2px;
    position: relative;
    z-index: 5;
}
.dww {
    margin: 0 2px;
    overflow: hidden;
    position: relative;
}
.dwsc .dwwl {
    background: #888;
    background: linear-gradient(#000 0%,#333 35%, #888 50%,#333 65%,#000 100%);
    background: -webkit-gradient(linear,left bottom,left top,from(#000),color-stop(0.35, #333),color-stop(0.50, #888),color-stop(0.65, #333),to(#000));
    background: -moz-linear-gradient(#000 0%,#333 35%, #888 50%,#333 65%,#000 100%);
    background: -o-linear-gradient(#000 0%,#333 35%, #888 50%,#333 65%,#000 100%);
}
.dwsc .dww {
    color: #fff;
    background: #444;
    background: linear-gradient(#000 0%,#444 45%, #444 55%, #000 100%);
    background: -webkit-gradient(linear,left bottom,left top,from(#000),color-stop(0.45, #444),color-stop(0.55, #444),to(#000));
    background: -moz-linear-gradient(#000 0%,#444 45%, #444 55%, #000 100%);
    background: -o-linear-gradient(#000 0%,#444 45%, #444 55%, #000 100%);
}
.dw-bf {
    -webkit-perspective: 1000;
    -webkit-backface-visibility: hidden;
}
.dw-ul {
    position: relative;
    z-index: 2;
}
.dw-li {
    padding: 0 5px;
    display: block;
    text-align: center;
    line-height: 40px;
    font-size: 26px;
    white-space: nowrap;
    text-shadow: 0 1px 1px #000;
    vertical-align: bottom;
    opacity: .3;
    filter: Alpha(Opacity=30);
}
/* Higlighted */
.dw-li.dw-hl {
    background: #fff;
    background: rgba(255,255,255,.3);
}
/* Valid entry */
.dw-li.dw-v {
    opacity: 1;
    filter: Alpha(Opacity=100);
}
/* Hidden entry */
.dw-li.dw-h {
    visibility: hidden;
}
.dw-i {
    position: relative;
    height: 100%;
}
/* Wheel +/- buttons */
.dwwb {
    position: absolute;
    z-index: 4;
    left: 0;
    cursor: pointer;
    width: 100%;
    height: 40px;
    text-align: center;
    opacity: 1;
    transition: opacity .2s linear;
    -webkit-transition: opacity .2s linear;
}
.dwa .dwwb {
    opacity: 0;
}
.dwwbp {
    top: 0;
    -webkit-border-radius: 3px 3px 0 0;
    border-radius: 3px 3px 0 0;
    font-size: 40px;
}
.dwwbm {
    bottom: 0;
    -webkit-border-radius: 0 0 3px 3px;
    border-radius: 0 0 3px 3px;
    font-size: 32px;
    font-weight: bold;
}
.dwpm .dwwc {
    background: transparent;
}
.dwpm .dww {
    margin: 0;
}
.dwpm .dw-li {
    text-shadow: none;
}
.dwpm .dwwol {
    display: none;
}
/* Datewheel wheel overlay */
.dwwo {
    position: absolute;
    z-index: 3;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(#000 0%,rgba(44,44,44,0) 52%, rgba(44,44,44,0) 48%, #000 100%);
    background: -webkit-gradient(linear,left bottom,left top,from(#000),color-stop(0.52, rgba(44,44,44,0)),color-stop(0.48, rgba(44,44,44,0)),to(#000));
    background: -moz-linear-gradient(#000 0%,rgba(44,44,44,0) 52%, rgba(44,44,44,0) 48%, #000 100%);
    background: -o-linear-gradient(#000 0%,rgba(44,44,44,0) 52%, rgba(44,44,44,0) 48%, #000 100%);
}
/* Background line */
.dwwol {
    position: absolute;
    z-index: 1;
    top: 50%;
    left: 0;
    width: 100%;
    height: 0;
    margin-top: -1px;
    border-top: 1px solid #333;
    border-bottom: 1px solid #555;
}
/* Datewheel button */
.dwbg .dwb {
    cursor: pointer;
    overflow: hidden;
    display: block;
    height: 40px;
    line-height: 40px;
    padding: 0 15px;
    margin: 0 2px;
    font-size: 14px;
    font-weight: bold;
    text-decoration: none;
    text-shadow: 0 -1px 1px #000;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.5);
    color: #fff;
    background: #000;
    background: linear-gradient(#6e6e6e 50%,#000 50%);
    background: -webkit-gradient(linear,left bottom,left top,color-stop(0.5, #000),color-stop(0.5, #6e6e6e));
    background: -moz-linear-gradient(#6e6e6e 50%,#000 50%);
    background: -o-linear-gradient(#6e6e6e 50%,#000 50%);
    white-space: nowrap;
    text-overflow: ellipsis;
    -webkit-box-shadow: 0 1px 3px rgba(0,0,0,0.5);
    -webkit-border-radius: 5px;
}
/* Datewheel button container */
.dwbc {
    padding: 5px 0;
    text-align: center;
    clear: both;
}
.dwbc:after {
    content: '';
    display: block;
    clear: both;
}
/* Datewheel button wrapper */
.dwbw {
    display: inline-block;
    float: left;
    width: 50%;
    position: relative;
    z-index: 5;
}
.dwbc-p .dwbw {
    width: 33.33%;
}
/* Hidden label */
.dwhl {
    padding-top: 10px;
}
.dwhl .dwl {
    display: none;
}
/* Multiple selection */
.dwms .dwwms .dw-li {
    padding: 0 40px;
    position: relative;
}
.dwms .dw-msel:after {
    width: 40px;
    text-align: center;
    position: absolute;
    top: 0;
    left: 0;
    content: '✔';
}
/* Backgrounds */
.dwbg {
    background: #fff;
    border-radius: 3px;
    -webkit-border-radius: 3px;
}
.dwbg .dwpm .dwwl {
    border: 1px solid #aaa;
}
.dwbg .dwpm .dww {
    color: #000;
    background: #fff;
    -webkit-border-radius: 3px;
}
.dwbg .dwwb {
    background: #ccc;
    color: #888;
    text-shadow: 0 -1px 1px #333;
    box-shadow: 0 0 5px #333;
    -webkit-box-shadow: 0 0 5px #333;
}
.dwbg .dwwbp {
    background: linear-gradient(#f7f7f7,#bdbdbd);
    background: -webkit-gradient(linear,left bottom,left top,from(#bdbdbd),to(#f7f7f7));
    background: -moz-linear-gradient(#f7f7f7,#bdbdbd);
    background: -o-linear-gradient(#f7f7f7,#bdbdbd);
}
.dwbg .dwwbm {
    background: linear-gradient(#bdbdbd,#f7f7f7);
    background: -webkit-gradient(linear,left bottom,left top,from(#f7f7f7),to(#bdbdbd));
    background: -moz-linear-gradient(#bdbdbd,#f7f7f7);
    background: -o-linear-gradient(#bdbdbd,#f7f7f7);
}
.dwbg .dwb-a {
    background: #3c7500;
    background: linear-gradient(#94c840 50%,#3c7500 50%);
    background: -webkit-gradient(linear,left bottom,left top,color-stop(0.5, #3c7500),color-stop(0.5, #94c840));
    background: -moz-linear-gradient(#94c840 50%,#3c7500 50%);
    background: -o-linear-gradient(#94c840 50%,#3c7500 50%);
}
.dwbg .dwwl .dwb-a {
    background: #3c7500;
    background: linear-gradient(#94c840,#3c7500);
    background: -webkit-gradient(linear,left bottom,left top,from(#3c7500),to(#94c840));
    background: -moz-linear-gradient(#94c840,#3c7500);
    background: -o-linear-gradient(#94c840,#3c7500);
}
