@charset "UTF-8";
.ui-icon-qq, .ui-icon-vip,
.ui-icon-svip,
.ui-icon-yearsvip,
.ui-icon-yearvip {
  display: inline-block;
  width: 24px;
  height: 1px;
  position: relative;
  vertical-align: middle; }

.ui-icon-qq:after, .ui-icon-vip:after,
.ui-icon-svip:after,
.ui-icon-yearsvip:after,
.ui-icon-yearvip:after {
  content: "";
  display: block;
  width: 24px;
  height: 24px;
  background: url(../img/vip/icon_vip.png) -2px 0;
  -webkit-background-size: 24px auto;
  position: absolute;
  line-height: 100px;
  top: -13px; }

.ui-icon-vip:after {
  background-position: -2px -25px; }

.ui-icon-svip:after {
  background-position: -2px -50px; }

.ui-icon-yearsvip:after {
  background-position: 0 -75px; }

.ui-icon-yearvip:after {
  background-position: 0 -100px; }

/*QQ等级图标*/
.ui-icon-qqlevel {
  display: inline-block;
  width: 15px;
  height: 1px;
  position: relative;
  vertical-align: middle; }

.ui-icon-qqlevel:after {
  content: "";
  display: block;
  width: 16px;
  background: url(../img/vip/icon_qqlevel_sprite.png) no-repeat;
  background-repeat: no-repeat;
  position: absolute;
  line-height: 100px;
  height: 16px;
  -webkit-background-size: 16px auto;
  top: -10px; }

.ui-icon-qqlevel-king:after {
  background-position: 0 0; }

.ui-icon-qqlevel-sun:after {
  background-position: 0 -48px; }

.ui-icon-qqlevel-moon:after {
  background-position: 0 -16px; }

.ui-icon-qqlevel-star:after {
  background-position: 0 -32px; }

.ui-icon-qqlevel-none:after {
  background-position: 0 -64px; }

/*图标基类*/
.ui-icon-viplevel, .ui-icon-viplevel-s, .ui-icon-sviplevel, .ui-icon-sviplevel-s, .ui-icon-yearviplevel, .ui-icon-yearviplevel-s, .ui-icon-yearsviplevel, .ui-icon-yearsviplevel-s, .ui-icon-mq {
  display: inline-block;
  height: 1px;
  position: relative;
  vertical-align: middle; }
  .ui-icon-viplevel span, .ui-icon-viplevel-s span, .ui-icon-sviplevel span, .ui-icon-sviplevel-s span, .ui-icon-yearviplevel span, .ui-icon-yearviplevel-s span, .ui-icon-yearsviplevel span, .ui-icon-yearsviplevel-s span, .ui-icon-mq span {
    overflow: hidden;
    display: block;
    width: 100%;
    background-repeat: no-repeat;
    position: absolute;
    line-height: 100px; }

/*等级图标*/
/*宽度与背景size*/
.ui-icon-viplevel, .ui-icon-viplevel-s, .ui-icon-sviplevel, .ui-icon-sviplevel-s, .ui-icon-yearviplevel, .ui-icon-yearviplevel-s, .ui-icon-yearsviplevel, .ui-icon-yearsviplevel-s, .ui-icon-mq {
  width: 48px; }
  .ui-icon-viplevel span, .ui-icon-viplevel-s span, .ui-icon-sviplevel span, .ui-icon-sviplevel-s span, .ui-icon-yearviplevel span, .ui-icon-yearviplevel-s span, .ui-icon-yearsviplevel span, .ui-icon-yearsviplevel-s span, .ui-icon-mq span {
    -webkit-background-size: auto 20px; }

.ui-icon-viplevel-s {
  width: 31px; }
  .ui-icon-viplevel-s span {
    -webkit-background-size: auto 12px; }

.ui-icon-yearviplevel {
  width: 55px; }
  .ui-icon-yearviplevel span {
    -webkit-background-size: auto 19px; }

.ui-icon-yearviplevel-s {
  width: 40px; }
  .ui-icon-yearviplevel-s span {
    -webkit-background-size: auto 12px; }

.ui-icon-sviplevel {
  width: 52px; }
  .ui-icon-sviplevel span {
    -webkit-background-size: auto 22px; }

.ui-icon-sviplevel-s {
  width: 36px; }
  .ui-icon-sviplevel-s span {
    -webkit-background-size: auto 12px; }

.ui-icon-yearsviplevel {
  width: 58px; }
  .ui-icon-yearsviplevel span {
    -webkit-background-size: auto 20px; }

.ui-icon-yearsviplevel-s {
  width: 45px; }
  .ui-icon-yearsviplevel-s span {
    -webkit-background-size: auto 12px; }

.ui-icon-viplevel span, .ui-icon-viplevel-s span, .ui-icon-sviplevel span, .ui-icon-sviplevel-s span, .ui-icon-yearviplevel span, .ui-icon-yearviplevel-s span, .ui-icon-yearsviplevel span, .ui-icon-yearsviplevel-s span, .ui-icon-mq span, .ui-icon-yearviplevel span {
  height: 22px;
  top: -12px; }

.ui-icon-sviplevel span, .ui-icon-yearsviplevel span {
  height: 22px;
  top: -14px; }

.ui-icon-viplevel-s span, .ui-icon-sviplevel-s span, .ui-icon-yearviplevel-s span, .ui-icon-yearsviplevel-s span {
  height: 20px;
  top: -7px; }

/*超Q图标*/
.ui-icon-mq {
  width: 28px; }
  .ui-icon-mq span {
    height: 28px;
    top: -15px;
    -webkit-background-size: 28px auto; }

/**
 * 开通会员按钮
 */
.ui-btn-vip {
  height: 26px;
  line-height: 26px;
  padding: 0 13px;
  display: inline-block;
  position: relative;
  text-align: center;
  font-size: 13px;
  background-color: #1fbaf3;
  border-radius: 3px;
  vertical-align: top;
  color: #fff;
  -webkit-box-sizing: border-box;
  background-clip: padding-box; }

.ui-btn-vip:not(.disabled):not(:disabled):active,
.ui-btn-vip.active {
  background-color: #1ba6d9;
  color: rgba(255, 255, 255, 0.5);
  background-clip: padding-box; }

.ui-tooltips-vip {
  width: 100%;
  position: relative;
  z-index: 99; }

.ui-tooltips-vip .ui-tooltips-cnt {
  background-color: rgba(248, 248, 248, 0.95);
  color: #000;
  line-height: 44px;
  height: 44px;
  padding-left: 10px;
  font-size: 0;
  display: -webkit-box; }

.ui-tooltips-vip .ui-tooltips-cnt span {
  font-size: 14px;
  display: block; }

.ui-tooltips-vip .ui-tooltips-cnt i {
  margin-right: 6px; }

.ui-tooltips-vip .ui-btn-vip {
  position: absolute;
  top: 50%;
  right: 10px;
  margin-top: -13px; }

/*# sourceMappingURL=vip.css.map */
