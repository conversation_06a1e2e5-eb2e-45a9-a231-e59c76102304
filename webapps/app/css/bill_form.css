body {
	background-color: #FFFFFF; 
	margin: 0;
	padding: 0;
	clear: both;
}
ul.mui-table {
	padding: 0;
	margin-bottom: 50px;
}

input {
	z-index: 99;
}

.radio-left {
	margin-left: 5%;
}

.radio-right {
	margin-left: 5%;
}
.btn-block {
	position: fixed;
	bottom: 0;
	width: 100%;
	padding: 10px;
	text-align: center;
	background-color: #C0C0C0;
	opacity: 0.8;
}

input[name='amount'].amount-input {
	border: 0;
	background: none;
	width: 100%;
	margin: 0;
	padding: 0;
	margin-top:5px;
	font-weight: bold;
	color: #777777;
}
input[type='text'].desc-input {
	border: 0;
	background: none;
	width: 100%;
	margin: 0;
	padding: 2px;
	color: #777777;
	font-weight: bold;
	
}
.labels {
	display: inline-block;
	width: 100px;
}

button.date-picker {
	width: 100%;
	height: 40px;
	border: none;
	text-align: left;
	padding: 0;
	font-weight: bold;
}

button.sel {
	width: 100%;
	height: 40px;
	border: none;
	text-align: left;
	padding: 0;
	padding-left: 2px;
	font-weight: bold;
}

button.mui-btn-success {
	background-color: #77b385;
	border: 0;
	border-radius: 20px;
}

button.radio-button{
	border: solid 1px #77B385;
	border-radius: 20px;
}

input[name="accessorynum"] {
	display: none;
	height: 0;
}
	