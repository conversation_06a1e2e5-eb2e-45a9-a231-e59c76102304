@charset "UTF-8";
@font-face {
  font-family: "iconfont";
  src: url(../font/iconfont.ttf) format("truetype"); }
.ui-icon, [class^="ui-icon-"] {
  font-family: "iconfont" !important;
  font-size: 32px;
  line-height: 44px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  display: block;
  color: rgba(0, 0, 0, 0.5); }

.ui-icon-close:before {
  content: ""; }

.ui-icon-search:before {
  content: ""; }

.ui-icon-return:before {
  content: ""; }

.ui-icon-close,
.ui-icon-search {
  color: #8e8e93; }

/*# sourceMappingURL=icon.css.map */
