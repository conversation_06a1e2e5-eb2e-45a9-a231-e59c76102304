@CHARSET "UTF-8";

.header {
	height: 100px;
	background-color:#22aadd;
	background-repeat: no-repeat;
	background-position-x: center;
	background-size: cover;
}

.header .tab1, .tab2, .tab3 {
	width: 33.3%;
	height: 100px;
	float: left;
	margin-top:80px;
	text-align: center;
}

.header img {
	line-height: inherit;
}

.tab1 .img-circle {
	width: 120px;
	height: 120px;
	background-color: #ffffff;
}

.tab2 .img-circle {
	width: 120px;
	height: 120px;
	background-color: #ffffff;
}

.tab3 .img-circle {
	width: 120px;
	height: 120px;
	background-color: #ffffff;
}

.content .title-tip {
	width: 100%;
	text-align: center;
}

.content .title p {
	font-size: 60px;
	color: #EB9316;
	padding: 20px;
	margin-bottom: 30px;
}

.content .title {
	width: 100%;
	height: 35px;
	text-align: center;
}

.content .hr {
	width: 10%;
	height: 4px;
	background: #0088BB;
	margin: 0 auto;
}

.content .title-tip .add-btn {
	position: absolute;
	right: 70px;
	top:110px;
	/* background-color: orange; */
	width: 100px;
	height: 100px;
	margin-top: 30px;
}

.content .btn-submit-all {
	width: 80%;
	height: 30px;
	margin-top: 120px;
	margin-left: 60px;
}

.content .btn-submit-all .sub-btn {
	font-size: 35px;
	text-decoration: none;
	background: transparent;
	color: #FFFFFF;
	padding: 15px;
	font-weight: bold;
	border-radius: 3px;
	/*float: left;*/
	background-color: #22AADD;
}

.content .listview {
	width: 100%;
	margin-top: 80px;
}

.content .listview .list-group .list-group-item img {
	background-color: #28A4C9;
	width: 160px;
	height: 160px;
	margin: 50px;
}

.content .listview .list-group .list-group-item .list-group-item-text {
	font-size: 35px;
	margin-left: 30px;
	position: absolute;
	top: 100px;
	left: 300px;
}

.content .listview .list-group .list-group-item .sub {
	position: absolute;
	top: 160px;
	left: 300px;
}

.content .listview .list-group .list-group-item .item-money {
	position: absolute;
	top: 160px;
	right: 90px;
	font-size: 35px;
}