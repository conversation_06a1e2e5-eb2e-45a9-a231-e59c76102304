@charset "UTF-8";
.ui-list {
  background-color: #fff;
  width: 100%; }
  .ui-list > li {
    position: relative;
    margin-left: 15px;
    display: -webkit-box; }

.ui-list-pure > li {
  display: block; }

/*文字列表*/
.ui-list-text > li,
.ui-list-pure > li {
  position: relative;
  padding-top: 10px;
  padding-bottom: 10px;
  padding-right: 15px;
  -webkit-box-align: center; }

.ui-list-text h4,
.ui-list-text p {
  -webkit-box-flex: 1; }

/*通栏列表*/
.ui-list-cover > li {
  padding-left: 15px;
  margin-left: 0px; }

.ui-list > li.ui-border-t:first-child,
.ui-list > li:first-child > .ui-border-t {
  border: none;
  background-image: none; }

/*列表缩略图*/
.ui-list-thumb,
.ui-list-thumb-s,
.ui-list-img,
.ui-list-icon {
  position: relative;
  margin: 10px 10px 10px 0px; }
  .ui-list-thumb > span,
  .ui-list-thumb-s > span,
  .ui-list-img > span,
  .ui-list-icon > span {
    display: block;
    width: 100%;
    height: 100%;
    z-index: 1;
    background-repeat: no-repeat;
    -webkit-background-size: cover; }

.ui-list-thumb {
  width: 50px;
  height: 50px; }

/*列表普通图片*/
.ui-list-img {
  width: 100px;
  height: 68px; }

.ui-list-thumb-s {
  width: 28px;
  height: 28px; }

/*列表icon*/
.ui-list-icon {
  width: 40px;
  height: 40px; }

.ui-list .ui-avatar,
.ui-list .ui-avatar-s,
.ui-list .ui-avatar-lg {
  margin: 10px 10px 10px 0px; }

/*列表主要信息*/
.ui-list-info {
  -webkit-box-flex: 1;
  padding-top: 10px;
  padding-bottom: 10px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-box-pack: center;
  padding-right: 15px; }
  .ui-list-info p {
    color: #777;
    font-size: 14px; }

.ui-list-text .ui-list-info {
  padding-top: 0;
  padding-bottom: 0; }

.ui-list li h4 {
  font-size: 16px; }

.ui-list:not(.ui-list-text) li > p,
.ui-list li > h5 {
  font-size: 14px;
  color: #777; }

/*列表按压态*/
.ui-list-active > li:active,
.ui-list li.active {
  background-color: #e5e6e7;
  padding-left: 15px;
  margin-left: 0px; }

.ui-list-active > li:active,
.ui-list > li.active,
.ui-list > li.active > .ui-border-t,
.ui-list > li.active + li > .ui-border-t,
.ui-list > li.active + li.ui-border-t {
  background-image: none;
  border-top-color: #e5e6e7; }

/*连接列表*/
.ui-list-link > li:after {
  font-family: "iconfont" !important;
  font-size: 32px;
  line-height: 44px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  display: block;
  color: rgba(0, 0, 0, 0.5);
  color: #c7c7c7;
  content: "";
  position: absolute;
  right: 15px;
  top: 50%;
  margin-top: -22px;
  margin-right: -10px; }
  @media (max-width: 320px) {
    .ui-list-link > li:after {
      right: 10px; } }

.ui-list-text.ui-list-link > li {
  padding-right: 30px; }

.ui-list-link .ui-list-info {
  padding-right: 30px; }

/*  功能类 */
.ui-list-function .ui-list-info {
  padding-right: 75px; }

.ui-list-function .ui-btn {
  position: absolute;
  top: 50%;
  right: 15px;
  margin-top: -15px; }

.ui-list-function .ui-btn-s {
  margin-top: -12px; }

.ui-list-function.ui-list-link .ui-list-info {
  padding-right: 90px; }

.ui-list-function.ui-list-link .ui-btn {
  right: 30px; }

.ui-list-function li {
  -webkit-box-align: inherit; }

.ui-list-one > li {
  padding-top: 0;
  padding-bottom: 0;
  line-height: 44px; }
.ui-list-one .ui-list-info {
  -webkit-box-orient: horizontal;
  -webkit-box-align: center; }
.ui-list-one h4 {
  -webkit-box-flex: 1; }

@media (max-width: 320px) {
  .ui-list > li {
    margin-left: 10px; }

  .ui-list-text > li,
  .ui-list-pure > li,
  .ui-list-info {
    padding-right: 10px; }

  .ui-list-cover > li,
  .ui-list-active > li:active,
  .ui-list li.active {
    padding-left: 10px; }

  .ui-list-text.ui-list-link > li {
    padding-right: 25px; }

  .ui-list-function .ui-list-info {
    padding-right: 70px; }

  .ui-list-function .ui-btn {
    right: 10px; }

  .ui-list-function.ui-list-link .ui-list-info {
    padding-right: 85px; }

  .ui-list-function.ui-list-link .ui-btn {
    right: 25px; } }

/*# sourceMappingURL=list.css.map */
