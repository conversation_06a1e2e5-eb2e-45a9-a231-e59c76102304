body {
	font-family: "微软雅黑";
}
.bu-report .mui-slider-indicator.mui-segmented-control {
	background: #fff;
}
.bu-report .mui-segmented-control.mui-segmented-control-inverted .mui-control-item.mui-active {
	color: #696969;
	font-size: 15px;
	font-weight: bold;
	background: #fff;
}
.bu-report .mui-segmented-control.mui-segmented-control-inverted~.mui-slider-progress-bar {
	background-color: #2999d1;
}
.bu-report .mui-segmented-control.mui-segmented-control-inverted .mui-control-item {
	background: #fff;
	color: #b8b8b8;
	font-size: 15px;
	font-weight: bold;
	height: 42px;
	line-height: 42px;
}
.bu-report .mui-slider .mui-segmented-control.mui-segmented-control-inverted~.mui-slider-group .mui-slider-item {
	border-top: none;
}
.bu-report .mui-fullscreen .mui-segmented-control~.mui-slider-group {
	top: 55px;
	background: #fff;
}
.bu-report .mui-slider .mui-slider-group .mui-slider-item {
	background: #fff;
}
.mui-scroll-wrapper.no-data-scroll:after {
	content:'暂无数据';
	font-size: 30px;
	color: #ccc;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
.bu-report .bu-report-pie {
	background: #fff;	
}
.bu-report .bu-canvas-content {
	width: 100%;
	height: 300px;
	background: #fff;
	display: -webkit-box;
	-webkit-box-align: center;
	-webkit-box-pack: center;
}
.bu-report .bu-canvas-content canvas {
	/*width: 300px;
	height: 300px;*/
}
.bu-report .bu-canvas-info .remain-amount{
	display: -webkit-box;
	height: 40px;
	line-height: 40px;
	font-size: 27px;
}
.bu-report .bu-canvas-info .canvas-legend{
	display: -webkit-box;
	height: 40px;
	line-height: 40px;
	font-size: 14px;
}
.bu-report .bu-canvas-info .canvas-legend {
	display: -webkit-box;
}
.bu-report .bu-canvas-info .canvas-legend div,
.bu-report .bu-canvas-info .remain-amount div {
	-webkit-box-flex: 1;
	width: 100%;
	text-align: center;
}
.bu-report .bu-canvas-info .remain-amount div:nth-of-type(1){
	color: #dd7070;
}
.bu-report .bu-canvas-info .remain-amount div:nth-of-type(2){
	color: #ee8600;
}
.bu-report .canvas-legend div{
	height: 40px;
	position: relative;
}
.bu-report .canvas-legend div em {
	position: absolute;
	height: 14px;
	width: 14px;
	border-radius: 7px;
	top: 13px;
}
.bu-report .canvas-legend div:nth-of-type(1) em {
	background: #DD7070;
}
.bu-report .canvas-legend div:nth-of-type(2) em {
	background: #EE8600;
}
.bu-report .canvas-legend div span {
	padding-left: 20px;
}
.bu-canvas-list {
	margin-top: -10px;
}
.bu-canvas-list-item {
	margin: 10px;
	padding: 10px;
	display: -webkit-box;
	-webkit-box-align: center;
	-webkit-box-pack: center;
	box-sizing: content-box;
	border-radius: 6px;
}
.bu-canvas-list-item.active {
	border: 0px solid #000000;
	box-shadow: 1px 1px 6px #888888;
}
.bu-canvas-list-item .bu-list-left {
	-webkit-box-flex: 1;
	border-right: 1px solid #a0a0a0;
	width: 50%;
	height: 40px;
	display: -webkit-box;
	-webkit-box-pack: center;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-box-orient: vertical;
}

.bu-canvas-list-item .bu-list-right {
	-webkit-box-flex: 1;
	width: 50%;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-align: center;
	-webkit-box-pack: center;
}
.bu-canvas-list-item .bu-list-right div:nth-of-type(1) {
	width: calc(100% - 20px);
}
.bu-canvas-list-item .bu-list-right.bu-list-no-arrow div:nth-of-type(1){
	width: 100%;
}
.bu-canvas-list-item .bu-list-right p {
	margin-bottom: 0px;
	text-align: center;
	width: 100%;
	word-break: break-all;
	overflow: hidden;
	text-overflow: ellipsis;
}
.bu-canvas-list-item .bu-list-right p:nth-of-type(2) {
	color: #ee8600;
	font-size: 16px;
}

.bu-canvas-list-item .bu-list-arrow {
	color: #e4e4e4;
	width: 20px;
	height: 40px;
	line-height: 40px;
}


/*bu_sub.html start*/
#echarts-bar {
	width: 100%;
	background: yellowgreen;
}
/*bu_sub.html end*/

/*SXQKDrillReport.html start*/
.sx-sub .mui-scroll-wrapper {
	background: #fff;
} 
.sx-sub.mui-content {
	background: #fff;
}
.sx-sub .bu-report-pie {
	display: block;
}
.sx-sub .no-data-wrap {
	display: none;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	position: absolute;
	width: 100%;
	height: 100%;
}
.sx-sub .no-data-wrap:after {
	content: '暂无数据';
	font-size: 30px;
	color: #ccc;
}
.sx-sub .mui-scroll {
	min-height: 100%;
}
.total-amount {
	display: -webkit-box;
	-webkit-box-align: center;
	-webkit-box-pack: center;
	padding: 0 20px 10px;
}
.total-amount span:nth-of-type(1) {
	display: inline-block;
	padding-right: 10px;
	height: 40px;
	line-height: 40px;
}
.total-amount div {
	-webkit-box-flex: 1;
	text-align: right;
	color: #EE8600;
	height: 40px;
	line-height: 40px;
	font-size: 27px;
	overflow: hidden;
	text-overflow: ellipsis;
}
/*SXQKDrillReport.html end*/

/*RYEReport.html start*/
.rye-report .top-table {
	background: #fff;
	padding-bottom: 10px;
}
.rye-report .top-table table {
	width: 100%;
	table-layout: fixed;
}
.rye-report .top-table table th,
.rye-report .top-table table td {
	text-align: center;
	width: 33.33%;
	word-break: break-all;
}
.rye-report .top-table table thead tr {
	height: 56px;
	line-height: 56px;
	font-size: 15px;
	font-weight: bold;
}
.top-table table tbody td {
	line-height: 20px;
	font-size: 15px;
	padding: 4px 0px;
}
#ryereport {
	background: #fff;
}
.balances-chart-wrap {
	position: relative;
	width: 100%;
	background: #efeff4;
	padding-top: 10px;
}
.balances-chart {
	display: block;
	height: 500px;
	width: 100%;
	background: #fff !important;
}
.balances-chart-wrap .no-data-wrap {
	-webkit-box-align: center;
	-webkit-box-pack: center;
	min-height: 300px;
	background: #fff;
	position: relative;
	display: none;
}
.balances-chart-wrap .no-data-wrap:after {
	content:'暂无数据';
	font-size: 30px;
	color: #ccc;
}
/*RYEReport.html end*/

/*下拉刷新 start*/
.mui-bar~.mui-content .mui-fullscreen {
	top: 0px;
	height: auto;
}
.mui-pull-top-tips {
	position: absolute;
	top: -20px;
	left: 50%;
	margin-left: -25px;
	width: 40px;
	height: 40px;
	border-radius: 100%;
	z-index: 1;
}
.mui-bar~.mui-pull-top-tips {
	top: 24px;
}
.mui-pull-top-wrapper {
	width: 42px;
	height: 42px;
	display: block;
	text-align: center;
	background-color: #efeff4;
	border: 1px solid #ddd;
	border-radius: 25px;
	background-clip: padding-box;
	box-shadow: 0 4px 10px #bbb;
	overflow: hidden;
}
.mui-pull-top-tips.mui-transitioning {
	-webkit-transition-duration: 200ms;
	transition-duration: 200ms;
}
.mui-pull-top-tips .mui-pull-loading {
	margin: 0;
}
.mui-pull-top-wrapper .mui-icon,
.mui-pull-top-wrapper .mui-spinner {
	margin-top: 7px;
}
.mui-pull-bottom-tips {
	text-align: center;
	background-color: #efeff4;
	font-size: 15px;
	line-height: 40px;
	color: #777;
}
.mui-pull-top-canvas {
	overflow: hidden;
	background-color: #fafafa;
	border-radius: 40px;
	box-shadow: 0 4px 10px #bbb;
	width: 40px;
	height: 40px;
	margin: 0 auto;
}
.mui-pull-top-canvas canvas {
	width: 40px;
}
.mui-slider-indicator.mui-segmented-control {
	background-color: #efeff4;
}
.fing-scroll .mui-scroll {
	min-height: 80%;
}
/*下拉刷新 end*/

/*YQHL.html start*/
.currency-list.mui-fullscreen {
	background: #eeeeef;
}
.currency-list .mui-slider-indicator.mui-segmented-control{
	background: #fff;	
}
.currency-list .mui-segmented-control.mui-scroll-wrapper {
	height: 42px;
}
.currency-list .mui-segmented-control.mui-scroll-wrapper .mui-scroll {
	height: 42px;
}
.currency-list .mui-segmented-control.mui-segmented-control-inverted .mui-control-item.mui-active {
	color: #6a6a6a;
	border-bottom: 2px solid #2e95d3;
}
.currency-list .mui-segmented-control.mui-segmented-control-inverted .mui-control-item {
	color: #b1b1b1;
	font-weight: bold;
}
.currency-list .mui-segmented-control .mui-control-item {
	line-height: 42px;
	height: 42px;
}
.currency-list.mui-fullscreen .mui-segmented-control~.mui-slider-group {
	top: 50px;
}
.currency-list.mui-slider .mui-segmented-control.mui-segmented-control-inverted~.mui-slider-group .mui-slider-item {
	border: 0px;
	background: #fff;
}
.currency-list .mui-segmented-control.mui-scroll-wrapper .mui-control-item {
	width: 90px
}
.currency-list .mui-control-content .mui-scroll {
	height: 100%;
}

.currency-rate-list.no-data {
	height: 100%;
	display: -webkit-box;
	-webkit-box-align: center;
	-webkit-box-pack: center;
}
.currency-rate-list.no-data:after {
	display: block;
	content: "暂无数据";
	font-size: 30px;
	color: #ccc;
}

.currency-detail-table {
	width: 100%;
	margin-top: 14px;
	table-layout: fixed;
}
.currency-detail-table th {
	height: 40px;
	line-height: 40px;
	text-align: center;
	font-weight: normal;
}
.currency-detail-table td {
	padding: 22px 0;
	line-height: 20px;
	text-align: center;
	font-weight: bold;
	word-break: break-all;
}
.currency-detail-table td:nth-of-type(1) {
	text-align: left;
	font-weight: normal;
	padding-left: 26px;
}
/*YQHL.html end*/

/*HLReport_sub.html start*/
#hlreport.mui-content {
	background:#fff;
}
.hl-report {
	background:#fff;
}
.rate-table {
	width: 100%;
	table-layout: fixed;
}
.rate-table th {
	height: 40px;
	line-height: 40px;
	text-align: center;
	font-weight: normal;
}
.rate-table td {
	padding: 10px 0;
	line-height: 20px;
	text-align: center;
	font-weight: bold;
	font-size: 13px;
	word-break: break-all;
}
.rate-table td:nth-of-type(1) {
	text-align: left;
	font-weight: normal;
	padding-left: 15px;
}
.rate-table td span.middle{
	color: #fff;
	border-radius: 5px;
	padding: 0 3px;
	display:inline-block;
	min-width:60px;
}
.rate-table td span.green{
	background-color: #43A5A0;
	border: 1px solid #43A5A0;
}
.rate-table td span.red{
	background-color: #DF6A6E;
	border: 1px solid #DF6A6E;
}
.rate-table td span.code{
	color: #9e9e9e;
}
#hlreport .no-data-wrap {
	display:none;
}
/*HLReport_sub.html end*/