.frozen-table-html,
.frozen-table-body {
    height:100%;
}
.frozen-table-wrap {
    height:100%;
    background: #fff;
    font-size: 14px;
    line-height: 1;
}
.frozen-table-wrap table {
    border:0px;
    border-spacing: 0;
    padding: 0;
}
.table-left {
    width: 80px;
    float: left;
    height:100%;
}
.table-left-top {
    width: 100%;
    height: 45px;
}
.table-left-frozen th {
    height:45px;
    line-height: 1;
}
.table-left-bottom {
    height:calc(100% - 45px);
    width: 100%;
    overflow: hidden;
}
.table-left-freeze {
    width: 100%;
    text-align: center;
    font-size: 13px;
}
.table-left-freeze tr:nth-child(odd){
    background: #f2f3f3;
}
.table-left th,
.table-left td {
    padding-left: 16px;
    text-align: left;
}

.table-right {
    height:100%;
    width: calc(100% - 80px);
    float: left;
    position: relative;
}
.table-right-top {
    width: 100%;
    height:45px;
    overflow: hidden;
}
.table-right-title {
    width: 800px;
    text-align: center;
    height:45px;
}
.table-right-bottom {
    height:calc(100% - 45px);
    width: 100%;
    overflow: auto;
    top:45px;
    position: absolute;
    font-size: 13px;
}
.table-right-main {
    width: 800px;
    text-align: center;
    table-layout: fixed;
}
.table-right-main td:nth-of-type(1) {
    width: 22%;
}
.table-right-main td:nth-of-type(2) {
    width: 39%;
}
.table-right-main td:nth-of-type(3) {
    width: 13%;
}
.table-right-main td:nth-of-type(4) {
    width: 13%;
}
.table-right-main td:nth-of-type(5) {
    width: 13%;
}
.table-right-main tr:nth-child(odd){
    background: #f2f3f3;
}

.frozen-table-wrap th,
.frozen-table-wrap td{
    color: #595757;
    font-weight: normal;
    text-align: left;
}
.table-right-main td {
    padding: 10px 30px 10px 0px;
    line-height: 1.5;
    white-space:nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.table-left-freeze td {
    padding: 10px 30px 10px 17px;
    line-height: 1.5;
}
.refresh-button {
    position: fixed;
    bottom: 10%;
    right:10%;
    width: 80px;
    height:40px;
    line-height: 40px;
    text-align: center;
    opacity: 0.5;
    background: #ccc;
    z-index: 999;
    border-radius: 20px;
}
.no-data-table {
    display: none;
    -webkit-box-align: center;
    -webkit-box-pack: center;
    background: #fff;
    height:100%;
}
.no-data-table:after {
    content: '暂无数据';
    font-size: 30px;
    color: #ccc;
}

/*bankStatistics.jsp start*/
.bank-statistics.mui-content {
    background: #fff;
}
.bank-statistics .mui-scroll {
    background: #fff;
    min-height: 100%;
}
.bank-statistics-table {
    width: 100%;
    table-layout: fixed;
    display: table;
    color: #595757;
}
.bank-statistics-table thead th {
    font-size: 14px;
    line-height: 20px;
    padding: 12px 0;
    font-weight: normal;
}
.bank-statistics-table tbody td {
    line-height: 20px;
    padding: 10px 0;
    font-weight: normal;
    font-size: 13px;
    text-align: center;
    word-break: break-all;
    overflow: hidden;
    white-space: nowrap;
    text-overflow:ellipsis;
}
.bank-statistics-table tbody tr:nth-of-type(odd) {
    background: #f2f3f3;
}
.bank-statistics-table thead th:nth-of-type(1),
.bank-statistics-table tbody td:nth-of-type(1) {
    padding-left: 15px;
}
.bank-statistics-table thead th:nth-of-type(1) {
    width: 32%;
    overflow: hidden;
    text-align: left;
}
.bank-statistics-table thead th:nth-of-type(2) {
    width: 24%;
    overflow: hidden;
}
.bank-statistics-table thead th:nth-of-type(3) {
    width: 24%;
    overflow: hidden;
}
.bank-statistics-table thead th:nth-of-type(4) {
    width: 20%;
    overflow: hidden;
}
.bank-statistics-table tbody td:nth-of-type(1) {
    text-align: left;
}
.no-data-bank-statistics {
    display: none;
}
.no-data-bank-statistics:after {
    position: absolute;
    top:50%;
    left:50%;
    -webkit-transform: translate(-50%,-50%);
    content: '暂无数据';
    font-size: 30px;
    color: #ccc;
}
/*bankStatistics.jsp end*/

/*teamStatistics.jsp start*/
.team-statistics.mui-content {
    background: #fff;
}
.team-statistics .mui-scroll {
    background: #fff;
    min-height: 100%;
}
.team-statistics-table {
    width: 100%;
    table-layout: fixed;
    display: table;
    color: #595757;
}
.team-statistics-table thead th {
    font-size: 14px;
    line-height: 20px;
    padding: 12px 0;
    font-weight: normal;
}
.team-statistics-table tbody td {
    line-height: 20px;
    padding: 10px 0;
    overflow: hidden;
    white-space: nowrap;
    text-overflow:ellipsis;
    font-weight: normal;
    font-size: 13px;
    text-align: center;
    word-break: break-all;
}
.team-statistics-table tbody tr:nth-of-type(odd) {
    background: #f2f3f3;
}
.team-statistics-table thead th:nth-of-type(1),
.team-statistics-table tbody td:nth-of-type(1) {
    padding-left: 15px;
}
.team-statistics-table thead th:nth-of-type(1) {
    width: 32%;
    overflow: hidden;
    text-align: left;
}
.team-statistics-table thead th:nth-of-type(2) {
    width: 24%;
    overflow: hidden;
}
.team-statistics-table thead th:nth-of-type(3) {
    width: 24%;
    overflow: hidden;
}
.team-statistics-table thead th:nth-of-type(4) {
    width: 20%;
    overflow: hidden;
}
.team-statistics-table tbody td:nth-of-type(1) {
    text-align: left;
}
.no-data-team-statistics {
    display: none;
}
.no-data-team-statistics:after {
    position: absolute;
    top:50%;
    left:50%;
    -webkit-transform: translate(-50%,-50%);
    content: '暂无数据';
    font-size: 30px;
    color: #ccc;
}
/*teamStatistics.jsp end*/

.bank-statistics-table tbody td.strong-td {
    font-weight: bold;
}