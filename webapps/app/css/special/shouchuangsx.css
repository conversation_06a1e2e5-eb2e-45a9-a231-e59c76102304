.frozen-table-html,
.frozen-table-body {
    height:100%;
}
.frozen-table-wrap {
    height:100%;
    background: #fff;
    font-size: 14px;
    line-height: 1;
}
.frozen-table-wrap table {
    border:0px;
    border-spacing: 0;
    padding: 0;
}
.table-left {
    width: 140px;
    float: left;
    height:100%;
}
.table-left-top {
    width: 100%;
    height: 45px;
}
.table-left .table-left-frozen th {
    height:45px;
    line-height: 1;
    width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.table-left-frozen {
    table-layout: fixed;
    width: 140px;
}
.table-left-bottom {
    height:calc(100% - 45px);
    width: 100%;
    overflow: hidden;
}
.table-left-freeze {
    width: 10px;
    text-align: center;
    font-size: 13px;
}
.table-left-freeze tr:nth-child(odd){
    background: #f2f3f3;
}
.table-left th,
.table-left td {
    padding-left: 16px;
    text-align: left;
}

.table-right {
    height:100%;
    width: calc(100% - 140px);
    float: left;
    position: relative;
}
.table-right-top {
    width: 100%;
    height:45px;
    overflow: hidden;
}
.table-right-title {
    width: 600px;
    text-align: center;
    height:45px;
}
.table-right-bottom {
    height:calc(100% - 45px);
    width: 100%;
    overflow: auto;
    top:45px;
    position: absolute;
    font-size: 13px;
}
.table-right-main {
    width: 600px;
    text-align: center;
    table-layout: fixed;
}
.table-right-main td:nth-of-type(1) {
    width: 25%;
}
.table-right-main td:nth-of-type(2) {
    width: 25%;
}
.table-right-main td:nth-of-type(3) {
    width: 25%;
}
.table-right-main td:nth-of-type(4) {
    width: 25%;
}
.table-right-main tr:nth-child(odd){
    background: #f2f3f3;
}

.frozen-table-wrap th,
.frozen-table-wrap td{
    color: #595757;
    font-weight: normal;
    text-align: left;
}
.table-right-main td {
    padding: 10px 30px 10px 10px;
    line-height: 1.5;
    white-space:nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.table-left-freeze td {
    padding: 10px 30px 10px 17px;
    line-height: 1.5;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.refresh-button {
    position: fixed;
    bottom: 10%;
    right:10%;
    width: 80px;
    height:40px;
    line-height: 40px;
    text-align: center;
    opacity: 0.5;
    background: #ccc;
    z-index: 999;
    border-radius: 20px;
}
.no-data-table {
    display: none;
    -webkit-box-align: center;
    -webkit-box-pack: center;
    background: #fff;
    height:100%;
}
.no-data-table:after {
    content: '暂无数据';
    font-size: 30px;
    color: #ccc;
}