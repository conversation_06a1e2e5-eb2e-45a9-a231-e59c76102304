.ui-panel {
  overflow: hidden;
  margin-bottom: 10px; }
  .ui-panel .ui-grid-halve, .ui-panel .ui-grid-trisect {
    padding-top: 0; }
  .ui-panel h1, .ui-panel h2, .ui-panel h3 {
    padding-left: 15px;
    padding-right: 15px;
    line-height: 44px;
    position: relative;
    overflow: hidden;
    display: -webkit-box; }
    @media (max-width: 320px) {
      .ui-panel h1, .ui-panel h2, .ui-panel h3 {
        padding-left: 10px;
        padding-right: 10px; } }
    .ui-panel h1 span, .ui-panel h2 span, .ui-panel h3 span {
      display: block; }

.ui-panel-card,
.ui-panel-simple {
  background-color: #fff; }

.ui-panel-pure h2,
.ui-panel-pure h3 {
  color: #777; }

.ui-panel-simple {
  margin-bottom: 0; }

.ui-panel-subtitle {
  font-size: 14px;
  color: #777;
  margin-left: 10px; }

.ui-panel-title-tips {
  font-size: 12px;
  color: #777;
  position: absolute;
  right: 15px; }
  @media (max-width: 320px) {
    .ui-panel-title-tips {
      right: 10px; } }

.ui-arrowlink .ui-panel-title-tips {
  right: 30px; }
  @media (max-width: 320px) {
    .ui-arrowlink .ui-panel-title-tips {
      right: 25px; } }

/*# sourceMappingURL=panel.css.map */
