html.hc-nav-yscroll {
	overflow-y: scroll
}

body.hc-nav-open {
	overflow: visible;
	position: fixed;
	width: 100%;
	min-height: 100%
}

.hc-offcanvas-nav {
	visibility: hidden;
	display: none;
	position: fixed;
	top: 0;
	height: 100%;
	z-index: 9999
}

.hc-offcanvas-nav.is-ios * {
	cursor: pointer !important
}

.hc-offcanvas-nav .nav-container {
	position: fixed;
	z-index: 9998;
	top: 0;
	width: 280px;
	height: 100%;
	max-width: 100%;
	max-height: 100%;
	box-sizing: border-box;
	transition: -webkit-transform .4s ease;
	transition: transform .4s ease;
	transition: transform .4s ease, -webkit-transform .4s ease
}

.hc-offcanvas-nav .nav-wrapper {
	width: 100%;
	height: 100%;
	max-height: 100vh;
	-ms-scroll-chaining: none;
	overscroll-behavior: none;
	box-sizing: border-box
}

.hc-offcanvas-nav .nav-content {
	height: 100%;
	max-height: 100vh
}

.hc-offcanvas-nav .nav-wrapper-0>.nav-content {
	overflow: scroll;
	overflow-x: visible;
	overflow-y: auto;
	box-sizing: border-box
}

.hc-offcanvas-nav ul {
	list-style: none;
	margin: 0;
	padding: 0
}

.hc-offcanvas-nav li {
	position: relative;
	display: block
}

.hc-offcanvas-nav li.level-open>.nav-wrapper {
	visibility: visible
}

.hc-offcanvas-nav input[type="checkbox"] {
	display: none
}

.hc-offcanvas-nav label {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 10;
	cursor: pointer
}

.hc-offcanvas-nav a {
	position: relative;
	display: block;
	box-sizing: border-box;
	cursor: pointer
}

.hc-offcanvas-nav a,
.hc-offcanvas-nav a:hover {
	text-decoration: none
}

.hc-offcanvas-nav .nav-item {
	position: relative;
	display: block;
	box-sizing: border-box
}

.hc-offcanvas-nav.disable-body::after,
.hc-offcanvas-nav .nav-wrapper::after {
	content: '';
	position: fixed;
	z-index: 9990;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100%;
	height: 100%;
	-ms-scroll-chaining: none;
	overscroll-behavior: none;
	visibility: hidden;
	opacity: 0;
	transition: visibility 0s ease .4s, opacity .4s ease
}

.hc-offcanvas-nav.disable-body.nav-open::after,
.hc-offcanvas-nav .sub-level-open::after {
	visibility: visible;
	opacity: 1;
	transition-delay: .05s
}

.hc-offcanvas-nav:not(.nav-open)::after {
	pointer-events: none
}

.hc-offcanvas-nav.nav-levels-expand .nav-content {
	overflow: scroll;
	overflow-x: visible;
	overflow-y: auto;
	box-sizing: border-box
}

.hc-offcanvas-nav.nav-levels-expand .nav-wrapper::after {
	display: none
}

.hc-offcanvas-nav.nav-levels-expand ul .nav-wrapper {
	min-width: 0;
	max-height: 0;
	overflow: hidden;
	transition: height 0s ease .4s
}

.hc-offcanvas-nav.nav-levels-expand .level-open>.nav-wrapper {
	max-height: none
}

.hc-offcanvas-nav.nav-levels-overlap .nav-content {
	overflow: scroll;
	overflow-x: visible;
	overflow-y: auto;
	box-sizing: border-box
}

.hc-offcanvas-nav.nav-levels-overlap ul .nav-wrapper {
	position: absolute;
	z-index: 9999;
	top: 0;
	height: 100%;
	visibility: hidden;
	transition: visibility 0s ease .4s, -webkit-transform .4s ease;
	transition: visibility 0s ease .4s, transform .4s ease;
	transition: visibility 0s ease .4s, transform .4s ease, -webkit-transform .4s ease
}

.hc-offcanvas-nav.nav-levels-overlap ul li.nav-parent {
	position: static
}

.hc-offcanvas-nav.nav-levels-overlap ul li.level-open>.nav-wrapper {
	visibility: visible;
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
	transition: -webkit-transform .4s ease;
	transition: transform .4s ease;
	transition: transform .4s ease, -webkit-transform .4s ease
}

.hc-offcanvas-nav.nav-position-left {
	left: 0
}

.hc-offcanvas-nav.nav-position-left .nav-container {
	left: 0;
	-webkit-transform: translate3d(-280px, 0, 0);
	transform: translate3d(-280px, 0, 0)
}

.hc-offcanvas-nav.nav-position-left.nav-levels-overlap li .nav-wrapper {
	left: 0;
	-webkit-transform: translate3d(-100%, 0, 0);
	transform: translate3d(-100%, 0, 0)
}

.hc-offcanvas-nav.nav-position-right {
	right: 0
}

.hc-offcanvas-nav.nav-position-right .nav-container {
	right: 0;
	-webkit-transform: translate3d(280px, 0, 0);
	transform: translate3d(280px, 0, 0)
}

.hc-offcanvas-nav.nav-position-right.nav-levels-overlap li .nav-wrapper {
	right: 0;
	-webkit-transform: translate3d(100%, 0, 0);
	transform: translate3d(100%, 0, 0)
}

.hc-offcanvas-nav.nav-position-top {
	top: 0
}

.hc-offcanvas-nav.nav-position-top .nav-container {
	top: 0;
	width: 100%;
	height: auto;
	-webkit-transform: translate3d(0, -100%, 0);
	transform: translate3d(0, -100%, 0)
}

.hc-offcanvas-nav.nav-position-top.nav-levels-overlap li .nav-wrapper {
	left: 0;
	-webkit-transform: translate3d(0, -100%, 0);
	transform: translate3d(0, -100%, 0)
}

.hc-offcanvas-nav.nav-position-bottom {
	top: auto;
	bottom: 0
}

.hc-offcanvas-nav.nav-position-bottom .nav-container {
	top: auto;
	bottom: 0;
	width: 100%;
	height: auto;
	-webkit-transform: translate3d(0, 100%, 0);
	transform: translate3d(0, 100%, 0)
}

.hc-offcanvas-nav.nav-position-bottom.nav-levels-overlap li .nav-wrapper {
	left: 0;
	-webkit-transform: translate3d(0, 100%, 0);
	transform: translate3d(0, 100%, 0)
}

.hc-offcanvas-nav.nav-open[class*='hc-nav-'] div.nav-container {
	-webkit-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0)
}

.hc-nav-trigger {
	position: absolute;
	cursor: pointer;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	display: none;
	top: 20px;
	z-index: 9980;
	width: 30px;
	min-height: 24px
}

.hc-nav-trigger span {
	width: 30px;
	top: 50%;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
	-webkit-transform-origin: 50% 50%;
	transform-origin: 50% 50%
}

.hc-nav-trigger span,
.hc-nav-trigger span::before,
.hc-nav-trigger span::after {
	display: block;
	position: absolute;
	left: 0;
	height: 4px;
	background: #34495E;
	transition: all .2s ease
}

.hc-nav-trigger span::before,
.hc-nav-trigger span::after {
	content: '';
	width: 100%
}

.hc-nav-trigger span::before {
	top: -10px
}

.hc-nav-trigger span::after {
	bottom: -10px
}

.hc-nav-trigger.toggle-open span {
	background: rgba(0, 0, 0, 0);
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg)
}

.hc-nav-trigger.toggle-open span::before {
	-webkit-transform: translate3d(0, 10px, 0);
	transform: translate3d(0, 10px, 0)
}

.hc-nav-trigger.toggle-open span::after {
	-webkit-transform: rotate(-90deg) translate3d(10px, 0, 0);
	transform: rotate(-90deg) translate3d(10px, 0, 0)
}

.hc-offcanvas-nav::after,
.hc-offcanvas-nav .nav-wrapper::after {
	background: rgba(0, 0, 0, 0.3)
}

.hc-offcanvas-nav .nav-container,
.hc-offcanvas-nav .nav-wrapper,
.hc-offcanvas-nav ul {
	background: #336ca6
}

.hc-offcanvas-nav h2 {
	font-size: 19px;
	font-weight: normal;
	text-align: left;
	padding: 20px 17px;
	color: #FFFFFF
}

.hc-offcanvas-nav a,
.hc-offcanvas-nav .nav-item {
	padding: 14px 17px;
	font-size: 15px;
	color: #fff;
	z-index: 1;
	background: rgba(0, 0, 0, 0);
	border-bottom: 1px solid #2c5d8f
}

.hc-offcanvas-nav:not(.touch-device) a:hover {
	background: #31679e
}

.hc-offcanvas-nav ul:first-of-type:not(:first-child)>li:first-child:not(.nav-back):not(.nav-close)>a {
	border-top: 1px solid #2c5d8f;
	margin-top: -1px
}

.hc-offcanvas-nav li {
	text-align: left
}

.hc-offcanvas-nav li.nav-close a,
.hc-offcanvas-nav li.nav-back a {
	background: #2c5d8f;
	border-top: 1px solid #295887;
	border-bottom: 1px solid #295887
}

.hc-offcanvas-nav li.nav-close a:hover,
.hc-offcanvas-nav li.nav-back a:hover {
	background: #2b5c8d
}

.hc-offcanvas-nav li.nav-close:not(:first-child) a,
.hc-offcanvas-nav li.nav-back:not(:first-child) a {
	margin-top: -1px
}

.hc-offcanvas-nav li.nav-parent .nav-item {
	padding-right: 58px
}

.hc-offcanvas-nav li.nav-close span,
.hc-offcanvas-nav li.nav-parent span.nav-next,
.hc-offcanvas-nav li.nav-back span {
	width: 45px;
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	text-align: center;
	cursor: pointer;
	transition: background .2s ease
}

.hc-offcanvas-nav li.nav-close span::before,
.hc-offcanvas-nav li.nav-close span::after {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	width: 6px;
	height: 6px;
	margin-top: -3px;
	border-top: 2px solid #fff;
	border-left: 2px solid #fff
}

.hc-offcanvas-nav li.nav-close span::before {
	margin-left: -9px;
	-webkit-transform: rotate(135deg);
	transform: rotate(135deg)
}

.hc-offcanvas-nav li.nav-close span::after {
	-webkit-transform: rotate(-45deg);
	transform: rotate(-45deg)
}

.hc-offcanvas-nav a[href]:not([href="#"])>span.nav-next {
	border-left: 1px solid #2c5d8f
}

.hc-offcanvas-nav span.nav-next::before,
.hc-offcanvas-nav li.nav-back span::before {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	width: 8px;
	height: 8px;
	margin-left: -2px;
	box-sizing: border-box;
	border-top: 2px solid #fff;
	border-left: 2px solid #fff;
	-webkit-transform-origin: center;
	transform-origin: center
}

.hc-offcanvas-nav span.nav-next::before {
	-webkit-transform: translate(-50%, -50%) rotate(135deg);
	transform: translate(-50%, -50%) rotate(135deg)
}

.hc-offcanvas-nav li.nav-back span::before {
	-webkit-transform: translate(-50%, -50%) rotate(-45deg);
	transform: translate(-50%, -50%) rotate(-45deg)
}

.hc-offcanvas-nav.nav-position-left.nav-open .nav-wrapper {
	box-shadow: 1px 0 2px rgba(0, 0, 0, 0.2)
}

.hc-offcanvas-nav.nav-position-right.nav-open .nav-wrapper {
	box-shadow: -1px 0 2px rgba(0, 0, 0, 0.2)
}

.hc-offcanvas-nav.nav-position-right span.nav-next::before {
	margin-left: 0;
	margin-right: -2px;
	-webkit-transform: translate(-50%, -50%) rotate(-45deg);
	transform: translate(-50%, -50%) rotate(-45deg)
}

.hc-offcanvas-nav.nav-position-right li.nav-back span::before {
	margin-left: 0;
	margin-right: -2px;
	-webkit-transform: translate(-50%, -50%) rotate(135deg);
	transform: translate(-50%, -50%) rotate(135deg)
}

.hc-offcanvas-nav.nav-position-top.nav-open .nav-wrapper {
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2)
}

.hc-offcanvas-nav.nav-position-top span.nav-next::before {
	margin-left: 0;
	margin-right: -2px;
	-webkit-transform: translate(-50%, -50%) rotate(-135deg);
	transform: translate(-50%, -50%) rotate(-135deg)
}

.hc-offcanvas-nav.nav-position-top li.nav-back span::before {
	margin-left: 0;
	margin-right: -2px;
	-webkit-transform: translate(-50%, -50%) rotate(45deg);
	transform: translate(-50%, -50%) rotate(45deg)
}

.hc-offcanvas-nav.nav-position-bottom.nav-open .nav-wrapper {
	box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2)
}

.hc-offcanvas-nav.nav-position-bottom span.nav-next::before {
	margin-left: 0;
	margin-right: -2px;
	-webkit-transform: translate(-50%, -50%) rotate(45deg);
	transform: translate(-50%, -50%) rotate(45deg)
}

.hc-offcanvas-nav.nav-position-bottom li.nav-back span::before {
	margin-left: 0;
	margin-right: -2px;
	-webkit-transform: translate(-50%, -50%) rotate(-135deg);
	transform: translate(-50%, -50%) rotate(-135deg)
}

.hc-offcanvas-nav.nav-levels-expand .nav-container ul .nav-wrapper,
.hc-offcanvas-nav.nav-levels-none .nav-container ul .nav-wrapper {
	box-shadow: none;
	background: transparent
}

.hc-offcanvas-nav.nav-levels-expand .nav-container ul h2,
.hc-offcanvas-nav.nav-levels-none .nav-container ul h2 {
	display: none
}

.hc-offcanvas-nav.nav-levels-expand .nav-container ul ul .nav-item,
.hc-offcanvas-nav.nav-levels-none .nav-container ul ul .nav-item {
	font-size: 14px
}

.hc-offcanvas-nav.nav-levels-expand .nav-container li,
.hc-offcanvas-nav.nav-levels-none .nav-container li {
	transition: background .3s ease
}

.hc-offcanvas-nav.nav-levels-expand .nav-container li.level-open,
.hc-offcanvas-nav.nav-levels-none .nav-container li.level-open {
	background: #2e6296
}

.hc-offcanvas-nav.nav-levels-expand .nav-container li.level-open a,
.hc-offcanvas-nav.nav-levels-none .nav-container li.level-open a {
	border-bottom: 1px solid #295887
}

.hc-offcanvas-nav.nav-levels-expand .nav-container li.level-open a:hover,
.hc-offcanvas-nav.nav-levels-none .nav-container li.level-open a:hover {
	background: #2f649a
}

.hc-offcanvas-nav.nav-levels-expand .nav-container li.level-open>.nav-item .nav-next::before,
.hc-offcanvas-nav.nav-levels-none .nav-container li.level-open>.nav-item .nav-next::before {
	margin-top: 2px;
	-webkit-transform: translate(-50%, -50%) rotate(45deg);
	transform: translate(-50%, -50%) rotate(45deg)
}

.hc-offcanvas-nav.nav-levels-expand .nav-container span.nav-next::before,
.hc-offcanvas-nav.nav-levels-none .nav-container span.nav-next::before {
	margin-top: -2px;
	-webkit-transform: translate(-50%, -50%) rotate(-135deg);
	transform: translate(-50%, -50%) rotate(-135deg)
}

html,
body,
div,
span,
header,
ul,
li,
a {
	margin: 0;
	padding: 0;
	border: 0;
	font-size: 100%;
	font: inherit;
	vertical-align: baseline
}

html {
	height: 100%
}

body {
	font-family: 'Raleway', sans-serif;
	text-align: center;
	color: #fffce1
}

h1,
h2,
h3,
h4,
h5,
h6 {
	margin: 0
}

em {
	font-style: italic
}

strong {
	font-weight: 600
}

ol,
ul {
	list-style: none
}

.cf::before,
.cf::after {
	content: '';
	display: block;
	height: 0;
	overflow: hidden
}

.cf::after {
	clear: both
}

#container {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	height: 100%;
	/*background: linear-gradient(-134deg, #466e8d 10%, #46b8da 100%)*/
	background-color: #466e8d;
}

.wrapper {
	max-width: 800px;
	margin: 0 auto;
	padding: 0 20px
}

#main-nav {
	display: none
}

header {
	position: relative;
	padding: 50px 0 20px
}

header h1 {
	font-size: 50px;
	font-weight: 700;
	text-align: center;
	letter-spacing: 5px;
	padding-bottom: 8px
}

header h2 {
	max-width: 680px;
	margin: auto;
	font-size: 20px;
	font-weight: 200;
	line-height: 1.4;
	text-align: center;
	letter-spacing: 1px;
	padding-bottom: 30px
}

header .git {
	display: inline-block;
	text-decoration: none;
	color: #fff;
	border-radius: 4px;
	padding: 4px 10px 4px 0;
	font-size: 15px;
	font-weight: 400;
	color: #fffce1;
	background: #54b9cb;
	transition: background .15s ease-in-out
}

header .git:hover {
	background: #4CA8B9
}

header .git:hover svg {
	border-color: #54b9cb
}

header .git svg {
	width: 15px;
	height: 15px;
	fill: #fffce1;
	position: relative;
	top: 2px;
	padding: 0 10px;
	margin-right: 10px;
	border-right: 1px solid #4daabb;
	transition: border-color .15s ease-in-out
}

header .ver {
	padding-top: 15px;
	font-weight: 200;
	color: #dab977
}

header .ver span {
	color: #fffce1
}

header .toggle {
	position: absolute;
	cursor: pointer;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	display: none;
	top: 20px;
	z-index: 9980;
	width: 35px;
	min-height: 24px;
	position: relative;
	width: auto;
	top: auto;
	left: auto;
	float: left;
	display: block;
	cursor: pointer;
	box-sizing: content-box;
	font-size: 20px;
	padding-left: 55px;
	line-height: 24px;
	margin-top: 55px
}

header .toggle span {
	width: 35px;
	top: 50%;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
	-webkit-transform-origin: 50% 50%;
	transform-origin: 50% 50%
}

header .toggle span,
header .toggle span::before,
header .toggle span::after {
	display: block;
	position: absolute;
	left: 0;
	height: 4px;
	background: #182631;
	transition: all .25s ease
}

header .toggle span::before,
header .toggle span::after {
	content: '';
	width: 100%
}

header .toggle span::before {
	top: -10px
}

header .toggle span::after {
	bottom: -10px
}

header .toggle.toggle-open span {
	background: rgba(0, 0, 0, 0);
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg)
}

header .toggle.toggle-open span::before {
	-webkit-transform: translate3d(0, 10px, 0);
	transform: translate3d(0, 10px, 0)
}

header .toggle.toggle-open span::after {
	-webkit-transform: rotate(-90deg) translate3d(10px, 0, 0);
	transform: rotate(-90deg) translate3d(10px, 0, 0)
}

header .toggle:hover span,
header .toggle:hover span::before,
header .toggle:hover span::after {
	background: #dab977
}

header .toggle div {
	display: inline-block;
	margin-right: 15px
}

footer {
	padding-bottom: 40px
}

footer .swm {
	display: inline-block;
	padding: 0 15px
}

footer .swm svg {
	display: block;
	width: auto;
	height: 17px;
	margin-top: 22px
}

footer .swm svg path {
	transition: fill .1s ease
}

footer .swm svg .l-1 {
	fill: #4fb5e1
}

footer .swm svg .l-2 {
	fill: #f2c053
}

footer .swm svg .l-3 {
	fill: #a7ce38
}

footer .swm:not(:hover) svg .l-1 {
	fill: #466e8d
}

footer .swm:not(:hover) svg .l-2 {
	fill: #9db9cf
}

footer .swm:not(:hover) svg .l-3 {
	fill: #5989ad
}

main {
	flex: 1 0 auto;
	padding-bottom: 30px;
	text-align: left
}

main .content {
	border-top: 1px solid rgba(255, 255, 255, 0.1)
}

main h4 {
	font-size: 15px;
	letter-spacing: 1px;
	font-weight: 600;
	text-transform: uppercase;
	margin: 20px 0
}

main h4:first-child {
	margin-top: 30px
}

main .actions {
	margin: 0 -15px;
	text-align: center
}

main .actions.checkboxes {
	text-align: left;
	padding-top: 8px
}

main .actions.checkboxes label {
	font-size: 14px;
	text-transform: uppercase;
	cursor: pointer
}

main .actions.checkboxes label input {
	display: none
}

main .actions.checkboxes label input:checked~span {
	background: #dab977
}

main .actions.checkboxes label input:checked~span::before {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	margin-top: -1px;
	border: solid #243949;
	border-width: 0 3px 3px 0;
	display: inline-block;
	padding: 5px 2px;
	-webkit-transform: translate(-50%, -50%) rotate(45deg);
	transform: translate(-50%, -50%) rotate(45deg)
}

main .actions.checkboxes label span {
	display: inline-block;
	position: relative;
	top: -1px;
	width: 22px;
	height: 22px;
	background: #fffce1;
	margin-right: 12px;
	vertical-align: top;
	transition: all .1s ease
}

main .actions div {
	padding: 0 15px 20px;
	box-sizing: border-box
}

@media screen and (min-width: 800px) {
	main .actions {
		display: flex;
		flex-wrap: wrap
	}

	main .actions div {
		float: left;
		flex: 1 1 33.33%;
		max-width: 33.33%
	}

	main .actions.position div {
		float: left;
		flex: 1 1 25%;
		max-width: 25%
	}
}

main .button {
	position: relative;
	display: block;
	padding: 18px 30px 16px;
	text-transform: uppercase;
	text-align: center;
	font-size: 16px;
	font-weight: 700;
	line-height: 1.4;
	letter-spacing: 1px;
	text-decoration: none;
	color: #243949;
	cursor: pointer;
	background: #fffce1;
	border-radius: 30px/80px;
	transition: all .1s ease
}

main .button:not(.active):hover {
	color: #d5af63
}

main .button.active {
	background: #dab977
}

.hc-offcanvas-nav .nav-wrapper-0>.nav-content {
	padding-bottom: 41px
}

.hc-offcanvas-nav h2 {
	font-weight: 400
}

.hc-offcanvas-nav a {
	font-size: 16px
}

.hc-offcanvas-nav li.search .nav-item {
	padding-top: 0
}

.hc-offcanvas-nav li.search input[type="text"] {
	width: 100%;
	box-sizing: border-box;
	border: none;
	border-radius: 3px;
	font-size: 14px;
	color: #fff;
	background: rgba(255, 255, 255, 0.12);
	padding: 5px 10px;
	box-shadow: none;
	outline: none
}

.hc-offcanvas-nav li.search input[type="text"]::-webkit-input-placeholder {
	color: rgba(255, 255, 255, 0.8)
}

.hc-offcanvas-nav li.search input[type="text"]:-ms-input-placeholder {
	color: rgba(255, 255, 255, 0.8)
}

.hc-offcanvas-nav li.search input[type="text"]::-ms-input-placeholder {
	color: rgba(255, 255, 255, 0.8)
}

.hc-offcanvas-nav li.search input[type="text"]::placeholder {
	color: rgba(255, 255, 255, 0.8)
}

.hc-offcanvas-nav li.add>a::before {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	display: inline-block;
	line-height: 1;
	text-transform: none;
	text-indent: 0;
	letter-spacing: normal;
	word-wrap: normal;
	white-space: nowrap;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	text-rendering: optimizeLegibility;
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-feature-settings: 'liga';
	font-feature-settings: 'liga';
	display: inline-block;
	width: 19px;
	height: 19px;
	margin-right: 15px;
	font-size: 19px;
	vertical-align: top;
	content: 'add'
}

.hc-offcanvas-nav li.new>.nav-item::before {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	display: inline-block;
	line-height: 1;
	text-transform: none;
	text-indent: 0;
	letter-spacing: normal;
	word-wrap: normal;
	white-space: nowrap;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	text-rendering: optimizeLegibility;
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-feature-settings: 'liga';
	font-feature-settings: 'liga';
	display: inline-block;
	width: 19px;
	height: 19px;
	margin-right: 15px;
	font-size: 19px;
	vertical-align: top;
	content: 'fiber_new'
}

.hc-offcanvas-nav li.cryptocurrency>.nav-item::before {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	display: inline-block;
	line-height: 1;
	text-transform: none;
	text-indent: 0;
	letter-spacing: normal;
	word-wrap: normal;
	white-space: nowrap;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	text-rendering: optimizeLegibility;
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-feature-settings: 'liga';
	font-feature-settings: 'liga';
	display: inline-block;
	width: 19px;
	height: 19px;
	margin-right: 15px;
	font-size: 19px;
	vertical-align: top;
	content: 'local_atm'
}

.hc-offcanvas-nav li.devices>.nav-item::before {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	display: inline-block;
	line-height: 1;
	text-transform: none;
	text-indent: 0;
	letter-spacing: normal;
	word-wrap: normal;
	white-space: nowrap;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	text-rendering: optimizeLegibility;
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-feature-settings: 'liga';
	font-feature-settings: 'liga';
	display: inline-block;
	width: 19px;
	height: 19px;
	margin-right: 15px;
	font-size: 19px;
	vertical-align: top;
	content: 'devices'
}

.hc-offcanvas-nav li.mobile>.nav-item::before {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	display: inline-block;
	line-height: 1;
	text-transform: none;
	text-indent: 0;
	letter-spacing: normal;
	word-wrap: normal;
	white-space: nowrap;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	text-rendering: optimizeLegibility;
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-feature-settings: 'liga';
	font-feature-settings: 'liga';
	display: inline-block;
	width: 19px;
	height: 19px;
	margin-right: 15px;
	font-size: 19px;
	vertical-align: top;
	content: 'phone_android'
}

.hc-offcanvas-nav li.television>.nav-item::before {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	display: inline-block;
	line-height: 1;
	text-transform: none;
	text-indent: 0;
	letter-spacing: normal;
	word-wrap: normal;
	white-space: nowrap;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	text-rendering: optimizeLegibility;
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-feature-settings: 'liga';
	font-feature-settings: 'liga';
	display: inline-block;
	width: 19px;
	height: 19px;
	margin-right: 15px;
	font-size: 19px;
	vertical-align: top;
	content: 'desktop_windows'
}

.hc-offcanvas-nav li.camera>.nav-item::before {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	display: inline-block;
	line-height: 1;
	text-transform: none;
	text-indent: 0;
	letter-spacing: normal;
	word-wrap: normal;
	white-space: nowrap;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	text-rendering: optimizeLegibility;
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-feature-settings: 'liga';
	font-feature-settings: 'liga';
	display: inline-block;
	width: 19px;
	height: 19px;
	margin-right: 15px;
	font-size: 19px;
	vertical-align: top;
	content: 'camera_alt'
}

.hc-offcanvas-nav li.magazines>.nav-item::before {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	display: inline-block;
	line-height: 1;
	text-transform: none;
	text-indent: 0;
	letter-spacing: normal;
	word-wrap: normal;
	white-space: nowrap;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	text-rendering: optimizeLegibility;
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-feature-settings: 'liga';
	font-feature-settings: 'liga';
	display: inline-block;
	width: 19px;
	height: 19px;
	margin-right: 15px;
	font-size: 19px;
	vertical-align: top;
	content: 'import_contacts'
}

.hc-offcanvas-nav li.store>.nav-item::before {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	display: inline-block;
	line-height: 1;
	text-transform: none;
	text-indent: 0;
	letter-spacing: normal;
	word-wrap: normal;
	white-space: nowrap;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	text-rendering: optimizeLegibility;
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-feature-settings: 'liga';
	font-feature-settings: 'liga';
	display: inline-block;
	width: 19px;
	height: 19px;
	margin-right: 15px;
	font-size: 19px;
	vertical-align: top;
	content: 'store'
}

.hc-offcanvas-nav li.collections>.nav-item::before {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	display: inline-block;
	line-height: 1;
	text-transform: none;
	text-indent: 0;
	letter-spacing: normal;
	word-wrap: normal;
	white-space: nowrap;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	text-rendering: optimizeLegibility;
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-feature-settings: 'liga';
	font-feature-settings: 'liga';
	display: inline-block;
	width: 19px;
	height: 19px;
	margin-right: 15px;
	font-size: 19px;
	vertical-align: top;
	content: 'collections'
}

.hc-offcanvas-nav li.credits>.nav-item::before {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	display: inline-block;
	line-height: 1;
	text-transform: none;
	text-indent: 0;
	letter-spacing: normal;
	word-wrap: normal;
	white-space: nowrap;
	direction: ltr;
	-webkit-font-smoothing: antialiased;
	text-rendering: optimizeLegibility;
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-feature-settings: 'liga';
	font-feature-settings: 'liga';
	display: inline-block;
	width: 19px;
	height: 19px;
	margin-right: 15px;
	font-size: 19px;
	vertical-align: top;
	content: 'credit_card'
}

.hc-offcanvas-nav ul.bottom-nav {
	position: absolute;
	z-index: 10;
	bottom: 0;
	width: 100%;
	display: flex;
	flex-wrap: nowrap;
	align-items: stretch;
	border-top: 1px solid #2c5d8f
}

.hc-offcanvas-nav ul.bottom-nav li {
	flex: auto
}

.hc-offcanvas-nav ul.bottom-nav li a {
	padding: 10px;
	text-align: center;
	height: 100%;
	border-bottom: none
}

.hc-offcanvas-nav ul.bottom-nav li svg {
	fill: #fff;
	display: inline-block;
	vertical-align: middle
}

.hc-offcanvas-nav ul.bottom-nav li.github svg {
	width: 17px;
	height: 17px
}

.hc-offcanvas-nav ul.bottom-nav li.ko-fi svg {
	width: 21px;
	height: 21px
}

.hc-offcanvas-nav ul.bottom-nav li.email svg {
	width: 19px;
	height: 19px
}
