/* reset */
html {
    color: #000;
    background: #f8f8f8;
}

body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, input, button, textarea, p, blockquote, th, td {
    margin: 0;
    padding: 0;
}

body {
    font: 14px/1 Tahoma, Helvetica, Arial, "\5b8b\4f53", sans-serif;
}

img {
    border: none;
}

em, strong {
    font-style: normal;
    font-weight: normal;
}

li {
    list-style: none;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

h1 {
    font-size: 18px;
}

h2 {
    font-size: 16px;
}

h3 {
    font-size: 14px;
}

h4, h5, h6 {
    font-size: 100%;
}

q:before, q:after {
    content: '';
}

/* 消除q前后的内容 */
button, input, select, textarea {
    font-size: 100%;
}

/* 使得表单元素在 ie 下能继承字体大小 */
input, button, textarea, select, optgroup, option {
    font-family: inherit;
    font-size: inherit;
    font-style: inherit;
    font-weight: inherit;
}

address, cite, dfn, em, var {
    font-style: normal;
}

/* 将斜体扶正 */

/* link */
a {
    color: #36c;
    text-decoration: none;
}

a:hover {
    color: #f60;
    text-decoration: underline;
}

.clear {
    clear: both;
}

.list-cell {
    margin: 10px 0;
}

.select-item {
    float: left;
    -webkit-appearance: none;
    border: 0;
    background: url(img/checkbox-unselect.png);
    vertical-align: middle;
    height: 30px;
    width: 30px;
    margin-left: 5px;
}

.select-item:checked {
    background: url(img/checkbox-selected.png);
}

.msg {
    float: right;
    width: 80%;
    background-color: #fff;
    margin-right: 3%;
    padding-left: 2%;
    border-radius: 5px;
}

.cell-msg li {
    border-bottom: dashed 1px #ccc;
    padding: 10px 20px 10px 0;
}

.cell-msg li:first-child {
    border-bottom: solid 1px #ccc;
}

.cell-msg li:last-child {
    border-bottom: none;
}

.cell-msg li:first-child span {
    font-size: 16px;
}

.msg-detail {
    float: right;
}

.label-btn-div {
    z-index: 1;
    position: fixed;
    bottom: 0;
    background-color: #cc3434;
    text-align: center;
    padding-top: 10px;
    margin: 0 0;
    width: 100%;
    color: #fff;
}

.ui-label {
    display: inline-block;
    position: relative;
    line-height: 30px;
    height: 30px;
    padding: 0 15px;
    border: 1px solid #cacccd;
    border-radius: 10px;
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
    .ui-label {
        position: relative;
        border: 0;
    }

    .ui-label:before {
        content: "";
        width: 200%;
        height: 200%;
        position: absolute;
        top: 0;
        left: 0;
        border: 1px solid #cacccd;
        -webkit-transform: scale(0.5);
        -webkit-transform-origin: 0 0;
        padding: 1px;
        -webkit-box-sizing: border-box;
        border-radius: 10px;
        pointer-events: none;
    }
}

.ui-label:active {
    background-color: #f3f2f2;
}

.ui-label-list .ui-label {
    margin: 0 10px 10px 0;
}

.mobility-vacation-v1 ul li div input, .mobility-vacation-v1 ul li div textarea, .mobility-vacation-v1 ul li div select {
    padding-left: 20px;
}

.submit-btn {
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0.5, #d80000), to(#d80000));
    color: #ffffff;
}

.tool {
    height: 50px;
    background-color: #d80000;
}

.tool-text {
    color: #fff;
    line-height: 50px;
    padding-left: 10px;
}

.btn-group {
    float: right;
    display: inline;
}

.tool .btn-group label {
    color: #d80000;
    background-color: #fff;
    /* border-radius: 5px; */
    margin: 10px 10px 10px 0;
}
