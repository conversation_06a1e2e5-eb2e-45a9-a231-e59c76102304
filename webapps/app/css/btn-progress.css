.ui-btn-progress {
  width: 55px;
  padding: 0;
  overflow: hidden; }
  .ui-btn-progress .ui-btn-inner {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    overflow: hidden;
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0.5, #1fbaf3), to(#18b4ed));
    border-bottom-left-radius: 3px;
    border-top-left-radius: 3px; }
    .ui-btn-progress .ui-btn-inner span {
      display: inline-block;
      color: white;
      position: absolute;
      width: 55px;
      left: 0; }
  .ui-btn-progress.disabled, .ui-btn-progress:disabled {
    background-color: #fefefe;
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0.5, white), to(#fafafa));
    color: #ccc;
    border: 1px solid #cacccd;
    background-clip: padding-box; }

@media screen and (-webkit-min-device-pixel-ratio: 2) {
  .ui-btn-progress.disabled,
  .ui-btn-progress:disabled {
    border: 0; }

  .ui-btn-progress.disabled:before,
  .ui-btn-progress:disabled:before {
    border: 1px solid #cacccd; } }

/*# sourceMappingURL=btn-progress.css.map */
