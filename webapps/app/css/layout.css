@charset "UTF-8";
.ui-header,
.ui-footer {
  position: fixed;
  width: 100%;
  z-index: 100;
  left: 0; }

.ui-header {
  top: 0;
  height: 45px;
  line-height: 45px; }

.ui-header-stable,
.ui-header-positive {
  padding: 0 10px;
  box-sizing: border-box; }

.ui-header-stable,
.ui-footer-stable {
  background-color: #f8f8f8; }

.ui-header-positive,
.ui-footer-positive {
  background-color: #18b4ed;
  color: #fff; }
  .ui-header-positive a, .ui-header-positive a:active, .ui-header-positive i,
  .ui-footer-positive a,
  .ui-footer-positive a:active,
  .ui-footer-positive i {
    color: #fff; }

.ui-footer-btn {
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #f9f9f9), to(#e0e0e0));
  color: #00a5e0; }
  .ui-footer-btn .ui-tiled {
    height: 100%; }

.ui-footer {
  bottom: 0;
  height: 56px; }

.ui-header ~ .ui-container {
  border-top: 45px solid transparent; }

.ui-footer ~ .ui-container {
  border-bottom: 56px solid transparent; }

.ui-header h1 {
  text-align: center;
  font-size: 18px; }

.ui-header .ui-icon-return {
  position: absolute;
  left: 0; }

.ui-header .ui-btn {
  display: block;
  position: absolute;
  right: 10px;
  top: 50%;
  margin-top: -15px; }

/**
 * 垂直上下居中
 */
.ui-center {
  width: 100%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-box-pack: center;
  -webkit-box-align: center;
  text-align: center;
  height: 150px; }

/**
 * 排版
 */
.ui-flex,
.ui-tiled {
  display: -webkit-box;
  width: 100%;
  -webkit-box-sizing: border-box; }

.ui-flex-ver {
  -webkit-box-orient: vertical; }

.ui-flex-pack-start {
  -webkit-box-pack: start; }

.ui-flex-pack-end {
  -webkit-box-pack: end; }

.ui-flex-pack-center {
  -webkit-box-pack: center; }

.ui-flex-align-start {
  -webkit-box-align: start; }

.ui-flex-align-end {
  -webkit-box-align: end; }

.ui-flex-align-center {
  -webkit-box-align: center; }

/**
 * 平铺
 */
.ui-tiled li {
  -webkit-box-flex: 1;
  width: 100%;
  text-align: center;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-box-pack: center;
  -webkit-box-align: center; }

/*# sourceMappingURL=layout.css.map */
