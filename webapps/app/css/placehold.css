.ui-placehold-wrap {
  padding-top: 31.25%;
  position: relative; }

.ui-placehold {
  color: #bbb;
  position: absolute;
  top: 0;
  width: 100%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-box-pack: center;
  -webkit-box-align: center;
  -webkit-box-sizing: border-box;
  text-align: center;
  height: 100%;
  z-index: -1; }

.ui-placehold-img {
  padding-top: 31.25%;
  position: relative; }
  .ui-placehold-img > span {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    background-repeat: no-repeat;
    -webkit-background-size: cover; }
  .ui-placehold-img img {
    width: 100%;
    height: 100%; }

/*# sourceMappingURL=placehold.css.map */
