
/*body,ul,li {
	padding:0;
	margin:0;
	border:0;
}

body {
	font-size:12px;
	-webkit-user-select:none;
    -webkit-text-size-adjust:none;
	font-family:helvetica;
}

#header {
	position:absolute;
	top:0; left:0;
	width:100%;
	height:45px;
	line-height:45px;
	background-image:-webkit-gradient(linear, 0 0, 0 100%, color-stop(0, #fe96c9), color-stop(0.05, #d51875), color-stop(1, #7b0a2e));
	background-image:-moz-linear-gradient(top, #fe96c9, #d51875 5%, #7b0a2e);
	background-image:-o-linear-gradient(top, #fe96c9, #d51875 5%, #7b0a2e);
	padding:0;
	color:#eee;
	font-size:20px;
	text-align:center;
}

#header a {
	color:#f3f3f3;
	text-decoration:none;
	font-weight:bold;
	text-shadow:0 -1px 0 rgba(0,0,0,0.5);
}

#footer {
	position:absolute;
	bottom:0; left:0;
	width:100%;
	height:48px;
	background-image:-webkit-gradient(linear, 0 0, 0 100%, color-stop(0, #999), color-stop(0.02, #666), color-stop(1, #222));
	background-image:-moz-linear-gradient(top, #999, #666 2%, #222);
	background-image:-o-linear-gradient(top, #999, #666 2%, #222);
	padding:0;
	border-top:1px solid #444;
}*/

#wrapper {
	position:absolute; z-index:1;
	left:0;
	width:100%;
	overflow:auto;
}
.with-header{
	top:45px;
}
.with-2-header{
	top:100px;
}
.without-header-bottom{
	top:0px;
	bottom:0px;
}
.without-header{
	top:0px;
}
.with-bottom{
	bottom:48px;
}
.withoutbottom{
	bottom:0px;
}

.ui-list-info .date{
    text-align: right;
}
.nodata{
	color:#cccccc;
	font-size:32px;
	text-align: center;
	margin-top: 200px;
}

#scroller {
	position:relative;
/*	-webkit-touch-callout:none;*/
	-webkit-tap-highlight-color:rgba(0,0,0,0);

	float:left;
	width:100%;
	padding:0;
}

/**
 *
 * 下拉样式 Pull down styles
 *
 */
#pullDown, #pullUp {
	background:#fff;
	height:40px;
	line-height:40px;
	padding:5px 10px;
	font-weight:bold;
	font-size:14px;
	color:#888;
	text-align: center;
}
#pullDown .pullDownIcon, #pullUp .pullUpIcon  {
	display:block; 
	float:left;
	width:40px; 
	height:40px;
	background:url(../img/<EMAIL>) 0 0 no-repeat;
	-webkit-background-size:40px 80px; background-size:40px 80px;
	-webkit-transition-property:-webkit-transform;
	-webkit-transition-duration:250ms;	
}
#pullDown .pullDownIcon {
	-webkit-transform:rotate(0deg) translateZ(0);
}
#pullUp .pullUpIcon  {
	-webkit-transform:rotate(-180deg) translateZ(0);
}

#pullDown.flip .pullDownIcon {
	-webkit-transform:rotate(-180deg) translateZ(0);
}

#pullUp.flip .pullUpIcon {
	-webkit-transform:rotate(0deg) translateZ(0);
}

#pullDown.loading .pullDownIcon, #pullUp.loading .pullUpIcon {
	background-position:0 100%;
	-webkit-transform:rotate(0deg) translateZ(0);
	-webkit-transition-duration:0ms;
	-webkit-animation-name:loading;
	-webkit-animation-duration:2s;
	-webkit-animation-iteration-count:infinite;
	-webkit-animation-timing-function:linear;
}

@-webkit-keyframes loading {
	from { -webkit-transform:rotate(0deg) translateZ(0); }
	to { -webkit-transform:rotate(360deg) translateZ(0); }
}

/*------------- S 以下css主要是仿IOS滚动条样式(可选样式)  -------------*/		
/**
 * Horizontal Scrollbar
 */
.myScrollbarH {
	position:absolute;
	z-index:100;
	height:7px;
	bottom:1px;
	left:2px;
	right:7px
}

.myScrollbarH > div {
	height:100%;
}

/**
 * Vertical Scrollbar
 */
.myScrollbarV {
	position:absolute;
	z-index:100;
	width:7px;bottom:7px;top:2px;right:1px
}

.myScrollbarV > div {
	width:100%;
}

.tipNoActions{
	color: #ffffff;
}

/**
 * Both Scrollbars
 */
.myScrollbarH > div,
.myScrollbarV > div {
	position:absolute;
	z-index:100;

	/* The following is probably what you want to customize */
	-webkit-box-sizing:border-box;
	-moz-box-sizing:border-box;
	-o-box-sizing:border-box;
	box-sizing:border-box;
	
	border-width:3px;
	-webkit-border-image:url(img/scrollbar.png) 6 6 6 6;
	-moz-border-image:url(img/scrollbar.png) 6 6 6 6;
	-o-border-image:url(img/scrollbar.png) 6 6 6 6;
	border-image:url(img/scrollbar.png) 6 6 6 6;
}
/*------------- E 以上css主要是仿IOS滚动条样式  -------------*/

