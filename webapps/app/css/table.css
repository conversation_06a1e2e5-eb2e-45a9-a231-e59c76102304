@charset "UTF-8";
/**
 * 表格
 */
.ui-table {
  width: 100%;
  border-collapse: collapse; }

.ui-table th {
  font-weight: 500; }

.ui-table td, .ui-table th {
  border-bottom: 1px solid #e0e0e0;
  border-right: 1px solid #e0e0e0;
  text-align: center; }

@media screen and (-webkit-min-device-pixel-ratio: 2) {
  .ui-table td, .ui-table th {
    position: relative;
    border-right: 0;
    border-bottom: 0; }

  .ui-table td:after, .ui-table th:after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-image: -webkit-gradient(linear, left top, right top, color-stop(0.5, transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0)), -webkit-gradient(linear, left top, left bottom, color-stop(0.5, transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0));
    background-size: 1px 100% ,100% 1px;
    background-repeat: no-repeat;
    background-position: right, bottom;
    pointer-events: none; }

  .ui-table tr td:last-child:after, .ui-table tr th:last-child:after {
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0.5, transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0));
    background-size: 100% 1px;
    background-repeat: no-repeat;
    background-position: bottom; }

  .ui-table tr:last-child td:not(:last-child):after {
    background-image: -webkit-gradient(linear, left top, right top, color-stop(0.5, transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0));
    background-size: 1px 100%;
    background-repeat: no-repeat;
    background-position: right; } }
.ui-table tr td:last-child, .ui-table tr th:last-child {
  border-right: 0; }

.ui-table tr:last-child td {
  border-bottom: 0; }

/*# sourceMappingURL=table.css.map */
