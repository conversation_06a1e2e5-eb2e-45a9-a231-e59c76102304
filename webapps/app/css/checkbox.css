@charset "UTF-8";
.ui-checkbox, .ui-checkbox-s {
  display: inline-block; }

.ui-checkbox input, .ui-checkbox-s input {
  display: inline-block;
  width: 25px;
  height: 1px;
  position: relative;
  overflow: visible;
  border: 0;
  background: none;
  -webkit-appearance: none;
  outline: none;
  margin-right: 8px;
  vertical-align: middle; }

.ui-checkbox input:before, .ui-checkbox-s input:before {
  font-family: "iconfont" !important;
  font-size: 32px;
  line-height: 44px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  display: block;
  color: rgba(0, 0, 0, 0.5);
  content: "";
  color: #18b4ed;
  position: absolute;
  top: -22px;
  left: -4px;
  color: #dedfe0; }

.ui-checkbox input:checked:before, .ui-checkbox-s input:checked:before {
  content: "";
  color: #18b4ed; }

.ui-checkbox-s input:before {
  content: ""; }

.ui-checkbox-s input:checked:before {
  content: ""; }

/*# sourceMappingURL=checkbox.css.map */
