@charset "UTF-8";
/**
 * 轮播组件
 */
.ui-slider {
  width: 100%;
  overflow: hidden;
  position: relative;
  padding-top: 31.25%; }

.ui-slider-content {
  display: -webkit-box;
  position: absolute;
  left: 0;
  top: 0;
  height: 100%; }

.ui-slider-content > li {
  -webkit-box-flex: 1;
  width: 100%;
  height: 100%; }

.ui-slider-content > li img {
  display: block;
  width: 100%; }

.ui-slider-content > li span {
  display: block;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  -webkit-background-size: 100% 100%; }

.ui-slider-content > li.active {
  opacity: .5; }

.ui-slider-indicators {
  position: absolute;
  display: -webkit-box;
  -webkit-box-pack: end;
  width: 100%;
  bottom: 10px;
  right: 4px;
  font-size: 0; }

.ui-slider-indicators li {
  display: block;
  text-indent: 100%;
  white-space: nowrap;
  overflow: hidden;
  font-size: 0;
  width: 7px;
  height: 7px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  margin-right: 6px;
  -webkit-box-sizing: border-box;
  background-clip: padding-box;
  position: relative; }

.ui-slider-indicators li.current:before {
  content: '';
  position: absolute;
  background-color: #fff;
  left: 1px;
  top: 1px;
  width: 5px;
  height: 5px;
  border-radius: 10px;
  -webkit-box-sizing: border-box;
  background-clip: padding-box; }

.ui-slider-indicators-center {
  -webkit-box-pack: center;
  right: 0; }

/*# sourceMappingURL=slider.css.map */
