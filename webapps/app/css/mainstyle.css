html {
    font-size: 62.5%;
}

/*header*/
.ui-header-positive, .ui-footer-positive {
    background-color: #3596CA;
}

.ui-header-positive, .ui-footer-positive {
    background-color: #3596CA;
    overflow: hidden;
}

/* footer */
.ui-footer {
    bottom: 0;
    height: 49px;
    background: #3596CA
}

footer {
    position: fixed;
    left: 0;
    bottom: -2px;
    width: 100%;
    z-index: 11;
    background: #f8f8f8;
    border-top: #ccc solid 1px;
    padding-top: 2px;
}

footer ul {
    display: inline-block;
    width: 25%;
    padding-top: 0;
    line-height: 2.4em;
    font-size: 10px;
    text-align: center;
    height: 2em;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
}

footer ul div {
    padding-top: 25px;
}

footer .l1 {
    background: url(../img/btn_01.png) center 2px no-repeat;
    background-size: auto 55%;
    color: #fff;
}

footer .l2 {
    background: url(../img/btn_02.png) center 2px no-repeat;
    background-size: auto 55%;
    color: #fff;
}

footer .l3 {
    background: url(../img/btn_03.png) center 2px no-repeat;
    background-size: auto 55%;
    color: #fff;
}

footer .l4 {
    background: url(../img/btn_04.png) center 2px no-repeat;
    background-size: auto 55%;
    color: #fff;
}

footer .l1-1 {
    background: url(../img/btn_01-1.png) center 2px no-repeat;
    background-size: auto 55%;
    color: #fff;
}

footer .l2-1 {
    background: url(../img/btn_02-1.png) center 2px no-repeat;
    background-size: auto 55%;
    color: #fff;
}

footer .l3-1 {
    background: url(../img/btn_03-1.png) center 2px no-repeat;
    background-size: auto 55%;
    color: #fff;
}

footer .l4-1 {
    background: url(../img/btn_04-1.png) center 2px no-repeat;
    background-size: auto 55%;
    color: #fff;
}

/*flow list*/
section.flow .ui-badge-num {
    margin: 25px 30px 25px 0;
}

/* flow detail */
.ui-list-text > li {
    padding-bottom: 0;
    padding-top: 0;
}

.ui-list-text .top-li-2 {
    font-size: 14px;
    color: #cccccc;
    padding-top: 0;
    padding-bottom: 10px;
}

.ui-list-text .ui-txt-info {
    font-size: 14px;
    color: #cccccc;
    padding-right: 10px;
}

.flow-detail .ui-list-info, .detail-result .ui-list-info {
    padding-left: 30px;
}

.flow-detail .ui-list-info .p-content, .detail-result .ui-list-info .p-content {
    padding-top: 5px;
    padding-left: 12px;
    color: #333333;
}

.label-btn-div {
    z-index: 1;
    position: fixed;
    bottom: 0;
    background-color: #d80000;
    text-align: center;
    padding-top: 10px;
    margin: 0 0;
    width: 100%;
}

.label-btn-div label {
    color: #d80000;
    background-color: #fff;
    border-radius: 5px;
}

/* node list */
li.node-li-1 {
    padding-top: 5px;
}

li.node-li-1 .ui-txt-info {
    font-size: 16px;
    color: #333333;
}

li.node-li-2 {
    font-size: 14px;
    color: #cccccc;
    padding-top: 0;
    padding-bottom: 0;
}

li.node-li-3 {
    padding-bottom: 5px;
}

.ui-tab-nav li.current {
    color: #3a94ca;
    border-bottom: 2px #3a94ca solid;
}

/* .ui-list-info p{color:#cccccc;}*/
.query-item li {
    margin-left: 30px;
}

.query-item .ui-list-info {
    padding-right: 40px;
}

.query-item .ui-list-info p {
    text-align: right;
}

.ui-dialog-ft button {
    color: #00a5e0;
}

h1 {
    font-weight: bold;
}

p.date {
    color: #cccccc;
}

/* input[type="date"] {border:0;-webkit-appearance: none;font-size:14px;color:#777;}
 */

.mobility-vacation-v1 {
    background: #fff;
    padding: 0 15px;
    border-bottom: 1px solid #e4e4e4;
}

.mobility-vacation-v1 ul li {
    padding-top: 12px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
    position: relative;
}

.mobility-vacation-v1 ul li:last-child {
    border-bottom: 0;
}

.mobility-vacation-v1 ul li h3 {
    font-size: 1.4rem;
    font-weight: normal;
    display: inline-block;
    position: absolute;
    left: 0;
    top: 50%;
    height: 20px;
    line-height: 20px;
    margin-top: -9px;
    color: #000;
}

.mobility-vacation-v1 ul li h3.top {
    top: auto;
    margin-top: 0;
}

.mobility-vacation-v1 ul li h3.letter {
    letter-spacing: 7px;
}

.mobility-vacation-v1 ul li div {
    padding-left: 80px;
}

.mobility-vacation-v1 ul li div.date {
    position: relative;
    z-index: 3;
}

.mobility-vacation-v1 ul li.padding-right div {
    padding-right: 35px;
    position: static;
}

.mobility-vacation-v1 ul li > span {
    display: inline-block;
    position: absolute;
    right: 0;
    top: 0;
}

.mobility-vacation-v1 ul li > span.date {
    top: 50%;
    height: 20px;
    line-height: 20px;
    width: 30px;
    margin-top: -10px;
    background: url(../img/date.png) no-repeat right;
    background-size: auto 100%;
    cursor: pointer;
}

.mobility-vacation-v1 ul li > span.arrow {
    top: 50%;
    height: 24px;
    width: 30px;
    margin-top: -12px;
    background: url(../img/arrow.png) no-repeat 6px -60px;
    background-size: 80px;
}

.mobility-vacation-v1 ul li > span.arrow.hover {
    background-position: 6px 6px;
}

.mobility-vacation-v1 ul li div input,
.mobility-vacation-v1 ul li div textarea,
.mobility-vacation-v1 ul li div select {
    font-size: 1.4rem;
    border: 0;
    margin: 0;
    padding: 0;
    line-height: 18px;
    height: 20px;
    display: block;
    width: 100%;
    color: #333;
    outline: 0;
    background: transparent;
}

.mobility-vacation-v1 ul li div textarea {
    line-height: 20px;
}

.mobility-vacation-v1 ul li div dl {
    font-size: 1.6rem;
    display: inline-block;
    background-color: #fff;
    border-radius: 5px;
    width: 80%;
    max-width: 600px;
    max-height: 60%;
    overflow: auto;
    color: #666;
    position: relative;
    top: 30%;
    box-shadow: 0 0 1px 2px #888;
}

.mobility-vacation-v1 ul li div dl dt {
    padding: 15px;
    border-bottom: 1px solid #f5f5f5;
    text-align: center;
}

.mobility-vacation-v1 ul li div dl dt:active {
    background-color: #f5f5f5;
}

.mobility-vacation-v1 ul li div dl dt:last-child {
    border-bottom: 0;
}

/* 遮罩 */
.mask-black {
    position: fixed;
    z-index: 98;
    height: 100%;
    width: 100%;
    background: rgba(0, 0, 0, 0.6);
    left: 0;
    top: 0;
    text-align: center;
}

.mask-white {
    position: fixed;
    z-index: 98;
    height: 100%;
    width: 100%;
    background: rgba(255, 255, 255, 0.6);
    left: 0;
    top: 0;
    text-align: center;
}

.mask-black .block,
.mask-white .block {
    transition: all 300ms;
    -webkit-transition: all 300ms;
    -moz-transition: all 300ms;
    -ms-transition: all 300ms;
    -o-transition: all 300ms;
}

.account-header, .banktransaction-header {
    padding-top: 5px;
    padding-bottom: 5px;
    padding-left: 20px;
}

.results-list .ui-list-info p {
    text-align: right;
}

.ui-list-date-top {
    min-width: 70px;
    -webkit-box-pack: start;
}

.ui-list-date {
    min-width: 70px;
    position: absolute;
    right: 0;
    bottom: 0;
}

.ui-list-date-up {
    position: absolute;
    right: 0;
    bottom: 25px;
}

.ui-title-letter:first-letter {
    font-size: 10px;
}
