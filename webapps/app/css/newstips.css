@charset "UTF-8";
/**
 * 页面消息提示
 */
.ui-newstips-wrap {
  margin: 20px 15px;
  text-align: center; }

.ui-newstips {
  background: #383939;
  position: relative;
  height: 40px;
  line-height: 40px;
  display: -webkit-inline-box;
  -webkit-box-align: center;
  padding-right: 25px;
  border-radius: 5px;
  font-size: 14px;
  color: #fff;
  padding-left: 15px; }
  .ui-newstips .ui-avatar-tiled, .ui-newstips .ui-newstips-thumb, .ui-newstips i {
    display: block;
    margin-left: -5px;
    margin-right: 10px; }
  .ui-newstips .ui-newstips-thumb {
    width: 30px;
    height: 30px;
    position: relative; }
    .ui-newstips .ui-newstips-thumb > span {
      display: block;
      width: 100%;
      height: 100%;
      z-index: 1;
      background-repeat: no-repeat;
      -webkit-background-size: cover; }
  .ui-newstips div {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    -webkit-box-flex: 1;
    height: inherit; }

.ui-newstips:after {
  font-family: "iconfont" !important;
  font-size: 32px;
  line-height: 44px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  display: block;
  color: rgba(0, 0, 0, 0.5);
  color: #c7c7c7;
  content: "";
  position: absolute;
  right: 15px;
  top: 50%;
  margin-top: -22px;
  margin-right: -10px; }
  @media (max-width: 320px) {
    .ui-newstips:after {
      right: 10px; } }

.ui-newstips .ui-reddot, .ui-newstips .ui-badge-num {
  margin-left: 10px;
  margin-right: 5px; }

/*# sourceMappingURL=newstips.css.map */
