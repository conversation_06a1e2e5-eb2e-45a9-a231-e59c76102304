.BeAlert_overlay {
    overflow: hidden;
    position: fixed;
    margin: 0;
    padding: 0;
    z-index: 9999;
    background: url("img/overlay.png");
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    width: auto;
    height: auto;
}
.BeAlert_box {
    position: fixed;
    top: 50%;
    left:50%;
    background-color: #fff;
    border-radius: 5px;
    padding: 20px;
    z-index: 10000;
    font-family: 微软雅黑;
    font-size: 12px;
    text-align: center;
}
.BeAlert_box .BeAlert_image {
    background: #fff;
    width: 60px;
    height: 60px;
    margin: 10px auto;
}
.BeAlert_box .BeAlert_image.warning {
    background: url("img/warning.png");
    background-size: 60px;
}
.BeAlert_box .BeAlert_image.error {
    background: url("img/error.png");
    background-size: 60px;
}
.BeAlert_box .BeAlert_image.info {
    background: url("img/info.png");
    background-size: 60px;
}
.BeAlert_box .BeAlert_image.question {
    background: url("img/question.png");
    background-size: 60px;
}
.BeAlert_box .BeAlert_image.success {
    background: url("img/success.png");
    background-size: 60px;
}
.BeAlert_box .BeAlert_title {
    font-size: 20px;
    margin: 5px auto;
}
.BeAlert_box .BeAlert_message {
    font-size: 14px;
    margin: 5px auto;
}
.BeAlert_box .BeAlert_button {
    margin-top: 20px;
}
.BeAlert_box .BeAlert_button button {
    display: none;
    background-color: #8CD4F5;
    color: #fff;
    border: none;
    box-shadow: none;
    font-size: 17px;
    font-weight: 500;
    -webkit-border-radius: 4px;
    border-radius: 5px;
    padding: 10px 30px;
    cursor: pointer;
    margin: 0 10px;
    outline: none;
}
.BeAlert_box .BeAlert_button button.BeAlert_cancel {
    background-color: #c1c1c1;
}
.BeAlert_box .BeAlert_button button.BeAlert_cancel:hover {
    background-color: #b9b9b9;
}
.BeAlert_box .BeAlert_button button.BeAlert_confirm:hover {
    background-color: #86CCEB;
}
