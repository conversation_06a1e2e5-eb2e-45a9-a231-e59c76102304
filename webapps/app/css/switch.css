.ui-switch {
  position: absolute;
  font-size: 16px;
  right: 15px;
  top: 50%;
  margin-top: -16px;
  width: 52px;
  height: 32px;
  line-height: 32px; }
  @media (max-width: 320px) {
    .ui-switch {
      right: 10px; } }
  .ui-switch input {
    width: 52px;
    height: 32px;
    position: absolute;
    z-index: 2;
    border: none;
    background: none;
    -webkit-appearance: none;
    outline: none; }
    .ui-switch input:before {
      content: '';
      width: 50px;
      height: 30px;
      border: 1px solid #dfdfdf;
      background-color: #fdfdfd;
      border-radius: 20px;
      cursor: pointer;
      display: inline-block;
      position: relative;
      vertical-align: middle;
      -webkit-box-sizing: content-box;
      box-sizing: content-box;
      border-color: #dfdfdf;
      box-shadow: #dfdfdf 0px 0px 0px 0px inset;
      -webkit-transition: border 0.4s, box-shadow 0.4s;
      transition: border 0.4s, box-shadow 0.4s;
      -webkit-background-clip: content-box;
      background-clip: content-box; }
    .ui-switch input:checked:before {
      border-color: #64bd63;
      box-shadow: #64bd63 0px 0px 0px 16px inset;
      background-color: #64bd63;
      transition: border 0.4s, box-shadow 0.4s,  background-color 1.2s;
      -webkit-transition: border 0.4s, box-shadow 0.4s, background-color 1.2s;
      background-color: #64bd63; }
    .ui-switch input:checked:after {
      left: 21px; }
    .ui-switch input:after {
      content: '';
      width: 30px;
      height: 30px;
      position: absolute;
      top: 1px;
      left: 0;
      border-radius: 100%;
      background-color: #fff;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
      -webkit-transition: left 0.2s;
      transition: left 0.2s; }

/*# sourceMappingURL=switch.css.map */
