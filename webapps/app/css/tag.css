@charset "UTF-8";
.ui-tag-t, .ui-tag-hot,
.ui-tag-new,
.ui-tag-s-hot,
.ui-tag-s-new,
.ui-tag-pop-hot,
.ui-tag-pop-new {
  position: relative; }

.ui-tag-t:before, .ui-tag-hot:before,
.ui-tag-new:before,
.ui-tag-s-hot:before,
.ui-tag-s-new:before,
.ui-tag-pop-hot:before,
.ui-tag-pop-new:before,
.ui-tag-t:after,
.ui-tag-hot:after,
.ui-tag-new:after,
.ui-tag-s-hot:after,
.ui-tag-s-new:after,
.ui-tag-pop-hot:after,
.ui-tag-pop-new:after {
  height: 20px;
  left: 0;
  top: 0;
  z-index: 9;
  display: block; }

.ui-tag-t:before, .ui-tag-hot:before,
.ui-tag-new:before,
.ui-tag-s-hot:before,
.ui-tag-s-new:before,
.ui-tag-pop-hot:before,
.ui-tag-pop-new:before,
.ui-tag-vip:before,
.ui-tag-svip:before,
.ui-tag-selected:after {
  font-family: "iconfont" !important;
  font-size: 32px;
  line-height: 44px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  display: block;
  color: rgba(0, 0, 0, 0.5);
  position: absolute; }

.ui-tag-t:before, .ui-tag-hot:before,
.ui-tag-new:before,
.ui-tag-s-hot:before,
.ui-tag-s-new:before,
.ui-tag-pop-hot:before,
.ui-tag-pop-new:before {
  content: "";
  line-height: 20px;
  color: #ff0000; }

.ui-tag-t:after, .ui-tag-hot:after,
.ui-tag-new:after,
.ui-tag-s-hot:after,
.ui-tag-s-new:after,
.ui-tag-pop-hot:after,
.ui-tag-pop-new:after {
  position: absolute;
  content: '';
  width: 22px;
  text-align: right;
  line-height: 20px;
  font-size: 12px;
  color: #fff;
  padding-right: 14px; }

.ui-tag-b, .ui-tag-freelimit,
.ui-tag-free,
.ui-tag-last,
.ui-tag-limit,
.ui-tag-act,
.ui-tag-xy,
.ui-tag-vip,
.ui-tag-svip {
  position: relative; }

.ui-tag-b:before, .ui-tag-freelimit:before,
.ui-tag-free:before,
.ui-tag-last:before,
.ui-tag-limit:before,
.ui-tag-act:before,
.ui-tag-xy:before,
.ui-tag-vip:before,
.ui-tag-svip:before {
  position: absolute;
  font-size: 10px;
  width: 28px;
  height: 13px;
  line-height: 13px;
  bottom: 0;
  right: 0;
  z-index: 9;
  color: #fff;
  border-radius: 2px;
  text-align: center; }

.ui-tag-vip:before,
.ui-tag-svip:before {
  font-size: 32px;
  text-indent: -2px;
  border-radius: 2px; }

.ui-tag-vip:before {
  background-color: #ff0000;
  color: #fffadf;
  content: ""; }

.ui-tag-svip:before {
  background-color: #ffd400;
  color: #b7440e;
  content: ""; }

.ui-tag-freelimit:before {
  background-color: #18b4ed;
  content: '限免'; }

.ui-tag-free:before {
  background-color: #5fb336;
  content: '免费'; }

.ui-tag-last:before {
  background-color: #8f6adb;
  content: '绝版'; }

.ui-tag-limit:before {
  background-color: #3385e6;
  content: '限量'; }

.ui-tag-act:before {
  background-color: #00c795;
  content: '活动'; }

.ui-tag-xy:before {
  background-color: #d7ba42;
  content: '星影'; }

.ui-tag-freemonthly:before {
  background-color: #ff7f0d;
  content: '包月'; }

.ui-tag-onsale:before {
  background-color: #00c795;
  content: '特价'; }

.ui-tag-hot:after,
.ui-tag-s-hot:after,
.ui-tag-pop-hot:after {
  content: '热'; }

.ui-tag-new:after,
.ui-tag-s-new:after,
.ui-tag-pop-new:after {
  content: '\u65b0'; }

.ui-tag-hot:before,
.ui-tag-s-hot:before,
.ui-tag-pop-hot:before {
  color: #ff7200; }

.ui-tag-s-hot:before,
.ui-tag-s-new:before {
  content: "";
  left: -2px; }

.ui-tag-s-hot:after,
.ui-tag-s-new:after {
  width: 16px;
  padding-right: 12px; }

.ui-tag-selected:after {
  content: "";
  color: #18b4ed;
  right: -5px;
  top: -5px;
  z-index: 9;
  width: 26px;
  height: 26px;
  background: #fff;
  border-radius: 13px;
  line-height: 26px;
  text-indent: -3px; }

.ui-tag-wrap {
  display: inline-block;
  position: relative;
  padding-right: 32px; }
  .ui-tag-wrap .ui-tag-vip,
  .ui-tag-wrap .ui-tag-svip {
    position: static; }
  .ui-tag-wrap .ui-tag-vip:before,
  .ui-tag-wrap .ui-tag-svip:before {
    top: 50%;
    margin-top: -7px; }

.ui-tag-pop-hot:before,
.ui-tag-pop-new:before {
  content: "";
  left: -10px;
  top: 1px; }

.ui-tag-pop-hot:after,
.ui-tag-pop-new:after {
  font-size: 11px;
  padding-right: 0;
  text-align: center;
  left: -5px; }

/*# sourceMappingURL=tag.css.map */
