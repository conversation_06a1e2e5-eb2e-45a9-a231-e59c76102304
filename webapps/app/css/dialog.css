@charset "UTF-8";
/**
 * 弹窗类
 */
.ui-dialog {
  position: fixed;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: -webkit-box;
  -webkit-box-orient: horizontal;
  -webkit-box-pack: center;
  -webkit-box-align: center;
  background: rgba(0, 0, 0, 0.4);
  display: none; }

.ui-dialog.show {
  display: -webkit-box;
  display: box; }

.ui-dialog-hd {
  height: 48px;
  line-height: 48px;
  text-align: center;
  position: relative; }

.ui-dialog-cnt {
  border-radius: 6px;
  width: 270px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  pointer-events: auto;
  background-color: rgba(253, 253, 253, 0.95);
  position: relative;
  font-size: 16px; }

.ui-dialog-bd {
  min-height: 71px;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  padding: 18px;
  display: -webkit-box;
  display: box;
  -webkit-box-pack: center;
  -webkit-box-align: center;
  -webkit-box-orient: vertical; }

.ui-dialog-bd > h4 {
  margin-bottom: 4px;
  width: 100%;
  text-align: center; }

.ui-dialog-bd > div, .ui-dialog-bd > ul {
  width: 100%; }

.ui-dialog-ft {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
  display: -webkit-box;
  width: 100%;
  box-sizing: border-box;
  -webkit-box-align: center;
  border-top: 1px solid #e0e0e0;
  height: 42px;
  line-height: 42px; }

.ui-dialog-close:before {
  font-family: "iconfont" !important;
  font-size: 32px;
  line-height: 44px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  display: block;
  color: rgba(0, 0, 0, 0.5);
  content: "";
  color: #828282;
  display: block;
  line-height: 32px;
  position: absolute;
  top: 3px;
  right: 3px; }

.ui-dialog-close:active {
  opacity: 0.5; }

.ui-dialog-ft button {
  color: #00a5e0;
  text-align: center;
  border-right: 1px #e0e0e0 solid;
  width: 100%;
  line-height: 42px;
  background: transparent;
  display: block;
  margin: 0 !important;
  -webkit-box-flex: 1; }
  .ui-dialog-ft button:active {
    background-color: rgba(0, 0, 0, 0.1) !important; }
  .ui-dialog-ft button:first-child {
    border-bottom-left-radius: 6px; }
  .ui-dialog-ft button:last-child {
    border-right: 0;
    border-bottom-right-radius: 6px; }

@media screen and (-webkit-min-device-pixel-ratio: 2) {
  .ui-dialog-ft {
    position: relative;
    border: 0;
    background-position: left top;
    background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0.5, transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0));
    background-repeat: repeat-x;
    -webkit-background-size: 100% 1px; }

  .ui-dialog-ft button {
    border-right: 0;
    background-position: right top;
    background-image: -webkit-gradient(linear, left top, right top, color-stop(0.5, transparent), color-stop(0.5, #e0e0e0), to(#e0e0e0));
    background-repeat: repeat-y;
    -webkit-background-size: 1px 100%; }
    .ui-dialog-ft button:last-child {
      background: none; } }

/*# sourceMappingURL=dialog.css.map */
