<?xml version="1.0" encoding="utf-8" ?>
<taglib xmlns="http://java.sun.com/xml/ns/j2ee"
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-jsptaglibrary_2_0.xsd"
version="2.0">
<tlib-version>0.1</tlib-version>
<short-name>my</short-name>
<uri>my</uri>
	<tag>
		<description>html5 list <AUTHOR>
		<name>list</name>
		<tag-class>com.fingard.app.delegate.tag.List</tag-class>
		<body-content>JSP</body-content>
		<attribute>
			<description>唯一标志，请注意需要全局唯一</description>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<description>标题</description>
			<name>title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<description>唯一标志，请注意需要全局唯一</description>
			<name>className</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<description>唯一标志，请注意需要全局唯一</description>
			<name>style</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<description>是否分页</description>
			<name>isPage</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<description>唯一标志，请注意需要全局唯一</description>
			<name>pageSize</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<description>html HasPermision <AUTHOR>
		<name>hp</name>
		<tag-class>com.fingard.app.delegate.tag.HasPermission</tag-class>
		<body-content>JSP</body-content>
		<attribute>
			<description>唯一标志，请注意需要全局唯一</description>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<description>菜单名称,允许用&amp;&amp;号分隔表示并且关系，或者用||表示或者关系</description>
			<name>menu</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
</taglib>