#######################################################################
#                       ÎÄ¼þÃèÊö£ºÏµÍ³log4jÈÕÖ¾ÅäÖÃÎÄ¼þ
#                       ÏµÍ³Ãû³Æ£ºHZHSGS»ù½ðÏúÊÛ¹ÜÀí/CRMÏµÍ³
#                       ÎÄ¼þ×÷Õß£ºÀîÃ÷ËÉ
#                       ½¨Á¢ÈÕÆÚ£º2004/08/01
#######################################################################
#¸ù¼ÇÂ¼Æ÷--¼ÇÂ¼µ½ÆÁÄ»
log4j.rootLogger=ERROR,STDOUT

#######################################################################
#STDOUT--´òÓ¡µ½ÆÁÄ»ÉÏ
log4j.appender.STDOUT=org.apache.log4j.ConsoleAppender
log4j.appender.STDOUT.layout=org.apache.log4j.PatternLayout
log4j.appender.STDOUT.layout.ConversionPattern=[%-5p][%d{HH:mm:ss}][%c][%M] %m %n


#######################################################################
#½«ÏµÍ³ÈÕÖ¾¼ÇÂ¼µ½account¼ÇÂ¼Æ÷ÖÐ
log4j.logger.account=debug,account
log4j.appender.account=org.apache.log4j.FileAppender
log4j.appender.account.File=../log/account.log
log4j.appender.account.layout=org.apache.log4j.PatternLayout
log4j.appender.account.layout.ConversionPattern=[%-5p][%d{yyyy-MM-dd HH:mm:ss}][%l]%m %n


#######################################################################
#½«ÏµÍ³ÈÕÖ¾¼ÇÂ¼µ½externalsettlemanage¼ÇÂ¼Æ÷ÖÐ
log4j.logger.externalsettlemanage=debug,externalsettlemanage
log4j.appender.externalsettlemanage=org.apache.log4j.FileAppender
log4j.appender.externalsettlemanage.File=../log/externalsettlemanage.log
log4j.appender.externalsettlemanage.layout=org.apache.log4j.PatternLayout
log4j.appender.externalsettlemanage.layout.ConversionPattern=[%-5p][%d{yyyy-MM-dd HH:mm:ss}][%l]%m %n


#######################################################################
#½«ÏµÍ³ÈÕÖ¾¼ÇÂ¼µ½fundtrademanage¼ÇÂ¼Æ÷ÖÐ
log4j.logger.fundtrademanage=debug,fundtrademanage
log4j.appender.fundtrademanage=org.apache.log4j.FileAppender
log4j.appender.fundtrademanage.File=../log/fundtrademanage.log
log4j.appender.fundtrademanage.layout=org.apache.log4j.PatternLayout
log4j.appender.fundtrademanage.layout.ConversionPattern=[%-5p][%d{yyyy-MM-dd HH:mm:ss}][%l]%m %n


#######################################################################
#½«ÏµÍ³ÈÕÖ¾¼ÇÂ¼µ½innerapplies¼ÇÂ¼Æ÷ÖÐ
log4j.logger.innerapplies=debug,innerapplies
log4j.appender.innerapplies=org.apache.log4j.FileAppender
log4j.appender.innerapplies.File=../log/innerapplies.log
log4j.appender.innerapplies.layout=org.apache.log4j.PatternLayout
log4j.appender.innerapplies.layout.ConversionPattern=[%-5p][%d{yyyy-MM-dd HH:mm:ss}][%l]%m %n

#######################################################################
#½«ÏµÍ³ÈÕÖ¾¼ÇÂ¼µ½param¼ÇÂ¼Æ÷ÖÐ
log4j.logger.param=debug,param
log4j.appender.param=org.apache.log4j.FileAppender
log4j.appender.param.File=../log/param.log
log4j.appender.param.layout=org.apache.log4j.PatternLayout
log4j.appender.param.layout.ConversionPattern=[%-5p][%d{yyyy-MM-dd HH:mm:ss}][%l]%m %n

#######################################################################
#½«ÏµÍ³ÈÕÖ¾¼ÇÂ¼µ½system¼ÇÂ¼Æ÷ÖÐ
log4j.logger.system=debug,system
log4j.appender.system=org.apache.log4j.FileAppender
log4j.appender.system.File=../log/system.log
log4j.appender.system.layout=org.apache.log4j.PatternLayout
log4j.appender.system.layout.ConversionPattern=[%-5p][%d{yyyy-MM-dd HH:mm:ss}][%l]%m %n

#######################################################################
#½«ÏµÍ³ÈÕÖ¾¼ÇÂ¼µ½task¼ÇÂ¼Æ÷ÖÐ
log4j.logger.task=debug,task
log4j.appender.task=org.apache.log4j.FileAppender
log4j.appender.task.File=../log/task.log
log4j.appender.task.layout=org.apache.log4j.PatternLayout
log4j.appender.task.layout.ConversionPattern=[%-5p][%d{yyyy-MM-dd HH:mm:ss}][%l]%m %n


#######################################################################
#½«ÏµÍ³ÈÕÖ¾¼ÇÂ¼µ½applymarginservice¼ÇÂ¼Æ÷ÖÐ
log4j.logger.applymarginservice=debug,applymarginservice
log4j.appender.applymarginservice=org.apache.log4j.FileAppender
log4j.appender.applymarginservice.File=../log/applymarginservice.log
log4j.appender.applymarginservice.layout=org.apache.log4j.PatternLayout
log4j.appender.applymarginservice.layout.ConversionPattern=[%-5p][%d{yyyy-MM-dd HH:mm:ss}][%l]%m %n


#######################################################################
#½«ÏµÍ³ÈÕÖ¾¼ÇÂ¼µ½syncbankbalanceservice¼ÇÂ¼Æ÷ÖÐ
log4j.logger.syncbankbalanceservice=debug,syncbankbalanceservice
log4j.appender.syncbankbalanceservice=org.apache.log4j.FileAppender
log4j.appender.syncbankbalanceservice.File=../log/syncbankbalanceservice.log
log4j.appender.syncbankbalanceservice.layout=org.apache.log4j.PatternLayout
log4j.appender.syncbankbalanceservice.layout.ConversionPattern=[%-5p][%d{yyyy-MM-dd HH:mm:ss}][%l]%m %n


#######################################################################
#½«ÏµÍ³ÈÕÖ¾¼ÇÂ¼µ½syncbankbalanceservice¼ÇÂ¼Æ÷ÖÐ
log4j.logger.tallyaccountservice=debug,tallyaccountservice
log4j.appender.tallyaccountservice=org.apache.log4j.FileAppender
log4j.appender.tallyaccountservice.File=../log/tallyaccountservice.log
log4j.appender.tallyaccountservice.layout=org.apache.log4j.PatternLayout
log4j.appender.tallyaccountservice.layout.ConversionPattern=[%-5p][%d{yyyy-MM-dd HH:mm:ss}][%l]%m %n


#######################################################################
#½«ÏµÍ³ÈÕÖ¾¼ÇÂ¼µ½upstreamfundsservice¼ÇÂ¼Æ÷ÖÐ
log4j.logger.upstreamfundsservice=debug,upstreamfundsservice
log4j.appender.upstreamfundsservice=org.apache.log4j.FileAppender
log4j.appender.upstreamfundsservice.File=../log/upstreamfundsservice.log
log4j.appender.upstreamfundsservice.layout=org.apache.log4j.PatternLayout
log4j.appender.upstreamfundsservice.layout.ConversionPattern=[%-5p][%d{yyyy-MM-dd HH:mm:ss}][%l]%m %n


#######################################################################
#½«ÏµÍ³ÈÕÖ¾¼ÇÂ¼µ½upstreamfundsservice¼ÇÂ¼Æ÷ÖÐ
log4j.logger.innerapplyaddeneservice=debug,innerapplyaddeneservice
log4j.appender.innerapplyaddeneservice=org.apache.log4j.FileAppender
log4j.appender.innerapplyaddeneservice.File=../log/innerapplyaddeneservice.log
log4j.appender.innerapplyaddeneservice.layout=org.apache.log4j.PatternLayout
log4j.appender.innerapplyaddeneservice.layout.ConversionPattern=[%-5p][%d{yyyy-MM-dd HH:mm:ss}][%l]%m %n


#######################################################################
#½«ÏµÍ³ÈÕÖ¾¼ÇÂ¼µ½messages¼ÇÂ¼Æ÷ÖÐ
log4j.logger.messages=debug,messages
log4j.appender.messages=org.apache.log4j.FileAppender
log4j.appender.messages.File=../log/messages.log
log4j.appender.messages.layout=org.apache.log4j.PatternLayout
log4j.appender.messages.layout.ConversionPattern=[%-5p][%d{yyyy-MM-dd HH:mm:ss}][%l]%m %n



#######################################################################
#½«ÏµÍ³ÈÕÖ¾¼ÇÂ¼µ½SYS¼ÇÂ¼Æ÷ÖÐ
log4j.logger.SYS=debug,SYS
log4j.appender.SYS=org.apache.log4j.FileAppender
log4j.appender.SYS.File=../log/fundcrm.log
log4j.appender.SYS.layout=org.apache.log4j.PatternLayout
log4j.appender.SYS.layout.ConversionPattern=[%-5p][%d{yyyy-MM-dd HH:mm:ss}][%l]%m %n

#######################################################################
#½«²éÑ¯·þÎñÈÕÖ¾¼ÇÂ¼µ½QUERY¼ÇÂ¼Æ÷ÖÐ
log4j.logger.QUERY=DEBUG,QUERY
log4j.appender.QUERY=org.apache.log4j.FileAppender
log4j.appender.QUERY.File=../log/querydebug.log
log4j.appender.QUERY.layout=org.apache.log4j.PatternLayout
log4j.appender.QUERY.layout.ConversionPattern=[%-5p][%d{yyyy-MM-dd HH:mm:ss}]%m %n

#######################################################################
#ÐÔÄÜ¸ú×ÙÆ÷ÈÕÖ¾¼ÇÂ¼µ½PMTRACE¼ÇÂ¼Æ÷ÖÐ
log4j.logger.PMTRACE=ERROR,PMTRACE
log4j.appender.PMTRACE=org.apache.log4j.FileAppender
log4j.appender.PMTRACE.File=../log/pmtrace.xml
log4j.appender.PMTRACE.layout=org.apache.log4j.PatternLayout
log4j.appender.PMTRACE.layout.ConversionPattern=%m %n

#######################################################################
#½«HibernateÈÕÖ¾¼ÇÂ¼µ½HIB¼ÇÂ¼Æ÷ÖÐ
log4j.logger.net.sf.hibernate=WARN,HIB
log4j.appender.HIB=org.apache.log4j.FileAppender
log4j.appender.HIB.File=../log/hibernate.log
log4j.appender.HIB.layout=org.apache.log4j.PatternLayout
log4j.appender.HIB.layout.ConversionPattern=%d{ABSOLUTE} %5p %c{1}:%L - %m%n