<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://java.sun.com/xml/ns/javaee"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
         id="app-delegate" version="2.5">
    <display-name>AtsService</display-name>

    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>classpath:spring/applicationContext.xml,
            classpath:spring/applicationContext-*.xml
        </param-value>
    </context-param>

    <!-- 远东租赁SSO，需要同域名下：fehorizon.com -->
    <!--
    <filter>
        <filter-name>fel-sso-filter</filter-name>
        <filter-class>com.fingard.app.delegate.filter.FelSsoAuthenticationFilter</filter-class>

        <init-param>
            <param-name>domain</param-name>
            <param-value>fehorizon.com</param-value>
        </init-param>
        <init-param>
            <param-name>loginUrl</param-name>
            <param-value>http://ha.fehorizon.com:40006/femsp/uaa/login.html?redirectURL=http%3a%2f%2ftiger.fehorizon.com%3a8889%2fapp%2fcustomer%2ffel%2flogin.jsp</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>fel-sso-filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    -->
    <!-- 祥生地产SSO 配置开始 -->
    <!--
    <filter>
        <filter-name>EKPSSOClient</filter-name>
        <filter-class>com.landray.sso.client.EKPSSOClient</filter-class>
        <init-param>
            <description>对应的配置文件位置</description>
            <param-name>filterConfigFile</param-name>
            <param-value>/sso-config.properties</param-value>
        </init-param>
    </filter>
    <filter>
        <filter-name>LoginFilter</filter-name>
        <filter-class>com.fingard.app.delegate.filter.EKPSSOClientAuthenticationFilter</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>EKPSSOClient</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>LoginFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    -->

    <!--常德城投sso-->
    <!--
    <filter>
        <filter-name>CAS Single Sign Out Filter</filter-name>
        <filter-class>org.jasig.cas.client.session.SingleSignOutFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>CAS Single Sign Out Filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter>
        <filter-name>AuthFilter</filter-name>
        <filter-class>com.fingard.app.delegate.filter.CdctSsoFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>AuthFilter</filter-name>
        <url-pattern>*.jsp</url-pattern>
    </filter-mapping>-->
    <!-- SSO 配置结束 -->

    <!-- 四川中烟sso
    <filter>
        <filter-name>CNTC SSO FILTER</filter-name>
        <filter-class>com.fingard.app.delegate.filter.CntcNewSsoFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>CNTC SSO FILTER</filter-name>
        <url-pattern>*.jsp</url-pattern>
    </filter-mapping>-->




    <filter>
        <filter-name>XssFilter</filter-name>
        <filter-class>com.fingard.app.delegate.filter.XssFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>XssFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>encodingFilter</filter-name>
        <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
        <init-param>
            <param-name>forceEncoding</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>encodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>


    <!--session filter-->
    <filter>
        <filter-name>replaceSessionFilter</filter-name>
        <filter-class>
            com.fingard.app.delegate.filter.FilterDispatcherWithLog4Session
        </filter-class>
        <init-param>
            <param-name>filterPattern</param-name>
            <param-value>.*do|.*\.jsp</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>replaceSessionFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <session-config>
        <session-timeout>-1</session-timeout>
    </session-config>

    <filter>
        <filter-name>permissionFilter</filter-name>
        <filter-class>com.fingard.app.delegate.permission.filter.FilterDispatcherWithLog4Permission</filter-class>
        <init-param>
            <param-name>filterPattern</param-name>
            <param-value>.*\.jsp</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>permissionFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>common-sf-filter</filter-name>
        <filter-class>com.fingard.app.delegate.filter.CommonSafeFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>common-sf-filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- Spring配置 -->
    <listener>
        <listener-class>
            org.springframework.web.context.ContextLoaderListener
        </listener-class>
    </listener>


    <!--
    <listener>
        <listener-class>com.fingard.app.delegate.listener.OnlineUserListener</listener-class>
    </listener>
    -->

    <servlet>
        <servlet-name>springmvc</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>classpath:applicationContext-mvc.xml</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>springmvc</servlet-name>
        <url-pattern>*.action</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>springmvc</servlet-name>
        <url-pattern>*.do</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>appMsgServlet</servlet-name>
        <servlet-class>com.fingard.app.delegate.servlet.AppMsgServlet</servlet-class>
        <load-on-startup>2</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>appMsgServlet</servlet-name>
        <url-pattern>/appMsgServlet</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>wechat</servlet-name>
        <servlet-class>com.fingard.app.delegate.controller.third.WechatController</servlet-class>
        <load-on-startup>2</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>wechat</servlet-name>
        <url-pattern>/wechat</url-pattern>
    </servlet-mapping>


    <context-param>
        <param-name>loginPage</param-name>
        <param-value>/customer/apptest/login.jsp</param-value>
    </context-param>

    <!-- 欢迎页面 -->
    <welcome-file-list>
        <welcome-file>/index.jsp</welcome-file>
    </welcome-file-list>

    <error-page>
        <error-code>404</error-code>
        <location>/error.jsp</location>
    </error-page>
    <error-page>
        <error-code>400</error-code>
        <location>/error.jsp</location>
    </error-page>
    <error-page>
        <error-code>500</error-code>
        <location>/error.jsp</location>
    </error-page>

</web-app>
