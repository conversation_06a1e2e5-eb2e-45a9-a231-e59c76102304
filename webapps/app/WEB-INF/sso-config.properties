
#¿ÉÈ¡ÖµÎªDEBUG/WARN/ERROR
log.level = WARN
#¹ýÂËÁ´£¬ÎªÒÔÏÂµÄÖµµÄ×éºÏ£¬ÓÃ;·Ö¸ô£¬com.landray.sso.client.filter°üÏÂÃæµÄÀà¿É²»´ø°üÃû£¬ÆäËü±ØÐë´ø°üÃû£¬²»ÄÜÊ¹ÓÃÎÞ°üÃûµÄÀà
#×¢Òâ£º¹ýÂËÁ´ÊÇÓÐÏÈºóË³ÐòµÄ£¬½¨Òé²»¸Ä±äÏÂÃæ¹ýÂËÆ÷µÄÏÈºóµÄË³Ðò
filter.chain = CASURLFilter;UsernameConvertFilter;SSOLoginRedirectFilter;SSOServerAuthenticateFilter
#===========================================
#TokenFilter
#¹¦ÄÜ£ºÁîÅÆ»·µÄÊ¶±ðÓëÉú³É
#²ÎÊý£ºÃÜÔ¿ÎÄ¼þÂ·¾¶,Â·¾¶ÊÇÏà¶ÔÓÚsso-config.properties¡£
###TokenFilter.keyFilePath = /LRToken
#²ÎÊý£º×¢ÏúURL£¬¿ÉÑ¡£¨Èô×¢Ïú¶¯×÷²»ÔÚ±¾·þÎñÆ÷Ö´ÐÐ£©
###TokenFilter.logoutURL = 
#=========================================== 
#===========================================
#CASURLFilter
#¹¦ÄÜ£ºCASµÇÂ¼/×¢ÏúÑéÖ¤Æ÷
#²ÎÊý£ºcas·þÎñÆ÷µØÖ·¡£/serviceValidate  Õâ¸ö²»ÐèÒª¼Ó£¬£¬ÏµÍ³»á×Ô¶¯¼ÓÕâ¸öµØÖ·
CASURLFilter.cas.server = http://csoa.xsjt.cn:8081/sso
#²ÎÊý£ºurlÖÐµÄticket²ÎÊýÃû£¬Ä¬ÈÏÎªticket
CASURLFilter.cas.ticket = ticket
#===========================================
#===========================================
#SSOServerAuthenticateFilter
#¹¦ÄÜ£º½«ÓÃ»§ÃûÃÜÂëÌá½»¸øSSO·þÎñÆ÷½øÐÐÑéÖ¤£¬ÑéÖ¤½á¹û½«±£´æµ½sessionµÄEKPSSOAuthenticate±äÁ¿ÖÐ£¬Îªsuccess»òfailure
#²ÎÊý£º·þÎñÆ÷ÑéÖ¤µØÖ·
SSOServerAuthenticateFilter.server.authentication.URL = http://csoa.xsjt.cn:8081/sso/userValidate
#²ÎÊý£º±¾µØµÇÂ¼Ò³ÃæÖÐµÄÓÃ»§Ãû×Ö¶Î
SSOServerAuthenticateFilter.local.form.username = j_username
#²ÎÊý£º±¾µØµÇÂ¼Ò³ÃæÖÐµÄÃÜÂë×Ö¶Î
SSOServerAuthenticateFilter.local.form.password = j_password
#²ÎÊý£º±¾µØµÇÂ¼Ò³ÃæµÄÌá½»µØÖ·£¨²»º¬contextPath£©
SSOServerAuthenticateFilter.local.form.action = /j_acegi_security_check
#²ÎÊý£ºµÇÂ¼Ê§°ÜÒ³ÃæURL
SSOServerAuthenticateFilter.local.login.URL = http://a878sm24.autoexpr.com:9000/CICenter/login.jsp?login_error=1
#===========================================
#===========================================
#UsernameConvertFilter
#¹¦ÄÜ£º±¾µØÓÃ»§-Í³Ò»ÓÃ»§µÄÇÐ»»Æ÷
#²ÎÊý£ºÓÃ»§×ª»»·þÎñµØÖ·
UsernameConvertFilter.serverAddress= http://csoa.xsjt.cn:8081/sso/userConvert?formApp=${FROM}&toApp=${TO}&usernames=${USERNAMES}
#²ÎÊý£º±¾µØ·þÎñÆ÷µÄKey
UsernameConvertFilter.localKey = CI
#===========================================
#===========================================
#SSOLoginRedirectFilter
#¹¦ÄÜ£ºµ±ÅÐ¶Ïµ½Î´µÇÂ¼Ê±£¬½øÐÐÒ³ÃæÌø×ª£¬ÈôÏµÍ³ÖÐ´æÔÚÄäÃû¿É·ÃÎÊµÄ×ÊÔ´£¬²»½¨ÒéÊ¹ÓÃ¸Ã¹ýÂËÆ÷£¬Ò»°ãÅäÔÚËùÓÐ×îºó
#²ÎÊý£ºÌø×ªURL£¬Ê¹ÓÃ¡°${URL}¡±Ìæ»»Ô­ÓÐµØÖ·
SSOLoginRedirectFilter.login.URL = http://csoa.xsjt.cn:8081/sso/login?service=${URL}
#²ÎÊý£ºURLµÄ±àÂë¸ñÊ½£¬¿É²»Ìî£¬±íÊ¾²»½«${URL}µÄÖµ×ªÂë
SSOLoginRedirectFilter.login.URLCharset = UTF-8
#²ÎÊý£º²»½øÐÐÌø×ªµÄURL£¬ÓÃ;·Ö¸ô¶àÖµ£¬Èç£º¡°/resource¡±±íÊ¾ËùÓÐÒÔ¡°/resource¡±¿ªÍ·µÄURL
SSOLoginRedirectFilter.noRedirectURLs = 
#===========================================
