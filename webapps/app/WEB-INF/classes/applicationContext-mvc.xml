<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans-3.0.xsd   
						http://www.springframework.org/schema/context   
						http://www.springframework.org/schema/context/spring-context-3.0.xsd  
						http://www.springframework.org/schema/aop   
						http://www.springframework.org/schema/aop/spring-aop-3.0.xsd">

    <context:component-scan base-package="com.fingard.app.delegate.controller"
                            use-default-filters="false">
        <context:include-filter type="annotation"
                                expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>

    <context:component-scan base-package="com.fingard.app.delegate.controller.reimbursement"
                            use-default-filters="false">
        <context:include-filter type="annotation"
                                expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>

    <aop:config>
        <aop:aspect id="OperationRecordAspect" ref="operationRecordAspect">
            <aop:pointcut id="recordTask" expression="execution(* com.fingard.app.delegate.controller.*.*(..))"/>
            <aop:around pointcut-ref="recordTask" method="aroundMethod"/>
        </aop:aspect>
    </aop:config>

    <aop:config>
        <aop:aspect id="exceptionAspect" ref="exceptionFilterAspect">
            <aop:pointcut id="exceptionPointCut"
                          expression="execution(* com.fingard.app.delegate.controller.*.* (..))) || execution(* com.fingard.app.delegate.*.controller..*.* (..)))"/>
            <aop:around method="aspectController" pointcut-ref="exceptionPointCut"/>
        </aop:aspect>
    </aop:config>
    <aop:aspectj-autoproxy proxy-target-class="true">
    </aop:aspectj-autoproxy>

    <!-- 配置使用@ResponseBody方法返回数据的bean -->
    <bean class="org.springframework.web.servlet.mvc.annotation.AnnotationMethodHandlerAdapter">
        <property name="messageConverters">
            <list>
                <bean class="org.springframework.http.converter.StringHttpMessageConverter">
                    <property name="supportedMediaTypes">
                        <list>
                            <value>text/html;charset=UTF-8</value>
                        </list>
                    </property>
                </bean>
                <bean class="org.springframework.http.converter.json.MappingJacksonHttpMessageConverter">
                    <property name="supportedMediaTypes">
                        <list>
                            <value>application/json;charset=UTF-8</value>
                        </list>
                    </property>
                </bean>
            </list>
        </property>
    </bean>


    <bean id="viewResolver"
          class="org.springframework.web.servlet.view.UrlBasedViewResolver">
        <property name="viewClass">
            <value>
                org.springframework.web.servlet.view.tiles2.TilesView
            </value>
        </property>
    </bean>
    <bean id="tilesConfigurer"
          class="org.springframework.web.servlet.view.tiles2.TilesConfigurer">
        <property name="definitions">
            <list>
                <value>/WEB-INF/tiles.xml</value>
            </list>
        </property>
    </bean>
</beans>