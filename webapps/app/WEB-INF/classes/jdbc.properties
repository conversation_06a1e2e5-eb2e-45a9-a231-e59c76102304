########################## COMMON ##########################
jdbc.schema.default=
jdbc.datasource=com.mchange.v2.c3p0.ComboPooledDataSource
hibernate.showsql=false

########################## JDBC Oracle ##########################
#jdbc.dialect=org.hibernate.dialect.Oracle10gDialect
#jdbc.driverClassName=oracle.jdbc.driver.OracleDriver
#jdbc.url=****************************************
#jdbc.username=jats001
#jdbc.password=jats001
#jdbc.default_schema=${jdbc.username}

########################## JDBC MySQL ##########################
jdbc.dbname=atsdb
jdbc.dialect=org.hibernate.dialect.MySQL5Dialect
jdbc.driverClassName=com.mysql.cj.jdbc.Driver
jdbc.url=*******************************/${jdbc.dbname}?useSSL=false&serverTimezone=GMT
jdbc.username=avatar
jdbc.password=fingard@1
jdbc.default_schema=${jdbc.dbname}

########################## JDBC PostgreSQL ##########################
#jdbc.dialect=org.hibernate.dialect.PostgreSQLDialect
#jdbc.driverClassName=org.postgresql.Driver
#jdbc.url=***************************************
#jdbc.username=postgres
#jdbc.password=123456
#jdbc.default_schema=public