<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd">


	<!-- <bean id="iRemoteTest"
	    	class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
	    <property name="serviceUrl" value="http://localhost:8087/aaa/remoting/remoteservice" />
	    <property name="serviceInterface" value="com.fingard.app.delegate.service.IRemoteService" />
	</bean> -->

	<!--
	<bean id="userService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${serviceUrl}${serviceName}/user" />
		<property name="serviceInterface" value="com.fingard.app.delegate.service.UserService" />
	</bean>

	<bean id="flowService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
		<property name="serviceUrl" value="${serviceUrl}${serviceName}/flow" />
		<property name="serviceInterface" value="com.fingard.app.delegate.service.FlowService" />
	</bean>
	-->

</beans>
