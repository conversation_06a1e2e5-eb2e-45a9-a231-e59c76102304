<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:jee="http://www.springframework.org/schema/jee" xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="
      http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
      http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
      http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd
      http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee-2.5.xsd
      http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">


	<bean id="commonService" class="com.fingard.app.delegate.core.service.impl.CommonServiceImpl" autowire="byName"/>
	<bean id="deviceService" class="com.fingard.app.delegate.core.service.impl.DeviceServiceImpl" autowire="byName"/>
	<bean id="systemService" class="com.fingard.app.delegate.core.service.impl.SystemServiceImpl" autowire="byName"/>
	<bean id="messageService" class="com.fingard.app.delegate.core.service.impl.MessageServiceImpl" init-method="init" autowire="byName"/>
	<bean id="messageSendService" class="com.fingard.app.delegate.core.service.impl.BrMessageSendServiceImpl" autowire="byName"/>
	<bean id="reimbursementService" class="com.fingard.app.delegate.core.service.impl.ReimbursementServiceImpl" autowire="byName"/>
	<bean id="systemUserService" class="com.fingard.app.delegate.core.service.impl.SystemUserServiceImpl" autowire="byName"/>
	<bean id="syscommonService" class="com.fingard.app.delegate.core.service.impl.SysCommonServiceImpl" autowire="byName"/>
	<bean id="wechatService" class="com.fingard.app.delegate.core.service.impl.WechatServiceImpl" autowire="byName"/>
	<bean id="parameterService" class="com.fingard.app.delegate.core.service.impl.ParameterServiceImpl" autowire="byName"/>


</beans>
