<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
      http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd


      http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">

    <!-- 配置placeholder -->
    <bean id="propertyConfigurer"
          class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>classpath:parameter.properties</value>
                <value>classpath:jdbc.properties</value>
            </list>
        </property>
        <property name="fileEncoding" value="utf-8"/>
    </bean>

    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="maxUploadSize" value="10000000"/>
    </bean>
    <!-- SpringMVC在超出上传文件限制时，会抛出org.springframework.web.multipart.MaxUploadSizeExceededException -->
    <!-- 该异常是SpringMVC在检查上传的文件信息时抛出来的，而且此时还没有进入到Controller方法中 -->
    <bean id="exceptionResolver" class="org.springframework.web.servlet.handler.SimpleMappingExceptionResolver">
        <property name="exceptionMappings">
            <props>
                <!-- 遇到MaxUploadSizeExceededException异常时，自动跳转到/WEB-INF/jsp/error_fileupload.jsp页面 -->
                <prop key="org.springframework.web.multipart.MaxUploadSizeExceededException">error_fileupload</prop>
            </props>
        </property>
    </bean>
    <bean class="com.fingard.app.delegate.framework.util.Globals"/>

    <!-- clob字段处理 -->
    <bean id="lobHandler" class="org.springframework.jdbc.support.lob.DefaultLobHandler" lazy-init="true"/>
    <bean id="oracleLobHandler" class="org.springframework.jdbc.support.lob.OracleLobHandler"
          lazy-init="true">
        <property name="nativeJdbcExtractor">
            <bean class="org.springframework.jdbc.support.nativejdbc.C3P0NativeJdbcExtractor"/>
        </property>
    </bean>

    <!-- 数据源配置,在weblogic/websphere生产环境使用应用服务器的数据库连接池-->
    <!-- <bean id="dataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName" value="ats" />
    </bean> -->

    <!-- 配置dataSource连接池 -->
    <bean id="dataSource" class="${jdbc.datasource}"
          destroy-method="close">
        <property name="driverClass" value="${jdbc.driverClassName}"/>
        <property name="jdbcUrl" value="${jdbc.url}"/>
        <property name="user" value="${jdbc.username}"/>
        <property name="password" value="${jdbc.password}"/>
        <property name="maxPoolSize" value="10"/>
        <property name="minPoolSize" value="3"/>
        <property name="maxIdleTime" value="180"/>
    </bean>

    <!-- 数据源配置,在weblogic/websphere生产环境使用应用服务器的数据库连接池-->
    <!-- <bean id="dataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName" value="ats" />
    </bean> -->

    <!-- 数据源配置,在tomcat生产环境使用应用服务器的数据库连接池
    <bean id="dataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName" value="java:/comp/env/TxDatasource" />
	</bean> -->

    <!-- 配置sessionFactory -->
    <bean id="sessionFactory"
          class="com.fingard.app.delegate.framework.util.AnnotationSessionFactoryBean">
        <property name="defaultSchema" value="${jdbc.schema.default}"/>
        <property name="customizedSchema">
            <map/>
        </property>
        <property name="dataSource" ref="dataSource"/>
        <property name="lobHandler" ref="oracleLobHandler"/>
        <property name="hibernateProperties">
            <value>
                hibernate.dialect=${jdbc.dialect}
                hibernate.jndi.class=com.yy.msg.framework.common.util.MockContextFactory
                hibernate.show_sql=${hibernate.showsql}
                hibernate.format_sql=false
                hibernate.hbm2ddl.auto=update
                hibernate.default_schema=${jdbc.default_schema}
            </value>

        </property>
        <property name="packagesToScan">
            <list>
                <value>com.fingard.app.delegate.core.model</value>
                <value>com.fingard.app.delegate.core.model.*</value>
            </list>
        </property>
    </bean>

    <bean id="exceptionFilterAspect" class="com.fingard.app.delegate.aspect.ExceptionFilterAspect"/>
    <bean id="operationRecordAspect" class="com.fingard.app.delegate.aspect.OperationRecordAspect"/>

    <bean id="remoteSessionFilterAspect" class="com.fingard.app.delegate.aspect.RemoteSessionFilterAspect"/>
    <bean id="loginFilterAspect" class="com.fingard.app.delegate.aspect.LoginFilterAspect"/>
    <aop:config>
        <aop:aspect id="logAspect" ref="remoteSessionFilterAspect">
            <aop:pointcut id="logPointCut" expression="execution(* com.fingard.app.delegate.service..*.* (..)))"/>
            <aop:around method="aspectService" pointcut-ref="logPointCut"/>
        </aop:aspect>
        <aop:aspect id="loginAspect" ref="loginFilterAspect">
            <aop:pointcut id="loginPointCut"
                          expression="execution(* com.fingard.app.delegate.core.service.SystemUserService.* (..)))"/>
            <aop:around method="aspectService" pointcut-ref="loginPointCut"/>
        </aop:aspect>
    </aop:config>

    <bean id="transactionManager" class="org.springframework.orm.hibernate3.HibernateTransactionManager">
        <property name="dataSource" ref="dataSource"/>
        <property name="sessionFactory" ref="sessionFactory"/>
    </bean>
    <!-- <bean id="hibernateTransactionTemplate" class="org.springframework.transaction.support.TransactionTemplate">
    <property name="transactionManager" ref="transactionManager"/>
    </bean> -->
    <tx:advice id="txAdvice">
        <tx:attributes>
            <tx:method name="*" rollback-for="com.fingard.app.delegate.exception.TaskRollException"/>
            <tx:method name="toTransaction" propagation="REQUIRES_NEW"
                       rollback-for="com.fingard.app.delegate.exception.TaskRollException"/>
        </tx:attributes>
    </tx:advice>
    <aop:config>
        <aop:advisor pointcut="execution(* com.fingard.app.delegate.core.service..*.* (..))" advice-ref="txAdvice"/>
    </aop:config>
</beans>
