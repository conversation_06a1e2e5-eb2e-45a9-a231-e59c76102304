<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd">


    <bean id="commonDao" class="com.fingard.app.delegate.core.dao.impl.CommonDaoImpl" autowire="byName"/>
    <bean id="deviceDao" class="com.fingard.app.delegate.core.dao.impl.DeviceDaoImpl" autowire="byName"/>
    <bean id="messageDao" class="com.fingard.app.delegate.core.dao.impl.MessageDaoImpl" autowire="byName"/>
    <bean id="systemUserDao" class="com.fingard.app.delegate.core.dao.impl.SystemUserDaoImpl" autowire="byName"/>
    <bean id="reimbursementDao" class="com.fingard.app.delegate.core.dao.impl.ReimbursementDaoImpl" autowire="byName"/>
    <bean id="systemDao" class="com.fingard.app.delegate.core.dao.impl.SystemDaoImpl" autowire="byName"/>
    <bean id="syscommonDao" class="com.fingard.app.delegate.core.dao.impl.SysCommonDaoImpl" autowire="byName"/>
    <bean id="wechatDao" class="com.fingard.app.delegate.core.dao.impl.WechatDaoImpl" autowire="byName"/>
    <bean id="parameterDao" class="com.fingard.app.delegate.core.dao.impl.ParameterDaoImpl" autowire="byName"/>


</beans>
