/**
 * UserDataService实现类
 * 用户数据业务逻辑实现
 */
package com.fingard.app.delegate.service.impl;

import com.fingard.app.delegate.service.UserDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Transactional
public class UserDataServiceImpl implements UserDataService {
    
    @Autowired
    private UserDataDao userDataDao;
    
    @Override
    public boolean createUserData(Map<String, Object> userData) throws Exception {
        // 数据验证
        if (!validateUserData(userData)) {
            throw new Exception("用户数据验证失败");
        }
        
        // 检查用户ID是否已存在
        String userId = (String) userData.get("userId");
        Map<String, Object> existingUser = userDataDao.getUserDataById(userId);
        if (existingUser != null) {
            throw new Exception("用户ID已存在: " + userId);
        }
        
        // 保存数据
        int rows = userDataDao.saveUserData(userData);
        return rows > 0;
    }
    
    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getUserDataById(String userId) throws Exception {
        if (userId == null || userId.trim().isEmpty()) {
            throw new Exception("用户ID不能为空");
        }
        
        return userDataDao.getUserDataById(userId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getAllUserData() throws Exception {
        return userDataDao.getAllUserData();
    }
    
    @Override
    public boolean updateUserData(Map<String, Object> userData) throws Exception {
        // 数据验证
        if (!validateUserData(userData)) {
            throw new Exception("用户数据验证失败");
        }
        
        // 检查用户是否存在
        String userId = (String) userData.get("userId");
        Map<String, Object> existingUser = userDataDao.getUserDataById(userId);
        if (existingUser == null) {
            throw new Exception("用户不存在: " + userId);
        }
        
        // 更新数据
        int rows = userDataDao.updateUserData(userData);
        return rows > 0;
    }
    
    @Override
    public boolean deleteUserData(String userId) throws Exception {
        if (userId == null || userId.trim().isEmpty()) {
            throw new Exception("用户ID不能为空");
        }
        
        // 检查用户是否存在
        Map<String, Object> existingUser = userDataDao.getUserDataById(userId);
        if (existingUser == null) {
            throw new Exception("用户不存在: " + userId);
        }
        
        // 删除数据
        int rows = userDataDao.deleteUserData(userId);
        return rows > 0;
    }
    
    @Override
    public boolean batchCreateUserData(List<Map<String, Object>> userDataList) throws Exception {
        if (userDataList == null || userDataList.isEmpty()) {
            throw new Exception("用户数据列表不能为空");
        }
        
        // 逐个验证数据
        for (Map<String, Object> userData : userDataList) {
            if (!validateUserData(userData)) {
                throw new Exception("批量数据中存在无效数据");
            }
        }
        
        // 批量插入
        int[] results = userDataDao.batchInsertUserData(userDataList);
        return results.length > 0;
    }
    
    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getUserDataByPage(int page, int pageSize) throws Exception {
        if (page < 1 || pageSize < 1) {
            throw new Exception("页码和每页大小必须大于0");
        }
        
        int offset = (page - 1) * pageSize;
        List<Map<String, Object>> dataList = userDataDao.getUserDataByPage(offset, pageSize);
        int totalCount = userDataDao.getUserDataCount();
        
        Map<String, Object> result = new HashMap<>();
        result.put("dataList", dataList);
        result.put("totalCount", totalCount);
        result.put("currentPage", page);
        result.put("pageSize", pageSize);
        result.put("totalPages", (int) Math.ceil((double) totalCount / pageSize));
        
        return result;
    }
    
    @Override
    public boolean validateUserData(Map<String, Object> userData) {
        if (userData == null) return false;
        
        // 验证必需字段
        String userId = (String) userData.get("userId");
        String name = (String) userData.get("name");
        String email = (String) userData.get("email");
        
        if (userId == null || userId.trim().isEmpty()) return false;
        if (name == null || name.trim().isEmpty()) return false;
        if (email == null || email.trim().isEmpty()) return false;
        
        // 验证邮箱格式
        if (!email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$")) {
            return false;
        }
        
        // 验证用户ID格式（只允许字母数字下划线）
        if (!userId.matches("^[a-zA-Z0-9_]+$")) {
            return false;
        }
        
        // 验证姓名长度
        if (name.length() > 50) return false;
        
        // 验证手机号格式（如果提供）
        String phone = (String) userData.get("phone");
        if (phone != null && !phone.trim().isEmpty()) {
            if (!phone.matches("^1[3-9]\\d{9}$")) {
                return false;
            }
        }
        
        return true;
    }
} 