/**
 * UserDataService接口
 * 用户数据业务逻辑接口
 */
package com.fingard.app.delegate.service;

import java.util.List;
import java.util.Map;

public interface UserDataService {
    /**
     * 创建用户数据
     */
    boolean createUserData(Map<String, Object> userData) throws Exception;
    
    /**
     * 根据用户ID获取用户数据
     */
    Map<String, Object> getUserDataById(String userId) throws Exception;
    
    /**
     * 获取所有用户数据
     */
    List<Map<String, Object>> getAllUserData() throws Exception;
    
    /**
     * 更新用户数据
     */
    boolean updateUserData(Map<String, Object> userData) throws Exception;
    
    /**
     * 删除用户数据
     */
    boolean deleteUserData(String userId) throws Exception;
    
    /**
     * 批量创建用户数据
     */
    boolean batchCreateUserData(List<Map<String, Object>> userDataList) throws Exception;
    
    /**
     * 分页查询用户数据
     */
    Map<String, Object> getUserDataByPage(int page, int pageSize) throws Exception;
    
    /**
     * 验证用户数据
     */
    boolean validateUserData(Map<String, Object> userData);
} 