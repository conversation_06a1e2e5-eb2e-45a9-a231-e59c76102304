/**
 * UserDataDao接口
 * 用户数据访问对象接口
 */
package com.fingard.app.delegate.dao;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

public interface UserDataDao {
    /**
     * 保存用户数据
     */
    int saveUserData(Map<String, Object> userData) throws SQLException;
    
    /**
     * 根据用户ID查询用户数据
     */
    Map<String, Object> getUserDataById(String userId) throws SQLException;
    
    /**
     * 查询所有用户数据
     */
    List<Map<String, Object>> getAllUserData() throws SQLException;
    
    /**
     * 更新用户数据
     */
    int updateUserData(Map<String, Object> userData) throws SQLException;
    
    /**
     * 删除用户数据
     */
    int deleteUserData(String userId) throws SQLException;
    
    /**
     * 批量插入用户数据
     */
    int[] batchInsertUserData(List<Map<String, Object>> userDataList) throws SQLException;
    
    /**
     * 分页查询用户数据
     */
    List<Map<String, Object>> getUserDataByPage(int offset, int limit) throws SQLException;
    
    /**
     * 统计用户数据总数
     */
    int getUserDataCount() throws SQLException;
} 