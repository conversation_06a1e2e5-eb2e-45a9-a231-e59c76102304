/**
 * 财务报表DAO接口
 * 处理复杂的财务数据查询
 */
package com.fingard.app.delegate.dao;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

public interface FinancialReportDao {
    /**
     * 流出资金构成查询
     * @param ym 年月参数，格式：YYYY-MM
     */
    List<Map<String, Object>> getOutflowComposition(String ym) throws SQLException;
    
    /**
     * 流入资金构成查询  
     */
    List<Map<String, Object>> getInflowComposition(String ym) throws SQLException;
    
    /**
     * 资金流向汇总
     */
    Map<String, Object> getCashFlowSummary(String ym) throws SQLException;
    
    /**
     * 按银行统计资金流向
     */
    List<Map<String, Object>> getCashFlowByBank(String ym) throws SQLException;
    
    /**
     * 获取财务详细数据
     */
    List<Map<String, Object>> getFinancialDetailData(String ym, String type) throws SQLException;
} 