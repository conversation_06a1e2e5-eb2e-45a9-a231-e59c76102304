/**
 * 财务报表Service接口
 * 提供财务报表相关的业务逻辑
 */
package com.fingard.app.delegate.service;

import java.util.List;
import java.util.Map;

public interface FinancialReportService {
    /**
     * 获取资金流出构成报表
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    List<Map<String, Object>> getOutflowCompositionReport(String startTime, String endTime) throws Exception;
    
    /**
     * 获取资金流入构成报表
     */
    List<Map<String, Object>> getInflowCompositionReport(String yearMonth) throws Exception;
    
    /**
     * 获取资金流向汇总报表
     */
    Map<String, Object> getCashFlowSummaryReport(String yearMonth) throws Exception;
    
    /**
     * 获取按银行统计的资金流向报表
     */
    List<Map<String, Object>> getCashFlowByBankReport(String yearMonth) throws Exception;
    
    /**
     * 获取综合财务报表（包含所有维度）
     */
    Map<String, Object> getComprehensiveFinancialReport(String yearMonth) throws Exception;
    
    /**
     * 验证年月参数格式
     */
    boolean validateYearMonth(String yearMonth);
} 