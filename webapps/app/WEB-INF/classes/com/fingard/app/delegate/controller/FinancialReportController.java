/**
 * 财务报表Controller
 * 处理财务报表相关的HTTP请求
 */
package com.fingard.app.delegate.controller;

import com.fingard.app.delegate.service.FinancialReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.alibaba.fastjson.JSON;

@Controller
@RequestMapping("/financial")
public class FinancialReportController {
    
    @Autowired
    private FinancialReportService financialReportService;
    
    /**
     * 设置响应头为JSON格式
     */
    private void setJsonResponse(HttpServletResponse response) {
        response.setContentType("application/json;charset=UTF-8");
        response.setHeader("Cache-Control", "no-cache");
    }
    
    /**
     * 获取资金流出构成报表
     */
    @RequestMapping(value = "/getOutflowComposition.do", method = RequestMethod.POST)
    public void getOutflowComposition(HttpServletResponse response, String startTime, String endTime) throws Exception {
        setJsonResponse(response);
        PrintWriter out = response.getWriter();

        try {
            if (startTime == null || endTime == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "时间参数不完整");
                out.print(JSON.toJSONString(result));
                return;
            }

            List<Map<String, Object>> data = financialReportService.getOutflowCompositionReport(startTime, endTime);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "查询成功");
            result.put("data", data);
            result.put("startTime", startTime);
            result.put("endTime", endTime);

            out.print(JSON.toJSONString(result));
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
            out.print(JSON.toJSONString(result));
        } finally {
            out.flush();
            out.close();
        }
    }
    
    /**
     * 获取资金流入构成报表
     */
    @RequestMapping(value = "/getInflowComposition.do", method = RequestMethod.POST)
    public void getInflowComposition(HttpServletResponse response, String yearMonth) throws Exception {
        setJsonResponse(response);
        PrintWriter out = response.getWriter();
        
        try {
            List<Map<String, Object>> data = financialReportService.getInflowCompositionReport(yearMonth);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "查询成功");
            result.put("data", data);
            result.put("yearMonth", yearMonth);
            
            out.print(JSON.toJSONString(result));
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
            out.print(JSON.toJSONString(result));
        } finally {
            out.flush();
            out.close();
        }
    }
    
    /**
     * 获取资金流向汇总报表
     */
    @RequestMapping(value = "/getCashFlowSummary.do", method = RequestMethod.POST)
    public void getCashFlowSummary(HttpServletResponse response, String yearMonth) throws Exception {
        setJsonResponse(response);
        PrintWriter out = response.getWriter();
        
        try {
            Map<String, Object> data = financialReportService.getCashFlowSummaryReport(yearMonth);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "查询成功");
            result.put("data", data);
            
            out.print(JSON.toJSONString(result));
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
            out.print(JSON.toJSONString(result));
        } finally {
            out.flush();
            out.close();
        }
    }
    
    /**
     * 获取综合财务报表
     */
    @RequestMapping(value = "/getComprehensiveReport.do", method = RequestMethod.POST)
    public void getComprehensiveReport(HttpServletResponse response, String yearMonth) throws Exception {
        setJsonResponse(response);
        PrintWriter out = response.getWriter();
        
        try {
            Map<String, Object> data = financialReportService.getComprehensiveFinancialReport(yearMonth);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "查询成功");
            result.put("data", data);
            
            out.print(JSON.toJSONString(result));
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
            out.print(JSON.toJSONString(result));
        } finally {
            out.flush();
            out.close();
        }
    }
    
    /**
     * 获取按银行统计的资金流向报表
     */
    @RequestMapping(value = "/getCashFlowByBank.do", method = RequestMethod.POST)
    public void getCashFlowByBank(HttpServletResponse response, String yearMonth) throws Exception {
        setJsonResponse(response);
        PrintWriter out = response.getWriter();
        
        try {
            List<Map<String, Object>> data = financialReportService.getCashFlowByBankReport(yearMonth);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "查询成功");
            result.put("data", data);
            result.put("yearMonth", yearMonth);
            
            out.print(JSON.toJSONString(result));
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
            out.print(JSON.toJSONString(result));
        } finally {
            out.flush();
            out.close();
        }
    }
} 