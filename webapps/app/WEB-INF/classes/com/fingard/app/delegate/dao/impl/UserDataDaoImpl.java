/**
 * UserDataDao实现类
 * 使用第二数据库进行用户数据操作
 */
package com.fingard.app.delegate.dao.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class UserDataDaoImpl implements UserDataDao {
    
    @Autowired
    @Qualifier("dataSource2")
    private DataSource dataSource2;
    
    @Override
    public int saveUserData(Map<String, Object> userData) throws SQLException {
        String sql = """
            INSERT INTO user_data (user_id, name, email, phone, address, create_time) 
            VALUES (?, ?, ?, ?, ?, NOW())
            """;
        
        try (Connection conn = dataSource2.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            pstmt.setString(1, (String) userData.get("userId"));
            pstmt.setString(2, (String) userData.get("name"));
            pstmt.setString(3, (String) userData.get("email"));
            pstmt.setString(4, (String) userData.get("phone"));
            pstmt.setString(5, (String) userData.get("address"));
            
            int rows = pstmt.executeUpdate();
            
            // 获取生成的主键
            if (rows > 0) {
                try (ResultSet rs = pstmt.getGeneratedKeys()) {
                    if (rs.next()) {
                        userData.put("id", rs.getInt(1));
                    }
                }
            }
            
            return rows;
        }
    }
    
    @Override
    public Map<String, Object> getUserDataById(String userId) throws SQLException {
        String sql = "SELECT * FROM user_data WHERE user_id = ?";
        
        try (Connection conn = dataSource2.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, userId);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return resultSetToMap(rs);
                }
            }
        }
        return null;
    }
    
    @Override
    public List<Map<String, Object>> getAllUserData() throws SQLException {
        String sql = "SELECT * FROM user_data ORDER BY create_time DESC";
        
        try (Connection conn = dataSource2.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            return resultSetToList(rs);
        }
    }
    
    @Override
    public int updateUserData(Map<String, Object> userData) throws SQLException {
        String sql = """
            UPDATE user_data 
            SET name = ?, email = ?, phone = ?, address = ?, update_time = NOW() 
            WHERE user_id = ?
            """;
        
        try (Connection conn = dataSource2.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, (String) userData.get("name"));
            pstmt.setString(2, (String) userData.get("email"));
            pstmt.setString(3, (String) userData.get("phone"));
            pstmt.setString(4, (String) userData.get("address"));
            pstmt.setString(5, (String) userData.get("userId"));
            
            return pstmt.executeUpdate();
        }
    }
    
    @Override
    public int deleteUserData(String userId) throws SQLException {
        String sql = "DELETE FROM user_data WHERE user_id = ?";
        
        try (Connection conn = dataSource2.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, userId);
            return pstmt.executeUpdate();
        }
    }
    
    @Override
    public int[] batchInsertUserData(List<Map<String, Object>> userDataList) throws SQLException {
        String sql = """
            INSERT INTO user_data (user_id, name, email, phone, address, create_time) 
            VALUES (?, ?, ?, ?, ?, NOW())
            """;
        
        try (Connection conn = dataSource2.getConnection()) {
            conn.setAutoCommit(false);
            
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                for (Map<String, Object> userData : userDataList) {
                    pstmt.setString(1, (String) userData.get("userId"));
                    pstmt.setString(2, (String) userData.get("name"));
                    pstmt.setString(3, (String) userData.get("email"));
                    pstmt.setString(4, (String) userData.get("phone"));
                    pstmt.setString(5, (String) userData.get("address"));
                    pstmt.addBatch();
                }
                
                int[] results = pstmt.executeBatch();
                conn.commit();
                return results;
            } catch (SQLException e) {
                conn.rollback();
                throw e;
            }
        }
    }
    
    @Override
    public List<Map<String, Object>> getUserDataByPage(int offset, int limit) throws SQLException {
        String sql = "SELECT * FROM user_data ORDER BY create_time DESC LIMIT ?, ?";
        
        try (Connection conn = dataSource2.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, offset);
            pstmt.setInt(2, limit);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                return resultSetToList(rs);
            }
        }
    }
    
    @Override
    public int getUserDataCount() throws SQLException {
        String sql = "SELECT COUNT(*) FROM user_data";
        
        try (Connection conn = dataSource2.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            if (rs.next()) {
                return rs.getInt(1);
            }
        }
        return 0;
    }
    
    /**
     * 工具方法：将ResultSet转换为Map
     */
    private Map<String, Object> resultSetToMap(ResultSet rs) throws SQLException {
        Map<String, Object> map = new HashMap<>();
        map.put("id", rs.getInt("id"));
        map.put("userId", rs.getString("user_id"));
        map.put("name", rs.getString("name"));
        map.put("email", rs.getString("email"));
        map.put("phone", rs.getString("phone"));
        map.put("address", rs.getString("address"));
        map.put("createTime", rs.getTimestamp("create_time"));
        map.put("updateTime", rs.getTimestamp("update_time"));
        return map;
    }
    
    /**
     * 工具方法：将ResultSet转换为List
     */
    private List<Map<String, Object>> resultSetToList(ResultSet rs) throws SQLException {
        List<Map<String, Object>> list = new ArrayList<>();
        while (rs.next()) {
            list.add(resultSetToMap(rs));
        }
        return list;
    }
} 