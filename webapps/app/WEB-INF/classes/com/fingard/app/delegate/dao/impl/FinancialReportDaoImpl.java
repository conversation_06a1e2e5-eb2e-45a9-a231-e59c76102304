/**
 * 财务报表DAO实现类
 * 处理复杂的财务数据查询，支持双数据源
 */
package com.fingard.app.delegate.dao.impl;

import com.fingard.app.delegate.dao.FinancialReportDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class FinancialReportDaoImpl implements FinancialReportDao {
    
    @Autowired
    @Qualifier("dataSource")  // 使用主数据库查询财务数据
    private DataSource mainDataSource;
    
    @Autowired
    @Qualifier("dataSource2") // 使用第二数据库缓存报表结果
    private DataSource cacheDataSource;
    
    @Override
    public List<Map<String, Object>> getOutflowComposition(String ym) throws SQLException {
        // 流出资金构成SQL - 适配原始SQL查询
        String sql = """
            SELECT capitalcategoryname as 资金流出类型, moneyway, sum(amount) 金额
            FROM (
                SELECT
                   ba.name,
                    t2.name paytype,-- 交易类型
                    t.paymadedate,
                    t3.name currencyname,
                     t2.moneyway ,ROUND(CASE currencyid
                                WHEN
                    '4'
                THEN
                t.ouramount* ( -- sourcecurrencyid为外币ID,rate为折本位币汇率,本位币默认为人民币
         SELECT
                t2.rate
         FROM (-- 排序顺序：汇率时间最近优先
                  SELECT t1.sourcecurrencyid,
                         t1.rate,
                         row_number() over ( partition BY sourcecurrencyid ORDER BY ratedate DESC ) rn
                  FROM (
                           SELECT t1.rate,
                                  cp.sourcecurrencyid, -- 基准货币
                                  t1.ratedate
                           FROM t_bd_currencyrates t1
                                    LEFT JOIN t_bd_currencypairs cp ON t1.currencypairid = cp.urid
                                    LEFT JOIN t_bd_currencyratetypes crt ON crt.urid = t1.currencyratetypeid
                           WHERE t1.ratedate <= sysdate()

                             AND cp.targetcecurrencyid = ( -- 询价货币(标价货币)
                               SELECT c.urid  -- 询价货币得是本位币
                               FROM t_bd_paramval p
                                        LEFT JOIN t_bd_currencies c ON p.value = c.code
                               WHERE   p.tenantid = '10001'
                                 AND p.paramdefid = (SELECT urid FROM t_bd_paramdef WHERE code = 'SET025')) -- 取系统参数"本位币"

                             AND t1.bankid = '1'
                             AND crt.isdefault = '1' -- 取默认汇率类型
                             AND t1.isactive = '1'
                             AND cp.isactive = '1'
                           ORDER BY ratedate DESC
                       ) t1
              ) t2
         WHERE rn = 1   AND currencyid= t2.sourcecurrencyid
                      )
                ELSE  t.ouramount
            END, 2) amount,

     tbu.name budgetitemname,-- 计划项目ID
    t5.name capitalcategoryname,-- 资金类别
    t6.c_caption paystatename
FROM
    T_SE_PAYMENTS t
        LEFT OUTER JOIN
    TSYS_ORGANIZATION t1 ON t.orgid = t1.org_id
        LEFT OUTER JOIN
    T_SE_PAYTYPES t2 ON t.paytypeid = t2.urid
        LEFT OUTER JOIN
    T_BD_CURRENCIES t3 ON t.ourcurrencyid = t3.urid
        LEFT OUTER JOIN
    TSYS_ORGANIZATION t4 ON t.ourorgid = t4.org_id
        LEFT OUTER JOIN
    T_BD_CATEGORIES t5 ON t.CAPITALCATEGORYID = t5.urid
        LEFT OUTER JOIN
    tdictionary t6 ON t.paystate = t6.c_keyvalue
        LEFT OUTER JOIN
    tsys_user tu ON t.createdby = tu.user_id
        LEFT OUTER JOIN
    T_BU_BUDGETITEMS tbu ON t.budgetitemid = tbu.urid
     LEFT JOIN t_sy_banks ba ON ba.urid = t.ourbankid
WHERE 1=1
        AND (? IS NULL OR ? = '' OR substring(paymadedate,1,7) = ?)
        and t6.l_keyno = '1008'-- 支付状态
        AND t.paystate = '2'-- 审批状态：已审批
        AND t.cancelstate = '1' -- 作废状态：未作废
 ) aaaa
 where capitalcategoryname is not null
 group by   capitalcategoryname, moneyway
 ORDER BY 金额 DESC
            """;

        try (Connection conn = mainDataSource.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, ym);
            pstmt.setString(2, ym);
            pstmt.setString(3, ym);

            return executeQuery(pstmt);
        }
    }
    
    @Override
    public List<Map<String, Object>> getInflowComposition(String ym) throws SQLException {
        // 资金流入构成SQL
        String sql = """
            SELECT capitalcategoryname as 资金流入类型, moneyway, sum(amount) 金额  
            FROM (
                SELECT 
                   ba.name,
                    t2.name paytype,
                    t.recmadedate as paymadedate,
                    t3.name currencyname,
                     t2.moneyway ,ROUND(CASE t.ourcurrencyid
                                        WHEN '4' THEN
                        t.ouramount * 1.0
                        ELSE  t.ouramount
                    END, 2) amount,
                    tbu.name budgetitemname,
                    t5.name capitalcategoryname,
                    t6.c_caption paystatename
                FROM T_SE_RECMENTS t
                    LEFT OUTER JOIN TSYS_ORGANIZATION t1 ON t.orgid = t1.org_id
                    LEFT OUTER JOIN T_SE_PAYTYPES t2 ON t.paytypeid = t2.urid
                    LEFT OUTER JOIN T_BD_CURRENCIES t3 ON t.ourcurrencyid = t3.urid
                    LEFT OUTER JOIN TSYS_ORGANIZATION t4 ON t.ourorgid = t4.org_id
                    LEFT OUTER JOIN T_BD_CATEGORIES t5 ON t.capitalcategoryid = t5.urid
                    LEFT OUTER JOIN tdictionary t6 ON t.paystate = t6.c_keyvalue
                    LEFT OUTER JOIN tsys_user tu ON t.createdby = tu.user_id
                    LEFT OUTER JOIN T_BU_BUDGETITEMS tbu ON t.budgetitemid = tbu.urid
                    LEFT JOIN t_sy_banks ba ON ba.urid = t.ourbankid
                WHERE 1=1
                    AND (? IS NULL OR ? = '' OR substring(recmadedate,1,7) = ?)
                    and t6.l_keyno = '1008'
                    AND t.paystate = '2'
                    AND t.cancelstate = '1'
             ) aaaa
             WHERE capitalcategoryname IS NOT NULL
             GROUP BY capitalcategoryname, moneyway
             ORDER BY 金额 DESC
            """;
        
        try (Connection conn = mainDataSource.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, ym);
            pstmt.setString(2, ym);
            pstmt.setString(3, ym);
            
            return executeQuery(pstmt);
        }
    }
    
    @Override
    public Map<String, Object> getCashFlowSummary(String ym) throws SQLException {
        Map<String, Object> summary = new HashMap<>();
        
        // 汇总流出金额
        List<Map<String, Object>> outflowData = getOutflowComposition(ym);
        double totalOutflow = outflowData.stream()
            .mapToDouble(item -> ((Number) item.get("金额")).doubleValue())
            .sum();
        
        // 汇总流入金额
        List<Map<String, Object>> inflowData = getInflowComposition(ym);
        double totalInflow = inflowData.stream()
            .mapToDouble(item -> ((Number) item.get("金额")).doubleValue())
            .sum();
        
        summary.put("totalOutflow", totalOutflow);
        summary.put("totalInflow", totalInflow);
        summary.put("netFlow", totalInflow - totalOutflow);
        summary.put("outflowCount", outflowData.size());
        summary.put("inflowCount", inflowData.size());
        summary.put("reportDate", ym);
        
        return summary;
    }
    
    @Override
    public List<Map<String, Object>> getCashFlowByBank(String ym) throws SQLException {
        String sql = """
            SELECT 
                ba.name as 银行名称,
                COUNT(*) as 交易笔数,
                SUM(amount) as 总金额
            FROM (
                SELECT ba.name, 
                       ROUND(t.ouramount, 2) as amount
                FROM T_SE_PAYMENTS t
                LEFT JOIN t_sy_banks ba ON ba.urid = t.ourbankid
                LEFT OUTER JOIN tdictionary t6 ON t.paystate = t6.c_keyvalue
                WHERE (? IS NULL OR ? = '' OR substring(paymadedate,1,7) = ?)
                AND t6.l_keyno = '1008' AND t.paystate = '2' AND t.cancelstate = '1'
                AND ba.name IS NOT NULL
            ) t
            GROUP BY ba.name
            ORDER BY 总金额 DESC
            """;
        
        try (Connection conn = mainDataSource.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, ym);
            pstmt.setString(2, ym);
            pstmt.setString(3, ym);
            
            return executeQuery(pstmt);
        }
    }
    
    @Override
    public List<Map<String, Object>> getFinancialDetailData(String ym, String type) throws SQLException {
        if ("outflow".equals(type)) {
            return getOutflowComposition(ym);
        } else if ("inflow".equals(type)) {
            return getInflowComposition(ym);
        } else if ("bank".equals(type)) {
            return getCashFlowByBank(ym);
        }
        return new ArrayList<>();
    }
    
    /**
     * 执行查询
     */
    private List<Map<String, Object>> executeQuery(PreparedStatement pstmt) throws SQLException {
        List<Map<String, Object>> results = new ArrayList<>();
        
        try (ResultSet rs = pstmt.executeQuery()) {
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            
            while (rs.next()) {
                Map<String, Object> row = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnLabel(i);
                    Object value = rs.getObject(i);
                    row.put(columnName, value);
                }
                results.add(row);
            }
        }
        
        return results;
    }
} 