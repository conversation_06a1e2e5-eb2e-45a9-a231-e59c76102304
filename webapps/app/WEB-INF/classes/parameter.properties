# æ ç¹æ®è¯´ææ éä¿®æ¹ï¼åèï¼https://alidocs.dingtalk.com/i/nodes/93NwLYZXWyg4znZaIdxrqbGGJkyEqBQm
currentCustomerName=corpwx
customerCode=fingard
#æ¯å¦å¼å¯é»ååç®¡ç:é»è®¤å³é­
blackList=false
#é»è®¤æ¯å¦åå«ä¸çº§ç»ç»
default.includeSub=false
#æ åå¯é¥
standard.desKey=fingard_ats
standard.aesKey=m5wrfzrdcclpuf32
# å®¢æ·åºç¨ç±»åï¼
# æ¯å¦å¾®ä¿¡å¹³å°ä¸å¼å¯ç»å®è´¦æ·æ¨¡å¼
# å¼å¯åè¯·æ£æ¥wechat.properties
weChat=false
#èæ¥éä¸ä¼ éä»¶å­æ¾å°å
saveUploadFilePath=D:/app-rbx/tmp
#appæ¶æ¯åéæ¶ï¼åå°ä¼ è¿æ¥çéªè¯è´¦å·å¯ç ï¼éè¦base64ç¼ç 
username=ZmluZ2FyZA==
password=ZmluZ2FyZDEyMw==
#æåæ¨é
#è´¢èµå¤§è
jPush.appKey=3f63f1bc69da7bf0f7bba2ec
jPush.masterSecret=34b5a7bbaae840793af59705
#jPush.appKey=cb05c78ff6f55212fe07fe03
#jPush.masterSecret=e08c630af217c93905edcedd
#ä¼éè
#jPush.appKey=357d45aa0da2d2caf0d1388e
#jPush.masterSecret=17e95bfb95bfb91327bedfc6
#ATS-ESå°å
# 2.0åèå°åï¼http://ip:port/webservice/outService.ws?wsdl
# 3.0åèå°åï¼http://ip:port/webservice/EXTERNALSERV/services/outService?wsdl
ats.endpoint=http://*************:20001/webservice/EXTERNALSERV/services/outService?wsdl
#ç§æ·å·ï¼é»è®¤=10001
#tenantId=24173
tenantId=10001

vangogh.apiUrl=http://vangogh-dataservice-ui.vangogh-dataservice-qj.avatar.fingard.cn
vangogh.token=069d1c33e83aeaa29aafc9dff37df71a
#éä»¶ä¸è½½å­è¯
ws.appId=oxb0cft3t4kpt9av
ws.appSecret=qvs7DBus
#ATS-Cloudå°å
#saas.endpoint=http://appserver.tfp-trustcloud-test.k8s.fingard.cn/appserver
saas.endpoint=http://appserver.tfp-czbbc.luna.fingard.cn/appserver
#æ¯å¦å¼å¯ç»å½å å¯ï¼é»è®¤å¼å¯
encryptLogin=true
encryptAtsLogin=true

#å®¡æ¹æ¶ä½¿ç¨æ¨éçæå¤§å®¡æ¹ç¬æ°
maxWaitingNum=10
#åé¡µå¤§å°
pageNum=50
# refereræ ¡éªï¼ä¸ºç©ºåä¸å¼å¯
domain=
#åä½èµéç»è®¡æ¥è¯¢å¨æ:1-12çæ°å­åå«ä»£è¡¨æè¿1-12ä¸ªæçæ¥è¯¢å¨æï¼
# é»è®¤ä¸º2ä¸ªæï¼æè¿60å¤©ï¼
queryPeriod=2
queryPeriodText=è¿ä¸¤ä¸ªæ
#æ¯å¦å¼å¯ä¸ä¸ªè´¦æ·åªè½åæ¶ç»å½ä¸å°è®¾å¤
oneUserOneLogin=false
#éç½®ãéè¦ãå±ç¤ºçåºé¨èå
# éç½®åèï¼https://alidocs.dingtalk.com/i/nodes/Obva6QBXJw9wOa2zta6nnDZ1Wn4qY5Pr
menus=home,flow,report,warning
home.page=/modules2.4/home_page/home.jsp?selectType=home
#é¢è­¦éé¢åæ°
pmQuota=1000000
#æ¾ç¤ºçæ¥è¡¨
# å¯¹åºå³ç³»ï¼https://alidocs.dingtalk.com/i/nodes/AR4GpnMqJzMvlGamh7Xwo7zMVKe0xjE3
showed.report=1,2,3,4,5,6,7,8,9,10,11,16
# æ¥è¡¨æåºå¼å³ï¼å¼å¯åæ ¹æ®showed.reportæ¥è¡¨ä»£ç é¡ºåºè¿è¡æåº
reportOrderOpen=true
# å·²æ¯æçæææµç¨
# å¨é¨å¯¹åºå³ç³»ï¼https://alidocs.dingtalk.com/i/nodes/Obva6QBXJw9wOa2zta9BbELqWn4qY5Pr
ats.flow.type=ZHSQ,ZJFK,ZJFKBATCH,XBSQ,ZJJH,JHTZ,ZJSH,ZJXB,ZJDBSQ,NBJKSQ,RZHKSQ,NBZHSQ,WTFKSQ
cnAmount=true
autoSendMsg=true
#sessionæå¡å¨çéç½®
#æ¯å¦å¼å¯(æ»å¼å³)
sessionCacheSwitch=false
#æå¡ç«¯ip
sessionServerIp=127.0.0.1
#æå¡ç«¯ç«¯å£å·
sessionServerPort=8382
#å¼å§sessionä»£ç#å¦æå³é­å¼å³ï¼å¨ç»å½åæ´æ¹sessionæ¶éè¦èªå·±æå¨å»ä¿å­sessionå°è¿ç¨sessionæå¡å¨
sessionHandlerSwitch=true
#å®¢æ·ç«¯æ¬å°åä¸ªè¯·æ±æ¶æ¯å¦ç¼å­Session
localCacheSwitch4SingleRequest=true
#æ°æ®çååéç¥æå¡å¨æ´æ°çç­å¾æ¶é´#ç­å¾æ¶é´å°å¨  request2ServerTime~2*request2ServerTime  ä¹é´
request2ServerWaitTime=100
#sessionååæ¯ï¼å®¢æ·ç«¯éè¦æå¡ç«¯éåçå¨ä½
actionWhenSessionChange=UPDATE
#cookieå­å¨æ¶é´#10å°æ¶
expreTime4cookie=36000
#####################################Common#######################################################
#socketä¿æè¿æ¥æ¹å¼#åè§SocketLinkTypeEnum
#SHORT/LONG
socketLinkType=LONG
#å¼å¯æºè½åæ¶ç©ºé²è¿æ¥
openIntelligenceControlConnNam=false
#å¼å¯è¿æ¥æ³æ¼æ£æµ#è¿ä¸ªæ¯è¾èèµæºï¼å¯ä»¥ä¸ç¨å¼
checkConnLeak=false
#sessionç¼å­æ¶é´#10å°æ¶
session.cacheTime=36000000
#æ¸é¤å¨æ
session.sleepTime=600000
#å­å¨ç­ç¥#åè§IPersistenceStrategy.strategyåç¼å±æ§	#strategy4Memory/strategy4File
saveStrategy=strategy4Memory
#server memorycacheï¼åå­å­å¨æ æï¼
memoryCache=false
memoryCacheSize=1000
#æä»¶å­å¨ï¼å­å¨è·¯å¾
savePath=D:/tmp/test/save/
#å è½½åå²æä»¶æ°æ®ï¼å½ç­ç¥æ¯æä»¶å­å¨ç­ç¥æ¶
loadHistoryData4FileStrategy=false
#ä¿å­å­å¨å²çªæ¶éåçç­ç¥ï¼åè§Action4PersistenceCollisionEnum
action4PresistenceCollision=COVER
log.safeMode=false
#æ§å¶é¦é¡µä¸éè¦å±ç¤ºçæçº¿
homeLineLimit=12345