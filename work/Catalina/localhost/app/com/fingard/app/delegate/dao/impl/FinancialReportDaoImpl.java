/**
 * 财务报表DAO实现类
 * 处理复杂的财务数据查询，支持双数据源
 */
package com.fingard.app.delegate.dao.impl;

import com.fingard.app.delegate.dao.FinancialReportDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.*;
import java.util.*;

@Repository
public class FinancialReportDaoImpl implements FinancialReportDao {

    @Autowired
    @Qualifier("dataSource")  // 使用主数据库查询财务数据
    private DataSource mainDataSource;

    @Override
    public List<Map<String, Object>> getOutflowComposition(String ym) throws SQLException {
        // 流出资金构成SQL - 适配原始SQL查询
        String sql = "SELECT capitalcategoryname as 资金流出类型, moneyway, sum(amount) 金额 " +
            "FROM ( " +
            "    SELECT " +
            "       ba.name, " +
            "        t2.name paytype,-- 交易类型 " +
            "        t.paymadedate, " +
            "        t3.name currencyname, " +
            "         t2.moneyway ,ROUND(CASE currencyid " +
            "                    WHEN " +
            "        '4' " +
            "    THEN " +
            "    t.ouramount* ( -- sourcecurrencyid为外币ID,rate为折本位币汇率,本位币默认为人民币 " +
            " SELECT   " +
            "        t2.rate " +
            " FROM (-- 排序顺序：汇率时间最近优先 " +
            "          SELECT t1.sourcecurrencyid, " +
            "                 t1.rate, " +
            "                 row_number() over ( partition BY sourcecurrencyid ORDER BY ratedate DESC ) rn " +
            "          FROM ( " +
            "                   SELECT t1.rate, " +
            "                          cp.sourcecurrencyid, -- 基准货币 " +
            "                          t1.ratedate " +
            "                   FROM t_bd_currencyrates t1 " +
            "                            LEFT JOIN t_bd_currencypairs cp ON t1.currencypairid = cp.urid " +
            "                            LEFT JOIN t_bd_currencyratetypes crt ON crt.urid = t1.currencyratetypeid " +
            "                   WHERE t1.ratedate <= sysdate() " +
            " " +
            "                     AND cp.targetcecurrencyid = ( -- 询价货币(标价货币) " +
            "                       SELECT c.urid  -- 询价货币得是本位币 " +
            "                       FROM t_bd_paramval p " +
            "                                LEFT JOIN t_bd_currencies c ON p.value = c.code " +
            "                       WHERE   p.tenantid = '10001' " +
            "                         AND p.paramdefid = (SELECT urid FROM t_bd_paramdef WHERE code = 'SET025')) -- 取系统参数\"本位币\" " +
            " " +
            "                     AND t1.bankid = '1' " +
            "                     AND crt.isdefault = '1' -- 取默认汇率类型 " +
            "                     AND t1.isactive = '1' " +
            "                     AND cp.isactive = '1' " +
            "                   ORDER BY ratedate DESC " +
            "               ) t1 " +
            "      ) t2 " +
            " WHERE rn = 1   AND currencyid= t2.sourcecurrencyid " +
            "              ) " +
            "            ELSE  t.ouramount " +
            "        END, 2) amount, " +
            " " +
            " tbu.name budgetitemname,-- 计划项目ID " +
            "t5.name capitalcategoryname,-- 资金类别 " +
            "t6.c_caption paystatename " +
            "FROM " +
            "T_SE_PAYMENTS t " +
            "    LEFT OUTER JOIN " +
            "TSYS_ORGANIZATION t1 ON t.orgid = t1.org_id " +
            "    LEFT OUTER JOIN " +
            "T_SE_PAYTYPES t2 ON t.paytypeid = t2.urid " +
            "    LEFT OUTER JOIN " +
            "T_BD_CURRENCIES t3 ON t.ourcurrencyid = t3.urid " +
            "    LEFT OUTER JOIN " +
            "TSYS_ORGANIZATION t4 ON t.ourorgid = t4.org_id " +
            "    LEFT OUTER JOIN " +
            "T_BD_CATEGORIES t5 ON t.CAPITALCATEGORYID = t5.urid " +
            "    LEFT OUTER JOIN " +
            "tdictionary t6 ON t.paystate = t6.c_keyvalue " +
            "    LEFT OUTER JOIN " +
            "tsys_user tu ON t.createdby = tu.user_id " +
            "    LEFT OUTER JOIN " +
            "T_BU_BUDGETITEMS tbu ON t.budgetitemid = tbu.urid " +
            " LEFT JOIN t_sy_banks ba ON ba.urid = t.ourbankid " +
            "WHERE 1=1 " +
            "    AND (? IS NULL OR ? = '' OR substring(paymadedate,1,7) = ?) " +
            "    and t6.l_keyno = '1008'-- 支付状态 " +
            "    AND t.paystate = '2'-- 审批状态：已审批 " +
            "    AND t.cancelstate = '1' -- 作废状态：未作废 " +
            ") aaaa " +
            "where capitalcategoryname is not null " +
            "group by   capitalcategoryname, moneyway " +
            "ORDER BY 金额 DESC";

        Connection conn = null;
        PreparedStatement pstmt = null;
        try {
            conn = mainDataSource.getConnection();
            pstmt = conn.prepareStatement(sql);

            pstmt.setString(1, ym);
            pstmt.setString(2, ym);
            pstmt.setString(3, ym);

            return executeQuery(pstmt);
        } finally {
            if (pstmt != null) {
                try {
                    pstmt.close();
                } catch (SQLException e) {
                    // 忽略关闭异常
                }
            }
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    // 忽略关闭异常
                }
            }
        }
    }

    /**
     * 执行查询
     */
    private List<Map<String, Object>> executeQuery(PreparedStatement pstmt) throws SQLException {
        List<Map<String, Object>> results = new ArrayList<>();
        ResultSet rs = null;

        try {
            rs = pstmt.executeQuery();
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            while (rs.next()) {
                Map<String, Object> row = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnLabel(i);
                    Object value = rs.getObject(i);
                    row.put(columnName, value);
                }
                results.add(row);
            }
        } finally {
            if (rs != null) {
                try {
                    rs.close();
                } catch (SQLException e) {
                    // 忽略关闭异常
                }
            }
        }

        return results;
    }
}