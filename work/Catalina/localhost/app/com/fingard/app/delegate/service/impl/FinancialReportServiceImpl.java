/**
 * 财务报表Service实现类
 * 提供财务报表相关的业务逻辑实现
 */
package com.fingard.app.delegate.service.impl;

import com.fingard.app.delegate.dao.FinancialReportDao;
import com.fingard.app.delegate.service.FinancialReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

@Service
@Transactional(readOnly = true)
public class FinancialReportServiceImpl implements FinancialReportService {
    
    @Autowired
    private FinancialReportDao financialReportDao;

    @Override
    public List<Map<String, Object>> getOutflowCompositionReport(String startTime, String endTime) throws Exception {
        // 参数验证
        if (startTime == null || endTime == null) {
            throw new IllegalArgumentException("时间参数不能为空");
        }

        // 时间格式验证和转换
        String ym = convertTimeRangeToYearMonth(startTime, endTime);

        try {
            return financialReportDao.getOutflowComposition(ym);
        } catch (Exception e) {
            throw new Exception("获取资金流出构成报表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将时间范围转换为年月格式
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 年月字符串，格式：YYYY-MM
     */
    private String convertTimeRangeToYearMonth(String startTime, String endTime) throws Exception {
        // 时间格式验证正则表达式
        Pattern datePattern = Pattern.compile("^\\d{4}-\\d{2}(-\\d{2})?$");

        if (!datePattern.matcher(startTime).matches()) {
            throw new IllegalArgumentException("开始时间格式不正确，应为YYYY-MM或YYYY-MM-DD格式");
        }

        if (!datePattern.matcher(endTime).matches()) {
            throw new IllegalArgumentException("结束时间格式不正确，应为YYYY-MM或YYYY-MM-DD格式");
        }

        // 提取年月部分
        String startYm = startTime.length() >= 7 ? startTime.substring(0, 7) : startTime;
        String endYm = endTime.length() >= 7 ? endTime.substring(0, 7) : endTime;

        // 如果开始和结束是同一个月，返回该月
        if (startYm.equals(endYm)) {
            return startYm;
        }

        // 如果跨月，返回开始月份（可根据业务需求调整）
        return startYm;
    }
}