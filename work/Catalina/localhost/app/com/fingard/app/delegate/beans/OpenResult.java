/**
 * 通用响应结果类
 * 用于封装API响应数据
 */
package com.fingard.app.delegate.beans;

import java.io.Serializable;

public class OpenResult implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 是否成功
     */
    private boolean successful;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private Object data;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 默认构造函数
     */
    public OpenResult() {
        this.successful = false;
    }
    
    /**
     * 构造函数
     * @param successful 是否成功
     * @param message 响应消息
     */
    public OpenResult(boolean successful, String message) {
        this.successful = successful;
        this.message = message;
    }
    
    /**
     * 构造函数
     * @param successful 是否成功
     * @param message 响应消息
     * @param data 响应数据
     */
    public OpenResult(boolean successful, String message, Object data) {
        this.successful = successful;
        this.message = message;
        this.data = data;
    }
    
    // Getter和Setter方法
    
    public boolean isSuccessful() {
        return successful;
    }
    
    public void setSuccessful(boolean successful) {
        this.successful = successful;
    }
    
    public String getSuccessful() {
        return String.valueOf(successful);
    }
    
    public void setSuccessful(String successful) {
        this.successful = Boolean.parseBoolean(successful);
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public Object getData() {
        return data;
    }
    
    public void setData(Object data) {
        this.data = data;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
    
    /**
     * 创建成功响应
     * @param message 响应消息
     * @return OpenResult对象
     */
    public static OpenResult success(String message) {
        return new OpenResult(true, message);
    }
    
    /**
     * 创建成功响应
     * @param message 响应消息
     * @param data 响应数据
     * @return OpenResult对象
     */
    public static OpenResult success(String message, Object data) {
        return new OpenResult(true, message, data);
    }
    
    /**
     * 创建失败响应
     * @param message 错误消息
     * @return OpenResult对象
     */
    public static OpenResult error(String message) {
        return new OpenResult(false, message);
    }
    
    /**
     * 创建失败响应
     * @param message 错误消息
     * @param errorCode 错误代码
     * @return OpenResult对象
     */
    public static OpenResult error(String message, String errorCode) {
        OpenResult result = new OpenResult(false, message);
        result.setErrorCode(errorCode);
        return result;
    }
    
    @Override
    public String toString() {
        return "OpenResult{" +
                "successful=" + successful +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", errorCode='" + errorCode + '\'' +
                '}';
    }
}
