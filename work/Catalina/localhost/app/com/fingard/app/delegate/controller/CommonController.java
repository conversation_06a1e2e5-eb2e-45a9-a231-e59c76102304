/**
 * 通用Controller基类
 * 提供公共的日志记录和响应处理功能
 */
package com.fingard.app.delegate.controller;

import com.fingard.app.delegate.beans.OpenResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.alibaba.fastjson.JSON;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

public abstract class CommonController {
    
    /**
     * 日志记录器
     */
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    /**
     * 输出响应结果
     * @param result 响应结果对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    protected void print(OpenResult result, HttpServletResponse response) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setHeader("Cache-Control", "no-cache");
        
        PrintWriter out = null;
        try {
            out = response.getWriter();
            out.print(JSON.toJSONString(result));
            out.flush();
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }
    
    /**
     * 输出JSON字符串响应
     * @param jsonString JSON字符串
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    protected void printJson(String jsonString, HttpServletResponse response) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setHeader("Cache-Control", "no-cache");
        
        PrintWriter out = null;
        try {
            out = response.getWriter();
            out.print(jsonString);
            out.flush();
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }
}
