/**
 * 财务报表Controller
 * 处理财务报表相关的HTTP请求
 */
package com.fingard.app.delegate.controller;

import com.fingard.app.delegate.beans.OpenResult;
import com.fingard.app.delegate.service.FinancialReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/financial")
public class FinancialReportController extends CommonController {

    @Autowired
    private FinancialReportService financialReportService;

    /**
     * 获取资金流出构成报表
     */
    @RequestMapping("/getOutflowComposition.do")
    public void getOutflowComposition(HttpServletRequest request, HttpServletResponse resp) throws Exception {
        try {
            String startTime = request.getParameter("startTime");
            String endTime = request.getParameter("endTime");

            if (startTime == null || endTime == null) {
                logger.error("获取资金流出构成报表失败：时间参数不完整");
                OpenResult errorResult = new OpenResult();
                errorResult.setSuccessful(false);
                errorResult.setMessage("时间参数不完整");
                print(errorResult, resp);
                return;
            }

            List<Map<String, Object>> listR = financialReportService.getOutflowCompositionReport(startTime, endTime);

            OpenResult or = new OpenResult();
            or.setSuccessful(true);
            or.setMessage("查询成功");
            or.setData(listR);
            print(or, resp);

        } catch (Exception e) {
            logger.error("获取资金流出构成报表失败", e);
            OpenResult errorResult = new OpenResult();
            errorResult.setSuccessful(false);
            errorResult.setMessage("查询失败: " + e.getMessage());
            print(errorResult, resp);
        }
    }

}