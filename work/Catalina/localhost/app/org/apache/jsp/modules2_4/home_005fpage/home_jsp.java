/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/8.5.100
 * Generated at: 2025-05-06 02:16:59 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.modules2_4.home_005fpage;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import com.fingard.app.delegate.helper.EndpointProperties;
import java.util.List;
import cn.hutool.core.util.StrUtil;
import com.fingard.app.delegate.framework.util.MyProperties;
import com.fingard.app.delegate.framework.util.Constants;
import com.fingard.app.delegate.framework.util.Constants;
import java.util.Set;
import com.fingard.app.delegate.controller.SysParamController;

public final class home_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(6);
    _jspx_dependants.put("/common/bottom_bar.jsp", Long.valueOf(1742193138000L));
    _jspx_dependants.put("jar:file:/E:/project/other/FinancialBox/apache-tomcat-8.5.100/webapps/app/WEB-INF/lib/jstl-1.2.jar!/META-INF/fmt.tld", Long.valueOf(1153356282000L));
    _jspx_dependants.put("/common/include.jsp", Long.valueOf(1742193138000L));
    _jspx_dependants.put("/WEB-INF/lib/jstl-1.2.jar", Long.valueOf(1742193118000L));
    _jspx_dependants.put("jar:file:/E:/project/other/FinancialBox/apache-tomcat-8.5.100/webapps/app/WEB-INF/lib/jstl-1.2.jar!/META-INF/c.tld", Long.valueOf(1153356282000L));
    _jspx_dependants.put("/WEB-INF/my.tld", Long.valueOf(1742193117000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(3);
    _jspx_imports_packages.add("javax.servlet");
    _jspx_imports_packages.add("javax.servlet.http");
    _jspx_imports_packages.add("javax.servlet.jsp");
    _jspx_imports_classes = new java.util.LinkedHashSet<>(7);
    _jspx_imports_classes.add("com.fingard.app.delegate.framework.util.Constants");
    _jspx_imports_classes.add("java.util.List");
    _jspx_imports_classes.add("cn.hutool.core.util.StrUtil");
    _jspx_imports_classes.add("com.fingard.app.delegate.framework.util.MyProperties");
    _jspx_imports_classes.add("com.fingard.app.delegate.helper.EndpointProperties");
    _jspx_imports_classes.add("com.fingard.app.delegate.controller.SysParamController");
    _jspx_imports_classes.add("java.util.Set");
  }

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;

  private volatile javax.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public javax.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.release();
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
      throws java.io.IOException, javax.servlet.ServletException {

    final java.lang.String _jspx_method = request.getMethod();
    if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method) && !javax.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSP 只允许 GET、POST 或 HEAD。Jasper 还允许 OPTIONS");
      return;
    }

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html;charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");

    String customer = MyProperties.getMyPropertiesInstance().getProperty("currentCustomerName");
    String customerCode = MyProperties.getMyPropertiesInstance().getProperty("customerCode");
    String version = MyProperties.getMyPropertiesInstance().getProperty("appDelegateVersion");
    String basePath = request.getContextPath();
    Boolean virtualSwitch = MyProperties.getMyPropertiesInstance().getProperty3("virtualSwitch", false);
    if (virtualSwitch) {
        String virtualPath = MyProperties.getMyPropertiesInstance().getProperty("virtualPath");
        virtualPath = StrUtil.isEmpty(virtualPath) ? StrUtil.EMPTY : virtualPath;
        basePath = virtualPath + basePath;
    }
    response.setHeader("Cache-Control", "no-store");
    response.setHeader("Pragma", "no-cache");
    response.setDateHeader("Expires", 0);

    String permissionCode = String.valueOf(request.getSession().getAttribute(Constants.AUTHORITY));
    String savedUserId = String.valueOf(request.getSession().getAttribute(Constants.USER));
    String savedUserName = String.valueOf(request.getSession().getAttribute(Constants.REAL_NAME));
    Boolean siczyMsgCheck = MyProperties.getMyPropertiesInstance().getProperty3("flow.msgCheck", false);
    String endpoint = MyProperties.getMyPropertiesInstance().getProperty("ats.endpoint");
    String atsVersion = "2.0";
    if(StrUtil.containsAny(endpoint,"EXTERNALSERV")){
        atsVersion = "3.0";
    }

//    boolean waterMakerOpen = MyProperties.getMyPropertiesInstance().getProperty3("waterMaker.open");


      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f0 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f0_reused = false;
      try {
        _jspx_th_c_005fset_005f0.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f0.setParent(null);
        // /common/include.jsp(36,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f0.setVar("ctx");
        // /common/include.jsp(36,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f0.setValue(basePath);
        int _jspx_eval_c_005fset_005f0 = _jspx_th_c_005fset_005f0.doStartTag();
        if (_jspx_th_c_005fset_005f0.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f0);
        _jspx_th_c_005fset_005f0_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f0_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f1 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f1_reused = false;
      try {
        _jspx_th_c_005fset_005f1.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f1.setParent(null);
        // /common/include.jsp(37,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f1.setVar("customer");
        // /common/include.jsp(37,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f1.setValue(customer);
        int _jspx_eval_c_005fset_005f1 = _jspx_th_c_005fset_005f1.doStartTag();
        if (_jspx_th_c_005fset_005f1.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f1);
        _jspx_th_c_005fset_005f1_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f1_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f2 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f2_reused = false;
      try {
        _jspx_th_c_005fset_005f2.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f2.setParent(null);
        // /common/include.jsp(38,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f2.setVar("permissionCode");
        // /common/include.jsp(38,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f2.setValue(permissionCode);
        int _jspx_eval_c_005fset_005f2 = _jspx_th_c_005fset_005f2.doStartTag();
        if (_jspx_th_c_005fset_005f2.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f2);
        _jspx_th_c_005fset_005f2_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f2_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f3 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f3_reused = false;
      try {
        _jspx_th_c_005fset_005f3.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f3.setParent(null);
        // /common/include.jsp(39,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f3.setVar("userId");
        // /common/include.jsp(39,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f3.setValue(savedUserId);
        int _jspx_eval_c_005fset_005f3 = _jspx_th_c_005fset_005f3.doStartTag();
        if (_jspx_th_c_005fset_005f3.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f3);
        _jspx_th_c_005fset_005f3_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f3_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f4 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f4_reused = false;
      try {
        _jspx_th_c_005fset_005f4.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f4.setParent(null);
        // /common/include.jsp(40,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f4.setVar("realUserName");
        // /common/include.jsp(40,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f4.setValue(savedUserName);
        int _jspx_eval_c_005fset_005f4 = _jspx_th_c_005fset_005f4.doStartTag();
        if (_jspx_th_c_005fset_005f4.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f4);
        _jspx_th_c_005fset_005f4_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f4_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f5 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f5_reused = false;
      try {
        _jspx_th_c_005fset_005f5.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f5.setParent(null);
        // /common/include.jsp(41,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f5.setVar("atsVersion");
        // /common/include.jsp(41,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f5.setValue(atsVersion);
        int _jspx_eval_c_005fset_005f5 = _jspx_th_c_005fset_005f5.doStartTag();
        if (_jspx_th_c_005fset_005f5.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f5);
        _jspx_th_c_005fset_005f5_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f5_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f6 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f6_reused = false;
      try {
        _jspx_th_c_005fset_005f6.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f6.setParent(null);
        // /common/include.jsp(42,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f6.setVar("customerCode");
        // /common/include.jsp(42,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f6.setValue(customerCode);
        int _jspx_eval_c_005fset_005f6 = _jspx_th_c_005fset_005f6.doStartTag();
        if (_jspx_th_c_005fset_005f6.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f6);
        _jspx_th_c_005fset_005f6_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f6_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f7 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f7_reused = false;
      try {
        _jspx_th_c_005fset_005f7.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f7.setParent(null);
        // /common/include.jsp(43,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f7.setVar("version");
        // /common/include.jsp(43,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f7.setValue(version);
        int _jspx_eval_c_005fset_005f7 = _jspx_th_c_005fset_005f7.doStartTag();
        if (_jspx_th_c_005fset_005f7.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f7);
        _jspx_th_c_005fset_005f7_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f7_reused);
      }
      out.write('\r');
      out.write('\n');
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write('\r');
      out.write('\n');

    List<String> flowTypeList = EndpointProperties.getFlowTypeList();
    boolean containsSinglePayment = flowTypeList.contains("ZJFK");

      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f8 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f8_reused = false;
      try {
        _jspx_th_c_005fset_005f8.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f8.setParent(null);
        // /modules2.4/home_page/home.jsp(9,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f8.setVar("containsSinglePayment");
        // /modules2.4/home_page/home.jsp(9,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f8.setValue(containsSinglePayment);
        int _jspx_eval_c_005fset_005f8 = _jspx_th_c_005fset_005f8.doStartTag();
        if (_jspx_th_c_005fset_005f8.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f8);
        _jspx_th_c_005fset_005f8_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f8, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f8_reused);
      }
      out.write("\r\n");
      out.write("\r\n");
      out.write("<!DOCTYPE html>\r\n");
      out.write("<html lang=\"en\">\r\n");
      out.write("<head>\r\n");
      out.write("    <meta charset=\"utf-8\">\r\n");
      out.write("    <title>首页</title>\r\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no\">\r\n");
      out.write("    <link rel=\"stylesheet\" href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/css/swiper.min.css\">\r\n");
      out.write("    <link rel=\"stylesheet\" href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/css/weui.min.css\"/>\r\n");
      out.write("    <link rel=\"stylesheet\" href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/css/weuix.min.css\"/>\r\n");
      out.write("\r\n");
      out.write("    <style>\r\n");
      out.write("        * {\r\n");
      out.write("            touch-action: pan-y;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        html,\r\n");
      out.write("        body {\r\n");
      out.write("            position: relative;\r\n");
      out.write("            height: 100%;\r\n");
      out.write("\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        body {\r\n");
      out.write("            background: #eee;\r\n");
      out.write("            font-family: Helvetica Neue, Helvetica, Arial, sans-serif;\r\n");
      out.write("            font-size: 14px;\r\n");
      out.write("            color: #000;\r\n");
      out.write("            margin: 0;\r\n");
      out.write("            padding: 0;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .swiper-container {\r\n");
      out.write("            position: relative;\r\n");
      out.write("            bottom: 2.05rem;\r\n");
      out.write("            width: 90%;\r\n");
      out.write("            height: 44px;\r\n");
      out.write("            border-radius: 0.525rem;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .swiper-slide {\r\n");
      out.write("            width: 100%;\r\n");
      out.write("            max-width: 100%;\r\n");
      out.write("            padding-left: 0.75rem;\r\n");
      out.write("            border-radius: 0.525rem;\r\n");
      out.write("            text-align: start;\r\n");
      out.write("            font-size: 15px;\r\n");
      out.write("            background: #fff;\r\n");
      out.write("            line-height: 44px;\r\n");
      out.write("            overflow: hidden;\r\n");
      out.write("            text-overflow: ellipsis;\r\n");
      out.write("            white-space: nowrap;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .weui-cells {\r\n");
      out.write("\r\n");
      out.write("            width: 90%;\r\n");
      out.write("            position: relative;\r\n");
      out.write("            bottom: 1.55rem;\r\n");
      out.write("            border-radius: 0.525rem;\r\n");
      out.write("            margin: 0 auto 3.25rem;\r\n");
      out.write("\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .icon-53 {\r\n");
      out.write("            margin-right: 0.625rem;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("    </style>\r\n");
      out.write("</head>\r\n");
      out.write("<body>\r\n");
      out.write("<div class=\"container\">\r\n");
      out.write("    <div class=\"content\" style=\"height:100%;overflow: auto;\">\r\n");
      out.write("        <div id=\"title-layout\">\r\n");
      out.write("            <div id=\"title-amount\"\r\n");
      out.write("                 style=\"display: none;width:100%;height:200px;background-color: #649CFF; margin-bottom: 0.625rem;\">\r\n");
      out.write("                <div style=\"text-align: center; padding-top: 50px; color: #FFFFFF;\">\r\n");
      out.write("                    <span id=\"tip-name\"></span>\r\n");
      out.write("                    <p id=\"total-amount\" style=\"font-size: 1.625rem;\"></p>\r\n");
      out.write("                </div>\r\n");
      out.write("            </div>\r\n");
      out.write("\r\n");
      out.write("            <div id=\"chart\"\r\n");
      out.write("                 style=\"margin: 0 auto; width:90%;height:300px;background-color: white; position: relative; bottom: 2.625rem; border-radius: 0.525rem;display: none;\">\r\n");
      out.write("            </div>\r\n");
      out.write("        </div>\r\n");
      out.write("        <div id=\"body-layout\">\r\n");
      out.write("            <div class=\"swiper-container\">\r\n");
      out.write("                <div class=\"swiper-wrapper\">\r\n");
      out.write("                </div>\r\n");
      out.write("            </div>\r\n");
      out.write("            <div class=\"weui-cells\">\r\n");
      out.write("            </div>\r\n");
      out.write("        </div>\r\n");
      out.write("        ");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");


    String menuCustomer = MyProperties.getMyPropertiesInstance().getProperty("currentCustomerName");
    Object authority = request.getSession().getAttribute(Constants.AUTHORITY);
    boolean hasWarningMsgPermission = true;
    if (authority instanceof Set) {
        Set<String> set = (Set<String>) authority;
        hasWarningMsgPermission = set.contains("WPSS01");

    }
    if (authority instanceof String) {
        String string = (String) authority;
        hasWarningMsgPermission = string.contains("WPSS01");

    }

    int flowTodoCount = SysParamController.getFlowTodoCount(request);


      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f9 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f9_reused = false;
      try {
        _jspx_th_c_005fset_005f9.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f9.setParent(null);
        // /common/bottom_bar.jsp(24,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f9.setVar("hasWarningMsgPermission");
        // /common/bottom_bar.jsp(24,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f9.setValue(hasWarningMsgPermission);
        int _jspx_eval_c_005fset_005f9 = _jspx_th_c_005fset_005f9.doStartTag();
        if (_jspx_th_c_005fset_005f9.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f9);
        _jspx_th_c_005fset_005f9_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f9, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f9_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f10 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f10_reused = false;
      try {
        _jspx_th_c_005fset_005f10.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f10.setParent(null);
        // /common/bottom_bar.jsp(25,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f10.setVar("flowTodoCount");
        // /common/bottom_bar.jsp(25,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f10.setValue(flowTodoCount);
        int _jspx_eval_c_005fset_005f10 = _jspx_th_c_005fset_005f10.doStartTag();
        if (_jspx_th_c_005fset_005f10.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f10);
        _jspx_th_c_005fset_005f10_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f10, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f10_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f11 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f11_reused = false;
      try {
        _jspx_th_c_005fset_005f11.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f11.setParent(null);
        // /common/bottom_bar.jsp(26,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f11.setVar("menu_customer");
        // /common/bottom_bar.jsp(26,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f11.setValue(menuCustomer);
        int _jspx_eval_c_005fset_005f11 = _jspx_th_c_005fset_005f11.doStartTag();
        if (_jspx_th_c_005fset_005f11.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f11);
        _jspx_th_c_005fset_005f11_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f11, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f11_reused);
      }
      out.write("\r\n");
      out.write("\r\n");
      out.write("<style>\r\n");
      out.write("\r\n");
      out.write("    .weui-tabbar__label {\r\n");
      out.write("        font-size: 12px;\r\n");
      out.write("        line-height: 16px;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .weui-tabbar__item.weui-bar__item--on .weui-tabbar__label {\r\n");
      out.write("        color: #649CFF;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .weui-tabbar__item {\r\n");
      out.write("        padding: 5px 0;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon {\r\n");
      out.write("        display: block;\r\n");
      out.write("        background-position: center;\r\n");
      out.write("        background-size: 28px 28px;\r\n");
      out.write("        width: 28px;\r\n");
      out.write("        height: 28px;\r\n");
      out.write("        margin-left: auto;\r\n");
      out.write("        margin-right: auto;\r\n");
      out.write("        background-repeat: no-repeat;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_1 {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/Group <EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_2 {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/Group <EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_3 {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/Group <EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_4 {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/Group <EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_5 {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/<EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_6 {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/org_ori.png');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_7 {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/mine.png');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_1_selected {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/Group <EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_2_selected {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/Group <EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_3_selected {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/Group <EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_4_selected {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/Group <EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_5_selected {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/<EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_6_selected {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/org_ori_select.png');\r\n");
      out.write("    }\r\n");
      out.write("    .tab_icon_7_selected {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/mine_2.png');\r\n");
      out.write("    }\r\n");
      out.write("</style>\r\n");
      out.write("<div class=\"weui-tab tab-bottom\" style=\"height:49px;z-index: 999;\">\r\n");
      out.write("    <div class=\"weui-tabbar\">\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f1(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f2(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f3(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f4(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f5(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f6(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f7(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f8(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f9(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f10(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f11(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("    </div>\r\n");
      out.write("</div>\r\n");
      out.write("\r\n");
      out.write("<script>\r\n");
      out.write("    function clickTab(obj){\r\n");
      out.write("        console.log(obj);\r\n");
      out.write("        location.replace(obj.data('url'))\r\n");
      out.write("    }\r\n");
      out.write("</script>\r\n");
      out.write("\r\n");
      out.write("    </div>\r\n");
      out.write("</div>\r\n");
      out.write("\r\n");
      out.write("<script src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/js/zepto.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/js/zepto.weui.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/js/swiper.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/js/echarts.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/js/common.js?version=");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${version}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("\"></script>\r\n");
      out.write("\r\n");
      out.write("<script>\r\n");
      out.write("\r\n");
      out.write("    overscroll(document.getElementsByClassName('content')[0]);\r\n");
      out.write("\r\n");
      out.write("    //回退刷新\r\n");
      out.write("    var isPageHide = false;\r\n");
      out.write("    window.addEventListener('pageshow', function (e) {\r\n");
      out.write("        if (isPageHide) {\r\n");
      out.write("            window.location.reload()\r\n");
      out.write("        }\r\n");
      out.write("    });\r\n");
      out.write("    window.addEventListener('pagehide', function () {\r\n");
      out.write("        isPageHide = true;\r\n");
      out.write("    });\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("    function initWarningMsg(msgData, permissions) {\r\n");
      out.write("        if (permissions.indexOf(PERMISSION_CODE_WARNING_MSG) === -1) {\r\n");
      out.write("            // $('.swiper-wrapper').append('<div class=\"swiper-slide\"><span class=\"icon icon-53\"></span>您没有查看预警消息的权限</div>');\r\n");
      out.write("            $('.swiper-container').remove();\r\n");
      out.write("\r\n");
      out.write("            return;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        if (msgData.totalNum === 0) {\r\n");
      out.write("            $('.swiper-wrapper').append('<div class=\"swiper-slide\"><span class=\"icon icon-53\"></span>暂无预警消息</div>');\r\n");
      out.write("        } else {\r\n");
      out.write("\r\n");
      out.write("            for (let i = 0; i < msgData.data.length; i++) {\r\n");
      out.write("                var msg = $('<div class=\"swiper-slide\"><span class=\"icon icon-53\"></span>' + msgData.data[i].warningMessage + '</div>');\r\n");
      out.write("                $('.swiper-wrapper').append(msg);\r\n");
      out.write("                msg.on('click', function () {\r\n");
      out.write("                    window.location.href = '../warning/warning_msg.jsp?selectType=warning';\r\n");
      out.write("                });\r\n");
      out.write("            }\r\n");
      out.write("            let swiper = new Swiper('.swiper-container', {\r\n");
      out.write("                autoplay: {\r\n");
      out.write("                    delay: 3500,\r\n");
      out.write("                    stopOnLastSlide: false,\r\n");
      out.write("                    disableOnInteraction: false,\r\n");
      out.write("                    autoHeight: true //高度随内容变化\r\n");
      out.write("                },\r\n");
      out.write("                loop: true,\r\n");
      out.write("                direction: 'vertical'\r\n");
      out.write("            });\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    function comdify(n) {\r\n");
      out.write("        var re = /\\d{1,3}(?=(\\d{3})+$)/g;\r\n");
      out.write("        var n1 = n.replace(/^(\\d+)((\\.\\d+)?)$/, function (s, s1, s2) {\r\n");
      out.write("            return s1.replace(re, \"$&,\") + s2;\r\n");
      out.write("        });\r\n");
      out.write("        return n1;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    function initPieChart(chartData) {\r\n");
      out.write("        $('#tip-name').text('资金存量/万元');\r\n");
      out.write("        $('#chart').css('display', 'block');\r\n");
      out.write("        if (chartData === null || chartData === undefined) {\r\n");
      out.write("            return;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        let lineData = chartData.data;\r\n");
      out.write("        if (lineData === null || lineData.length === 0) {\r\n");
      out.write("            // 隐藏金额和图表\r\n");
      out.write("            $('#title-layout').hide();\r\n");
      out.write("            let body_layout = $('#body-layout');\r\n");
      out.write("            body_layout.css('position', 'relative');\r\n");
      out.write("            body_layout.css('top', '50px');\r\n");
      out.write("            return;\r\n");
      out.write("        }\r\n");
      out.write("        // 设置标题金额\r\n");
      out.write("        if (chartData.totalAmt === null || chartData.totalAmt === undefined) {\r\n");
      out.write("            chartData.totalAmt = '0.00';\r\n");
      out.write("        }\r\n");
      out.write("        $('#total-amount').html(chartData.totalAmt);\r\n");
      out.write("\r\n");
      out.write("        let seriesData = [];\r\n");
      out.write("        let keyData = [];\r\n");
      out.write("\r\n");
      out.write("        if (lineData.length > 0) {\r\n");
      out.write("            for (let i = 0; i < lineData.length; i++) {\r\n");
      out.write("                let itemLineName = lineData[i].lineKey;\r\n");
      out.write("                keyData.push({name: itemLineName})\r\n");
      out.write("                let itemLineData = lineData[i].lineData;\r\n");
      out.write("                let lineDataObj = itemLineData.pop();\r\n");
      out.write("                seriesData.push({value: lineDataObj.noteMoney, name: itemLineName})\r\n");
      out.write("            }\r\n");
      out.write("        } else {\r\n");
      out.write("            alert(\"无权限\");\r\n");
      out.write("            return;\r\n");
      out.write("        }\r\n");
      out.write("        // 调整顺序，按照活期、定期、募集资金、外币、票据、理财的顺序展示图例。泸州老窖目前只有这几种账户性质，如果后面增加需要对应修改。\r\n");
      out.write("        let defaultOrder = [{name: '活期余额'}, {name: '定期余额'}, {name: '募集资金余额'}, {name: '外币余额'}, {name: '票据余额'}, {name: '理财余额'}];\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("        let myChart = echarts.init(document.getElementById('chart'));\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("        let option = {\r\n");
      out.write("            color: ['#FCE172', '#908CFF', '#FB8D8D', '#7BE5C2', '#FF91BB', '#B496F1', '#74E2AB', '#649CFF', '#6E7074', '#546570', '#C4CCD3'],\r\n");
      out.write("            tooltip: {\r\n");
      out.write("                trigger: 'item',\r\n");
      out.write("                formatter: function (params, ticket, callback) {\r\n");
      out.write("                    return comdify(params.value) + '万元 (' + params.percent + '%)';\r\n");
      out.write("                },\r\n");
      out.write("                confine: true\r\n");
      out.write("            },\r\n");
      out.write("            legend: {\r\n");
      out.write("                type: 'scroll',\r\n");
      out.write("                orient: 'vertical',\r\n");
      out.write("                right: 10,\r\n");
      out.write("                top: 'center',\r\n");
      out.write("                data: defaultOrder,\r\n");
      out.write("            },\r\n");
      out.write("            series: [\r\n");
      out.write("                {\r\n");
      out.write("                    type: 'pie',\r\n");
      out.write("                    radius: '60%',\r\n");
      out.write("                    center: ['30%', '50%'],\r\n");
      out.write("                    data: seriesData,\r\n");
      out.write("                    labelLine: {\r\n");
      out.write("                        show: false\r\n");
      out.write("                    },\r\n");
      out.write("                    label: {\r\n");
      out.write("                        show: false\r\n");
      out.write("                    },\r\n");
      out.write("                    emphasis: {\r\n");
      out.write("                        itemStyle: {\r\n");
      out.write("                            shadowBlur: 10,\r\n");
      out.write("                            shadowOffsetX: 0,\r\n");
      out.write("                            shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n");
      out.write("                        }\r\n");
      out.write("                    }\r\n");
      out.write("                }\r\n");
      out.write("            ]\r\n");
      out.write("        };\r\n");
      out.write("\r\n");
      out.write("        myChart.setOption(option);\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    function initChart(chartData) {\r\n");
      out.write("        $('#tip-name').text('账户余额/万元');\r\n");
      out.write("        $('#chart').css('display', 'block');\r\n");
      out.write("        if (chartData === null || chartData === undefined) {\r\n");
      out.write("            return;\r\n");
      out.write("        }\r\n");
      out.write("        let lineData = chartData.data;\r\n");
      out.write("        if (lineData === null || lineData.length === 0) {\r\n");
      out.write("            // 隐藏金额和图表\r\n");
      out.write("            $('#title-layout').hide();\r\n");
      out.write("            let body_layout = $('#body-layout');\r\n");
      out.write("            body_layout.css('position', 'relative');\r\n");
      out.write("            body_layout.css('top', '50px');\r\n");
      out.write("            return;\r\n");
      out.write("        }\r\n");
      out.write("        // 设置标题金额\r\n");
      out.write("        if (chartData.totalAmt === null || chartData.totalAmt === undefined) {\r\n");
      out.write("            chartData.totalAmt = '0.00';\r\n");
      out.write("        }\r\n");
      out.write("        $('#total-amount').html(chartData.totalAmt);\r\n");
      out.write("\r\n");
      out.write("        let seriesData = [];\r\n");
      out.write("        let default_colors = ['#FCE172', '#908CFF', '#FB8D8D', '#7BE5C2', '#FF91BB', '#B496F1', '#74E2AB', '#649CFF', '#6E7074', '#546570', '#C4CCD3'];\r\n");
      out.write("        let keyData = [];\r\n");
      out.write("        let dateList = [];\r\n");
      out.write("\r\n");
      out.write("        if (lineData.length > 0) {\r\n");
      out.write("            for (let i = 0; i < lineData.length; i++) {\r\n");
      out.write("\r\n");
      out.write("                let itemLineName = lineData[i].lineKey;\r\n");
      out.write("                let itemLineData = lineData[i].lineData;\r\n");
      out.write("                keyData.push(itemLineName);\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("                let itemSeries = [];\r\n");
      out.write("                dateList = [];\r\n");
      out.write("                for (let itemData of itemLineData) {\r\n");
      out.write("                    itemSeries.push(itemData.noteMoney);\r\n");
      out.write("                    dateList.push(itemData.reportDate);\r\n");
      out.write("                }\r\n");
      out.write("\r\n");
      out.write("                let series = {\r\n");
      out.write("                    name: itemLineName,\r\n");
      out.write("                    type: 'line',\r\n");
      out.write("                    symbol: 'circle',\r\n");
      out.write("                    symbolSize: 1,\r\n");
      out.write("                    stack: itemLineName,\r\n");
      out.write("                    data: itemSeries,\r\n");
      out.write("                    itemStyle: {\r\n");
      out.write("                        normal: {\r\n");
      out.write("                            lineStyle: {\r\n");
      out.write("                                color: default_colors[i % default_colors.length]\r\n");
      out.write("                            }\r\n");
      out.write("                        }\r\n");
      out.write("                    }\r\n");
      out.write("                }\r\n");
      out.write("\r\n");
      out.write("                seriesData.push(series);\r\n");
      out.write("\r\n");
      out.write("            }\r\n");
      out.write("\r\n");
      out.write("        } else {\r\n");
      out.write("            //无顶部折线图权限\r\n");
      out.write("            $(\"#title-layout\").remove();\r\n");
      out.write("            $('.swiper-container').css('bottom', 'unset');\r\n");
      out.write("            $('.swiper-container').css('margin-top', '0.5rem');\r\n");
      out.write("            $('.weui-cells').css('bottom', 'unset');\r\n");
      out.write("            $('.weui-cells').css('margin-top', '0.5rem');\r\n");
      out.write("            return;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("        let myChart = echarts.init(document.getElementById('chart'));\r\n");
      out.write("\r\n");
      out.write("        let option = {\r\n");
      out.write("            title: {\r\n");
      out.write("                text: ''\r\n");
      out.write("            },\r\n");
      out.write("            tooltip: {\r\n");
      out.write("                trigger: 'axis',\r\n");
      out.write("                confine: true\r\n");
      out.write("            },\r\n");
      out.write("            color: default_colors,\r\n");
      out.write("            legend: {\r\n");
      out.write("                data: keyData,\r\n");
      out.write("                top: 15\r\n");
      out.write("            },\r\n");
      out.write("            grid: {\r\n");
      out.write("                left: '10%',\r\n");
      out.write("                right: '10%',\r\n");
      out.write("                bottom: '5%',\r\n");
      out.write("                top: '20%',\r\n");
      out.write("                containLabel: true\r\n");
      out.write("            },\r\n");
      out.write("            xAxis: {\r\n");
      out.write("                type: 'category',\r\n");
      out.write("                boundaryGap: false,\r\n");
      out.write("                data: dateList,\r\n");
      out.write("                splitLine: {\r\n");
      out.write("                    show: false\r\n");
      out.write("                },\r\n");
      out.write("                axisLine: {\r\n");
      out.write("                    lineStyle: {\r\n");
      out.write("                        color: '#D6D6D6',\r\n");
      out.write("                    }\r\n");
      out.write("                },\r\n");
      out.write("                axisLabel: {\r\n");
      out.write("                    color: '#888888',\r\n");
      out.write("                    fontSize: 10,\r\n");
      out.write("                    interval: 0,\r\n");
      out.write("                },\r\n");
      out.write("                axisTick: {\r\n");
      out.write("                    show: false\r\n");
      out.write("                }\r\n");
      out.write("            },\r\n");
      out.write("            yAxis: {\r\n");
      out.write("                show: false,\r\n");
      out.write("                type: 'value',\r\n");
      out.write("                splitLine: {\r\n");
      out.write("                    show: false\r\n");
      out.write("                },\r\n");
      out.write("                axisLabel: {\r\n");
      out.write("                    show: false\r\n");
      out.write("                }\r\n");
      out.write("            },\r\n");
      out.write("            series: seriesData\r\n");
      out.write("        };\r\n");
      out.write("\r\n");
      out.write("        myChart.setOption(option);\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    function initPayments(paymentsData, permissions, containsSinglePayment) {\r\n");
      out.write("        let data = paymentsData.data;\r\n");
      out.write("        let cells = $('.weui-cells');\r\n");
      out.write("        cells.html('');\r\n");
      out.write("\r\n");
      out.write("        let cellHeader = $('<div class=\"weui-cell\" style=\"font-weight: bold\">' +\r\n");
      out.write("            '<div class=\"weui-cell__bd\"><p>单笔付款审批</p></div>' +\r\n");
      out.write("            '<div class=\"weui-cell__ft\"></div></div>');\r\n");
      out.write("\r\n");
      out.write("        cells.append(cellHeader);\r\n");
      out.write("        if (permissions.indexOf(PERMISSION_CODE_FLOW) === -1 || containsSinglePayment === 'false') {\r\n");
      out.write("            cells.remove();\r\n");
      out.write("            // cells.append('<div class=\"weui-cell\"><div class=\"weui-cell__hd\">您没有查看审批流程的权限</div></div>');\r\n");
      out.write("            return;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        if (data == null || data.length === 0) {\r\n");
      out.write("            cells.append('<div class=\"weui-cell\"><div class=\"weui-cell__hd\">暂无数据</div></div>');\r\n");
      out.write("            return;\r\n");
      out.write("        }\r\n");
      out.write("        for (let i = 0; i < data.length; i++) {\r\n");
      out.write("            let cell = $('<label class=\"weui-cell weui-check__label\" for=\"check__no__' + data[i].itemId + '\">' +\r\n");
      out.write("                '            <div class=\"weui-cell__bd\">' +\r\n");
      out.write("                '                <p style=\"font-size: 17px; color: #212121; font-weight: bold\">' + data[i].title + '</p>' +\r\n");
      out.write("                '                <div class=\"item_list\">' +\r\n");
      out.write("                '                </div>' +\r\n");
      out.write("                '            </div>' +\r\n");
      out.write("                '            <div class=\"weui-cell__ft\" style=\"position: absolute;top:15px;right: 10px\">' +\r\n");
      out.write("                '                <p style=\"font-size: 12px\">' + data[i].time + '</p>' +\r\n");
      out.write("                '            </div>' +\r\n");
      out.write("                '        </label>');\r\n");
      out.write("\r\n");
      out.write("            let cellItem = cell.find('.item_list');\r\n");
      out.write("\r\n");
      out.write("            let items = data[i].item;\r\n");
      out.write("            for (let j = 0; j < items.length; j++) {\r\n");
      out.write("                let item = $('<p style=\"font-size: 13px;color: #5A5858\"><span style=\"width: 100px\">' + items[j].key + '：</span>' + items[j].value + '</p>');\r\n");
      out.write("                cellItem.append(item);\r\n");
      out.write("            }\r\n");
      out.write("\r\n");
      out.write("            cells.append(cell);\r\n");
      out.write("\r\n");
      out.write("            cell.on('click', function () {\r\n");
      out.write("                sessionStorage.setItem(\"DETAIL_FLOW_PARAMS\", JSON.stringify(data[i]));\r\n");
      out.write("                sessionStorage.setItem(\"TODO\", TODO_TAG);\r\n");
      out.write("                sessionStorage.setItem(\"FLOWTYPEID\", \"ZJFK\");\r\n");
      out.write("\r\n");
      out.write("                window.location.href = \"../flow/flowDetail.jsp\";\r\n");
      out.write("            });\r\n");
      out.write("\r\n");
      out.write("        }\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("    $(document).ready(function () {\r\n");
      out.write("        ddAjaxRequestCallback('加载中', '/saasmainpage/getIndexPageDataSummary.do', {}, function (data) {\r\n");
      out.write("            initWarningMsg(data.result.warningMessageData, '");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${permissionCode}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("');\r\n");
      out.write("            $('#title-amount').css('display', 'block');\r\n");
      out.write("            if ('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customer}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("'.substr(0, 4) == \"lzlj\") {\r\n");
      out.write("                initPieChart(data.result.accountBalanceData);\r\n");
      out.write("            } else {\r\n");
      out.write("                initChart(data.result.accountBalanceData);\r\n");
      out.write("            }\r\n");
      out.write("            initPayments(data.result.singlePaymentData, '");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${permissionCode}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("', '");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${containsSinglePayment}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("')\r\n");
      out.write("        });\r\n");
      out.write("    });\r\n");
      out.write("</script>\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("</body>\r\n");
      out.write("</html>\r\n");
      out.write("\r\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_my_005fhp_005f0(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f0 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f0_reused = false;
    try {
      _jspx_th_my_005fhp_005f0.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f0.setParent(null);
      // /common/bottom_bar.jsp(111,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f0.setMenu("home");
      int _jspx_eval_my_005fhp_005f0 = _jspx_th_my_005fhp_005f0.doStartTag();
      if (_jspx_eval_my_005fhp_005f0 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f0 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f0);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a  onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/home_page/home.jsp?selectType=home\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f0(_jspx_th_my_005fhp_005f0, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f1(_jspx_th_my_005fhp_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f2(_jspx_th_my_005fhp_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">首页</p>\r\n");
          out.write("            </a>\r\n");
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f0.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f0 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f0.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f0);
      _jspx_th_my_005fhp_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f0, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f0(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f0, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f0_reused = false;
    try {
      _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f0.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f0);
      // /common/bottom_bar.jsp(114,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='home' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
      if (_jspx_eval_c_005fif_005f0 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f0.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      _jspx_th_c_005fif_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f1(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f0, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f1 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f1_reused = false;
    try {
      _jspx_th_c_005fif_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f1.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f0);
      // /common/bottom_bar.jsp(115,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='home' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f1 = _jspx_th_c_005fif_005f1.doStartTag();
      if (_jspx_eval_c_005fif_005f1 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f1.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f1.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
      _jspx_th_c_005fif_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f2(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f0, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f2 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f2_reused = false;
    try {
      _jspx_th_c_005fif_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f2.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f0);
      // /common/bottom_bar.jsp(118,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='home' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f2 = _jspx_th_c_005fif_005f2.doStartTag();
      if (_jspx_eval_c_005fif_005f2 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f2.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f2.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
      _jspx_th_c_005fif_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f1(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f1 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f1_reused = false;
    try {
      _jspx_th_my_005fhp_005f1.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f1.setParent(null);
      // /common/bottom_bar.jsp(125,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f1.setMenu("cdcyjtHome");
      int _jspx_eval_my_005fhp_005f1 = _jspx_th_my_005fhp_005f1.doStartTag();
      if (_jspx_eval_my_005fhp_005f1 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f1 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f1);
        }
        do {
          out.write("\r\n");
          out.write("            <a  onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/home_page/cdcyjt_home.jsp?selectType=cdcyjtHome\"\r\n");
          out.write("                class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f3(_jspx_th_my_005fhp_005f1, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f4(_jspx_th_my_005fhp_005f1, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f5(_jspx_th_my_005fhp_005f1, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">首页</p>\r\n");
          out.write("            </a>\r\n");
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f1.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f1 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f1.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f1);
      _jspx_th_my_005fhp_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f1, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f3(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f1, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f3 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f3_reused = false;
    try {
      _jspx_th_c_005fif_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f3.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f1);
      // /common/bottom_bar.jsp(127,41) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f3.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='home' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f3 = _jspx_th_c_005fif_005f3.doStartTag();
      if (_jspx_eval_c_005fif_005f3 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f3.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f3.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f3);
      _jspx_th_c_005fif_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f4(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f1, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f4 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f4_reused = false;
    try {
      _jspx_th_c_005fif_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f4.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f1);
      // /common/bottom_bar.jsp(128,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f4.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='cdcyjtHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f4 = _jspx_th_c_005fif_005f4.doStartTag();
      if (_jspx_eval_c_005fif_005f4 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f4.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f4.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f4);
      _jspx_th_c_005fif_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f5(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f1, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f5 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f5_reused = false;
    try {
      _jspx_th_c_005fif_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f5.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f1);
      // /common/bottom_bar.jsp(131,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f5.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='cdcyjtHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f5 = _jspx_th_c_005fif_005f5.doStartTag();
      if (_jspx_eval_c_005fif_005f5 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f5.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f5.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f5);
      _jspx_th_c_005fif_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f2(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f2 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f2_reused = false;
    try {
      _jspx_th_my_005fhp_005f2.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f2.setParent(null);
      // /common/bottom_bar.jsp(138,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f2.setMenu("siczyHome");
      int _jspx_eval_my_005fhp_005f2 = _jspx_th_my_005fhp_005f2.doStartTag();
      if (_jspx_eval_my_005fhp_005f2 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f2 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f2);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/report/standard/SCZYBankBalance.jsp?selectType=siczyHome\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f6(_jspx_th_my_005fhp_005f2, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f7(_jspx_th_my_005fhp_005f2, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f8(_jspx_th_my_005fhp_005f2, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">首页</p>\r\n");
          out.write("            </a>\r\n");
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f2.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f2 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f2.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f2);
      _jspx_th_my_005fhp_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f2, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f6(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f2, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f6 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f6_reused = false;
    try {
      _jspx_th_c_005fif_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f6.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f2);
      // /common/bottom_bar.jsp(141,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f6.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='siczyHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f6 = _jspx_th_c_005fif_005f6.doStartTag();
      if (_jspx_eval_c_005fif_005f6 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f6.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f6.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f6);
      _jspx_th_c_005fif_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f7(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f2, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f7 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f7_reused = false;
    try {
      _jspx_th_c_005fif_005f7.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f7.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f2);
      // /common/bottom_bar.jsp(142,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f7.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='siczyHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f7 = _jspx_th_c_005fif_005f7.doStartTag();
      if (_jspx_eval_c_005fif_005f7 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f7.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f7.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f7);
      _jspx_th_c_005fif_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f8(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f2, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f8 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f8_reused = false;
    try {
      _jspx_th_c_005fif_005f8.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f8.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f2);
      // /common/bottom_bar.jsp(145,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f8.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='siczyHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f8 = _jspx_th_c_005fif_005f8.doStartTag();
      if (_jspx_eval_c_005fif_005f8 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f8.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f8.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f8);
      _jspx_th_c_005fif_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f8, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f3(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f3 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f3_reused = false;
    try {
      _jspx_th_my_005fhp_005f3.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f3.setParent(null);
      // /common/bottom_bar.jsp(152,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f3.setMenu("zllHome");
      int _jspx_eval_my_005fhp_005f3 = _jspx_th_my_005fhp_005f3.doStartTag();
      if (_jspx_eval_my_005fhp_005f3 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f3 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f3);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/report/standard/new_fund_summary_bank_balance_financing.jsp?selectType=zllHome\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f9(_jspx_th_my_005fhp_005f3, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f10(_jspx_th_my_005fhp_005f3, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f11(_jspx_th_my_005fhp_005f3, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">首页</p>\r\n");
          out.write("            </a>\r\n");
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f3.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f3 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f3.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f3);
      _jspx_th_my_005fhp_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f3, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f9(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f3, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f9 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f9_reused = false;
    try {
      _jspx_th_c_005fif_005f9.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f9.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f3);
      // /common/bottom_bar.jsp(155,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f9.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='zllHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f9 = _jspx_th_c_005fif_005f9.doStartTag();
      if (_jspx_eval_c_005fif_005f9 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f9.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f9.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f9);
      _jspx_th_c_005fif_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f9, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f10(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f3, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f10 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f10_reused = false;
    try {
      _jspx_th_c_005fif_005f10.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f10.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f3);
      // /common/bottom_bar.jsp(156,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f10.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='zllHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f10 = _jspx_th_c_005fif_005f10.doStartTag();
      if (_jspx_eval_c_005fif_005f10 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f10.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f10.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f10);
      _jspx_th_c_005fif_005f10_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f10, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f10_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f11(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f3, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f11 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f11_reused = false;
    try {
      _jspx_th_c_005fif_005f11.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f11.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f3);
      // /common/bottom_bar.jsp(159,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f11.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='zllHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f11 = _jspx_th_c_005fif_005f11.doStartTag();
      if (_jspx_eval_c_005fif_005f11 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f11.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f11.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f11);
      _jspx_th_c_005fif_005f11_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f11, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f11_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f4(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f4 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f4_reused = false;
    try {
      _jspx_th_my_005fhp_005f4.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f4.setParent(null);
      // /common/bottom_bar.jsp(166,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f4.setMenu("zmjHome");
      int _jspx_eval_my_005fhp_005f4 = _jspx_th_my_005fhp_005f4.doStartTag();
      if (_jspx_eval_my_005fhp_005f4 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f4 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f4);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/home_page/zmj_home.jsp?selectType=zmjHome\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f12(_jspx_th_my_005fhp_005f4, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f13(_jspx_th_my_005fhp_005f4, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f14(_jspx_th_my_005fhp_005f4, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">首页</p>\r\n");
          out.write("            </a>\r\n");
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f4.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f4 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f4.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f4);
      _jspx_th_my_005fhp_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f4, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f12(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f4, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f12 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f12_reused = false;
    try {
      _jspx_th_c_005fif_005f12.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f12.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f4);
      // /common/bottom_bar.jsp(169,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f12.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='zmjHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f12 = _jspx_th_c_005fif_005f12.doStartTag();
      if (_jspx_eval_c_005fif_005f12 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f12.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f12.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f12);
      _jspx_th_c_005fif_005f12_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f12, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f12_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f13(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f4, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f13 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f13_reused = false;
    try {
      _jspx_th_c_005fif_005f13.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f13.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f4);
      // /common/bottom_bar.jsp(170,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f13.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='zmjHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f13 = _jspx_th_c_005fif_005f13.doStartTag();
      if (_jspx_eval_c_005fif_005f13 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f13.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f13.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f13);
      _jspx_th_c_005fif_005f13_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f13, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f13_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f14(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f4, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f14 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f14_reused = false;
    try {
      _jspx_th_c_005fif_005f14.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f14.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f4);
      // /common/bottom_bar.jsp(173,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f14.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='zmjHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f14 = _jspx_th_c_005fif_005f14.doStartTag();
      if (_jspx_eval_c_005fif_005f14 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f14.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f14.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f14);
      _jspx_th_c_005fif_005f14_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f14, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f14_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f5(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f5 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f5_reused = false;
    try {
      _jspx_th_my_005fhp_005f5.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f5.setParent(null);
      // /common/bottom_bar.jsp(180,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f5.setMenu("flow");
      int _jspx_eval_my_005fhp_005f5 = _jspx_th_my_005fhp_005f5.doStartTag();
      if (_jspx_eval_my_005fhp_005f5 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f5 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f5);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/flow/flowTypeList.jsp?selectType=flow\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f15(_jspx_th_my_005fhp_005f5, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f16(_jspx_th_my_005fhp_005f5, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f17(_jspx_th_my_005fhp_005f5, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f18(_jspx_th_my_005fhp_005f5, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">待办</p>\r\n");
          out.write("            </a> ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f5.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f5 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f5.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f5);
      _jspx_th_my_005fhp_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f5, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f15(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f5, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f15 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f15_reused = false;
    try {
      _jspx_th_c_005fif_005f15.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f15.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f5);
      // /common/bottom_bar.jsp(183,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f15.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='flow' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f15 = _jspx_th_c_005fif_005f15.doStartTag();
      if (_jspx_eval_c_005fif_005f15 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f15.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f15.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f15);
      _jspx_th_c_005fif_005f15_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f15, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f15_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f16(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f5, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f16 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f16_reused = false;
    try {
      _jspx_th_c_005fif_005f16.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f16.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f5);
      // /common/bottom_bar.jsp(184,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f16.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${flowTodoCount!=0}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f16 = _jspx_th_c_005fif_005f16.doStartTag();
      if (_jspx_eval_c_005fif_005f16 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <span class=\"weui-badge\"\r\n");
          out.write("                          style=\"position: absolute;top: -.4em;\">");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${flowTodoCount}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("</span>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f16.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f16.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f16);
      _jspx_th_c_005fif_005f16_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f16, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f16_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f17(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f5, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f17 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f17_reused = false;
    try {
      _jspx_th_c_005fif_005f17.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f17.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f5);
      // /common/bottom_bar.jsp(188,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f17.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='flow' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f17 = _jspx_th_c_005fif_005f17.doStartTag();
      if (_jspx_eval_c_005fif_005f17 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_2_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f17.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f17.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f17);
      _jspx_th_c_005fif_005f17_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f17, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f17_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f18(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f5, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f18 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f18_reused = false;
    try {
      _jspx_th_c_005fif_005f18.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f18.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f5);
      // /common/bottom_bar.jsp(191,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f18.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='flow' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f18 = _jspx_th_c_005fif_005f18.doStartTag();
      if (_jspx_eval_c_005fif_005f18 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_2\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f18.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f18.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f18);
      _jspx_th_c_005fif_005f18_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f18, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f18_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f6(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f6 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f6_reused = false;
    try {
      _jspx_th_my_005fhp_005f6.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f6.setParent(null);
      // /common/bottom_bar.jsp(197,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f6.setMenu("twFlow");
      int _jspx_eval_my_005fhp_005f6 = _jspx_th_my_005fhp_005f6.doStartTag();
      if (_jspx_eval_my_005fhp_005f6 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f6 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f6);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/flow/twgf/flowTypeList.jsp?selectType=flow\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f19(_jspx_th_my_005fhp_005f6, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f20(_jspx_th_my_005fhp_005f6, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f21(_jspx_th_my_005fhp_005f6, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f22(_jspx_th_my_005fhp_005f6, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">待办</p>\r\n");
          out.write("            </a> ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f6.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f6 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f6.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f6);
      _jspx_th_my_005fhp_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f6, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f19(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f6, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f19 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f19_reused = false;
    try {
      _jspx_th_c_005fif_005f19.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f19.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f6);
      // /common/bottom_bar.jsp(200,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f19.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='flow' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f19 = _jspx_th_c_005fif_005f19.doStartTag();
      if (_jspx_eval_c_005fif_005f19 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f19.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f19.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f19);
      _jspx_th_c_005fif_005f19_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f19, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f19_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f20(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f6, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f20 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f20_reused = false;
    try {
      _jspx_th_c_005fif_005f20.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f20.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f6);
      // /common/bottom_bar.jsp(201,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f20.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${flowTodoCount!=0}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f20 = _jspx_th_c_005fif_005f20.doStartTag();
      if (_jspx_eval_c_005fif_005f20 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <span class=\"weui-badge\"\r\n");
          out.write("                          style=\"position: absolute;top: -.4em;\">");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${flowTodoCount}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("</span>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f20.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f20.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f20);
      _jspx_th_c_005fif_005f20_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f20, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f20_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f21(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f6, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f21 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f21_reused = false;
    try {
      _jspx_th_c_005fif_005f21.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f21.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f6);
      // /common/bottom_bar.jsp(205,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f21.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='flow' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f21 = _jspx_th_c_005fif_005f21.doStartTag();
      if (_jspx_eval_c_005fif_005f21 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_2_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f21.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f21.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f21);
      _jspx_th_c_005fif_005f21_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f21, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f21_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f22(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f6, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f22 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f22_reused = false;
    try {
      _jspx_th_c_005fif_005f22.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f22.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f6);
      // /common/bottom_bar.jsp(208,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f22.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='flow' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f22 = _jspx_th_c_005fif_005f22.doStartTag();
      if (_jspx_eval_c_005fif_005f22 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_2\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f22.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f22.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f22);
      _jspx_th_c_005fif_005f22_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f22, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f22_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f7(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f7 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f7_reused = false;
    try {
      _jspx_th_my_005fhp_005f7.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f7.setParent(null);
      // /common/bottom_bar.jsp(214,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f7.setMenu("report");
      int _jspx_eval_my_005fhp_005f7 = _jspx_th_my_005fhp_005f7.doStartTag();
      if (_jspx_eval_my_005fhp_005f7 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f7 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f7);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/report/originalReportList.jsp?selectType=report\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f23(_jspx_th_my_005fhp_005f7, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f24(_jspx_th_my_005fhp_005f7, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f25(_jspx_th_my_005fhp_005f7, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">统计分析</p>\r\n");
          out.write("            </a> ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f7.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f7 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f7.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f7);
      _jspx_th_my_005fhp_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f7, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f23(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f7, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f23 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f23_reused = false;
    try {
      _jspx_th_c_005fif_005f23.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f23.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f7);
      // /common/bottom_bar.jsp(217,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f23.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='report' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f23 = _jspx_th_c_005fif_005f23.doStartTag();
      if (_jspx_eval_c_005fif_005f23 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f23.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f23.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f23);
      _jspx_th_c_005fif_005f23_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f23, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f23_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f24(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f7, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f24 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f24_reused = false;
    try {
      _jspx_th_c_005fif_005f24.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f24.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f7);
      // /common/bottom_bar.jsp(218,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f24.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='report' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f24 = _jspx_th_c_005fif_005f24.doStartTag();
      if (_jspx_eval_c_005fif_005f24 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_3_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f24.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f24.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f24);
      _jspx_th_c_005fif_005f24_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f24, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f24_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f25(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f7, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f25 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f25_reused = false;
    try {
      _jspx_th_c_005fif_005f25.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f25.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f7);
      // /common/bottom_bar.jsp(221,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f25.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='report' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f25 = _jspx_th_c_005fif_005f25.doStartTag();
      if (_jspx_eval_c_005fif_005f25 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_3\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f25.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f25.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f25);
      _jspx_th_c_005fif_005f25_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f25, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f25_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f8(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f8 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f8_reused = false;
    try {
      _jspx_th_my_005fhp_005f8.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f8.setParent(null);
      // /common/bottom_bar.jsp(227,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f8.setMenu("warning");
      int _jspx_eval_my_005fhp_005f8 = _jspx_th_my_005fhp_005f8.doStartTag();
      if (_jspx_eval_my_005fhp_005f8 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f8 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f8);
        }
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fif_005f26(_jspx_th_my_005fhp_005f8, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f8.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f8 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f8.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f8);
      _jspx_th_my_005fhp_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f8, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f26(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f8, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f26 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f26_reused = false;
    try {
      _jspx_th_c_005fif_005f26.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f26.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f8);
      // /common/bottom_bar.jsp(228,12) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f26.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${hasWarningMsgPermission=='true'}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f26 = _jspx_th_c_005fif_005f26.doStartTag();
      if (_jspx_eval_c_005fif_005f26 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("                <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/warning/warning_msg.jsp?selectType=warning\"\r\n");
          out.write("                   class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f27(_jspx_th_c_005fif_005f26, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                    ");
          if (_jspx_meth_c_005fif_005f28(_jspx_th_c_005fif_005f26, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                    ");
          if (_jspx_meth_c_005fif_005f29(_jspx_th_c_005fif_005f26, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                    <p class=\"weui-tabbar__label\">消息提醒</p>\r\n");
          out.write("                </a>");
          int evalDoAfterBody = _jspx_th_c_005fif_005f26.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f26.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f26);
      _jspx_th_c_005fif_005f26_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f26, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f26_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f27(javax.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f26, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f27 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f27_reused = false;
    try {
      _jspx_th_c_005fif_005f27.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f27.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f26);
      // /common/bottom_bar.jsp(231,44) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f27.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='warning' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f27 = _jspx_th_c_005fif_005f27.doStartTag();
      if (_jspx_eval_c_005fif_005f27 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f27.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f27.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f27);
      _jspx_th_c_005fif_005f27_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f27, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f27_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f28(javax.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f26, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f28 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f28_reused = false;
    try {
      _jspx_th_c_005fif_005f28.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f28.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f26);
      // /common/bottom_bar.jsp(232,20) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f28.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='warning' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f28 = _jspx_th_c_005fif_005f28.doStartTag();
      if (_jspx_eval_c_005fif_005f28 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                        <i class=\"tab_icon tab_icon_4_selected\"></i>\r\n");
          out.write("                    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f28.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f28.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f28);
      _jspx_th_c_005fif_005f28_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f28, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f28_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f29(javax.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f26, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f29 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f29_reused = false;
    try {
      _jspx_th_c_005fif_005f29.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f29.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f26);
      // /common/bottom_bar.jsp(235,20) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f29.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='warning' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f29 = _jspx_th_c_005fif_005f29.doStartTag();
      if (_jspx_eval_c_005fif_005f29 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                        <i class=\"tab_icon tab_icon_4\"></i>\r\n");
          out.write("                    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f29.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f29.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f29);
      _jspx_th_c_005fif_005f29_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f29, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f29_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f9(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f9 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f9_reused = false;
    try {
      _jspx_th_my_005fhp_005f9.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f9.setParent(null);
      // /common/bottom_bar.jsp(242,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f9.setMenu("org");
      int _jspx_eval_my_005fhp_005f9 = _jspx_th_my_005fhp_005f9.doStartTag();
      if (_jspx_eval_my_005fhp_005f9 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f9 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f9);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/system/orgTrans.jsp?selectType=org\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f30(_jspx_th_my_005fhp_005f9, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f31(_jspx_th_my_005fhp_005f9, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f32(_jspx_th_my_005fhp_005f9, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">组织</p>\r\n");
          out.write("            </a>\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f9.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f9 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f9.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f9);
      _jspx_th_my_005fhp_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f9, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f30(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f9, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f30 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f30_reused = false;
    try {
      _jspx_th_c_005fif_005f30.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f30.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f9);
      // /common/bottom_bar.jsp(245,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f30.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='org' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f30 = _jspx_th_c_005fif_005f30.doStartTag();
      if (_jspx_eval_c_005fif_005f30 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f30.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f30.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f30);
      _jspx_th_c_005fif_005f30_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f30, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f30_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f31(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f9, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f31 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f31_reused = false;
    try {
      _jspx_th_c_005fif_005f31.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f31.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f9);
      // /common/bottom_bar.jsp(246,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f31.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='org' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f31 = _jspx_th_c_005fif_005f31.doStartTag();
      if (_jspx_eval_c_005fif_005f31 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_5_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f31.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f31.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f31);
      _jspx_th_c_005fif_005f31_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f31, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f31_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f32(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f9, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f32 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f32_reused = false;
    try {
      _jspx_th_c_005fif_005f32.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f32.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f9);
      // /common/bottom_bar.jsp(249,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f32.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='org' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f32 = _jspx_th_c_005fif_005f32.doStartTag();
      if (_jspx_eval_c_005fif_005f32 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_5\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f32.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f32.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f32);
      _jspx_th_c_005fif_005f32_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f32, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f32_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f10(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f10 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f10_reused = false;
    try {
      _jspx_th_my_005fhp_005f10.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f10.setParent(null);
      // /common/bottom_bar.jsp(256,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f10.setMenu("oriOrg");
      int _jspx_eval_my_005fhp_005f10 = _jspx_th_my_005fhp_005f10.doStartTag();
      if (_jspx_eval_my_005fhp_005f10 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f10 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f10);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/system/organizationSwitch.jsp?selectType=oriOrg\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f33(_jspx_th_my_005fhp_005f10, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f34(_jspx_th_my_005fhp_005f10, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f35(_jspx_th_my_005fhp_005f10, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">组织</p>\r\n");
          out.write("            </a>\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f10.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f10 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f10.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f10);
      _jspx_th_my_005fhp_005f10_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f10, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f10_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f33(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f10, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f33 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f33_reused = false;
    try {
      _jspx_th_c_005fif_005f33.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f33.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f10);
      // /common/bottom_bar.jsp(259,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f33.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='oriOrg' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f33 = _jspx_th_c_005fif_005f33.doStartTag();
      if (_jspx_eval_c_005fif_005f33 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f33.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f33.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f33);
      _jspx_th_c_005fif_005f33_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f33, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f33_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f34(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f10, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f34 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f34_reused = false;
    try {
      _jspx_th_c_005fif_005f34.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f34.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f10);
      // /common/bottom_bar.jsp(260,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f34.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='oriOrg' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f34 = _jspx_th_c_005fif_005f34.doStartTag();
      if (_jspx_eval_c_005fif_005f34 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_6_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f34.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f34.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f34);
      _jspx_th_c_005fif_005f34_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f34, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f34_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f35(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f10, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f35 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f35_reused = false;
    try {
      _jspx_th_c_005fif_005f35.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f35.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f10);
      // /common/bottom_bar.jsp(263,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f35.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='oriOrg' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f35 = _jspx_th_c_005fif_005f35.doStartTag();
      if (_jspx_eval_c_005fif_005f35 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_6\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f35.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f35.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f35);
      _jspx_th_c_005fif_005f35_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f35, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f35_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f11(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f11 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f11_reused = false;
    try {
      _jspx_th_my_005fhp_005f11.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f11.setParent(null);
      // /common/bottom_bar.jsp(270,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f11.setMenu("mine");
      int _jspx_eval_my_005fhp_005f11 = _jspx_th_my_005fhp_005f11.doStartTag();
      if (_jspx_eval_my_005fhp_005f11 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f11 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f11);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/system/mine.jsp?selectType=mine\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f36(_jspx_th_my_005fhp_005f11, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f37(_jspx_th_my_005fhp_005f11, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f38(_jspx_th_my_005fhp_005f11, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">我的</p>\r\n");
          out.write("            </a>\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f11.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f11 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f11.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f11);
      _jspx_th_my_005fhp_005f11_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f11, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f11_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f36(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f11, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f36 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f36_reused = false;
    try {
      _jspx_th_c_005fif_005f36.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f36.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f11);
      // /common/bottom_bar.jsp(273,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f36.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='mine' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f36 = _jspx_th_c_005fif_005f36.doStartTag();
      if (_jspx_eval_c_005fif_005f36 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f36.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f36.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f36);
      _jspx_th_c_005fif_005f36_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f36, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f36_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f37(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f11, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f37 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f37_reused = false;
    try {
      _jspx_th_c_005fif_005f37.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f37.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f11);
      // /common/bottom_bar.jsp(274,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f37.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='mine' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f37 = _jspx_th_c_005fif_005f37.doStartTag();
      if (_jspx_eval_c_005fif_005f37 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_7_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f37.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f37.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f37);
      _jspx_th_c_005fif_005f37_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f37, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f37_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f38(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f11, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f38 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f38_reused = false;
    try {
      _jspx_th_c_005fif_005f38.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f38.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f11);
      // /common/bottom_bar.jsp(277,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f38.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='mine' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f38 = _jspx_th_c_005fif_005f38.doStartTag();
      if (_jspx_eval_c_005fif_005f38 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_7\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f38.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f38.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f38);
      _jspx_th_c_005fif_005f38_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f38, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f38_reused);
    }
    return false;
  }
}
