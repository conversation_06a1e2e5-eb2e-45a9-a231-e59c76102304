/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/8.5.100
 * Generated at: 2025-04-29 09:57:08 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.modules2_4.flow;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import cn.hutool.core.util.StrUtil;
import com.fingard.app.delegate.framework.util.MyProperties;
import com.fingard.app.delegate.framework.util.Constants;
import com.fingard.app.delegate.framework.util.Constants;
import java.util.Set;
import com.fingard.app.delegate.controller.SysParamController;

public final class flowTypeList_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(6);
    _jspx_dependants.put("/common/bottom_bar.jsp", Long.valueOf(1742193138000L));
    _jspx_dependants.put("jar:file:/E:/project/other/FinancialBox/apache-tomcat-8.5.100/webapps/app/WEB-INF/lib/jstl-1.2.jar!/META-INF/fmt.tld", Long.valueOf(1153356282000L));
    _jspx_dependants.put("/common/include.jsp", Long.valueOf(1742193138000L));
    _jspx_dependants.put("/WEB-INF/lib/jstl-1.2.jar", Long.valueOf(1742193118000L));
    _jspx_dependants.put("jar:file:/E:/project/other/FinancialBox/apache-tomcat-8.5.100/webapps/app/WEB-INF/lib/jstl-1.2.jar!/META-INF/c.tld", Long.valueOf(1153356282000L));
    _jspx_dependants.put("/WEB-INF/my.tld", Long.valueOf(1742193117000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(3);
    _jspx_imports_packages.add("javax.servlet");
    _jspx_imports_packages.add("javax.servlet.http");
    _jspx_imports_packages.add("javax.servlet.jsp");
    _jspx_imports_classes = new java.util.LinkedHashSet<>(5);
    _jspx_imports_classes.add("com.fingard.app.delegate.framework.util.Constants");
    _jspx_imports_classes.add("cn.hutool.core.util.StrUtil");
    _jspx_imports_classes.add("com.fingard.app.delegate.framework.util.MyProperties");
    _jspx_imports_classes.add("com.fingard.app.delegate.controller.SysParamController");
    _jspx_imports_classes.add("java.util.Set");
  }

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;

  private volatile javax.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public javax.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.release();
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
      throws java.io.IOException, javax.servlet.ServletException {

    final java.lang.String _jspx_method = request.getMethod();
    if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method) && !javax.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSP 只允许 GET、POST 或 HEAD。Jasper 还允许 OPTIONS");
      return;
    }

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html;charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write('\r');
      out.write('\n');
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");

    String customer = MyProperties.getMyPropertiesInstance().getProperty("currentCustomerName");
    String customerCode = MyProperties.getMyPropertiesInstance().getProperty("customerCode");
    String version = MyProperties.getMyPropertiesInstance().getProperty("appDelegateVersion");
    String basePath = request.getContextPath();
    Boolean virtualSwitch = MyProperties.getMyPropertiesInstance().getProperty3("virtualSwitch", false);
    if (virtualSwitch) {
        String virtualPath = MyProperties.getMyPropertiesInstance().getProperty("virtualPath");
        virtualPath = StrUtil.isEmpty(virtualPath) ? StrUtil.EMPTY : virtualPath;
        basePath = virtualPath + basePath;
    }
    response.setHeader("Cache-Control", "no-store");
    response.setHeader("Pragma", "no-cache");
    response.setDateHeader("Expires", 0);

    String permissionCode = String.valueOf(request.getSession().getAttribute(Constants.AUTHORITY));
    String savedUserId = String.valueOf(request.getSession().getAttribute(Constants.USER));
    String savedUserName = String.valueOf(request.getSession().getAttribute(Constants.REAL_NAME));
    Boolean siczyMsgCheck = MyProperties.getMyPropertiesInstance().getProperty3("flow.msgCheck", false);
    String endpoint = MyProperties.getMyPropertiesInstance().getProperty("ats.endpoint");
    String atsVersion = "2.0";
    if(StrUtil.containsAny(endpoint,"EXTERNALSERV")){
        atsVersion = "3.0";
    }

//    boolean waterMakerOpen = MyProperties.getMyPropertiesInstance().getProperty3("waterMaker.open");


      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f0 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f0_reused = false;
      try {
        _jspx_th_c_005fset_005f0.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f0.setParent(null);
        // /common/include.jsp(36,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f0.setVar("ctx");
        // /common/include.jsp(36,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f0.setValue(basePath);
        int _jspx_eval_c_005fset_005f0 = _jspx_th_c_005fset_005f0.doStartTag();
        if (_jspx_th_c_005fset_005f0.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f0);
        _jspx_th_c_005fset_005f0_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f0_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f1 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f1_reused = false;
      try {
        _jspx_th_c_005fset_005f1.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f1.setParent(null);
        // /common/include.jsp(37,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f1.setVar("customer");
        // /common/include.jsp(37,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f1.setValue(customer);
        int _jspx_eval_c_005fset_005f1 = _jspx_th_c_005fset_005f1.doStartTag();
        if (_jspx_th_c_005fset_005f1.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f1);
        _jspx_th_c_005fset_005f1_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f1_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f2 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f2_reused = false;
      try {
        _jspx_th_c_005fset_005f2.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f2.setParent(null);
        // /common/include.jsp(38,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f2.setVar("permissionCode");
        // /common/include.jsp(38,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f2.setValue(permissionCode);
        int _jspx_eval_c_005fset_005f2 = _jspx_th_c_005fset_005f2.doStartTag();
        if (_jspx_th_c_005fset_005f2.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f2);
        _jspx_th_c_005fset_005f2_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f2_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f3 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f3_reused = false;
      try {
        _jspx_th_c_005fset_005f3.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f3.setParent(null);
        // /common/include.jsp(39,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f3.setVar("userId");
        // /common/include.jsp(39,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f3.setValue(savedUserId);
        int _jspx_eval_c_005fset_005f3 = _jspx_th_c_005fset_005f3.doStartTag();
        if (_jspx_th_c_005fset_005f3.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f3);
        _jspx_th_c_005fset_005f3_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f3_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f4 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f4_reused = false;
      try {
        _jspx_th_c_005fset_005f4.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f4.setParent(null);
        // /common/include.jsp(40,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f4.setVar("realUserName");
        // /common/include.jsp(40,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f4.setValue(savedUserName);
        int _jspx_eval_c_005fset_005f4 = _jspx_th_c_005fset_005f4.doStartTag();
        if (_jspx_th_c_005fset_005f4.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f4);
        _jspx_th_c_005fset_005f4_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f4_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f5 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f5_reused = false;
      try {
        _jspx_th_c_005fset_005f5.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f5.setParent(null);
        // /common/include.jsp(41,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f5.setVar("atsVersion");
        // /common/include.jsp(41,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f5.setValue(atsVersion);
        int _jspx_eval_c_005fset_005f5 = _jspx_th_c_005fset_005f5.doStartTag();
        if (_jspx_th_c_005fset_005f5.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f5);
        _jspx_th_c_005fset_005f5_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f5_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f6 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f6_reused = false;
      try {
        _jspx_th_c_005fset_005f6.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f6.setParent(null);
        // /common/include.jsp(42,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f6.setVar("customerCode");
        // /common/include.jsp(42,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f6.setValue(customerCode);
        int _jspx_eval_c_005fset_005f6 = _jspx_th_c_005fset_005f6.doStartTag();
        if (_jspx_th_c_005fset_005f6.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f6);
        _jspx_th_c_005fset_005f6_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f6_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f7 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f7_reused = false;
      try {
        _jspx_th_c_005fset_005f7.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f7.setParent(null);
        // /common/include.jsp(43,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f7.setVar("version");
        // /common/include.jsp(43,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f7.setValue(version);
        int _jspx_eval_c_005fset_005f7 = _jspx_th_c_005fset_005f7.doStartTag();
        if (_jspx_th_c_005fset_005f7.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f7);
        _jspx_th_c_005fset_005f7_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f7_reused);
      }
      out.write('\r');
      out.write('\n');
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write('\r');
      out.write('\n');

    String openFlowCode = MyProperties.getMyPropertiesInstance().getProperty("ats.flow.type");
    String custoemrName = MyProperties.getMyPropertiesInstance().getProperty("currentCustomerName");

      out.write("\r\n");
      out.write("<!DOCTYPE html>\r\n");
      out.write("<html lang=\"en\">\r\n");
      out.write("\r\n");
      out.write("<head>\r\n");
      out.write("    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\"/>\r\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no\">\r\n");
      out.write("    <meta content=\"no-cache\">\r\n");
      out.write("    <meta content=\"0\">\r\n");
      out.write("    <title>流程类别列表</title>\r\n");
      out.write("    <link rel=\"stylesheet\" href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/css/weui.min.css\">\r\n");
      out.write("    <link rel=\"stylesheet\" href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/css/weuix.min.css\">\r\n");
      out.write("\r\n");
      out.write("    <style>\r\n");
      out.write("\r\n");
      out.write("        .weui-cells {\r\n");
      out.write("            margin-top: 0;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .weui-cell {\r\n");
      out.write("            padding: 15px 20px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .weui-msgbox p {\r\n");
      out.write("            padding: 80% 15px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .weui-navbar + .weui-tab__bd {\r\n");
      out.write("            padding-bottom: 55px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .no-permission {\r\n");
      out.write("            position: relative;\r\n");
      out.write("            display: none;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .no-permission img {\r\n");
      out.write("            width: 95px;\r\n");
      out.write("            height: 95px;\r\n");
      out.write("            display: inline-block;\r\n");
      out.write("            position: absolute;\r\n");
      out.write("            margin-left: 50%;\r\n");
      out.write("            margin-top: 50%;\r\n");
      out.write("            left: -50px;\r\n");
      out.write("            top: 50px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .no-permission span {\r\n");
      out.write("            display: inline-block;\r\n");
      out.write("            position: absolute;\r\n");
      out.write("            margin-left: 50%;\r\n");
      out.write("            margin-top: 50%;\r\n");
      out.write("            left: -32px;\r\n");
      out.write("            top: 145px;\r\n");
      out.write("            color: #999999;\r\n");
      out.write("            font-size: 14px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("    </style>\r\n");
      out.write("</head>\r\n");
      out.write("\r\n");
      out.write("<body>\r\n");
      out.write("\r\n");
      out.write("<div class=\"weui-tab\">\r\n");
      out.write("\r\n");
      out.write("    <div class=\"weui-navbar \" style=\"position: fixed; top: 0;\">\r\n");
      out.write("        <a id=\"todo-tab\" class=\"weui-navbar__item weui-bar__item--on\" href=\"#todo__list\">\r\n");
      out.write("            待办\r\n");
      out.write("        </a>\r\n");
      out.write("        <a id=\"done-tab\" class=\"weui-navbar__item\" href=\"#done__list\">\r\n");
      out.write("            已办\r\n");
      out.write("        </a>\r\n");
      out.write("    </div>\r\n");
      out.write("\r\n");
      out.write("    <!-- 其他内容 -->\r\n");
      out.write("    <div class=\"weui-tab__bd\">\r\n");
      out.write("        <div id=\"todo__list\" class=\"weui-tab__bd-item weui-tab__bd-item--active\"\r\n");
      out.write("             style=\"position: relative;margin-top: -100px;height: calc(100% + 50px);-webkit-overflow-scrolling: touch;\">\r\n");
      out.write("            <div class=\"weui-pull-to-refresh__layer\" style=\"background: #ffffff\">\r\n");
      out.write("                <div class=\"weui-pull-to-refresh__arrow\"></div>\r\n");
      out.write("                <div class=\"weui-pull-to-refresh__preloader\"></div>\r\n");
      out.write("                <div class=\"down\">下拉刷新</div>\r\n");
      out.write("                <div class=\"up\">释放刷新</div>\r\n");
      out.write("                <div class=\"refresh\">正在刷新</div>\r\n");
      out.write("            </div>\r\n");
      out.write("            <div class=\"weui-cells\">\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("            </div>\r\n");
      out.write("            <div class=\"no-permission\" style=\"display: none;\">\r\n");
      out.write("                <img src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/report/<EMAIL>\" alt=\"暂无权限\">\r\n");
      out.write("                <span>暂无权限</span>\r\n");
      out.write("            </div>\r\n");
      out.write("        </div>\r\n");
      out.write("        <div id=\"done__list\" class=\"weui-tab__bd-item\"\r\n");
      out.write("             style=\"position: relative;margin-top: -100px;height: calc(100% + 50px);-webkit-overflow-scrolling: touch;\">\r\n");
      out.write("            <div class=\"weui-pull-to-refresh__layer\" style=\"background: #ffffff\">\r\n");
      out.write("                <div class=\"weui-pull-to-refresh__arrow\"></div>\r\n");
      out.write("                <div class=\"weui-pull-to-refresh__preloader\"></div>\r\n");
      out.write("                <div class=\"down\">下拉刷新</div>\r\n");
      out.write("                <div class=\"up\">释放刷新</div>\r\n");
      out.write("                <div class=\"refresh\">正在刷新</div>\r\n");
      out.write("            </div>\r\n");
      out.write("            <div class=\"weui-cells\">\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("            </div>\r\n");
      out.write("            <div class=\"no-permission\" style=\"display: none;\">\r\n");
      out.write("                <img src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/report/<EMAIL>\" alt=\"暂无权限\">\r\n");
      out.write("                <span>暂无权限</span>\r\n");
      out.write("            </div>\r\n");
      out.write("        </div>\r\n");
      out.write("    </div>\r\n");
      out.write("\r\n");
      out.write("    ");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");


    String menuCustomer = MyProperties.getMyPropertiesInstance().getProperty("currentCustomerName");
    Object authority = request.getSession().getAttribute(Constants.AUTHORITY);
    boolean hasWarningMsgPermission = true;
    if (authority instanceof Set) {
        Set<String> set = (Set<String>) authority;
        hasWarningMsgPermission = set.contains("WPSS01");

    }
    if (authority instanceof String) {
        String string = (String) authority;
        hasWarningMsgPermission = string.contains("WPSS01");

    }

    int flowTodoCount = SysParamController.getFlowTodoCount(request);


      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f8 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f8_reused = false;
      try {
        _jspx_th_c_005fset_005f8.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f8.setParent(null);
        // /common/bottom_bar.jsp(24,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f8.setVar("hasWarningMsgPermission");
        // /common/bottom_bar.jsp(24,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f8.setValue(hasWarningMsgPermission);
        int _jspx_eval_c_005fset_005f8 = _jspx_th_c_005fset_005f8.doStartTag();
        if (_jspx_th_c_005fset_005f8.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f8);
        _jspx_th_c_005fset_005f8_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f8, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f8_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f9 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f9_reused = false;
      try {
        _jspx_th_c_005fset_005f9.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f9.setParent(null);
        // /common/bottom_bar.jsp(25,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f9.setVar("flowTodoCount");
        // /common/bottom_bar.jsp(25,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f9.setValue(flowTodoCount);
        int _jspx_eval_c_005fset_005f9 = _jspx_th_c_005fset_005f9.doStartTag();
        if (_jspx_th_c_005fset_005f9.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f9);
        _jspx_th_c_005fset_005f9_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f9, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f9_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f10 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f10_reused = false;
      try {
        _jspx_th_c_005fset_005f10.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f10.setParent(null);
        // /common/bottom_bar.jsp(26,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f10.setVar("menu_customer");
        // /common/bottom_bar.jsp(26,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f10.setValue(menuCustomer);
        int _jspx_eval_c_005fset_005f10 = _jspx_th_c_005fset_005f10.doStartTag();
        if (_jspx_th_c_005fset_005f10.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f10);
        _jspx_th_c_005fset_005f10_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f10, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f10_reused);
      }
      out.write("\r\n");
      out.write("\r\n");
      out.write("<style>\r\n");
      out.write("\r\n");
      out.write("    .weui-tabbar__label {\r\n");
      out.write("        font-size: 12px;\r\n");
      out.write("        line-height: 16px;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .weui-tabbar__item.weui-bar__item--on .weui-tabbar__label {\r\n");
      out.write("        color: #649CFF;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .weui-tabbar__item {\r\n");
      out.write("        padding: 5px 0;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon {\r\n");
      out.write("        display: block;\r\n");
      out.write("        background-position: center;\r\n");
      out.write("        background-size: 28px 28px;\r\n");
      out.write("        width: 28px;\r\n");
      out.write("        height: 28px;\r\n");
      out.write("        margin-left: auto;\r\n");
      out.write("        margin-right: auto;\r\n");
      out.write("        background-repeat: no-repeat;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_1 {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/Group <EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_2 {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/Group <EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_3 {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/Group <EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_4 {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/Group <EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_5 {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/<EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_6 {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/org_ori.png');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_7 {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/mine.png');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_1_selected {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/Group <EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_2_selected {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/Group <EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_3_selected {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/Group <EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_4_selected {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/Group <EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_5_selected {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/<EMAIL>');\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .tab_icon_6_selected {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/org_ori_select.png');\r\n");
      out.write("    }\r\n");
      out.write("    .tab_icon_7_selected {\r\n");
      out.write("        background-image: url('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/mine_2.png');\r\n");
      out.write("    }\r\n");
      out.write("</style>\r\n");
      out.write("<div class=\"weui-tab tab-bottom\" style=\"height:49px;z-index: 999;\">\r\n");
      out.write("    <div class=\"weui-tabbar\">\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f1(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f2(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f3(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f4(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f5(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f6(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f7(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f8(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f9(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f10(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_my_005fhp_005f11(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("    </div>\r\n");
      out.write("</div>\r\n");
      out.write("\r\n");
      out.write("<script>\r\n");
      out.write("    function clickTab(obj){\r\n");
      out.write("        console.log(obj);\r\n");
      out.write("        location.replace(obj.data('url'))\r\n");
      out.write("    }\r\n");
      out.write("</script>\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("</div>\r\n");
      out.write("\r\n");
      out.write("<script src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/js/zepto.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/js/zepto.weui.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/js/iscroll-lite.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/js/common.js?version=");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${version}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("\"></script>\r\n");
      out.write("\r\n");
      out.write("<script>\r\n");
      out.write("\r\n");
      out.write("    let openFLowCode = '");
      out.print(openFlowCode);
      out.write("'.split(',');\r\n");
      out.write("    let customerName = '");
      out.print(custoemrName);
      out.write("';\r\n");
      out.write("    sessionStorage.setItem(\"customerName\", customerName);\r\n");
      out.write("    $(document).ready(function () {\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("        let todoTag = sessionStorage.getItem(\"TODO\");\r\n");
      out.write("        if (todoTag == null || todoTag == undefined) {\r\n");
      out.write("            todoTag = TODO_TAG;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        var isPageHide = false;\r\n");
      out.write("        window.addEventListener('pageshow', function () {\r\n");
      out.write("            if (isPageHide) {\r\n");
      out.write("                window.location.reload();\r\n");
      out.write("            }\r\n");
      out.write("        });\r\n");
      out.write("        window.addEventListener('pagehide', function () {\r\n");
      out.write("            isPageHide = true;\r\n");
      out.write("        });\r\n");
      out.write("        let todo_list = $('#todo__list');\r\n");
      out.write("        let done_list = $('#done__list');\r\n");
      out.write("\r\n");
      out.write("        let todo_tab = $('#todo-tab');\r\n");
      out.write("        let done_tab = $('#done-tab');\r\n");
      out.write("\r\n");
      out.write("        if (todoTag == DONE_TAG) {\r\n");
      out.write("            $('#todo-tab').removeClass('weui-bar__item--on')\r\n");
      out.write("            $('#done-tab').addClass('weui-bar__item--on')\r\n");
      out.write("            todo_list.removeClass('weui-tab__bd-item--active');\r\n");
      out.write("            done_list.addClass('weui-tab__bd-item--active');\r\n");
      out.write("\r\n");
      out.write("        }\r\n");
      out.write("        initData(function () {\r\n");
      out.write("\r\n");
      out.write("        }, todoTag);\r\n");
      out.write("        todo_list.pullToRefresh().on('pull-to-refresh', function (done) {\r\n");
      out.write("            initData(function () {\r\n");
      out.write("            }, TODO_TAG);\r\n");
      out.write("        });\r\n");
      out.write("        done_list.pullToRefresh().on('pull-to-refresh', function (done) {\r\n");
      out.write("            initData(function () {\r\n");
      out.write("            }, DONE_TAG);\r\n");
      out.write("        });\r\n");
      out.write("\r\n");
      out.write("        todo_tab.on('click', function () {\r\n");
      out.write("\r\n");
      out.write("            initData(function () {\r\n");
      out.write("            }, TODO_TAG);\r\n");
      out.write("\r\n");
      out.write("            todoTag = TODO_TAG;\r\n");
      out.write("            sessionStorage.setItem(\"TODO\", todoTag);\r\n");
      out.write("\r\n");
      out.write("        });\r\n");
      out.write("\r\n");
      out.write("        done_tab.on('click', function () {\r\n");
      out.write("            initData(function () {\r\n");
      out.write("            }, DONE_TAG);\r\n");
      out.write("\r\n");
      out.write("            todoTag = DONE_TAG;\r\n");
      out.write("            sessionStorage.setItem(\"TODO\", todoTag);\r\n");
      out.write("\r\n");
      out.write("        });\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("        $(document.body).on('touchmove', function (e) {\r\n");
      out.write("            e.preventDefault();\r\n");
      out.write("        })\r\n");
      out.write("\r\n");
      out.write("    });\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("    let toPage = function (cellData) {\r\n");
      out.write("        console.log(cellData);\r\n");
      out.write("        sessionStorage.setItem('SUB_FLOW_PARAMS', JSON.stringify(cellData));\r\n");
      out.write("        sessionStorage.setItem('ZJFK', 'false');\r\n");
      out.write("        sessionStorage.setItem(\"TODO\", cellData.dealState);\r\n");
      out.write("        sessionStorage.setItem(\"FLOWTYPEID\", cellData.flowTypeId);\r\n");
      out.write("\r\n");
      out.write("        if (cellData.totalNum == 0) {\r\n");
      out.write("            $.toast(\"该流程暂无待办事项\", \"cancel\");\r\n");
      out.write("        } else {\r\n");
      out.write("            if (cellData.flowTypeId !== 'ZJFK') {\r\n");
      out.write("                location.href = 'flowList.jsp';\r\n");
      out.write("            } else {\r\n");
      out.write("                location.href = 'flowListSingle.jsp';\r\n");
      out.write("\r\n");
      out.write("            }\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("    };\r\n");
      out.write("\r\n");
      out.write("    let initData = function (afterSuccess, tag) {\r\n");
      out.write("        delegate.ready(function (paramsData) {\r\n");
      out.write("            if (paramsData.result.permissionCode.indexOf(PERMISSION_CODE_FLOW) === -1) {\r\n");
      out.write("                $('.no-permission').css('display', 'block');\r\n");
      out.write("            } else {\r\n");
      out.write("                $.showLoading(\"加载中...\")\r\n");
      out.write("                let params = {};\r\n");
      out.write("                params.dealState = tag;\r\n");
      out.write("                ajaxRequest('/saasflow/getFullFlowTypeList.do', params, function (data) {\r\n");
      out.write("                    let cells_todo, cells_done;\r\n");
      out.write("\r\n");
      out.write("                    if (tag == TODO_TAG) {\r\n");
      out.write("                        cells_todo = $('#todo__list').find('.weui-cells');\r\n");
      out.write("                        $('#todo__list').pullToRefreshDone();\r\n");
      out.write("                        cells_todo.html('');\r\n");
      out.write("                        let todo_list = data.result.todoList;\r\n");
      out.write("                        if (data.message === '查询记录为空' || todo_list.length === 0) {\r\n");
      out.write("                            cells_todo.append('<div class=\"weui-msgbox\" style=\"height: 50%; line-height: 50%\">\\n' +\r\n");
      out.write("                                '    <p>\\n' +\r\n");
      out.write("                                '        <i class=\"weui-icon-info-circle\"></i>查询记录为空\\n' +\r\n");
      out.write("                                '    </p>\\n' +\r\n");
      out.write("                                '</div>');\r\n");
      out.write("                        }\r\n");
      out.write("\r\n");
      out.write("                        if (todo_list.length === 0) {\r\n");
      out.write("                            return;\r\n");
      out.write("                        }\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("                        for (let i = 0; i < todo_list.length; i++) {\r\n");
      out.write("                            let cellData = todo_list[i];\r\n");
      out.write("                            let cell;\r\n");
      out.write("                            let obj = JSON.stringify(cellData);\r\n");
      out.write("                            if (openFLowCode.indexOf(cellData.flowTypeId) !== -1) {\r\n");
      out.write("                                let flowDesc;\r\n");
      out.write("                                if (cellData.totalNum === '0') {\r\n");
      out.write("                                    flowDesc = \"暂无待办事项\";\r\n");
      out.write("                                } else {\r\n");
      out.write("                                    flowDesc = '您有' + cellData.totalNum + '笔' + cellData.flowTypeName + '，请及时处理';\r\n");
      out.write("                                }\r\n");
      out.write("                                let img = cellData.totalNum === '0' ? (iconImg[cellData.flowTypeId] === undefined ? iconImg.ZHSQ : iconImg[cellData.flowTypeId]) : (iconImgPoint[cellData.flowTypeId] === undefined ? iconImgPoint.ZHSQ : iconImgPoint[cellData.flowTypeId]);\r\n");
      out.write("                                cell = $(\"<a class='weui-cell weui-cell_access' href='javascript:toPage(\" + obj + \");'>\" +\r\n");
      out.write("                                    '<div class=\"weui-cell__hd\"><img src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/czdn/flow/' + img + '\" style=\"width: 50px;height: 50px; margin-right: 10px;\">' +\r\n");
      out.write("                                    '</div>' +\r\n");
      out.write("                                    '    <div class=\"weui-cell__bd\">' +\r\n");
      out.write("                                    '      <h4>' + cellData.flowTypeName + '</h4>' +\r\n");
      out.write("                                    '      <p style=\"color: #BDBDBD; font-size: 13px\" >' + flowDesc + '</p>' +\r\n");
      out.write("\r\n");
      out.write("                                    '    </div>' +\r\n");
      out.write("                                    '<div class=\"weui-cell__ft\"></div>' +\r\n");
      out.write("                                    '  </a>');\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("                                cells_todo.append(cell);\r\n");
      out.write("                            }\r\n");
      out.write("                        }\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("                    } else {\r\n");
      out.write("                        cells_done = $('#done__list').find('.weui-cells');\r\n");
      out.write("                        $('#done__list').pullToRefreshDone();\r\n");
      out.write("                        cells_done.html('');\r\n");
      out.write("                        let done_list = data.result.doneList;\r\n");
      out.write("                        if (data.message === '查询记录为空' || done_list.length === 0) {\r\n");
      out.write("                            cells_done.append('<div class=\"weui-msgbox\" style=\"height: 50%; line-height: 50%\">\\n' +\r\n");
      out.write("                                '    <p>\\n' +\r\n");
      out.write("                                '        <i class=\"weui-icon-info-circle\"></i>查询记录为空\\n' +\r\n");
      out.write("                                '    </p>\\n' +\r\n");
      out.write("                                '</div>');\r\n");
      out.write("                        }\r\n");
      out.write("                        if (done_list.length === 0) {\r\n");
      out.write("                            return;\r\n");
      out.write("                        }\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("                        for (let i = 0; i < done_list.length; i++) {\r\n");
      out.write("                            let cellData = done_list[i];\r\n");
      out.write("                            let cell;\r\n");
      out.write("                            let obj = JSON.stringify(cellData);\r\n");
      out.write("                            if (openFLowCode.indexOf(cellData.flowTypeId) !== -1) {\r\n");
      out.write("                                let img = iconImgDoneFlow[cellData.flowTypeId]===undefined?iconImgDoneFlow.ZHSQ:iconImgDoneFlow[cellData.flowTypeId];\r\n");
      out.write("                                cell = $(\"<a class='weui-cell weui-cell_access' href='javascript:toPage(\" + obj + \");'>\" +\r\n");
      out.write("                                    '<div class=\"weui-cell__hd\"><img src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/czdn/flow/' + img + '\" style=\"width: 40px;height: 40px; margin-right: 10px;\">' +\r\n");
      out.write("                                    '</div>' +\r\n");
      out.write("\r\n");
      out.write("                                    '    <div class=\"weui-cell__bd\">' +\r\n");
      out.write("                                    '      <h4>' + cellData.flowTypeName + '</h4>' +\r\n");
      out.write("                                    '    </div>' +\r\n");
      out.write("                                    '    <div class=\"weui-cell__ft\">' +\r\n");
      out.write("                                    '<span style=\"margin-left: 5px;\">' + cellData.totalNum + '</span>' +\r\n");
      out.write("                                    '</div>' +\r\n");
      out.write("                                    '  </a>');\r\n");
      out.write("\r\n");
      out.write("                                cells_done.append(cell);\r\n");
      out.write("                            }\r\n");
      out.write("                        }\r\n");
      out.write("\r\n");
      out.write("                    }\r\n");
      out.write("\r\n");
      out.write("                    if (afterSuccess !== undefined && afterSuccess != null) {\r\n");
      out.write("                        afterSuccess();\r\n");
      out.write("                    }\r\n");
      out.write("                });\r\n");
      out.write("            }\r\n");
      out.write("\r\n");
      out.write("        })\r\n");
      out.write("    }\r\n");
      out.write("</script>\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("</body>\r\n");
      out.write("\r\n");
      out.write("</html>\r\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_my_005fhp_005f0(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f0 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f0_reused = false;
    try {
      _jspx_th_my_005fhp_005f0.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f0.setParent(null);
      // /common/bottom_bar.jsp(111,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f0.setMenu("home");
      int _jspx_eval_my_005fhp_005f0 = _jspx_th_my_005fhp_005f0.doStartTag();
      if (_jspx_eval_my_005fhp_005f0 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f0 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f0);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a  onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/home_page/home.jsp?selectType=home\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f0(_jspx_th_my_005fhp_005f0, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f1(_jspx_th_my_005fhp_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f2(_jspx_th_my_005fhp_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">首页</p>\r\n");
          out.write("            </a>\r\n");
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f0.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f0 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f0.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f0);
      _jspx_th_my_005fhp_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f0, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f0(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f0, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f0_reused = false;
    try {
      _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f0.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f0);
      // /common/bottom_bar.jsp(114,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='home' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
      if (_jspx_eval_c_005fif_005f0 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f0.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      _jspx_th_c_005fif_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f1(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f0, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f1 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f1_reused = false;
    try {
      _jspx_th_c_005fif_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f1.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f0);
      // /common/bottom_bar.jsp(115,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='home' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f1 = _jspx_th_c_005fif_005f1.doStartTag();
      if (_jspx_eval_c_005fif_005f1 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f1.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f1.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
      _jspx_th_c_005fif_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f2(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f0, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f2 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f2_reused = false;
    try {
      _jspx_th_c_005fif_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f2.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f0);
      // /common/bottom_bar.jsp(118,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='home' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f2 = _jspx_th_c_005fif_005f2.doStartTag();
      if (_jspx_eval_c_005fif_005f2 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f2.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f2.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
      _jspx_th_c_005fif_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f1(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f1 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f1_reused = false;
    try {
      _jspx_th_my_005fhp_005f1.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f1.setParent(null);
      // /common/bottom_bar.jsp(125,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f1.setMenu("cdcyjtHome");
      int _jspx_eval_my_005fhp_005f1 = _jspx_th_my_005fhp_005f1.doStartTag();
      if (_jspx_eval_my_005fhp_005f1 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f1 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f1);
        }
        do {
          out.write("\r\n");
          out.write("            <a  onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/home_page/cdcyjt_home.jsp?selectType=cdcyjtHome\"\r\n");
          out.write("                class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f3(_jspx_th_my_005fhp_005f1, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f4(_jspx_th_my_005fhp_005f1, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f5(_jspx_th_my_005fhp_005f1, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">首页</p>\r\n");
          out.write("            </a>\r\n");
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f1.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f1 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f1.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f1);
      _jspx_th_my_005fhp_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f1, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f3(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f1, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f3 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f3_reused = false;
    try {
      _jspx_th_c_005fif_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f3.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f1);
      // /common/bottom_bar.jsp(127,41) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f3.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='home' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f3 = _jspx_th_c_005fif_005f3.doStartTag();
      if (_jspx_eval_c_005fif_005f3 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f3.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f3.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f3);
      _jspx_th_c_005fif_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f4(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f1, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f4 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f4_reused = false;
    try {
      _jspx_th_c_005fif_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f4.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f1);
      // /common/bottom_bar.jsp(128,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f4.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='cdcyjtHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f4 = _jspx_th_c_005fif_005f4.doStartTag();
      if (_jspx_eval_c_005fif_005f4 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f4.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f4.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f4);
      _jspx_th_c_005fif_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f5(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f1, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f5 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f5_reused = false;
    try {
      _jspx_th_c_005fif_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f5.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f1);
      // /common/bottom_bar.jsp(131,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f5.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='cdcyjtHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f5 = _jspx_th_c_005fif_005f5.doStartTag();
      if (_jspx_eval_c_005fif_005f5 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f5.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f5.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f5);
      _jspx_th_c_005fif_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f2(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f2 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f2_reused = false;
    try {
      _jspx_th_my_005fhp_005f2.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f2.setParent(null);
      // /common/bottom_bar.jsp(138,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f2.setMenu("siczyHome");
      int _jspx_eval_my_005fhp_005f2 = _jspx_th_my_005fhp_005f2.doStartTag();
      if (_jspx_eval_my_005fhp_005f2 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f2 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f2);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/report/standard/SCZYBankBalance.jsp?selectType=siczyHome\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f6(_jspx_th_my_005fhp_005f2, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f7(_jspx_th_my_005fhp_005f2, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f8(_jspx_th_my_005fhp_005f2, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">首页</p>\r\n");
          out.write("            </a>\r\n");
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f2.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f2 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f2.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f2);
      _jspx_th_my_005fhp_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f2, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f6(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f2, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f6 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f6_reused = false;
    try {
      _jspx_th_c_005fif_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f6.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f2);
      // /common/bottom_bar.jsp(141,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f6.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='siczyHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f6 = _jspx_th_c_005fif_005f6.doStartTag();
      if (_jspx_eval_c_005fif_005f6 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f6.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f6.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f6);
      _jspx_th_c_005fif_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f7(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f2, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f7 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f7_reused = false;
    try {
      _jspx_th_c_005fif_005f7.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f7.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f2);
      // /common/bottom_bar.jsp(142,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f7.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='siczyHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f7 = _jspx_th_c_005fif_005f7.doStartTag();
      if (_jspx_eval_c_005fif_005f7 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f7.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f7.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f7);
      _jspx_th_c_005fif_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f8(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f2, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f8 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f8_reused = false;
    try {
      _jspx_th_c_005fif_005f8.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f8.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f2);
      // /common/bottom_bar.jsp(145,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f8.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='siczyHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f8 = _jspx_th_c_005fif_005f8.doStartTag();
      if (_jspx_eval_c_005fif_005f8 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f8.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f8.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f8);
      _jspx_th_c_005fif_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f8, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f3(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f3 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f3_reused = false;
    try {
      _jspx_th_my_005fhp_005f3.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f3.setParent(null);
      // /common/bottom_bar.jsp(152,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f3.setMenu("zllHome");
      int _jspx_eval_my_005fhp_005f3 = _jspx_th_my_005fhp_005f3.doStartTag();
      if (_jspx_eval_my_005fhp_005f3 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f3 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f3);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/report/standard/new_fund_summary_bank_balance_financing.jsp?selectType=zllHome\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f9(_jspx_th_my_005fhp_005f3, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f10(_jspx_th_my_005fhp_005f3, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f11(_jspx_th_my_005fhp_005f3, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">首页</p>\r\n");
          out.write("            </a>\r\n");
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f3.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f3 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f3.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f3);
      _jspx_th_my_005fhp_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f3, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f9(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f3, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f9 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f9_reused = false;
    try {
      _jspx_th_c_005fif_005f9.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f9.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f3);
      // /common/bottom_bar.jsp(155,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f9.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='zllHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f9 = _jspx_th_c_005fif_005f9.doStartTag();
      if (_jspx_eval_c_005fif_005f9 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f9.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f9.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f9);
      _jspx_th_c_005fif_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f9, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f10(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f3, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f10 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f10_reused = false;
    try {
      _jspx_th_c_005fif_005f10.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f10.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f3);
      // /common/bottom_bar.jsp(156,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f10.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='zllHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f10 = _jspx_th_c_005fif_005f10.doStartTag();
      if (_jspx_eval_c_005fif_005f10 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f10.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f10.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f10);
      _jspx_th_c_005fif_005f10_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f10, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f10_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f11(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f3, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f11 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f11_reused = false;
    try {
      _jspx_th_c_005fif_005f11.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f11.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f3);
      // /common/bottom_bar.jsp(159,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f11.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='zllHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f11 = _jspx_th_c_005fif_005f11.doStartTag();
      if (_jspx_eval_c_005fif_005f11 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f11.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f11.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f11);
      _jspx_th_c_005fif_005f11_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f11, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f11_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f4(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f4 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f4_reused = false;
    try {
      _jspx_th_my_005fhp_005f4.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f4.setParent(null);
      // /common/bottom_bar.jsp(166,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f4.setMenu("zmjHome");
      int _jspx_eval_my_005fhp_005f4 = _jspx_th_my_005fhp_005f4.doStartTag();
      if (_jspx_eval_my_005fhp_005f4 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f4 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f4);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/home_page/zmj_home.jsp?selectType=zmjHome\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f12(_jspx_th_my_005fhp_005f4, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f13(_jspx_th_my_005fhp_005f4, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f14(_jspx_th_my_005fhp_005f4, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">首页</p>\r\n");
          out.write("            </a>\r\n");
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f4.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f4 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f4.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f4);
      _jspx_th_my_005fhp_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f4, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f12(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f4, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f12 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f12_reused = false;
    try {
      _jspx_th_c_005fif_005f12.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f12.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f4);
      // /common/bottom_bar.jsp(169,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f12.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='zmjHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f12 = _jspx_th_c_005fif_005f12.doStartTag();
      if (_jspx_eval_c_005fif_005f12 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f12.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f12.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f12);
      _jspx_th_c_005fif_005f12_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f12, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f12_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f13(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f4, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f13 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f13_reused = false;
    try {
      _jspx_th_c_005fif_005f13.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f13.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f4);
      // /common/bottom_bar.jsp(170,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f13.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='zmjHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f13 = _jspx_th_c_005fif_005f13.doStartTag();
      if (_jspx_eval_c_005fif_005f13 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f13.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f13.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f13);
      _jspx_th_c_005fif_005f13_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f13, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f13_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f14(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f4, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f14 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f14_reused = false;
    try {
      _jspx_th_c_005fif_005f14.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f14.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f4);
      // /common/bottom_bar.jsp(173,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f14.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='zmjHome' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f14 = _jspx_th_c_005fif_005f14.doStartTag();
      if (_jspx_eval_c_005fif_005f14 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_1\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f14.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f14.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f14);
      _jspx_th_c_005fif_005f14_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f14, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f14_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f5(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f5 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f5_reused = false;
    try {
      _jspx_th_my_005fhp_005f5.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f5.setParent(null);
      // /common/bottom_bar.jsp(180,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f5.setMenu("flow");
      int _jspx_eval_my_005fhp_005f5 = _jspx_th_my_005fhp_005f5.doStartTag();
      if (_jspx_eval_my_005fhp_005f5 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f5 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f5);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/flow/flowTypeList.jsp?selectType=flow\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f15(_jspx_th_my_005fhp_005f5, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f16(_jspx_th_my_005fhp_005f5, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f17(_jspx_th_my_005fhp_005f5, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f18(_jspx_th_my_005fhp_005f5, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">待办</p>\r\n");
          out.write("            </a> ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f5.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f5 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f5.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f5);
      _jspx_th_my_005fhp_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f5, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f15(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f5, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f15 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f15_reused = false;
    try {
      _jspx_th_c_005fif_005f15.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f15.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f5);
      // /common/bottom_bar.jsp(183,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f15.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='flow' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f15 = _jspx_th_c_005fif_005f15.doStartTag();
      if (_jspx_eval_c_005fif_005f15 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f15.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f15.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f15);
      _jspx_th_c_005fif_005f15_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f15, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f15_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f16(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f5, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f16 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f16_reused = false;
    try {
      _jspx_th_c_005fif_005f16.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f16.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f5);
      // /common/bottom_bar.jsp(184,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f16.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${flowTodoCount!=0}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f16 = _jspx_th_c_005fif_005f16.doStartTag();
      if (_jspx_eval_c_005fif_005f16 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <span class=\"weui-badge\"\r\n");
          out.write("                          style=\"position: absolute;top: -.4em;\">");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${flowTodoCount}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("</span>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f16.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f16.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f16);
      _jspx_th_c_005fif_005f16_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f16, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f16_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f17(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f5, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f17 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f17_reused = false;
    try {
      _jspx_th_c_005fif_005f17.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f17.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f5);
      // /common/bottom_bar.jsp(188,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f17.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='flow' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f17 = _jspx_th_c_005fif_005f17.doStartTag();
      if (_jspx_eval_c_005fif_005f17 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_2_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f17.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f17.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f17);
      _jspx_th_c_005fif_005f17_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f17, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f17_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f18(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f5, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f18 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f18_reused = false;
    try {
      _jspx_th_c_005fif_005f18.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f18.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f5);
      // /common/bottom_bar.jsp(191,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f18.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='flow' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f18 = _jspx_th_c_005fif_005f18.doStartTag();
      if (_jspx_eval_c_005fif_005f18 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_2\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f18.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f18.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f18);
      _jspx_th_c_005fif_005f18_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f18, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f18_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f6(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f6 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f6_reused = false;
    try {
      _jspx_th_my_005fhp_005f6.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f6.setParent(null);
      // /common/bottom_bar.jsp(197,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f6.setMenu("twFlow");
      int _jspx_eval_my_005fhp_005f6 = _jspx_th_my_005fhp_005f6.doStartTag();
      if (_jspx_eval_my_005fhp_005f6 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f6 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f6);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/flow/twgf/flowTypeList.jsp?selectType=flow\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f19(_jspx_th_my_005fhp_005f6, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f20(_jspx_th_my_005fhp_005f6, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f21(_jspx_th_my_005fhp_005f6, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f22(_jspx_th_my_005fhp_005f6, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">待办</p>\r\n");
          out.write("            </a> ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f6.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f6 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f6.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f6);
      _jspx_th_my_005fhp_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f6, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f19(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f6, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f19 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f19_reused = false;
    try {
      _jspx_th_c_005fif_005f19.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f19.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f6);
      // /common/bottom_bar.jsp(200,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f19.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='flow' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f19 = _jspx_th_c_005fif_005f19.doStartTag();
      if (_jspx_eval_c_005fif_005f19 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f19.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f19.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f19);
      _jspx_th_c_005fif_005f19_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f19, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f19_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f20(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f6, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f20 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f20_reused = false;
    try {
      _jspx_th_c_005fif_005f20.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f20.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f6);
      // /common/bottom_bar.jsp(201,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f20.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${flowTodoCount!=0}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f20 = _jspx_th_c_005fif_005f20.doStartTag();
      if (_jspx_eval_c_005fif_005f20 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <span class=\"weui-badge\"\r\n");
          out.write("                          style=\"position: absolute;top: -.4em;\">");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${flowTodoCount}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("</span>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f20.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f20.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f20);
      _jspx_th_c_005fif_005f20_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f20, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f20_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f21(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f6, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f21 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f21_reused = false;
    try {
      _jspx_th_c_005fif_005f21.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f21.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f6);
      // /common/bottom_bar.jsp(205,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f21.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='flow' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f21 = _jspx_th_c_005fif_005f21.doStartTag();
      if (_jspx_eval_c_005fif_005f21 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_2_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f21.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f21.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f21);
      _jspx_th_c_005fif_005f21_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f21, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f21_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f22(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f6, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f22 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f22_reused = false;
    try {
      _jspx_th_c_005fif_005f22.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f22.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f6);
      // /common/bottom_bar.jsp(208,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f22.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='flow' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f22 = _jspx_th_c_005fif_005f22.doStartTag();
      if (_jspx_eval_c_005fif_005f22 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_2\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f22.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f22.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f22);
      _jspx_th_c_005fif_005f22_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f22, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f22_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f7(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f7 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f7_reused = false;
    try {
      _jspx_th_my_005fhp_005f7.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f7.setParent(null);
      // /common/bottom_bar.jsp(214,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f7.setMenu("report");
      int _jspx_eval_my_005fhp_005f7 = _jspx_th_my_005fhp_005f7.doStartTag();
      if (_jspx_eval_my_005fhp_005f7 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f7 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f7);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/report/originalReportList.jsp?selectType=report\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f23(_jspx_th_my_005fhp_005f7, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f24(_jspx_th_my_005fhp_005f7, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f25(_jspx_th_my_005fhp_005f7, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">统计分析</p>\r\n");
          out.write("            </a> ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f7.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f7 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f7.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f7);
      _jspx_th_my_005fhp_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f7, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f23(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f7, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f23 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f23_reused = false;
    try {
      _jspx_th_c_005fif_005f23.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f23.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f7);
      // /common/bottom_bar.jsp(217,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f23.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='report' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f23 = _jspx_th_c_005fif_005f23.doStartTag();
      if (_jspx_eval_c_005fif_005f23 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f23.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f23.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f23);
      _jspx_th_c_005fif_005f23_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f23, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f23_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f24(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f7, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f24 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f24_reused = false;
    try {
      _jspx_th_c_005fif_005f24.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f24.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f7);
      // /common/bottom_bar.jsp(218,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f24.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='report' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f24 = _jspx_th_c_005fif_005f24.doStartTag();
      if (_jspx_eval_c_005fif_005f24 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_3_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f24.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f24.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f24);
      _jspx_th_c_005fif_005f24_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f24, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f24_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f25(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f7, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f25 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f25_reused = false;
    try {
      _jspx_th_c_005fif_005f25.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f25.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f7);
      // /common/bottom_bar.jsp(221,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f25.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='report' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f25 = _jspx_th_c_005fif_005f25.doStartTag();
      if (_jspx_eval_c_005fif_005f25 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_3\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f25.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f25.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f25);
      _jspx_th_c_005fif_005f25_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f25, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f25_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f8(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f8 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f8_reused = false;
    try {
      _jspx_th_my_005fhp_005f8.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f8.setParent(null);
      // /common/bottom_bar.jsp(227,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f8.setMenu("warning");
      int _jspx_eval_my_005fhp_005f8 = _jspx_th_my_005fhp_005f8.doStartTag();
      if (_jspx_eval_my_005fhp_005f8 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f8 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f8);
        }
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fif_005f26(_jspx_th_my_005fhp_005f8, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f8.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f8 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f8.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f8);
      _jspx_th_my_005fhp_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f8, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f26(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f8, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f26 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f26_reused = false;
    try {
      _jspx_th_c_005fif_005f26.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f26.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f8);
      // /common/bottom_bar.jsp(228,12) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f26.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${hasWarningMsgPermission=='true'}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f26 = _jspx_th_c_005fif_005f26.doStartTag();
      if (_jspx_eval_c_005fif_005f26 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("                <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/warning/warning_msg.jsp?selectType=warning\"\r\n");
          out.write("                   class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f27(_jspx_th_c_005fif_005f26, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                    ");
          if (_jspx_meth_c_005fif_005f28(_jspx_th_c_005fif_005f26, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                    ");
          if (_jspx_meth_c_005fif_005f29(_jspx_th_c_005fif_005f26, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                    <p class=\"weui-tabbar__label\">消息提醒</p>\r\n");
          out.write("                </a>");
          int evalDoAfterBody = _jspx_th_c_005fif_005f26.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f26.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f26);
      _jspx_th_c_005fif_005f26_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f26, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f26_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f27(javax.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f26, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f27 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f27_reused = false;
    try {
      _jspx_th_c_005fif_005f27.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f27.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f26);
      // /common/bottom_bar.jsp(231,44) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f27.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='warning' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f27 = _jspx_th_c_005fif_005f27.doStartTag();
      if (_jspx_eval_c_005fif_005f27 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f27.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f27.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f27);
      _jspx_th_c_005fif_005f27_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f27, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f27_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f28(javax.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f26, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f28 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f28_reused = false;
    try {
      _jspx_th_c_005fif_005f28.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f28.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f26);
      // /common/bottom_bar.jsp(232,20) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f28.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='warning' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f28 = _jspx_th_c_005fif_005f28.doStartTag();
      if (_jspx_eval_c_005fif_005f28 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                        <i class=\"tab_icon tab_icon_4_selected\"></i>\r\n");
          out.write("                    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f28.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f28.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f28);
      _jspx_th_c_005fif_005f28_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f28, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f28_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f29(javax.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f26, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f29 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f29_reused = false;
    try {
      _jspx_th_c_005fif_005f29.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f29.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f26);
      // /common/bottom_bar.jsp(235,20) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f29.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='warning' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f29 = _jspx_th_c_005fif_005f29.doStartTag();
      if (_jspx_eval_c_005fif_005f29 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                        <i class=\"tab_icon tab_icon_4\"></i>\r\n");
          out.write("                    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f29.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f29.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f29);
      _jspx_th_c_005fif_005f29_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f29, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f29_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f9(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f9 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f9_reused = false;
    try {
      _jspx_th_my_005fhp_005f9.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f9.setParent(null);
      // /common/bottom_bar.jsp(242,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f9.setMenu("org");
      int _jspx_eval_my_005fhp_005f9 = _jspx_th_my_005fhp_005f9.doStartTag();
      if (_jspx_eval_my_005fhp_005f9 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f9 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f9);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/system/orgTrans.jsp?selectType=org\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f30(_jspx_th_my_005fhp_005f9, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f31(_jspx_th_my_005fhp_005f9, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f32(_jspx_th_my_005fhp_005f9, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">组织</p>\r\n");
          out.write("            </a>\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f9.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f9 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f9.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f9);
      _jspx_th_my_005fhp_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f9, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f30(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f9, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f30 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f30_reused = false;
    try {
      _jspx_th_c_005fif_005f30.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f30.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f9);
      // /common/bottom_bar.jsp(245,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f30.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='org' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f30 = _jspx_th_c_005fif_005f30.doStartTag();
      if (_jspx_eval_c_005fif_005f30 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f30.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f30.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f30);
      _jspx_th_c_005fif_005f30_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f30, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f30_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f31(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f9, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f31 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f31_reused = false;
    try {
      _jspx_th_c_005fif_005f31.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f31.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f9);
      // /common/bottom_bar.jsp(246,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f31.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='org' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f31 = _jspx_th_c_005fif_005f31.doStartTag();
      if (_jspx_eval_c_005fif_005f31 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_5_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f31.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f31.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f31);
      _jspx_th_c_005fif_005f31_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f31, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f31_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f32(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f9, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f32 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f32_reused = false;
    try {
      _jspx_th_c_005fif_005f32.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f32.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f9);
      // /common/bottom_bar.jsp(249,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f32.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='org' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f32 = _jspx_th_c_005fif_005f32.doStartTag();
      if (_jspx_eval_c_005fif_005f32 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_5\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f32.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f32.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f32);
      _jspx_th_c_005fif_005f32_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f32, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f32_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f10(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f10 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f10_reused = false;
    try {
      _jspx_th_my_005fhp_005f10.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f10.setParent(null);
      // /common/bottom_bar.jsp(256,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f10.setMenu("oriOrg");
      int _jspx_eval_my_005fhp_005f10 = _jspx_th_my_005fhp_005f10.doStartTag();
      if (_jspx_eval_my_005fhp_005f10 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f10 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f10);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/system/organizationSwitch.jsp?selectType=oriOrg\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f33(_jspx_th_my_005fhp_005f10, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f34(_jspx_th_my_005fhp_005f10, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f35(_jspx_th_my_005fhp_005f10, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">组织</p>\r\n");
          out.write("            </a>\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f10.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f10 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f10.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f10);
      _jspx_th_my_005fhp_005f10_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f10, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f10_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f33(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f10, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f33 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f33_reused = false;
    try {
      _jspx_th_c_005fif_005f33.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f33.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f10);
      // /common/bottom_bar.jsp(259,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f33.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='oriOrg' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f33 = _jspx_th_c_005fif_005f33.doStartTag();
      if (_jspx_eval_c_005fif_005f33 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f33.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f33.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f33);
      _jspx_th_c_005fif_005f33_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f33, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f33_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f34(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f10, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f34 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f34_reused = false;
    try {
      _jspx_th_c_005fif_005f34.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f34.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f10);
      // /common/bottom_bar.jsp(260,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f34.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='oriOrg' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f34 = _jspx_th_c_005fif_005f34.doStartTag();
      if (_jspx_eval_c_005fif_005f34 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_6_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f34.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f34.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f34);
      _jspx_th_c_005fif_005f34_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f34, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f34_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f35(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f10, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f35 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f35_reused = false;
    try {
      _jspx_th_c_005fif_005f35.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f35.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f10);
      // /common/bottom_bar.jsp(263,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f35.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='oriOrg' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f35 = _jspx_th_c_005fif_005f35.doStartTag();
      if (_jspx_eval_c_005fif_005f35 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_6\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f35.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f35.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f35);
      _jspx_th_c_005fif_005f35_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f35, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f35_reused);
    }
    return false;
  }

  private boolean _jspx_meth_my_005fhp_005f11(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  my:hp
    com.fingard.app.delegate.tag.HasPermission _jspx_th_my_005fhp_005f11 = (com.fingard.app.delegate.tag.HasPermission) _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.get(com.fingard.app.delegate.tag.HasPermission.class);
    boolean _jspx_th_my_005fhp_005f11_reused = false;
    try {
      _jspx_th_my_005fhp_005f11.setPageContext(_jspx_page_context);
      _jspx_th_my_005fhp_005f11.setParent(null);
      // /common/bottom_bar.jsp(270,8) name = menu type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_my_005fhp_005f11.setMenu("mine");
      int _jspx_eval_my_005fhp_005f11 = _jspx_th_my_005fhp_005f11.doStartTag();
      if (_jspx_eval_my_005fhp_005f11 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_my_005fhp_005f11 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_my_005fhp_005f11);
        }
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <a onclick=clickTab($(this)) data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("/modules2.4/system/mine.jsp?selectType=mine\"\r\n");
          out.write("               class=\"weui-tabbar__item ");
          if (_jspx_meth_c_005fif_005f36(_jspx_th_my_005fhp_005f11, _jspx_page_context))
            return true;
          out.write("\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f37(_jspx_th_my_005fhp_005f11, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f38(_jspx_th_my_005fhp_005f11, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("                <p class=\"weui-tabbar__label\">我的</p>\r\n");
          out.write("            </a>\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_my_005fhp_005f11.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_my_005fhp_005f11 != javax.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_my_005fhp_005f11.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fmy_005fhp_0026_005fmenu.reuse(_jspx_th_my_005fhp_005f11);
      _jspx_th_my_005fhp_005f11_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_my_005fhp_005f11, _jsp_getInstanceManager(), _jspx_th_my_005fhp_005f11_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f36(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f11, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f36 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f36_reused = false;
    try {
      _jspx_th_c_005fif_005f36.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f36.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f11);
      // /common/bottom_bar.jsp(273,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f36.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='mine' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f36 = _jspx_th_c_005fif_005f36.doStartTag();
      if (_jspx_eval_c_005fif_005f36 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("weui-bar__item--on");
          int evalDoAfterBody = _jspx_th_c_005fif_005f36.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f36.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f36);
      _jspx_th_c_005fif_005f36_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f36, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f36_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f37(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f11, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f37 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f37_reused = false;
    try {
      _jspx_th_c_005fif_005f37.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f37.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f11);
      // /common/bottom_bar.jsp(274,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f37.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType=='mine' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f37 = _jspx_th_c_005fif_005f37.doStartTag();
      if (_jspx_eval_c_005fif_005f37 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_7_selected\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f37.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f37.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f37);
      _jspx_th_c_005fif_005f37_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f37, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f37_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f38(javax.servlet.jsp.tagext.JspTag _jspx_th_my_005fhp_005f11, javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f38 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f38_reused = false;
    try {
      _jspx_th_c_005fif_005f38.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f38.setParent((javax.servlet.jsp.tagext.Tag) _jspx_th_my_005fhp_005f11);
      // /common/bottom_bar.jsp(277,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f38.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.selectType!='mine' || empty param.selectType}", boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f38 = _jspx_th_c_005fif_005f38.doStartTag();
      if (_jspx_eval_c_005fif_005f38 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <i class=\"tab_icon tab_icon_7\"></i>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f38.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f38.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f38);
      _jspx_th_c_005fif_005f38_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f38, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f38_reused);
    }
    return false;
  }
}
