/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/8.5.100
 * Generated at: 2025-05-06 02:19:43 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.modules2_4.report.standard;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import cn.hutool.core.util.StrUtil;
import com.fingard.app.delegate.framework.util.MyProperties;
import com.fingard.app.delegate.framework.util.Constants;

public final class BankBalanceReport_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(5);
    _jspx_dependants.put("jar:file:/E:/project/other/FinancialBox/apache-tomcat-8.5.100/webapps/app/WEB-INF/lib/jstl-1.2.jar!/META-INF/fmt.tld", Long.valueOf(********82000L));
    _jspx_dependants.put("/common/include.jsp", Long.valueOf(1742193138000L));
    _jspx_dependants.put("/WEB-INF/lib/jstl-1.2.jar", Long.valueOf(1742193118000L));
    _jspx_dependants.put("jar:file:/E:/project/other/FinancialBox/apache-tomcat-8.5.100/webapps/app/WEB-INF/lib/jstl-1.2.jar!/META-INF/c.tld", Long.valueOf(********82000L));
    _jspx_dependants.put("/WEB-INF/my.tld", Long.valueOf(1742193117000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(3);
    _jspx_imports_packages.add("javax.servlet");
    _jspx_imports_packages.add("javax.servlet.http");
    _jspx_imports_packages.add("javax.servlet.jsp");
    _jspx_imports_classes = new java.util.LinkedHashSet<>(3);
    _jspx_imports_classes.add("com.fingard.app.delegate.framework.util.Constants");
    _jspx_imports_classes.add("cn.hutool.core.util.StrUtil");
    _jspx_imports_classes.add("com.fingard.app.delegate.framework.util.MyProperties");
  }

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody;

  private volatile javax.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public javax.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.release();
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
      throws java.io.IOException, javax.servlet.ServletException {

    final java.lang.String _jspx_method = request.getMethod();
    if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method) && !javax.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSP 只允许 GET、POST 或 HEAD。Jasper 还允许 OPTIONS");
      return;
    }

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html;charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write('\r');
      out.write('\n');
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");

    String customer = MyProperties.getMyPropertiesInstance().getProperty("currentCustomerName");
    String customerCode = MyProperties.getMyPropertiesInstance().getProperty("customerCode");
    String version = MyProperties.getMyPropertiesInstance().getProperty("appDelegateVersion");
    String basePath = request.getContextPath();
    Boolean virtualSwitch = MyProperties.getMyPropertiesInstance().getProperty3("virtualSwitch", false);
    if (virtualSwitch) {
        String virtualPath = MyProperties.getMyPropertiesInstance().getProperty("virtualPath");
        virtualPath = StrUtil.isEmpty(virtualPath) ? StrUtil.EMPTY : virtualPath;
        basePath = virtualPath + basePath;
    }
    response.setHeader("Cache-Control", "no-store");
    response.setHeader("Pragma", "no-cache");
    response.setDateHeader("Expires", 0);

    String permissionCode = String.valueOf(request.getSession().getAttribute(Constants.AUTHORITY));
    String savedUserId = String.valueOf(request.getSession().getAttribute(Constants.USER));
    String savedUserName = String.valueOf(request.getSession().getAttribute(Constants.REAL_NAME));
    Boolean siczyMsgCheck = MyProperties.getMyPropertiesInstance().getProperty3("flow.msgCheck", false);
    String endpoint = MyProperties.getMyPropertiesInstance().getProperty("ats.endpoint");
    String atsVersion = "2.0";
    if(StrUtil.containsAny(endpoint,"EXTERNALSERV")){
        atsVersion = "3.0";
    }

//    boolean waterMakerOpen = MyProperties.getMyPropertiesInstance().getProperty3("waterMaker.open");


      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f0 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f0_reused = false;
      try {
        _jspx_th_c_005fset_005f0.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f0.setParent(null);
        // /common/include.jsp(36,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f0.setVar("ctx");
        // /common/include.jsp(36,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f0.setValue(basePath);
        int _jspx_eval_c_005fset_005f0 = _jspx_th_c_005fset_005f0.doStartTag();
        if (_jspx_th_c_005fset_005f0.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f0);
        _jspx_th_c_005fset_005f0_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f0_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f1 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f1_reused = false;
      try {
        _jspx_th_c_005fset_005f1.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f1.setParent(null);
        // /common/include.jsp(37,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f1.setVar("customer");
        // /common/include.jsp(37,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f1.setValue(customer);
        int _jspx_eval_c_005fset_005f1 = _jspx_th_c_005fset_005f1.doStartTag();
        if (_jspx_th_c_005fset_005f1.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f1);
        _jspx_th_c_005fset_005f1_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f1_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f2 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f2_reused = false;
      try {
        _jspx_th_c_005fset_005f2.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f2.setParent(null);
        // /common/include.jsp(38,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f2.setVar("permissionCode");
        // /common/include.jsp(38,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f2.setValue(permissionCode);
        int _jspx_eval_c_005fset_005f2 = _jspx_th_c_005fset_005f2.doStartTag();
        if (_jspx_th_c_005fset_005f2.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f2);
        _jspx_th_c_005fset_005f2_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f2_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f3 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f3_reused = false;
      try {
        _jspx_th_c_005fset_005f3.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f3.setParent(null);
        // /common/include.jsp(39,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f3.setVar("userId");
        // /common/include.jsp(39,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f3.setValue(savedUserId);
        int _jspx_eval_c_005fset_005f3 = _jspx_th_c_005fset_005f3.doStartTag();
        if (_jspx_th_c_005fset_005f3.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f3);
        _jspx_th_c_005fset_005f3_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f3_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f4 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f4_reused = false;
      try {
        _jspx_th_c_005fset_005f4.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f4.setParent(null);
        // /common/include.jsp(40,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f4.setVar("realUserName");
        // /common/include.jsp(40,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f4.setValue(savedUserName);
        int _jspx_eval_c_005fset_005f4 = _jspx_th_c_005fset_005f4.doStartTag();
        if (_jspx_th_c_005fset_005f4.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f4);
        _jspx_th_c_005fset_005f4_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f4_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f5 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f5_reused = false;
      try {
        _jspx_th_c_005fset_005f5.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f5.setParent(null);
        // /common/include.jsp(41,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f5.setVar("atsVersion");
        // /common/include.jsp(41,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f5.setValue(atsVersion);
        int _jspx_eval_c_005fset_005f5 = _jspx_th_c_005fset_005f5.doStartTag();
        if (_jspx_th_c_005fset_005f5.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f5);
        _jspx_th_c_005fset_005f5_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f5_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f6 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f6_reused = false;
      try {
        _jspx_th_c_005fset_005f6.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f6.setParent(null);
        // /common/include.jsp(42,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f6.setVar("customerCode");
        // /common/include.jsp(42,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f6.setValue(customerCode);
        int _jspx_eval_c_005fset_005f6 = _jspx_th_c_005fset_005f6.doStartTag();
        if (_jspx_th_c_005fset_005f6.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f6);
        _jspx_th_c_005fset_005f6_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f6_reused);
      }
      out.write('\r');
      out.write('\n');
      //  c:set
      org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f7 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
      boolean _jspx_th_c_005fset_005f7_reused = false;
      try {
        _jspx_th_c_005fset_005f7.setPageContext(_jspx_page_context);
        _jspx_th_c_005fset_005f7.setParent(null);
        // /common/include.jsp(43,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f7.setVar("version");
        // /common/include.jsp(43,0) name = value type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
        _jspx_th_c_005fset_005f7.setValue(version);
        int _jspx_eval_c_005fset_005f7 = _jspx_th_c_005fset_005f7.doStartTag();
        if (_jspx_th_c_005fset_005f7.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return;
        }
        _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f7);
        _jspx_th_c_005fset_005f7_reused = true;
      } finally {
        org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f7_reused);
      }
      out.write('\r');
      out.write('\n');
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("<!DOCTYPE html>\r\n");
      out.write("<html lang=\"en\">\r\n");
      out.write("<head>\r\n");
      out.write("    <meta charset=\"utf-8\">\r\n");
      out.write("    <title>银行余额统计</title>\r\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no\">\r\n");
      out.write("    <link rel=\"stylesheet\" href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/css/weui.min.css\"/>\r\n");
      out.write("    <link rel=\"stylesheet\" href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/css/weuix.min.css\"/>\r\n");
      out.write("    <style>\r\n");
      out.write("        #tagnav {\r\n");
      out.write("            position: fixed;\r\n");
      out.write("            top: 0;\r\n");
      out.write("            z-index: 999;\r\n");
      out.write("            background: #649CFF;\r\n");
      out.write("            border-bottom: 1px solid rgba(255, 255, 255, 0.2);\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .weui-navigator-list li {\r\n");
      out.write("            line-height: 40px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a {\r\n");
      out.write("            color: #93FFDB;\r\n");
      out.write("            font-weight: bold;\r\n");
      out.write("            border-bottom: 1px solid #93FFDB;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .weui-navigator-list li.weui-state-disabled a {\r\n");
      out.write("            color: rgba(255, 255, 255, 0.2);\r\n");
      out.write("            border-bottom: 1px solid #649CFF;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .weui-navigator-list li a {\r\n");
      out.write("            color: #ffffff;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .weui-navigator-list li.weui-state-hover, .weui-navigator-list li.weui-state-active a:after {\r\n");
      out.write("            background-color: transparent;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        #reportTitle {\r\n");
      out.write("            padding-top: 43px;\r\n");
      out.write("            background: #649CFF;\r\n");
      out.write("            color: #fff;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        #center {\r\n");
      out.write("            width: 100%;\r\n");
      out.write("            text-align: center;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        #center .tip {\r\n");
      out.write("            display: block;\r\n");
      out.write("            padding-top: 30px;\r\n");
      out.write("            font-size: 12px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        #center .amount {\r\n");
      out.write("            display: block;\r\n");
      out.write("            padding-bottom: 30px;\r\n");
      out.write("            font-size: 24px;\r\n");
      out.write("            line-height: 24px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        #left, #right {\r\n");
      out.write("            display: inline-block;\r\n");
      out.write("            width: 48%;\r\n");
      out.write("            text-align: center;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .leftTip, .rightTip {\r\n");
      out.write("            display: inline-block;\r\n");
      out.write("            padding-top: 38px;\r\n");
      out.write("            font-size: 12px;\r\n");
      out.write("            color: rgba(255, 255, 255, 0.7);\r\n");
      out.write("            position: relative;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .leftTip:before, .rightTip:before {\r\n");
      out.write("            content: ' ';\r\n");
      out.write("            width: 11px;\r\n");
      out.write("            height: 11px;\r\n");
      out.write("            display: inline-block;\r\n");
      out.write("            border: 1px solid #ffffff;\r\n");
      out.write("            border-radius: 6px;\r\n");
      out.write("            position: absolute;\r\n");
      out.write("            top: 40px;\r\n");
      out.write("            left: -15px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .leftTip:before {\r\n");
      out.write("            background: #84DD86;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .rightTip:before {\r\n");
      out.write("            background: #FFD392;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .leftAmount, .rightAmount {\r\n");
      out.write("            padding-bottom: 30px;\r\n");
      out.write("            display: block;\r\n");
      out.write("            font-size: 18px;\r\n");
      out.write("            font-weight: 500;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .line {\r\n");
      out.write("            display: inline-block;\r\n");
      out.write("            width: 1%;\r\n");
      out.write("            max-width: 1px;\r\n");
      out.write("            background: #fff;\r\n");
      out.write("            height: 30px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .titleInfo {\r\n");
      out.write("            padding-left: 20px;\r\n");
      out.write("            padding-top: 18px;\r\n");
      out.write("            font-size: 14px;\r\n");
      out.write("            color: #5A5858;\r\n");
      out.write("            font-weight: bold;\r\n");
      out.write("            display: block;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .titleInfoArrow:after {\r\n");
      out.write("            content: \" \";\r\n");
      out.write("            display: inline-block;\r\n");
      out.write("            height: 7px;\r\n");
      out.write("            width: 7px;\r\n");
      out.write("            border-width: 2px 2px 0 0;\r\n");
      out.write("            border-color: #c8c8cd;\r\n");
      out.write("            border-style: solid;\r\n");
      out.write("            transform: matrix(.71, .71, -.71, .71, 0, 0);\r\n");
      out.write("            webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);\r\n");
      out.write("            top: -2px;\r\n");
      out.write("            position: absolute;\r\n");
      out.write("            top: 50%;\r\n");
      out.write("            margin-top: -4px;\r\n");
      out.write("            right: 22px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .amountAndTip3 {\r\n");
      out.write("            display: inline-block;\r\n");
      out.write("            width: calc(30% - 20px);\r\n");
      out.write("            padding-left: 20px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .amountAndTip3 .amount {\r\n");
      out.write("            display: block;\r\n");
      out.write("            color: #FF7979;\r\n");
      out.write("            font-size: 16px;\r\n");
      out.write("            font-weight: bold;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .amountAndTip3 .tip {\r\n");
      out.write("            font-size: 10px;\r\n");
      out.write("            color: #999999;\r\n");
      out.write("            display: block;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .amountAndTip {\r\n");
      out.write("            display: inline-block;\r\n");
      out.write("            width: calc(48% - 20px);\r\n");
      out.write("            padding-left: 20px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .amountAndTip .amount {\r\n");
      out.write("            display: block;\r\n");
      out.write("            color: #FF7979;\r\n");
      out.write("            font-size: 16px;\r\n");
      out.write("            font-weight: bold;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .amountAndTip .tip {\r\n");
      out.write("            font-size: 10px;\r\n");
      out.write("            color: #999999;\r\n");
      out.write("            display: block;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        #noMsg {\r\n");
      out.write("            position: relative;\r\n");
      out.write("            display: none;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        #noMsg img {\r\n");
      out.write("            width: 95px;\r\n");
      out.write("            height: 95px;\r\n");
      out.write("            display: inline-block;\r\n");
      out.write("            position: absolute;\r\n");
      out.write("            margin-left: 50%;\r\n");
      out.write("            margin-top: 50%;\r\n");
      out.write("            left: -50px;\r\n");
      out.write("            top: 50px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        #noMsg span {\r\n");
      out.write("            display: inline-block;\r\n");
      out.write("            position: absolute;\r\n");
      out.write("            margin-left: 50%;\r\n");
      out.write("            margin-top: 50%;\r\n");
      out.write("            left: -32px;\r\n");
      out.write("            top: 145px;\r\n");
      out.write("            color: #999999;\r\n");
      out.write("            font-size: 14px;\r\n");
      out.write("        }\r\n");
      out.write("    </style>\r\n");
      out.write("</head>\r\n");
      out.write("<body>\r\n");
      out.write("<div id=\"tagnav\" class=\"weui-navigator weui-navigator-wrapper\">\r\n");
      out.write("    <ul class=\"weui-navigator-list\"></ul>\r\n");
      out.write("</div>\r\n");
      out.write("<div id=\"reportTitle\">\r\n");
      out.write("    <!-- <div id=\"center\"><span class=\"tip\">总余额/万元</span><span class=\"amount\" style=\"padding-bottom: 0px;\">3828,222.72</span></div>\r\n");
      out.write("    <div id=\"left\"><span class=\"leftTip\">直联/万元</span><span class=\"leftAmount\">10000</span></div>\r\n");
      out.write("    <div class=\"line\"></div>\r\n");
      out.write("    <div id=\"right\"><span class=\"rightTip\">非直联/万元</span><span class=\"rightAmount\">18888</span></div> -->\r\n");
      out.write("</div>\r\n");
      out.write("<div id=\"chartSection\"></div>\r\n");
      out.write("<div id=\"chartListInfo\"></div>\r\n");
      out.write("<div id=\"noMsg\">\r\n");
      out.write("    <img src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/img/report/<EMAIL>\" alt=\"暂无数据\">\r\n");
      out.write("    <span>暂无数据</span>\r\n");
      out.write("</div>\r\n");
      out.write("\r\n");
      out.write("<script src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/js/zepto.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/js/zepto.weui.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/js/iscroll-lite.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/js/common.js?version=");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${version}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("\"></script>\r\n");
      out.write("<script src=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ctx}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/js/echarts.min.js\"></script>\r\n");
      out.write("\r\n");
      out.write("<script>\r\n");
      out.write("    $(function () {\r\n");
      out.write("        var reportActiveCurrCode = sessionStorage.getItem('reportActiveCurrCode');\r\n");
      out.write("        var realCurrCode = sessionStorage.getItem('currCode');\r\n");
      out.write("        var tabIndex = 0;\r\n");
      out.write("        var currCodeArray = JSON.parse(reportActiveCurrCode);\r\n");
      out.write("        let tabs = $('.weui-navigator-list');\r\n");
      out.write("        for (let i = 0; i < currCodeArray.length; i++) {\r\n");
      out.write("            let tabItem = currCodeArray[i];\r\n");
      out.write("            let currName = tabItem.currName;\r\n");
      out.write("            let currCode = tabItem.currCode;\r\n");
      out.write("            if (currCode === realCurrCode) {\r\n");
      out.write("                tabIndex = i;\r\n");
      out.write("            }\r\n");
      out.write("            let tab;\r\n");
      out.write("            if (tabItem.hasData) {\r\n");
      out.write("                tab = $('<li data-currCode=\"' + currCode + '\"><a>' + currName + '</a></li>');\r\n");
      out.write("            } else {\r\n");
      out.write("                tab = $('<li class= \"weui-state-disabled\"><a>' + currName + '</a></li>');\r\n");
      out.write("            }\r\n");
      out.write("            tabs.append(tab);\r\n");
      out.write("        }\r\n");
      out.write("        //使用这个是为了避免滑动时出现滚动条。\r\n");
      out.write("        TagNav('#tagnav', {\r\n");
      out.write("            type: 'scrollToNext',\r\n");
      out.write("            curClassName: 'weui-state',\r\n");
      out.write("            //不能使用weui-state-active，随便写一个，以免下面的重写失效。\r\n");
      out.write("            index: tabIndex\r\n");
      out.write("        });\r\n");
      out.write("        //重写切换事件和选中事件\r\n");
      out.write("        $(\"#tagnav li\").eq(tabIndex).addClass('weui-state-active');\r\n");
      out.write("        $('#tagnav').on('click', 'li',\r\n");
      out.write("            function (event) {\r\n");
      out.write("                event.preventDefault();\r\n");
      out.write("                $(this).addClass('weui-state-active');\r\n");
      out.write("                if (!$(this).hasClass('weui-state-disabled')) {\r\n");
      out.write("                    $(this).siblings('li').removeClass('weui-state-active');\r\n");
      out.write("                    requestData($(this).attr('data-currCode'));\r\n");
      out.write("                }\r\n");
      out.write("            });\r\n");
      out.write("        requestData(realCurrCode);\r\n");
      out.write("\r\n");
      out.write("    });\r\n");
      out.write("\r\n");
      out.write("    function requestData(currCode) {\r\n");
      out.write("        showNoMsg(false)\r\n");
      out.write("        var param = {'currCode': currCode, 'tag': 'ats'};\r\n");
      out.write("        $.ajax({\r\n");
      out.write("            cache: true,\r\n");
      out.write("            type: 'POST',\r\n");
      out.write("            url: \"");
      out.print(basePath);
      out.write("/saasreport/getBankBalance.do\",\r\n");
      out.write("            data: param,\r\n");
      out.write("            async: true,\r\n");
      out.write("            dataType: 'json',\r\n");
      out.write("            beforeSend: function () {\r\n");
      out.write("                $.showLoading();\r\n");
      out.write("            },\r\n");
      out.write("            error: function () {\r\n");
      out.write("                $.hideLoading();\r\n");
      out.write("                $.toast('网络服务情况异常，请检查', \"forbidden\");\r\n");
      out.write("                showNoMsg(true);\r\n");
      out.write("            },\r\n");
      out.write("            success: function (data) {\r\n");
      out.write("                $.hideLoading();\r\n");
      out.write("                if (data.successful == true || \"true\" == data.successful) {\r\n");
      out.write("                    var dataArray = data.result.data;\r\n");
      out.write("                    if (dataArray.length == 3) {\r\n");
      out.write("                        //顶部数据\r\n");
      out.write("                        layoutTopTitle(dataArray[0].reportData);\r\n");
      out.write("                        //chart数据\r\n");
      out.write("                        $('#chartSection').html('');\r\n");
      out.write("                        $('#chartSection').append(\r\n");
      out.write("                            '<div id=\"chartView\" style=\"width: 100%;height: 268px;\"></div>' +\r\n");
      out.write("                            '<div class=\"chartPadding\" style=\"width: 100%;height: 10px;background: #F1F3F5\"></div>');\r\n");
      out.write("                        var myChart = echarts.init(document.getElementById('chartView'));\r\n");
      out.write("                        var option = dataCollation(dataArray[1].reportData);\r\n");
      out.write("                        myChart.setOption(option);\r\n");
      out.write("                        //列表数据\r\n");
      out.write("                        layoutListInfo(dataArray[2].reportData);\r\n");
      out.write("                    } else {\r\n");
      out.write("                        // 暂无数据\r\n");
      out.write("                        showNoMsg(true);\r\n");
      out.write("                    }\r\n");
      out.write("                } else {\r\n");
      out.write("                    $.toast('网络服务情况异常，请检查', \"forbidden\");\r\n");
      out.write("                    showNoMsg(true);\r\n");
      out.write("                }\r\n");
      out.write("            }\r\n");
      out.write("        });\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    function showNoMsg(show) {\r\n");
      out.write("        if (show) {\r\n");
      out.write("            $(\"#reportTitle\").html('');\r\n");
      out.write("            $(\"#chartSection\").html('');\r\n");
      out.write("            $(\"#chartListInfo\").html('');\r\n");
      out.write("            $(\"#noMsg\").css('display', 'block');\r\n");
      out.write("        } else {\r\n");
      out.write("            $(\"#noMsg\").css('display', 'none');\r\n");
      out.write("        }\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    function layoutTopTitle(reportDataOne) {\r\n");
      out.write("        $(\"#reportTitle\").html('');\r\n");
      out.write("        let dic = reportDataOne[0];\r\n");
      out.write("        if ('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customerCode}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("'.indexOf('cdcyjt') == -1) {\r\n");
      out.write("\r\n");
      out.write("            $(\"#reportTitle\").append(\r\n");
      out.write("                '<div id=\"center\"><span class=\"tip\">总余额/万元</span><span class=\"amount\" style=\"padding-bottom: 0px;\">' + dic.totalAmount + '</span></div>' +\r\n");
      out.write("                '<div id=\"left\"><span class=\"leftTip\">直联/万元</span><span class=\"leftAmount\">' + dic.directAmount + '</span></div>' +\r\n");
      out.write("                '<div class=\"line\"></div>' +\r\n");
      out.write("                '<div id=\"right\"><span class=\"rightTip\">非直联/万元</span><span class=\"rightAmount\">' + dic.unDirectAmount + '</span></div>');\r\n");
      out.write("        } else {\r\n");
      out.write("            $(\"#reportTitle\").append(\r\n");
      out.write("                '<div id=\"center\" style=\"padding-bottom: 28px\"><span class=\"tip\">总余额/万元</span><span class=\"amount\" style=\"padding-bottom: 0px;\">' + dic.totalAmount + '</span></div>');\r\n");
      out.write("        }\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    function layoutListInfo(reportDataThree) {\r\n");
      out.write("        $('#chartListInfo').html('');\r\n");
      out.write("        for (let i = 0; i < reportDataThree.length; i++) {\r\n");
      out.write("            let dic = reportDataThree[i];\r\n");
      out.write("            if ('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customerCode}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("'.indexOf('cdcyjt') == -1) {\r\n");
      out.write("\r\n");
      out.write("                $(\"#chartListInfo\").append(\r\n");
      out.write("                    '<div class=\"cell titleInfoArrow\" style=\"position: relative;\" data-item = ' + dic.itemId + '>' +\r\n");
      out.write("                    '<span class=\"titleInfo\">' + dic.bankName + '</span>' +\r\n");
      out.write("                    '<div class=\"amountAndTip3\"><span class=\"amount\">' + dic.directAmount + '</span><span class=\"tip\">直联</span></div>' +\r\n");
      out.write("                    '<div class=\"amountAndTip3\"><span class=\"amount\">' + dic.unDirectAmount + '</span><span class=\"tip\">非直联</span></div>' +\r\n");
      out.write("                    '<div class=\"amountAndTip3\"><span class=\"amount\" style=\"color:#FFB06B\">' + dic.proportion + '</span><span class=\"tip\">占比</span></div>' +\r\n");
      out.write("                    '<div style=\"width: 100%;height: 10px;background: #ffffff\"></div>' +\r\n");
      out.write("                    '<div class=\"cellPadding\" style=\"width: 100%;height: 5px;background: #F1F3F5\"></div>' +\r\n");
      out.write("                    '</div>');\r\n");
      out.write("            } else {\r\n");
      out.write("                $(\"#chartListInfo\").append(\r\n");
      out.write("                    '<div class=\"cell titleInfoArrow\" style=\"position: relative;\" data-item = ' + dic.itemId + '>' +\r\n");
      out.write("                    '<span class=\"titleInfo\">' + dic.bankName + '</span>' +\r\n");
      out.write("                    '<div class=\"amountAndTip\"><span class=\"amount\">' + dic.totalAmount + '</span><span class=\"tip\">银行余额</span></div>' +\r\n");
      out.write("                    '<div class=\"amountAndTip\"><span class=\"amount\" style=\"color:#FFB06B\">' + dic.proportion + '</span><span class=\"tip\">占比</span></div>' +\r\n");
      out.write("                    '<div style=\"width: 100%;height: 10px;background: #ffffff\"></div>' +\r\n");
      out.write("                    '<div class=\"cellPadding\" style=\"width: 100%;height: 5px;background: #F1F3F5\"></div>' +\r\n");
      out.write("                    '</div>');\r\n");
      out.write("            }\r\n");
      out.write("        }\r\n");
      out.write("        var clickable;\r\n");
      out.write("        $(\".cell\").on('touchstart', function (event) {\r\n");
      out.write("            clickable = true;\r\n");
      out.write("        });\r\n");
      out.write("        $(\".cell\").on('touchmove', function (event) {\r\n");
      out.write("            clickable = false;\r\n");
      out.write("        });\r\n");
      out.write("        $(\".cell\").on('click', function (event) {\r\n");
      out.write("            event.preventDefault();\r\n");
      out.write("            if (!clickable) {\r\n");
      out.write("                return;\r\n");
      out.write("            }\r\n");
      out.write("            if ($(this).hasClass('titleInfoArrow')) {\r\n");
      out.write("                let itemId = $(this).attr('data-item');\r\n");
      out.write("                let currCode = $('#tagnav .weui-state-active').attr('data-currcode');\r\n");
      out.write("                let name = $(this).find('.titleInfo').text();\r\n");
      out.write("                sessionStorage.setItem('itemName', name);\r\n");
      out.write("                sessionStorage.setItem('itemId', itemId);\r\n");
      out.write("                sessionStorage.setItem('currCode', currCode);\r\n");
      out.write("                if ('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customerCode}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("'.indexOf('cdcyjt') !== -1) {\r\n");
      out.write("                    // 成都产投\r\n");
      out.write("                    location.href = \"sub_cdcyjt_bank_balance_org.jsp\";\r\n");
      out.write("                } else if ('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customerCode}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("' === 'jtwl') {\r\n");
      out.write("                    // 建投物流\r\n");
      out.write("                    location.href = \"sub_jtwl_bank_account_balance.jsp\";\r\n");
      out.write("                } else {\r\n");
      out.write("                    location.href = \"subBankBalanceReport.jsp\";\r\n");
      out.write("\r\n");
      out.write("                }\r\n");
      out.write("            }\r\n");
      out.write("        });\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    function dataCollation(reportDataTwo) {\r\n");
      out.write("        var xAxisData = [];\r\n");
      out.write("        var seriesData = [];\r\n");
      out.write("        var directAmountData = [];\r\n");
      out.write("        var unDirectAmountData = [];\r\n");
      out.write("        var amountData = [];\r\n");
      out.write("        for (let i = 0; i < reportDataTwo.length; i++) {\r\n");
      out.write("            let dic = reportDataTwo[i];\r\n");
      out.write("            let bankName = dic.bankName;\r\n");
      out.write("            let len = reportDataTwo.length > 4 ? 4 : 6;\r\n");
      out.write("            if (bankName.length > len) {\r\n");
      out.write("                bankName = bankName.substring(0, len);\r\n");
      out.write("            }\r\n");
      out.write("            xAxisData.push(bankName)\r\n");
      out.write("            directAmountData.push(dic.directAmount);\r\n");
      out.write("            unDirectAmountData.push(dic.unDirectAmount);\r\n");
      out.write("            amountData.push(dic.totalAmount);\r\n");
      out.write("        }\r\n");
      out.write("        if ('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customerCode}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("'.indexOf('cdcyjt') == -1) {\r\n");
      out.write("            let barData1 = {\r\n");
      out.write("                name: \"直联\",\r\n");
      out.write("                type: 'bar',\r\n");
      out.write("                stack: 'Itme',\r\n");
      out.write("                data: directAmountData\r\n");
      out.write("            }\r\n");
      out.write("            let barData2 = {\r\n");
      out.write("                name: \"非直联\",\r\n");
      out.write("                type: 'bar',\r\n");
      out.write("                stack: 'Itme',\r\n");
      out.write("                data: unDirectAmountData\r\n");
      out.write("            }\r\n");
      out.write("            seriesData.push(barData2);\r\n");
      out.write("            seriesData.push(barData1);\r\n");
      out.write("        } else {\r\n");
      out.write("            let barData = {\r\n");
      out.write("                name: \"银行余额\",\r\n");
      out.write("                type: 'bar',\r\n");
      out.write("                stack: 'Itme',\r\n");
      out.write("                data: amountData\r\n");
      out.write("            }\r\n");
      out.write("            seriesData.push(barData);\r\n");
      out.write("\r\n");
      out.write("        }\r\n");
      out.write("        var option = {\r\n");
      out.write("            tooltip: {\r\n");
      out.write("                trigger: 'axis',\r\n");
      out.write("                axisPointer: {\r\n");
      out.write("                    type: 'shadow'\r\n");
      out.write("                }\r\n");
      out.write("            },\r\n");
      out.write("            color: ['#FFD392', '#84DD86'],\r\n");
      out.write("            grid: {\r\n");
      out.write("                top: '8%',\r\n");
      out.write("                left: '3%',\r\n");
      out.write("                right: '4%',\r\n");
      out.write("                bottom: '3%',\r\n");
      out.write("                containLabel: true,\r\n");
      out.write("            },\r\n");
      out.write("            xAxis: [\r\n");
      out.write("                {\r\n");
      out.write("                    type: 'category',\r\n");
      out.write("                    data: xAxisData,\r\n");
      out.write("                    axisLine: {\r\n");
      out.write("                        lineStyle: {\r\n");
      out.write("                            color: '#D6D6D6',\r\n");
      out.write("                        }\r\n");
      out.write("                    },\r\n");
      out.write("                    axisLabel: {\r\n");
      out.write("                        color: '#888888',\r\n");
      out.write("                        fontSize: 10,\r\n");
      out.write("                        interval: 0,\r\n");
      out.write("                    },\r\n");
      out.write("                    axisTick: {\r\n");
      out.write("                        show: false\r\n");
      out.write("                    },\r\n");
      out.write("\r\n");
      out.write("                }\r\n");
      out.write("            ],\r\n");
      out.write("            yAxis: [\r\n");
      out.write("                {\r\n");
      out.write("                    type: 'value',\r\n");
      out.write("                    axisLine: {\r\n");
      out.write("                        lineStyle: {\r\n");
      out.write("                            color: '#fff',\r\n");
      out.write("                        }\r\n");
      out.write("                    },\r\n");
      out.write("                    axisLabel: {\r\n");
      out.write("                        color: '#888888'\r\n");
      out.write("                    },\r\n");
      out.write("                    splitLine: {\r\n");
      out.write("                        show: true,\r\n");
      out.write("                        lineStyle: {\r\n");
      out.write("                            color: 'rgba(67,67,67,0.05)'\r\n");
      out.write("                        }\r\n");
      out.write("                    }\r\n");
      out.write("                }\r\n");
      out.write("            ],\r\n");
      out.write("            series: seriesData\r\n");
      out.write("        };\r\n");
      out.write("        return option;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("</script>\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("</body>\r\n");
      out.write("</html>\r\n");
      out.write("\r\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
