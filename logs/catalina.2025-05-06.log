06-May-2025 10:10:47.660 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Server.�������汾: Apache Tomcat/8.5.100
06-May-2025 10:10:47.664 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����������:        Mar 19 2024 13:54:42 UTC
06-May-2025 10:10:47.664 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �������汾��:      *********
06-May-2025 10:10:47.664 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����ϵͳ����:      Windows 11
06-May-2025 10:10:47.665 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log OS.�汾:           10.0
06-May-2025 10:10:47.665 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �ܹ�:              amd64
06-May-2025 10:10:47.665 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java ��������:     D:\app\jdk\jdk8\jre
06-May-2025 10:10:47.665 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java������汾:    1.8.0_431-b10
06-May-2025 10:10:47.665 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.��Ӧ��:        Oracle Corporation
06-May-2025 10:10:47.665 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 10:10:47.665 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 10:10:47.665 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.config.file=E:\project\other\FinancialBox\apache-tomcat-8.5.100\conf\logging.properties
06-May-2025 10:10:47.666 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
06-May-2025 10:10:47.666 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djdk.tls.ephemeralDHKeySize=2048
06-May-2025 10:10:47.666 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
06-May-2025 10:10:47.666 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dignore.endorsed.dirs=
06-May-2025 10:10:47.666 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.base=E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 10:10:47.670 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.home=E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 10:10:47.671 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.io.tmpdir=E:\project\other\FinancialBox\apache-tomcat-8.5.100\temp
06-May-2025 10:10:47.671 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -agentpath:C:\Program Files\Palo Alto Networks\Traps\cyjagent.dll
06-May-2025 10:10:47.671 ��Ϣ [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent ��java.library.path:[D:\app\jdk\jdk8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;D:\app\vm\bin\;D:\app\python\Scripts\;D:\app\python\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\app\nvm\nvm;D:\app\node\nodejs;d:\app\cursor\cursor\resources\app\bin;D:\app\git\Git\cmd;D:\app\maven\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;D:\app\bandzip\Bandizip\;D:\app\mysql\mysql-8.0.41\bin;C:\Program Files\Tailscale\;D:\app\jdk\jdk8\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\shims;C:\Users\<USER>\AppData\Roaming\Python\Scripts;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\app\cursor\cursor\resources\app\bin;.]���Ҳ�������APR��Apache Tomcat�����⣬�ÿ����������������л���������
06-May-2025 10:10:48.150 ��Ϣ [main] org.apache.coyote.AbstractProtocol.init ��ʼ��Э�鴦���� ["http-nio-8080"]
06-May-2025 10:10:48.167 ��Ϣ [main] org.apache.catalina.startup.Catalina.load Initialization processed in 1354 ms
06-May-2025 10:10:48.218 ��Ϣ [main] org.apache.catalina.core.StandardService.startInternal ������������[Catalina]
06-May-2025 10:10:48.218 ��Ϣ [main] org.apache.catalina.core.StandardEngine.startInternal �������� Servlet ���棺[Apache Tomcat/8.5.100]
06-May-2025 10:10:48.226 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]
06-May-2025 10:10:48.797 ���� [localhost-startStop-1] org.apache.catalina.util.SessionIdGeneratorBase.createSecureRandom ʹ��[SHA1PRNG]�����ỰID���ɵ�SecureRandomʵ��������[427]���롣
06-May-2025 10:10:48.813 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]�Ĳ�������[588]���������
06-May-2025 10:10:48.814 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]
06-May-2025 10:10:49.020 ���� [localhost-startStop-1] org.apache.catalina.startup.ContextConfig.processAnnotationsWebResource ���ܴ���ע�͵�Web��Դ[/WEB-INF/classes/com/fingard/app/delegate/aspect/OperationRecordAspect.class]
	org.apache.tomcat.util.bcel.classfile.ClassFormatException: Invalid byte tag in constant pool: 32
		at org.apache.tomcat.util.bcel.classfile.Constant.readConstant(Constant.java:72)
		at org.apache.tomcat.util.bcel.classfile.ConstantPool.<init>(ConstantPool.java:50)
		at org.apache.tomcat.util.bcel.classfile.ClassParser.readConstantPool(ClassParser.java:178)
		at org.apache.tomcat.util.bcel.classfile.ClassParser.parse(ClassParser.java:79)
		at org.apache.catalina.startup.ContextConfig.processAnnotationsStream(ContextConfig.java:1885)
		at org.apache.catalina.startup.ContextConfig.processAnnotationsWebResource(ContextConfig.java:1802)
		at org.apache.catalina.startup.ContextConfig.processAnnotationsWebResource(ContextConfig.java:1797)
		at org.apache.catalina.startup.ContextConfig.processAnnotationsWebResource(ContextConfig.java:1797)
		at org.apache.catalina.startup.ContextConfig.processAnnotationsWebResource(ContextConfig.java:1797)
		at org.apache.catalina.startup.ContextConfig.processAnnotationsWebResource(ContextConfig.java:1797)
		at org.apache.catalina.startup.ContextConfig.processAnnotationsWebResource(ContextConfig.java:1797)
		at org.apache.catalina.startup.ContextConfig.processClasses(ContextConfig.java:1119)
		at org.apache.catalina.startup.ContextConfig.webConfig(ContextConfig.java:1041)
		at org.apache.catalina.startup.ContextConfig.configureStart(ContextConfig.java:744)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:285)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:114)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4804)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:710)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:687)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:660)
		at org.apache.catalina.startup.HostConfig.deployDirectory(HostConfig.java:1176)
		at org.apache.catalina.startup.HostConfig$DeployDirectory.run(HostConfig.java:1889)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
		at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
		at java.lang.Thread.run(Thread.java:750)
06-May-2025 10:10:51.760 ��Ϣ [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars ������һ��JAR��ɨ������TLD����δ����TLD�� Ϊ�˼�¼�����õ�����־��¼���Ի�ȡ��ɨ�赫δ�������ҵ�TLD������JAR�б� ��ɨ���ڼ���������Ҫ��JAR������������ʱ���JSP����ʱ�䡣
06-May-2025 10:11:40.563 ���� [localhost-startStop-1] org.apache.catalina.core.StandardContext.startInternal һ������listeners����ʧ�ܣ�������ϸ��Ϣ�鿴��Ӧ��������־�ļ�
06-May-2025 10:11:40.575 ���� [localhost-startStop-1] org.apache.catalina.core.StandardContext.startInternal ����֮ǰ�Ĵ���Context[/app]����ʧ��
06-May-2025 10:11:40.596 ���� [localhost-startStop-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc WebӦ�ó��� [app] ע����JDBC�������� [com.mysql.cj.jdbc.Driver]������WebӦ�ó���ֹͣʱ�޷�ע������ Ϊ��ֹ�ڴ�й©��JDBC���������ѱ�ǿ��ȡ��ע�ᡣ
06-May-2025 10:11:40.597 ���� [localhost-startStop-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Log4j2-TF-3-Scheduled-1]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1093)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)]
06-May-2025 10:11:40.597 ���� [localhost-startStop-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Abandoned connection cleanup thread]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:150)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)]
06-May-2025 10:11:40.604 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]�Ĳ�������[51,790]���������
06-May-2025 10:11:40.606 ��Ϣ [main] org.apache.coyote.AbstractProtocol.start ��ʼЭ�鴦����["http-nio-8080"]
06-May-2025 10:11:40.623 ��Ϣ [main] org.apache.catalina.startup.Catalina.start Server startup in 52455 ms
06-May-2025 10:11:43.817 ��Ϣ [Abandoned connection cleanup thread] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading �Ƿ����ʣ���WebӦ�ó���ʵ����ֹͣ���޷�����[]��Ϊ�˵����Լ���ֹ���·Ƿ����ʵ��̣߳����׳����¶�ջ���١�
	java.lang.IllegalStateException: �Ƿ����ʣ���WebӦ�ó���ʵ����ֹͣ���޷�����[]��Ϊ�˵����Լ���ֹ���·Ƿ����ʵ��̣߳����׳����¶�ջ���١�
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1358)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:990)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkContextClassLoaders(AbandonedConnectionCleanupThread.java:96)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:69)
		at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
		at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
		at java.lang.Thread.run(Thread.java:750)
06-May-2025 10:13:44.180 ��Ϣ [Thread-6] org.apache.coyote.AbstractProtocol.pause ��ͣProtocolHandler["http-nio-8080"]
06-May-2025 10:13:44.597 ��Ϣ [Thread-6] org.apache.catalina.core.StandardService.stopInternal ����ֹͣ����[Catalina]
06-May-2025 10:13:44.604 ��Ϣ [Thread-6] org.apache.coyote.AbstractProtocol.stop ����ֹͣProtocolHandler ["http-nio-8080"]
06-May-2025 10:13:44.606 ��Ϣ [Thread-6] org.apache.coyote.AbstractProtocol.destroy ���ڴݻ�Э�鴦���� ["http-nio-8080"]
06-May-2025 10:14:29.719 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Server.�������汾: Apache Tomcat/8.5.100
06-May-2025 10:14:29.722 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����������:        Mar 19 2024 13:54:42 UTC
06-May-2025 10:14:29.722 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �������汾��:      *********
06-May-2025 10:14:29.722 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����ϵͳ����:      Windows 11
06-May-2025 10:14:29.723 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log OS.�汾:           10.0
06-May-2025 10:14:29.723 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �ܹ�:              amd64
06-May-2025 10:14:29.723 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java ��������:     D:\app\jdk\jdk8\jre
06-May-2025 10:14:29.723 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java������汾:    1.8.0_431-b10
06-May-2025 10:14:29.723 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.��Ӧ��:        Oracle Corporation
06-May-2025 10:14:29.723 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 10:14:29.723 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 10:14:29.724 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.config.file=E:\project\other\FinancialBox\apache-tomcat-8.5.100\conf\logging.properties
06-May-2025 10:14:29.724 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
06-May-2025 10:14:29.724 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djdk.tls.ephemeralDHKeySize=2048
06-May-2025 10:14:29.724 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
06-May-2025 10:14:29.724 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dignore.endorsed.dirs=
06-May-2025 10:14:29.724 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.base=E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 10:14:29.729 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.home=E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 10:14:29.729 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.io.tmpdir=E:\project\other\FinancialBox\apache-tomcat-8.5.100\temp
06-May-2025 10:14:29.729 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -agentpath:C:\Program Files\Palo Alto Networks\Traps\cyjagent.dll
06-May-2025 10:14:29.730 ��Ϣ [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent ��java.library.path:[D:\app\jdk\jdk8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;D:\app\vm\bin\;D:\app\python\Scripts\;D:\app\python\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\app\nvm\nvm;D:\app\node\nodejs;d:\app\cursor\cursor\resources\app\bin;D:\app\git\Git\cmd;D:\app\maven\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;D:\app\bandzip\Bandizip\;D:\app\mysql\mysql-8.0.41\bin;C:\Program Files\Tailscale\;D:\app\jdk\jdk8\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\shims;C:\Users\<USER>\AppData\Roaming\Python\Scripts;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\app\cursor\cursor\resources\app\bin;.]���Ҳ�������APR��Apache Tomcat�����⣬�ÿ����������������л���������
06-May-2025 10:14:29.838 ��Ϣ [main] org.apache.coyote.AbstractProtocol.init ��ʼ��Э�鴦���� ["http-nio-8080"]
06-May-2025 10:14:29.850 ��Ϣ [main] org.apache.catalina.startup.Catalina.load Initialization processed in 383 ms
06-May-2025 10:14:29.891 ��Ϣ [main] org.apache.catalina.core.StandardService.startInternal ������������[Catalina]
06-May-2025 10:14:29.892 ��Ϣ [main] org.apache.catalina.core.StandardEngine.startInternal �������� Servlet ���棺[Apache Tomcat/8.5.100]
06-May-2025 10:14:29.898 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]
06-May-2025 10:14:30.459 ���� [localhost-startStop-1] org.apache.catalina.util.SessionIdGeneratorBase.createSecureRandom ʹ��[SHA1PRNG]�����ỰID���ɵ�SecureRandomʵ��������[432]���롣
06-May-2025 10:14:30.472 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]�Ĳ�������[574]���������
06-May-2025 10:14:30.472 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]
06-May-2025 10:14:33.044 ��Ϣ [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars ������һ��JAR��ɨ������TLD����δ����TLD�� Ϊ�˼�¼�����õ�����־��¼���Ի�ȡ��ɨ�赫δ�������ҵ�TLD������JAR�б� ��ɨ���ڼ���������Ҫ��JAR������������ʱ���JSP����ʱ�䡣
06-May-2025 10:14:46.163 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]�Ĳ�������[15,691]���������
06-May-2025 10:14:46.166 ��Ϣ [main] org.apache.coyote.AbstractProtocol.start ��ʼЭ�鴦����["http-nio-8080"]
06-May-2025 10:14:46.177 ��Ϣ [main] org.apache.catalina.startup.Catalina.start Server startup in 16327 ms
06-May-2025 10:16:20.390 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.pause ��ͣProtocolHandler["http-nio-8080"]
06-May-2025 10:16:21.222 ��Ϣ [Thread-7] org.apache.catalina.core.StandardService.stopInternal ����ֹͣ����[Catalina]
06-May-2025 10:16:21.272 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc WebӦ�ó��� [app] ע����JDBC�������� [com.mysql.cj.jdbc.Driver]������WebӦ�ó���ֹͣʱ�޷�ע������ Ϊ��ֹ�ڴ�й©��JDBC���������ѱ�ǿ��ȡ��ע�ᡣ
06-May-2025 10:16:21.273 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Log4j2-TF-3-Scheduled-1]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1093)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)]
06-May-2025 10:16:21.273 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Abandoned connection cleanup thread]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:150)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)]
06-May-2025 10:16:21.274 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[MessageServiceImpl]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Thread.sleep(Native Method)
 com.fingard.app.delegate.core.service.impl.MessageServiceImpl$1.run(MessageServiceImpl.java:106)]
06-May-2025 10:16:21.274 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Timer-0]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Object.wait(Native Method)
 java.util.TimerThread.mainLoop(Timer.java:552)
 java.util.TimerThread.run(Timer.java:505)]
06-May-2025 10:16:21.286 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.stop ����ֹͣProtocolHandler ["http-nio-8080"]
06-May-2025 10:16:21.293 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.destroy ���ڴݻ�Э�鴦���� ["http-nio-8080"]
06-May-2025 10:16:29.069 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Server.�������汾: Apache Tomcat/8.5.100
06-May-2025 10:16:29.072 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����������:        Mar 19 2024 13:54:42 UTC
06-May-2025 10:16:29.072 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �������汾��:      *********
06-May-2025 10:16:29.072 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����ϵͳ����:      Windows 11
06-May-2025 10:16:29.072 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log OS.�汾:           10.0
06-May-2025 10:16:29.073 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �ܹ�:              amd64
06-May-2025 10:16:29.073 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java ��������:     D:\app\jdk\jdk8\jre
06-May-2025 10:16:29.073 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java������汾:    1.8.0_431-b10
06-May-2025 10:16:29.073 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.��Ӧ��:        Oracle Corporation
06-May-2025 10:16:29.073 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 10:16:29.073 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 10:16:29.073 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.config.file=E:\project\other\FinancialBox\apache-tomcat-8.5.100\conf\logging.properties
06-May-2025 10:16:29.073 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
06-May-2025 10:16:29.073 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djdk.tls.ephemeralDHKeySize=2048
06-May-2025 10:16:29.074 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
06-May-2025 10:16:29.074 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dignore.endorsed.dirs=
06-May-2025 10:16:29.074 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.base=E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 10:16:29.078 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.home=E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 10:16:29.078 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.io.tmpdir=E:\project\other\FinancialBox\apache-tomcat-8.5.100\temp
06-May-2025 10:16:29.078 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -agentpath:C:\Program Files\Palo Alto Networks\Traps\cyjagent.dll
06-May-2025 10:16:29.078 ��Ϣ [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent ��java.library.path:[D:\app\jdk\jdk8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;D:\app\vm\bin\;D:\app\python\Scripts\;D:\app\python\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\app\nvm\nvm;D:\app\node\nodejs;d:\app\cursor\cursor\resources\app\bin;D:\app\git\Git\cmd;D:\app\maven\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;D:\app\bandzip\Bandizip\;D:\app\mysql\mysql-8.0.41\bin;C:\Program Files\Tailscale\;D:\app\jdk\jdk8\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\shims;C:\Users\<USER>\AppData\Roaming\Python\Scripts;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\app\cursor\cursor\resources\app\bin;.]���Ҳ�������APR��Apache Tomcat�����⣬�ÿ����������������л���������
06-May-2025 10:16:29.188 ��Ϣ [main] org.apache.coyote.AbstractProtocol.init ��ʼ��Э�鴦���� ["http-nio-8080"]
06-May-2025 10:16:29.201 ��Ϣ [main] org.apache.catalina.startup.Catalina.load Initialization processed in 391 ms
06-May-2025 10:16:29.248 ��Ϣ [main] org.apache.catalina.core.StandardService.startInternal ������������[Catalina]
06-May-2025 10:16:29.248 ��Ϣ [main] org.apache.catalina.core.StandardEngine.startInternal �������� Servlet ���棺[Apache Tomcat/8.5.100]
06-May-2025 10:16:29.255 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]
06-May-2025 10:16:29.850 ���� [localhost-startStop-1] org.apache.catalina.util.SessionIdGeneratorBase.createSecureRandom ʹ��[SHA1PRNG]�����ỰID���ɵ�SecureRandomʵ��������[464]���롣
06-May-2025 10:16:29.863 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]�Ĳ�������[608]���������
06-May-2025 10:16:29.864 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]
06-May-2025 10:16:32.377 ��Ϣ [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars ������һ��JAR��ɨ������TLD����δ����TLD�� Ϊ�˼�¼�����õ�����־��¼���Ի�ȡ��ɨ�赫δ�������ҵ�TLD������JAR�б� ��ɨ���ڼ���������Ҫ��JAR������������ʱ���JSP����ʱ�䡣
06-May-2025 10:16:51.602 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]�Ĳ�������[21,738]���������
06-May-2025 10:16:51.619 ��Ϣ [main] org.apache.coyote.AbstractProtocol.start ��ʼЭ�鴦����["http-nio-8080"]
06-May-2025 10:16:51.635 ��Ϣ [main] org.apache.catalina.startup.Catalina.start Server startup in 22433 ms
06-May-2025 10:17:13.040 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.pause ��ͣProtocolHandler["http-nio-8080"]
06-May-2025 10:17:14.007 ��Ϣ [Thread-7] org.apache.catalina.core.StandardService.stopInternal ����ֹͣ����[Catalina]
06-May-2025 10:17:14.081 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc WebӦ�ó��� [app] ע����JDBC�������� [com.mysql.cj.jdbc.Driver]������WebӦ�ó���ֹͣʱ�޷�ע������ Ϊ��ֹ�ڴ�й©��JDBC���������ѱ�ǿ��ȡ��ע�ᡣ
06-May-2025 10:17:14.082 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Log4j2-TF-3-Scheduled-1]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1093)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)]
06-May-2025 10:17:14.083 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Abandoned connection cleanup thread]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:150)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)]
06-May-2025 10:17:14.083 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[MessageServiceImpl]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Thread.sleep(Native Method)
 com.fingard.app.delegate.core.service.impl.MessageServiceImpl$1.run(MessageServiceImpl.java:106)]
06-May-2025 10:17:14.083 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Timer-0]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Object.wait(Native Method)
 java.util.TimerThread.mainLoop(Timer.java:552)
 java.util.TimerThread.run(Timer.java:505)]
06-May-2025 10:17:14.096 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.stop ����ֹͣProtocolHandler ["http-nio-8080"]
06-May-2025 10:17:14.109 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.destroy ���ڴݻ�Э�鴦���� ["http-nio-8080"]
06-May-2025 10:18:53.981 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Server.�������汾: Apache Tomcat/8.5.100
06-May-2025 10:18:53.984 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����������:        Mar 19 2024 13:54:42 UTC
06-May-2025 10:18:53.984 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �������汾��:      *********
06-May-2025 10:18:53.984 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����ϵͳ����:      Windows 11
06-May-2025 10:18:53.985 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log OS.�汾:           10.0
06-May-2025 10:18:53.985 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �ܹ�:              amd64
06-May-2025 10:18:53.985 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java ��������:     D:\app\jdk\jdk8\jre
06-May-2025 10:18:53.985 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java������汾:    1.8.0_431-b10
06-May-2025 10:18:53.985 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.��Ӧ��:        Oracle Corporation
06-May-2025 10:18:53.985 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 10:18:53.985 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 10:18:53.986 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.config.file=E:\project\other\FinancialBox\apache-tomcat-8.5.100\conf\logging.properties
06-May-2025 10:18:53.986 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
06-May-2025 10:18:53.986 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djdk.tls.ephemeralDHKeySize=2048
06-May-2025 10:18:53.986 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
06-May-2025 10:18:53.986 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dignore.endorsed.dirs=
06-May-2025 10:18:53.986 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.base=E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 10:18:53.991 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.home=E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 10:18:53.991 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.io.tmpdir=E:\project\other\FinancialBox\apache-tomcat-8.5.100\temp
06-May-2025 10:18:53.991 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -agentpath:C:\Program Files\Palo Alto Networks\Traps\cyjagent.dll
06-May-2025 10:18:53.991 ��Ϣ [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent ��java.library.path:[D:\app\jdk\jdk8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;D:\app\vm\bin\;D:\app\python\Scripts\;D:\app\python\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\app\nvm\nvm;D:\app\node\nodejs;d:\app\cursor\cursor\resources\app\bin;D:\app\git\Git\cmd;D:\app\maven\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;D:\app\bandzip\Bandizip\;D:\app\mysql\mysql-8.0.41\bin;C:\Program Files\Tailscale\;D:\app\jdk\jdk8\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\shims;C:\Users\<USER>\AppData\Roaming\Python\Scripts;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\app\cursor\cursor\resources\app\bin;.]���Ҳ�������APR��Apache Tomcat�����⣬�ÿ����������������л���������
06-May-2025 10:18:54.099 ��Ϣ [main] org.apache.coyote.AbstractProtocol.init ��ʼ��Э�鴦���� ["http-nio-8080"]
06-May-2025 10:18:54.112 ��Ϣ [main] org.apache.catalina.startup.Catalina.load Initialization processed in 392 ms
06-May-2025 10:18:54.158 ��Ϣ [main] org.apache.catalina.core.StandardService.startInternal ������������[Catalina]
06-May-2025 10:18:54.158 ��Ϣ [main] org.apache.catalina.core.StandardEngine.startInternal �������� Servlet ���棺[Apache Tomcat/8.5.100]
06-May-2025 10:18:54.165 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]
06-May-2025 10:18:54.764 ���� [localhost-startStop-1] org.apache.catalina.util.SessionIdGeneratorBase.createSecureRandom ʹ��[SHA1PRNG]�����ỰID���ɵ�SecureRandomʵ��������[475]���롣
06-May-2025 10:18:54.775 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]�Ĳ�������[610]���������
06-May-2025 10:18:54.775 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]
06-May-2025 10:18:57.367 ��Ϣ [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars ������һ��JAR��ɨ������TLD����δ����TLD�� Ϊ�˼�¼�����õ�����־��¼���Ի�ȡ��ɨ�赫δ�������ҵ�TLD������JAR�б� ��ɨ���ڼ���������Ҫ��JAR������������ʱ���JSP����ʱ�䡣
06-May-2025 10:19:11.351 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]�Ĳ�������[16,576]���������
06-May-2025 10:19:11.354 ��Ϣ [main] org.apache.coyote.AbstractProtocol.start ��ʼЭ�鴦����["http-nio-8080"]
06-May-2025 10:19:11.366 ��Ϣ [main] org.apache.catalina.startup.Catalina.start Server startup in 17252 ms
06-May-2025 11:18:23.105 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Server.�������汾: Apache Tomcat/8.5.100
06-May-2025 11:18:23.108 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����������:        Mar 19 2024 13:54:42 UTC
06-May-2025 11:18:23.108 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �������汾��:      *********
06-May-2025 11:18:23.108 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����ϵͳ����:      Windows 11
06-May-2025 11:18:23.108 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log OS.�汾:           10.0
06-May-2025 11:18:23.108 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �ܹ�:              amd64
06-May-2025 11:18:23.108 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java ��������:     D:\app\jdk\jdk8\jre
06-May-2025 11:18:23.108 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java������汾:    1.8.0_431-b10
06-May-2025 11:18:23.108 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.��Ӧ��:        Oracle Corporation
06-May-2025 11:18:23.109 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 11:18:23.109 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 11:18:23.109 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.config.file=E:\project\other\FinancialBox\apache-tomcat-8.5.100\conf\logging.properties
06-May-2025 11:18:23.109 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
06-May-2025 11:18:23.109 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djdk.tls.ephemeralDHKeySize=2048
06-May-2025 11:18:23.109 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
06-May-2025 11:18:23.110 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dignore.endorsed.dirs=
06-May-2025 11:18:23.110 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.base=E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 11:18:23.113 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.home=E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 11:18:23.113 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.io.tmpdir=E:\project\other\FinancialBox\apache-tomcat-8.5.100\temp
06-May-2025 11:18:23.113 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -agentpath:C:\Program Files\Palo Alto Networks\Traps\cyjagent.dll
06-May-2025 11:18:23.114 ��Ϣ [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent ��java.library.path:[D:\app\jdk\jdk8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;D:\app\vm\bin\;D:\app\python\Scripts\;D:\app\python\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\app\nvm\nvm;D:\app\node\nodejs;d:\app\cursor\cursor\resources\app\bin;D:\app\git\Git\cmd;D:\app\maven\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;D:\app\bandzip\Bandizip\;D:\app\mysql\mysql-8.0.41\bin;C:\Program Files\Tailscale\;D:\app\jdk\jdk8\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\shims;C:\Users\<USER>\AppData\Roaming\Python\Scripts;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\app\cursor\cursor\resources\app\bin;.]���Ҳ�������APR��Apache Tomcat�����⣬�ÿ����������������л���������
06-May-2025 11:18:23.224 ��Ϣ [main] org.apache.coyote.AbstractProtocol.init ��ʼ��Э�鴦���� ["http-nio-8080"]
06-May-2025 11:18:23.230 ���� [main] org.apache.catalina.core.StandardService.initInternal Failed to initialize connector [Connector["http-nio-8080"]]
	org.apache.catalina.LifecycleException: Э�鴦������ʼ��ʧ��
		at org.apache.catalina.connector.Connector.initInternal(Connector.java:1076)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:127)
		at org.apache.catalina.core.StandardService.initInternal(StandardService.java:564)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:127)
		at org.apache.catalina.core.StandardServer.initInternal(StandardServer.java:861)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:127)
		at org.apache.catalina.startup.Catalina.load(Catalina.java:596)
		at org.apache.catalina.startup.Catalina.load(Catalina.java:619)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.load(Bootstrap.java:302)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:475)
	Caused by: java.net.BindException: Address already in use: bind
		at sun.nio.ch.Net.bind0(Native Method)
		at sun.nio.ch.Net.bind(Net.java:438)
		at sun.nio.ch.Net.bind(Net.java:430)
		at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:225)
		at sun.nio.ch.ServerSocketAdaptor.bind(ServerSocketAdaptor.java:74)
		at org.apache.tomcat.util.net.NioEndpoint.initServerSocket(NioEndpoint.java:218)
		at org.apache.tomcat.util.net.NioEndpoint.bind(NioEndpoint.java:194)
		at org.apache.tomcat.util.net.AbstractEndpoint.bindWithCleanup(AbstractEndpoint.java:1328)
		at org.apache.tomcat.util.net.AbstractEndpoint.init(AbstractEndpoint.java:1341)
		at org.apache.tomcat.util.net.AbstractJsseEndpoint.init(AbstractJsseEndpoint.java:241)
		at org.apache.coyote.AbstractProtocol.init(AbstractProtocol.java:695)
		at org.apache.coyote.http11.AbstractHttp11Protocol.init(AbstractHttp11Protocol.java:76)
		at org.apache.catalina.connector.Connector.initInternal(Connector.java:1074)
		... 13 more
06-May-2025 11:18:23.231 ��Ϣ [main] org.apache.catalina.startup.Catalina.load Initialization processed in 392 ms
06-May-2025 11:18:23.274 ��Ϣ [main] org.apache.catalina.core.StandardService.startInternal ������������[Catalina]
06-May-2025 11:18:23.275 ��Ϣ [main] org.apache.catalina.core.StandardEngine.startInternal �������� Servlet ���棺[Apache Tomcat/8.5.100]
06-May-2025 11:18:23.282 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]
06-May-2025 11:18:23.829 ���� [localhost-startStop-1] org.apache.catalina.util.SessionIdGeneratorBase.createSecureRandom ʹ��[SHA1PRNG]�����ỰID���ɵ�SecureRandomʵ��������[426]���롣
06-May-2025 11:18:23.841 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]�Ĳ�������[559]���������
06-May-2025 11:18:23.841 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]
06-May-2025 11:18:26.508 ��Ϣ [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars ������һ��JAR��ɨ������TLD����δ����TLD�� Ϊ�˼�¼�����õ�����־��¼���Ի�ȡ��ɨ�赫δ�������ҵ�TLD������JAR�б� ��ɨ���ڼ���������Ҫ��JAR������������ʱ���JSP����ʱ�䡣
06-May-2025 11:18:42.995 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]�Ĳ�������[19,154]���������
06-May-2025 11:18:43.006 ��Ϣ [main] org.apache.catalina.startup.Catalina.start Server startup in 19775 ms
06-May-2025 11:19:07.149 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.pause ��ͣProtocolHandler["http-nio-8080"]
06-May-2025 11:19:07.149 ��Ϣ [Thread-7] org.apache.catalina.core.StandardService.stopInternal ����ֹͣ����[Catalina]
06-May-2025 11:19:07.181 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc WebӦ�ó��� [app] ע����JDBC�������� [com.mysql.cj.jdbc.Driver]������WebӦ�ó���ֹͣʱ�޷�ע������ Ϊ��ֹ�ڴ�й©��JDBC���������ѱ�ǿ��ȡ��ע�ᡣ
06-May-2025 11:19:07.182 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Log4j2-TF-3-Scheduled-1]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1093)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)]
06-May-2025 11:19:07.182 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Abandoned connection cleanup thread]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:150)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)]
06-May-2025 11:19:07.182 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[MessageServiceImpl]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Thread.sleep(Native Method)
 com.fingard.app.delegate.core.service.impl.MessageServiceImpl$1.run(MessageServiceImpl.java:106)]
06-May-2025 11:19:07.182 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Timer-0]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Object.wait(Native Method)
 java.util.TimerThread.mainLoop(Timer.java:552)
 java.util.TimerThread.run(Timer.java:505)]
06-May-2025 11:19:07.189 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.stop ����ֹͣProtocolHandler ["http-nio-8080"]
06-May-2025 11:19:07.189 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.destroy ���ڴݻ�Э�鴦���� ["http-nio-8080"]
06-May-2025 11:19:11.636 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Server.�������汾: Apache Tomcat/8.5.100
06-May-2025 11:19:11.639 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����������:        Mar 19 2024 13:54:42 UTC
06-May-2025 11:19:11.639 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �������汾��:      *********
06-May-2025 11:19:11.639 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����ϵͳ����:      Windows 11
06-May-2025 11:19:11.639 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log OS.�汾:           10.0
06-May-2025 11:19:11.640 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �ܹ�:              amd64
06-May-2025 11:19:11.640 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java ��������:     D:\app\jdk\jdk8\jre
06-May-2025 11:19:11.640 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java������汾:    1.8.0_431-b10
06-May-2025 11:19:11.640 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.��Ӧ��:        Oracle Corporation
06-May-2025 11:19:11.640 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 11:19:11.640 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 11:19:11.640 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.config.file=E:\project\other\FinancialBox\apache-tomcat-8.5.100\conf\logging.properties
06-May-2025 11:19:11.641 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
06-May-2025 11:19:11.641 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djdk.tls.ephemeralDHKeySize=2048
06-May-2025 11:19:11.641 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
06-May-2025 11:19:11.641 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dignore.endorsed.dirs=
06-May-2025 11:19:11.641 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.base=E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 11:19:11.646 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.home=E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 11:19:11.647 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.io.tmpdir=E:\project\other\FinancialBox\apache-tomcat-8.5.100\temp
06-May-2025 11:19:11.647 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -agentpath:C:\Program Files\Palo Alto Networks\Traps\cyjagent.dll
06-May-2025 11:19:11.647 ��Ϣ [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent ��java.library.path:[D:\app\jdk\jdk8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;D:\app\vm\bin\;D:\app\python\Scripts\;D:\app\python\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\app\nvm\nvm;D:\app\node\nodejs;d:\app\cursor\cursor\resources\app\bin;D:\app\git\Git\cmd;D:\app\maven\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;D:\app\bandzip\Bandizip\;D:\app\mysql\mysql-8.0.41\bin;C:\Program Files\Tailscale\;D:\app\jdk\jdk8\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\shims;C:\Users\<USER>\AppData\Roaming\Python\Scripts;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\app\cursor\cursor\resources\app\bin;.]���Ҳ�������APR��Apache Tomcat�����⣬�ÿ����������������л���������
06-May-2025 11:19:11.765 ��Ϣ [main] org.apache.coyote.AbstractProtocol.init ��ʼ��Э�鴦���� ["http-nio-8080"]
06-May-2025 11:19:11.781 ��Ϣ [main] org.apache.catalina.startup.Catalina.load Initialization processed in 521 ms
06-May-2025 11:19:11.839 ��Ϣ [main] org.apache.catalina.core.StandardService.startInternal ������������[Catalina]
06-May-2025 11:19:11.839 ��Ϣ [main] org.apache.catalina.core.StandardEngine.startInternal �������� Servlet ���棺[Apache Tomcat/8.5.100]
06-May-2025 11:19:11.851 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]
06-May-2025 11:19:12.505 ���� [localhost-startStop-1] org.apache.catalina.util.SessionIdGeneratorBase.createSecureRandom ʹ��[SHA1PRNG]�����ỰID���ɵ�SecureRandomʵ��������[481]���롣
06-May-2025 11:19:12.520 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]�Ĳ�������[669]���������
06-May-2025 11:19:12.520 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]
06-May-2025 11:19:15.289 ��Ϣ [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars ������һ��JAR��ɨ������TLD����δ����TLD�� Ϊ�˼�¼�����õ�����־��¼���Ի�ȡ��ɨ�赫δ�������ҵ�TLD������JAR�б� ��ɨ���ڼ���������Ҫ��JAR������������ʱ���JSP����ʱ�䡣
06-May-2025 11:19:33.785 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]�Ĳ�������[21,265]���������
06-May-2025 11:19:33.788 ��Ϣ [main] org.apache.coyote.AbstractProtocol.start ��ʼЭ�鴦����["http-nio-8080"]
06-May-2025 11:19:33.811 ��Ϣ [main] org.apache.catalina.startup.Catalina.start Server startup in 22029 ms
06-May-2025 11:22:26.769 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Server.�������汾: Apache Tomcat/8.5.100
06-May-2025 11:22:26.772 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����������:        Mar 19 2024 13:54:42 UTC
06-May-2025 11:22:26.773 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �������汾��:      *********
06-May-2025 11:22:26.773 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����ϵͳ����:      Windows 11
06-May-2025 11:22:26.773 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log OS.�汾:           10.0
06-May-2025 11:22:26.773 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �ܹ�:              amd64
06-May-2025 11:22:26.773 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java ��������:     D:\app\jdk\jdk8\jre
06-May-2025 11:22:26.773 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java������汾:    1.8.0_431-b10
06-May-2025 11:22:26.773 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.��Ӧ��:        Oracle Corporation
06-May-2025 11:22:26.773 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 11:22:26.774 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 11:22:26.774 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.config.file=E:\project\other\FinancialBox\apache-tomcat-8.5.100\conf\logging.properties
06-May-2025 11:22:26.774 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
06-May-2025 11:22:26.774 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djdk.tls.ephemeralDHKeySize=2048
06-May-2025 11:22:26.774 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
06-May-2025 11:22:26.774 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dignore.endorsed.dirs=
06-May-2025 11:22:26.774 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.base=E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 11:22:26.779 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.home=E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 11:22:26.779 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.io.tmpdir=E:\project\other\FinancialBox\apache-tomcat-8.5.100\temp
06-May-2025 11:22:26.780 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -agentpath:C:\Program Files\Palo Alto Networks\Traps\cyjagent.dll
06-May-2025 11:22:26.780 ��Ϣ [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent ��java.library.path:[D:\app\jdk\jdk8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;D:\app\vm\bin\;D:\app\python\Scripts\;D:\app\python\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\app\nvm\nvm;D:\app\node\nodejs;d:\app\cursor\cursor\resources\app\bin;D:\app\git\Git\cmd;D:\app\maven\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;D:\app\bandzip\Bandizip\;D:\app\mysql\mysql-8.0.41\bin;C:\Program Files\Tailscale\;D:\app\jdk\jdk8\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\shims;C:\Users\<USER>\AppData\Roaming\Python\Scripts;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\app\cursor\cursor\resources\app\bin;.]���Ҳ�������APR��Apache Tomcat�����⣬�ÿ����������������л���������
06-May-2025 11:22:26.890 ��Ϣ [main] org.apache.coyote.AbstractProtocol.init ��ʼ��Э�鴦���� ["http-nio-8080"]
06-May-2025 11:22:26.903 ��Ϣ [main] org.apache.catalina.startup.Catalina.load Initialization processed in 417 ms
06-May-2025 11:22:26.949 ��Ϣ [main] org.apache.catalina.core.StandardService.startInternal ������������[Catalina]
06-May-2025 11:22:26.949 ��Ϣ [main] org.apache.catalina.core.StandardEngine.startInternal �������� Servlet ���棺[Apache Tomcat/8.5.100]
06-May-2025 11:22:26.956 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]
06-May-2025 11:22:27.543 ���� [localhost-startStop-1] org.apache.catalina.util.SessionIdGeneratorBase.createSecureRandom ʹ��[SHA1PRNG]�����ỰID���ɵ�SecureRandomʵ��������[459]���롣
06-May-2025 11:22:27.558 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]�Ĳ�������[602]���������
06-May-2025 11:22:27.558 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]
06-May-2025 11:22:30.111 ��Ϣ [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars ������һ��JAR��ɨ������TLD����δ����TLD�� Ϊ�˼�¼�����õ�����־��¼���Ի�ȡ��ɨ�赫δ�������ҵ�TLD������JAR�б� ��ɨ���ڼ���������Ҫ��JAR������������ʱ���JSP����ʱ�䡣
06-May-2025 11:22:44.345 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]�Ĳ�������[16,787]���������
06-May-2025 11:22:44.349 ��Ϣ [main] org.apache.coyote.AbstractProtocol.start ��ʼЭ�鴦����["http-nio-8080"]
06-May-2025 11:22:44.373 ��Ϣ [main] org.apache.catalina.startup.Catalina.start Server startup in 17469 ms
06-May-2025 11:36:02.562 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.pause ��ͣProtocolHandler["http-nio-8080"]
06-May-2025 11:36:03.393 ��Ϣ [Thread-7] org.apache.catalina.core.StandardService.stopInternal ����ֹͣ����[Catalina]
06-May-2025 11:36:03.453 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc WebӦ�ó��� [app] ע����JDBC�������� [com.mysql.cj.jdbc.Driver]������WebӦ�ó���ֹͣʱ�޷�ע������ Ϊ��ֹ�ڴ�й©��JDBC���������ѱ�ǿ��ȡ��ע�ᡣ
06-May-2025 11:36:03.454 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Log4j2-TF-3-Scheduled-1]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1093)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)]
06-May-2025 11:36:03.454 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Abandoned connection cleanup thread]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:150)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)]
06-May-2025 11:36:03.455 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[MessageServiceImpl]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Thread.sleep(Native Method)
 com.fingard.app.delegate.core.service.impl.MessageServiceImpl$1.run(MessageServiceImpl.java:106)]
06-May-2025 11:36:03.455 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Timer-0]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Object.wait(Native Method)
 java.util.TimerThread.mainLoop(Timer.java:552)
 java.util.TimerThread.run(Timer.java:505)]
06-May-2025 11:36:03.466 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.stop ����ֹͣProtocolHandler ["http-nio-8080"]
06-May-2025 11:36:03.469 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.destroy ���ڴݻ�Э�鴦���� ["http-nio-8080"]
06-May-2025 11:43:57.528 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Server.�������汾: Apache Tomcat/8.5.100
06-May-2025 11:43:57.531 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����������:        Mar 19 2024 13:54:42 UTC
06-May-2025 11:43:57.531 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �������汾��:      *********
06-May-2025 11:43:57.531 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����ϵͳ����:      Windows 11
06-May-2025 11:43:57.531 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log OS.�汾:           10.0
06-May-2025 11:43:57.532 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �ܹ�:              amd64
06-May-2025 11:43:57.532 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java ��������:     D:\app\jdk\jdk8\jre
06-May-2025 11:43:57.532 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java������汾:    1.8.0_431-b10
06-May-2025 11:43:57.532 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.��Ӧ��:        Oracle Corporation
06-May-2025 11:43:57.532 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 11:43:57.532 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 11:43:57.532 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.config.file=E:\project\other\FinancialBox\apache-tomcat-8.5.100\conf\logging.properties
06-May-2025 11:43:57.533 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
06-May-2025 11:43:57.533 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djdk.tls.ephemeralDHKeySize=2048
06-May-2025 11:43:57.533 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
06-May-2025 11:43:57.533 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dignore.endorsed.dirs=
06-May-2025 11:43:57.533 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.base=E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 11:43:57.538 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.home=E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 11:43:57.538 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.io.tmpdir=E:\project\other\FinancialBox\apache-tomcat-8.5.100\temp
06-May-2025 11:43:57.538 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -agentpath:C:\Program Files\Palo Alto Networks\Traps\cyjagent.dll
06-May-2025 11:43:57.538 ��Ϣ [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent ��java.library.path:[D:\app\jdk\jdk8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;D:\app\vm\bin\;D:\app\python\Scripts\;D:\app\python\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\app\nvm\nvm;D:\app\node\nodejs;d:\app\cursor\cursor\resources\app\bin;D:\app\git\Git\cmd;D:\app\maven\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;D:\app\bandzip\Bandizip\;D:\app\mysql\mysql-8.0.41\bin;C:\Program Files\Tailscale\;D:\app\jdk\jdk8\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\shims;C:\Users\<USER>\AppData\Roaming\Python\Scripts;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\app\cursor\cursor\resources\app\bin;.]���Ҳ�������APR��Apache Tomcat�����⣬�ÿ����������������л���������
06-May-2025 11:43:57.647 ��Ϣ [main] org.apache.coyote.AbstractProtocol.init ��ʼ��Э�鴦���� ["http-nio-8080"]
06-May-2025 11:43:57.661 ��Ϣ [main] org.apache.catalina.startup.Catalina.load Initialization processed in 449 ms
06-May-2025 11:43:57.704 ��Ϣ [main] org.apache.catalina.core.StandardService.startInternal ������������[Catalina]
06-May-2025 11:43:57.704 ��Ϣ [main] org.apache.catalina.core.StandardEngine.startInternal �������� Servlet ���棺[Apache Tomcat/8.5.100]
06-May-2025 11:43:57.710 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]
06-May-2025 11:43:58.301 ���� [localhost-startStop-1] org.apache.catalina.util.SessionIdGeneratorBase.createSecureRandom ʹ��[SHA1PRNG]�����ỰID���ɵ�SecureRandomʵ��������[457]���롣
06-May-2025 11:43:58.316 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]�Ĳ�������[606]���������
06-May-2025 11:43:58.316 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]
06-May-2025 11:44:00.959 ��Ϣ [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars ������һ��JAR��ɨ������TLD����δ����TLD�� Ϊ�˼�¼�����õ�����־��¼���Ի�ȡ��ɨ�赫δ�������ҵ�TLD������JAR�б� ��ɨ���ڼ���������Ҫ��JAR������������ʱ���JSP����ʱ�䡣
06-May-2025 11:44:20.232 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]�Ĳ�������[21,916]���������
06-May-2025 11:44:20.234 ��Ϣ [main] org.apache.coyote.AbstractProtocol.start ��ʼЭ�鴦����["http-nio-8080"]
06-May-2025 11:44:20.256 ��Ϣ [main] org.apache.catalina.startup.Catalina.start Server startup in 22595 ms
06-May-2025 14:37:53.594 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.pause ��ͣProtocolHandler["http-nio-8080"]
06-May-2025 14:37:54.561 ��Ϣ [Thread-7] org.apache.catalina.core.StandardService.stopInternal ����ֹͣ����[Catalina]
06-May-2025 14:37:54.669 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc WebӦ�ó��� [app] ע����JDBC�������� [com.mysql.cj.jdbc.Driver]������WebӦ�ó���ֹͣʱ�޷�ע������ Ϊ��ֹ�ڴ�й©��JDBC���������ѱ�ǿ��ȡ��ע�ᡣ
06-May-2025 14:37:54.671 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Log4j2-TF-3-Scheduled-1]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1093)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)]
06-May-2025 14:37:54.672 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Abandoned connection cleanup thread]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:150)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)]
06-May-2025 14:37:54.672 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Timer-0]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Object.wait(Native Method)
 java.util.TimerThread.mainLoop(Timer.java:552)
 java.util.TimerThread.run(Timer.java:505)]
06-May-2025 14:37:54.687 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.stop ����ֹͣProtocolHandler ["http-nio-8080"]
06-May-2025 14:37:54.695 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.destroy ���ڴݻ�Э�鴦���� ["http-nio-8080"]
06-May-2025 14:38:03.687 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Server.�������汾: Apache Tomcat/8.5.100
06-May-2025 14:38:03.690 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����������:        Mar 19 2024 13:54:42 UTC
06-May-2025 14:38:03.690 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �������汾��:      *********
06-May-2025 14:38:03.690 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����ϵͳ����:      Windows 11
06-May-2025 14:38:03.691 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log OS.�汾:           10.0
06-May-2025 14:38:03.691 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �ܹ�:              amd64
06-May-2025 14:38:03.691 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java ��������:     D:\app\jdk\jdk8\jre
06-May-2025 14:38:03.692 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java������汾:    1.8.0_431-b10
06-May-2025 14:38:03.692 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.��Ӧ��:        Oracle Corporation
06-May-2025 14:38:03.692 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 14:38:03.692 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 14:38:03.692 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.config.file=E:\project\other\FinancialBox\apache-tomcat-8.5.100\conf\logging.properties
06-May-2025 14:38:03.692 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
06-May-2025 14:38:03.692 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djdk.tls.ephemeralDHKeySize=2048
06-May-2025 14:38:03.692 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
06-May-2025 14:38:03.693 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dignore.endorsed.dirs=
06-May-2025 14:38:03.693 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.base=E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 14:38:03.697 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.home=E:\project\other\FinancialBox\apache-tomcat-8.5.100
06-May-2025 14:38:03.697 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.io.tmpdir=E:\project\other\FinancialBox\apache-tomcat-8.5.100\temp
06-May-2025 14:38:03.697 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -agentpath:C:\Program Files\Palo Alto Networks\Traps\cyjagent.dll
06-May-2025 14:38:03.698 ��Ϣ [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent ��java.library.path:[D:\app\jdk\jdk8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;D:\app\vm\bin\;D:\app\python\Scripts\;D:\app\python\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\app\nvm\nvm;D:\app\node\nodejs;d:\app\cursor\cursor\resources\app\bin;D:\app\git\Git\cmd;D:\app\maven\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;D:\app\bandzip\Bandizip\;D:\app\mysql\mysql-8.0.41\bin;C:\Program Files\Tailscale\;D:\app\jdk\jdk8\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\shims;C:\Users\<USER>\AppData\Roaming\Python\Scripts;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\app\cursor\cursor\resources\app\bin;.]���Ҳ�������APR��Apache Tomcat�����⣬�ÿ����������������л���������
06-May-2025 14:38:03.810 ��Ϣ [main] org.apache.coyote.AbstractProtocol.init ��ʼ��Э�鴦���� ["http-nio-8080"]
06-May-2025 14:38:03.822 ��Ϣ [main] org.apache.catalina.startup.Catalina.load Initialization processed in 418 ms
06-May-2025 14:38:03.865 ��Ϣ [main] org.apache.catalina.core.StandardService.startInternal ������������[Catalina]
06-May-2025 14:38:03.866 ��Ϣ [main] org.apache.catalina.core.StandardEngine.startInternal �������� Servlet ���棺[Apache Tomcat/8.5.100]
06-May-2025 14:38:03.872 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]
06-May-2025 14:38:04.495 ���� [localhost-startStop-1] org.apache.catalina.util.SessionIdGeneratorBase.createSecureRandom ʹ��[SHA1PRNG]�����ỰID���ɵ�SecureRandomʵ��������[486]���롣
06-May-2025 14:38:04.509 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]�Ĳ�������[636]���������
06-May-2025 14:38:04.509 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]
06-May-2025 14:38:07.266 ��Ϣ [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars ������һ��JAR��ɨ������TLD����δ����TLD�� Ϊ�˼�¼�����õ�����־��¼���Ի�ȡ��ɨ�赫δ�������ҵ�TLD������JAR�б� ��ɨ���ڼ���������Ҫ��JAR������������ʱ���JSP����ʱ�䡣
06-May-2025 14:38:29.288 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]�Ĳ�������[24,778]���������
06-May-2025 14:38:29.291 ��Ϣ [main] org.apache.coyote.AbstractProtocol.start ��ʼЭ�鴦����["http-nio-8080"]
06-May-2025 14:38:29.306 ��Ϣ [main] org.apache.catalina.startup.Catalina.start Server startup in 25484 ms
06-May-2025 16:22:54.439 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.pause ��ͣProtocolHandler["http-nio-8080"]
06-May-2025 16:22:55.233 ��Ϣ [Thread-7] org.apache.catalina.core.StandardService.stopInternal ����ֹͣ����[Catalina]
06-May-2025 16:22:55.336 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc WebӦ�ó��� [app] ע����JDBC�������� [com.mysql.cj.jdbc.Driver]������WebӦ�ó���ֹͣʱ�޷�ע������ Ϊ��ֹ�ڴ�й©��JDBC���������ѱ�ǿ��ȡ��ע�ᡣ
06-May-2025 16:22:55.337 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Log4j2-TF-3-Scheduled-1]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1093)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)]
06-May-2025 16:22:55.338 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Abandoned connection cleanup thread]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:150)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)]
06-May-2025 16:22:55.338 ���� [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Timer-0]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Object.wait(Native Method)
 java.util.TimerThread.mainLoop(Timer.java:552)
 java.util.TimerThread.run(Timer.java:505)]
06-May-2025 16:22:55.380 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.stop ����ֹͣProtocolHandler ["http-nio-8080"]
06-May-2025 16:22:55.385 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.destroy ���ڴݻ�Э�鴦���� ["http-nio-8080"]
