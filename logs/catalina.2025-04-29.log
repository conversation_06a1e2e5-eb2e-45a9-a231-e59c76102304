29-Apr-2025 17:51:40.822 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Server.�������汾: Apache Tomcat/8.5.100
29-Apr-2025 17:51:40.825 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����������:        Mar 19 2024 13:54:42 UTC
29-Apr-2025 17:51:40.826 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �������汾��:      8.5.100.0
29-Apr-2025 17:51:40.826 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����ϵͳ����:      Windows 11
29-Apr-2025 17:51:40.826 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log OS.�汾:           10.0
29-Apr-2025 17:51:40.826 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �ܹ�:              amd64
29-Apr-2025 17:51:40.826 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java ��������:     D:\app\jdk\jdk8\jre
29-Apr-2025 17:51:40.827 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java������汾:    1.8.0_431-b10
29-Apr-2025 17:51:40.827 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.��Ӧ��:        Oracle Corporation
29-Apr-2025 17:51:40.827 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
29-Apr-2025 17:51:40.827 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
29-Apr-2025 17:51:40.827 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.config.file=E:\project\other\FinancialBox\apache-tomcat-8.5.100\conf\logging.properties
29-Apr-2025 17:51:40.827 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
29-Apr-2025 17:51:40.827 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djdk.tls.ephemeralDHKeySize=2048
29-Apr-2025 17:51:40.828 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
29-Apr-2025 17:51:40.828 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dignore.endorsed.dirs=
29-Apr-2025 17:51:40.828 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.base=E:\project\other\FinancialBox\apache-tomcat-8.5.100
29-Apr-2025 17:51:40.832 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.home=E:\project\other\FinancialBox\apache-tomcat-8.5.100
29-Apr-2025 17:51:40.832 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.io.tmpdir=E:\project\other\FinancialBox\apache-tomcat-8.5.100\temp
29-Apr-2025 17:51:40.832 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -agentpath:C:\Program Files\Palo Alto Networks\Traps\cyjagent.dll
29-Apr-2025 17:51:40.832 ��Ϣ [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent ��java.library.path:[D:\app\jdk\jdk8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;D:\app\vm\bin\;D:\app\python\Scripts\;D:\app\python\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\app\nvm\nvm;D:\app\node\nodejs;d:\app\cursor\cursor\resources\app\bin;D:\app\git\Git\cmd;D:\app\maven\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;D:\app\bandzip\Bandizip\;D:\app\mysql\mysql-8.0.41\bin;C:\Program Files\Tailscale\;D:\app\jdk\jdk8\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\shims;C:\Users\<USER>\AppData\Roaming\Python\Scripts;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\app\cursor\cursor\resources\app\bin;.]���Ҳ�������APR��Apache Tomcat�����⣬�ÿ����������������л���������
29-Apr-2025 17:51:40.970 ��Ϣ [main] org.apache.coyote.AbstractProtocol.init ��ʼ��Э�鴦���� ["http-nio-8080"]
29-Apr-2025 17:51:40.987 ��Ϣ [main] org.apache.catalina.startup.Catalina.load Initialization processed in 566 ms
29-Apr-2025 17:51:41.040 ��Ϣ [main] org.apache.catalina.core.StandardService.startInternal ������������[Catalina]
29-Apr-2025 17:51:41.041 ��Ϣ [main] org.apache.catalina.core.StandardEngine.startInternal �������� Servlet ���棺[Apache Tomcat/8.5.100]
29-Apr-2025 17:51:41.048 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]
29-Apr-2025 17:51:41.533 ���� [localhost-startStop-1] org.apache.catalina.util.SessionIdGeneratorBase.createSecureRandom ʹ��[SHA1PRNG]�����ỰID���ɵ�SecureRandomʵ��������[343]���롣
29-Apr-2025 17:51:41.549 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]�Ĳ�������[501]���������
29-Apr-2025 17:51:41.549 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]
29-Apr-2025 17:51:44.239 ��Ϣ [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars ������һ��JAR��ɨ������TLD����δ����TLD�� Ϊ�˼�¼�����õ�����־��¼���Ի�ȡ��ɨ�赫δ�������ҵ�TLD������JAR�б� ��ɨ���ڼ���������Ҫ��JAR������������ʱ���JSP����ʱ�䡣
29-Apr-2025 17:52:03.431 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]�Ĳ�������[21,882]���������
29-Apr-2025 17:52:03.442 ��Ϣ [main] org.apache.coyote.AbstractProtocol.start ��ʼЭ�鴦����["http-nio-8080"]
29-Apr-2025 17:52:03.458 ��Ϣ [main] org.apache.catalina.startup.Catalina.start Server startup in 22471 ms
29-Apr-2025 17:52:12.112 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.pause ��ͣProtocolHandler["http-nio-8080"]
29-Apr-2025 17:52:12.477 ��Ϣ [Thread-7] org.apache.catalina.core.StandardService.stopInternal ����ֹͣ����[Catalina]
29-Apr-2025 17:52:12.508 ���� [localhost-startStop-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc WebӦ�ó��� [app] ע����JDBC�������� [com.mysql.cj.jdbc.Driver]������WebӦ�ó���ֹͣʱ�޷�ע������ Ϊ��ֹ�ڴ�й©��JDBC���������ѱ�ǿ��ȡ��ע�ᡣ
29-Apr-2025 17:52:12.510 ���� [localhost-startStop-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Log4j2-TF-3-Scheduled-1]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:215)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2078)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1093)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)]
29-Apr-2025 17:52:12.510 ���� [localhost-startStop-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Abandoned connection cleanup thread]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:150)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)]
29-Apr-2025 17:52:12.510 ���� [localhost-startStop-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[MessageServiceImpl]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Thread.sleep(Native Method)
 com.fingard.app.delegate.core.service.impl.MessageServiceImpl$1.run(MessageServiceImpl.java:106)]
29-Apr-2025 17:52:12.511 ���� [localhost-startStop-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads WebӦ�ó���[app]�ƺ�������һ����Ϊ[Timer-0]���̣߳���δ��ֹͣ������ܿ��ܻ�����ڴ�й©���̵߳Ķ�ջ���٣�[
 java.lang.Object.wait(Native Method)
 java.util.TimerThread.mainLoop(Timer.java:552)
 java.util.TimerThread.run(Timer.java:505)]
29-Apr-2025 17:52:12.519 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.stop ����ֹͣProtocolHandler ["http-nio-8080"]
29-Apr-2025 17:52:12.525 ��Ϣ [Thread-7] org.apache.coyote.AbstractProtocol.destroy ���ڴݻ�Э�鴦���� ["http-nio-8080"]
29-Apr-2025 17:52:41.260 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Server.�������汾: Apache Tomcat/8.5.100
29-Apr-2025 17:52:41.262 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����������:        Mar 19 2024 13:54:42 UTC
29-Apr-2025 17:52:41.262 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �������汾��:      8.5.100.0
29-Apr-2025 17:52:41.262 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log ����ϵͳ����:      Windows 11
29-Apr-2025 17:52:41.263 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log OS.�汾:           10.0
29-Apr-2025 17:52:41.263 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �ܹ�:              amd64
29-Apr-2025 17:52:41.263 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java ��������:     D:\app\jdk\jdk8\jre
29-Apr-2025 17:52:41.263 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log Java������汾:    1.8.0_431-b10
29-Apr-2025 17:52:41.264 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.��Ӧ��:        Oracle Corporation
29-Apr-2025 17:52:41.264 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
29-Apr-2025 17:52:41.264 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     E:\project\other\FinancialBox\apache-tomcat-8.5.100
29-Apr-2025 17:52:41.264 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.config.file=E:\project\other\FinancialBox\apache-tomcat-8.5.100\conf\logging.properties
29-Apr-2025 17:52:41.264 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
29-Apr-2025 17:52:41.264 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djdk.tls.ephemeralDHKeySize=2048
29-Apr-2025 17:52:41.265 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
29-Apr-2025 17:52:41.265 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dignore.endorsed.dirs=
29-Apr-2025 17:52:41.265 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.base=E:\project\other\FinancialBox\apache-tomcat-8.5.100
29-Apr-2025 17:52:41.269 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Dcatalina.home=E:\project\other\FinancialBox\apache-tomcat-8.5.100
29-Apr-2025 17:52:41.269 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -Djava.io.tmpdir=E:\project\other\FinancialBox\apache-tomcat-8.5.100\temp
29-Apr-2025 17:52:41.269 ��Ϣ [main] org.apache.catalina.startup.VersionLoggerListener.log �����в�����       -agentpath:C:\Program Files\Palo Alto Networks\Traps\cyjagent.dll
29-Apr-2025 17:52:41.269 ��Ϣ [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent ��java.library.path:[D:\app\jdk\jdk8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;D:\app\vm\bin\;D:\app\python\Scripts\;D:\app\python\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\app\nvm\nvm;D:\app\node\nodejs;d:\app\cursor\cursor\resources\app\bin;D:\app\git\Git\cmd;D:\app\maven\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;D:\app\bandzip\Bandizip\;D:\app\mysql\mysql-8.0.41\bin;C:\Program Files\Tailscale\;D:\app\jdk\jdk8\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\bin;D:\app\pyenv\pyenv-win-master\pyenv-win\shims;C:\Users\<USER>\AppData\Roaming\Python\Scripts;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\app\cursor\cursor\resources\app\bin;.]���Ҳ�������APR��Apache Tomcat�����⣬�ÿ����������������л���������
29-Apr-2025 17:52:41.379 ��Ϣ [main] org.apache.coyote.AbstractProtocol.init ��ʼ��Э�鴦���� ["http-nio-8080"]
29-Apr-2025 17:52:41.393 ��Ϣ [main] org.apache.catalina.startup.Catalina.load Initialization processed in 396 ms
29-Apr-2025 17:52:41.438 ��Ϣ [main] org.apache.catalina.core.StandardService.startInternal ������������[Catalina]
29-Apr-2025 17:52:41.439 ��Ϣ [main] org.apache.catalina.core.StandardEngine.startInternal �������� Servlet ���棺[Apache Tomcat/8.5.100]
29-Apr-2025 17:52:41.446 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]
29-Apr-2025 17:52:41.910 ���� [localhost-startStop-1] org.apache.catalina.util.SessionIdGeneratorBase.createSecureRandom ʹ��[SHA1PRNG]�����ỰID���ɵ�SecureRandomʵ��������[339]���롣
29-Apr-2025 17:52:41.922 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\.idea]�Ĳ�������[476]���������
29-Apr-2025 17:52:41.922 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory ��web Ӧ�ó�����Ŀ¼ [E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]
29-Apr-2025 17:52:44.267 ��Ϣ [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars ������һ��JAR��ɨ������TLD����δ����TLD�� Ϊ�˼�¼�����õ�����־��¼���Ի�ȡ��ɨ�赫δ�������ҵ�TLD������JAR�б� ��ɨ���ڼ���������Ҫ��JAR������������ʱ���JSP����ʱ�䡣
29-Apr-2025 17:54:56.754 ��Ϣ [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory WebӦ�ó���Ŀ¼[E:\project\other\FinancialBox\apache-tomcat-8.5.100\webapps\app]�Ĳ�������[134,832]���������
29-Apr-2025 17:54:56.772 ��Ϣ [main] org.apache.coyote.AbstractProtocol.start ��ʼЭ�鴦����["http-nio-8080"]
29-Apr-2025 17:54:56.797 ��Ϣ [main] org.apache.catalina.startup.Catalina.start Server startup in 135402 ms
