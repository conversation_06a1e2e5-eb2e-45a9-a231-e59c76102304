25-Mar-2025 16:10:07.139 警告 [main] org.apache.tomcat.util.digester.SetPropertiesRule.begin [SetPropertiesRule]{Server/Service/Engine/Host/Context} Setting property 'debug' to '0' did not find a matching property.
25-Mar-2025 16:10:07.155 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Server.服务器版本: Apache Tomcat/8.5.100
25-Mar-2025 16:10:07.155 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器构建:        Mar 19 2024 13:54:42 UTC
25-Mar-2025 16:10:07.156 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器版本号:      *********
25-Mar-2025 16:10:07.156 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 操作系统名称:      Linux
25-Mar-2025 16:10:07.156 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log OS.版本:           3.10.0-1160.el7.x86_64
25-Mar-2025 16:10:07.156 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 架构:              amd64
25-Mar-2025 16:10:07.156 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java 环境变量:     /home/<USER>/onekey-deploy/jdk1.8.0_181/jre
25-Mar-2025 16:10:07.156 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java虚拟机版本:    1.8.0_181-b13
25-Mar-2025 16:10:07.156 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.供应商:        Oracle Corporation
25-Mar-2025 16:10:07.156 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:10:07.156 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:10:07.156 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.config.file=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/conf/logging.properties
25-Mar-2025 16:10:07.156 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
25-Mar-2025 16:10:07.156 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djdk.tls.ephemeralDHKeySize=2048
25-Mar-2025 16:10:07.156 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
25-Mar-2025 16:10:07.159 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
25-Mar-2025 16:10:07.159 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dignore.endorsed.dirs=
25-Mar-2025 16:10:07.159 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.base=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:10:07.159 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.home=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:10:07.160 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.io.tmpdir=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/temp
25-Mar-2025 16:10:07.160 信息 [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent 在java.library.path:[/usr/java/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib]上找不到基于APR的Apache Tomcat本机库，该库允许在生产环境中获得最佳性能
25-Mar-2025 16:10:07.242 信息 [main] org.apache.coyote.AbstractProtocol.init 初始化协议处理器 ["http-nio-8080"]
25-Mar-2025 16:10:07.255 信息 [main] org.apache.catalina.startup.Catalina.load Initialization processed in 388 ms
25-Mar-2025 16:10:07.273 信息 [main] org.apache.catalina.core.StandardService.startInternal 正在启动服务[Catalina]
25-Mar-2025 16:10:07.274 信息 [main] org.apache.catalina.core.StandardEngine.startInternal 正在启动 Servlet 引擎：[Apache Tomcat/8.5.100]
25-Mar-2025 16:10:09.510 信息 [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars 至少有一个JAR被扫描用于TLD但尚未包含TLD。 为此记录器启用调试日志记录，以获取已扫描但未在其中找到TLD的完整JAR列表。 在扫描期间跳过不需要的JAR可以缩短启动时间和JSP编译时间。
log4j:WARN No appenders could be found for logger (org.springframework.web.context.ContextLoader).
log4j:WARN Please initialize the log4j system properly.
25-Mar-2025 16:12:11.807 严重 [localhost-startStop-1] org.apache.catalina.core.StandardContext.startInternal 一个或多个listeners启动失败，更多详细信息查看对应的容器日志文件
25-Mar-2025 16:12:11.813 严重 [localhost-startStop-1] org.apache.catalina.core.StandardContext.startInternal 由于之前的错误，Context[/app]启动失败
25-Mar-2025 16:12:11.819 警告 [localhost-startStop-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc Web应用程序 [app] 注册了JDBC驱动程序 [com.mysql.cj.jdbc.Driver]，但在Web应用程序停止时无法注销它。 为防止内存泄漏，JDBC驱动程序已被强制取消注册。
25-Mar-2025 16:12:11.819 警告 [localhost-startStop-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Abandoned connection cleanup thread]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:144)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)]
25-Mar-2025 16:12:11.826 信息 [main] org.apache.coyote.AbstractProtocol.start 开始协议处理句柄["http-nio-8080"]
25-Mar-2025 16:12:11.840 信息 [main] org.apache.catalina.startup.Catalina.start Server startup in 124585 ms
25-Mar-2025 16:12:15.788 信息 [Abandoned connection cleanup thread] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading 非法访问：此Web应用程序实例已停止。无法加载[]。为了调试以及终止导致非法访问的线程，将抛出以下堆栈跟踪。
	java.lang.IllegalStateException: 非法访问：此Web应用程序实例已停止。无法加载[]。为了调试以及终止导致非法访问的线程，将抛出以下堆栈跟踪。
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1358)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:990)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkContextClassLoaders(AbandonedConnectionCleanupThread.java:96)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:69)
		at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
		at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
		at java.lang.Thread.run(Thread.java:748)
25-Mar-2025 16:12:23.610 信息 [Thread-6] org.apache.coyote.AbstractProtocol.pause 暂停ProtocolHandler["http-nio-8080"]
25-Mar-2025 16:12:23.698 信息 [Thread-6] org.apache.catalina.core.StandardService.stopInternal 正在停止服务[Catalina]
25-Mar-2025 16:12:23.700 信息 [Thread-6] org.apache.coyote.AbstractProtocol.stop 正在停止ProtocolHandler ["http-nio-8080"]
25-Mar-2025 16:12:23.705 信息 [Thread-6] org.apache.coyote.AbstractProtocol.destroy 正在摧毁协议处理器 ["http-nio-8080"]
25-Mar-2025 16:12:31.901 警告 [main] org.apache.tomcat.util.digester.SetPropertiesRule.begin [SetPropertiesRule]{Server/Service/Engine/Host/Context} Setting property 'debug' to '0' did not find a matching property.
25-Mar-2025 16:12:31.917 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Server.服务器版本: Apache Tomcat/8.5.100
25-Mar-2025 16:12:31.917 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器构建:        Mar 19 2024 13:54:42 UTC
25-Mar-2025 16:12:31.917 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器版本号:      *********
25-Mar-2025 16:12:31.917 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 操作系统名称:      Linux
25-Mar-2025 16:12:31.918 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log OS.版本:           3.10.0-1160.el7.x86_64
25-Mar-2025 16:12:31.918 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 架构:              amd64
25-Mar-2025 16:12:31.918 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java 环境变量:     /home/<USER>/onekey-deploy/jdk1.8.0_181/jre
25-Mar-2025 16:12:31.918 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java虚拟机版本:    1.8.0_181-b13
25-Mar-2025 16:12:31.918 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.供应商:        Oracle Corporation
25-Mar-2025 16:12:31.918 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:12:31.918 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:12:31.918 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.config.file=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/conf/logging.properties
25-Mar-2025 16:12:31.918 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
25-Mar-2025 16:12:31.918 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djdk.tls.ephemeralDHKeySize=2048
25-Mar-2025 16:12:31.918 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
25-Mar-2025 16:12:31.921 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
25-Mar-2025 16:12:31.921 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dignore.endorsed.dirs=
25-Mar-2025 16:12:31.921 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.base=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:12:31.921 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.home=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:12:31.921 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.io.tmpdir=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/temp
25-Mar-2025 16:12:31.922 信息 [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent 在java.library.path:[/usr/java/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib]上找不到基于APR的Apache Tomcat本机库，该库允许在生产环境中获得最佳性能
25-Mar-2025 16:12:32.006 信息 [main] org.apache.coyote.AbstractProtocol.init 初始化协议处理器 ["http-nio-8080"]
25-Mar-2025 16:12:32.020 信息 [main] org.apache.catalina.startup.Catalina.load Initialization processed in 390 ms
25-Mar-2025 16:12:32.040 信息 [main] org.apache.catalina.core.StandardService.startInternal 正在启动服务[Catalina]
25-Mar-2025 16:12:32.040 信息 [main] org.apache.catalina.core.StandardEngine.startInternal 正在启动 Servlet 引擎：[Apache Tomcat/8.5.100]
25-Mar-2025 16:12:34.248 信息 [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars 至少有一个JAR被扫描用于TLD但尚未包含TLD。 为此记录器启用调试日志记录，以获取已扫描但未在其中找到TLD的完整JAR列表。 在扫描期间跳过不需要的JAR可以缩短启动时间和JSP编译时间。
log4j:WARN No appenders could be found for logger (org.springframework.web.context.ContextLoader).
log4j:WARN Please initialize the log4j system properly.
25-Mar-2025 16:12:39.563 信息 [main] org.apache.coyote.AbstractProtocol.start 开始协议处理句柄["http-nio-8080"]
25-Mar-2025 16:12:39.574 信息 [main] org.apache.catalina.startup.Catalina.start Server startup in 7553 ms
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
25-Mar-2025 16:17:45.970 信息 [Thread-7] org.apache.coyote.AbstractProtocol.pause 暂停ProtocolHandler["http-nio-8080"]
25-Mar-2025 16:17:45.972 信息 [Thread-7] org.apache.catalina.core.StandardService.stopInternal 正在停止服务[Catalina]
25-Mar-2025 16:17:45.997 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc Web应用程序 [app] 注册了JDBC驱动程序 [com.mysql.cj.jdbc.Driver]，但在Web应用程序停止时无法注销它。 为防止内存泄漏，JDBC驱动程序已被强制取消注册。
25-Mar-2025 16:17:45.998 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Abandoned connection cleanup thread]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:144)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)]
25-Mar-2025 16:17:45.999 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[MessageServiceImpl]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Thread.sleep(Native Method)
 com.fingard.app.delegate.core.service.impl.MessageServiceImpl$1.run(MessageServiceImpl.java:106)]
25-Mar-2025 16:17:45.999 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Timer-0]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.util.TimerThread.mainLoop(Timer.java:552)
 java.util.TimerThread.run(Timer.java:505)]
25-Mar-2025 16:17:46.007 信息 [Thread-7] org.apache.coyote.AbstractProtocol.stop 正在停止ProtocolHandler ["http-nio-8080"]
25-Mar-2025 16:17:46.015 信息 [Thread-7] org.apache.coyote.AbstractProtocol.destroy 正在摧毁协议处理器 ["http-nio-8080"]
25-Mar-2025 16:17:51.689 警告 [main] org.apache.tomcat.util.digester.SetPropertiesRule.begin [SetPropertiesRule]{Server/Service/Engine/Host/Context} Setting property 'debug' to '0' did not find a matching property.
25-Mar-2025 16:17:51.705 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Server.服务器版本: Apache Tomcat/8.5.100
25-Mar-2025 16:17:51.705 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器构建:        Mar 19 2024 13:54:42 UTC
25-Mar-2025 16:17:51.705 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器版本号:      *********
25-Mar-2025 16:17:51.705 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 操作系统名称:      Linux
25-Mar-2025 16:17:51.706 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log OS.版本:           3.10.0-1160.el7.x86_64
25-Mar-2025 16:17:51.706 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 架构:              amd64
25-Mar-2025 16:17:51.706 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java 环境变量:     /home/<USER>/onekey-deploy/jdk1.8.0_181/jre
25-Mar-2025 16:17:51.706 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java虚拟机版本:    1.8.0_181-b13
25-Mar-2025 16:17:51.706 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.供应商:        Oracle Corporation
25-Mar-2025 16:17:51.706 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:17:51.706 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:17:51.706 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.config.file=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/conf/logging.properties
25-Mar-2025 16:17:51.706 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
25-Mar-2025 16:17:51.706 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djdk.tls.ephemeralDHKeySize=2048
25-Mar-2025 16:17:51.706 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
25-Mar-2025 16:17:51.706 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
25-Mar-2025 16:17:51.707 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dignore.endorsed.dirs=
25-Mar-2025 16:17:51.707 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.base=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:17:51.707 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.home=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:17:51.707 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.io.tmpdir=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/temp
25-Mar-2025 16:17:51.707 信息 [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent 在java.library.path:[/usr/java/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib]上找不到基于APR的Apache Tomcat本机库，该库允许在生产环境中获得最佳性能
25-Mar-2025 16:17:51.790 信息 [main] org.apache.coyote.AbstractProtocol.init 初始化协议处理器 ["http-nio-8080"]
25-Mar-2025 16:17:51.804 信息 [main] org.apache.catalina.startup.Catalina.load Initialization processed in 387 ms
25-Mar-2025 16:17:51.823 信息 [main] org.apache.catalina.core.StandardService.startInternal 正在启动服务[Catalina]
25-Mar-2025 16:17:51.823 信息 [main] org.apache.catalina.core.StandardEngine.startInternal 正在启动 Servlet 引擎：[Apache Tomcat/8.5.100]
25-Mar-2025 16:17:53.944 信息 [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars 至少有一个JAR被扫描用于TLD但尚未包含TLD。 为此记录器启用调试日志记录，以获取已扫描但未在其中找到TLD的完整JAR列表。 在扫描期间跳过不需要的JAR可以缩短启动时间和JSP编译时间。
log4j:WARN No appenders could be found for logger (org.springframework.web.context.ContextLoader).
log4j:WARN Please initialize the log4j system properly.
25-Mar-2025 16:17:59.022 信息 [main] org.apache.coyote.AbstractProtocol.start 开始协议处理句柄["http-nio-8080"]
25-Mar-2025 16:17:59.034 信息 [main] org.apache.catalina.startup.Catalina.start Server startup in 7230 ms
25-Mar-2025 16:24:07.618 信息 [main] org.apache.catalina.core.StandardServer.await 通过关闭端口接收到有效的关闭命令。正在停止服务器实例。
25-Mar-2025 16:24:07.619 信息 [main] org.apache.coyote.AbstractProtocol.pause 暂停ProtocolHandler["http-nio-8080"]
25-Mar-2025 16:24:07.621 信息 [main] org.apache.catalina.core.StandardService.stopInternal 正在停止服务[Catalina]
25-Mar-2025 16:24:07.645 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc Web应用程序 [app] 注册了JDBC驱动程序 [com.mysql.cj.jdbc.Driver]，但在Web应用程序停止时无法注销它。 为防止内存泄漏，JDBC驱动程序已被强制取消注册。
25-Mar-2025 16:24:07.645 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Abandoned connection cleanup thread]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:144)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)]
25-Mar-2025 16:24:07.646 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[MessageServiceImpl]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Thread.sleep(Native Method)
 com.fingard.app.delegate.core.service.impl.MessageServiceImpl$1.run(MessageServiceImpl.java:106)]
25-Mar-2025 16:24:07.646 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Timer-0]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.util.TimerThread.mainLoop(Timer.java:552)
 java.util.TimerThread.run(Timer.java:505)]
25-Mar-2025 16:24:07.652 信息 [main] org.apache.coyote.AbstractProtocol.stop 正在停止ProtocolHandler ["http-nio-8080"]
25-Mar-2025 16:24:07.664 信息 [main] org.apache.coyote.AbstractProtocol.destroy 正在摧毁协议处理器 ["http-nio-8080"]
25-Mar-2025 16:24:32.873 警告 [main] org.apache.tomcat.util.digester.SetPropertiesRule.begin [SetPropertiesRule]{Server/Service/Engine/Host/Context} Setting property 'debug' to '0' did not find a matching property.
25-Mar-2025 16:24:32.887 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Server.服务器版本: Apache Tomcat/8.5.100
25-Mar-2025 16:24:32.888 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器构建:        Mar 19 2024 13:54:42 UTC
25-Mar-2025 16:24:32.888 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器版本号:      *********
25-Mar-2025 16:24:32.888 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 操作系统名称:      Linux
25-Mar-2025 16:24:32.888 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log OS.版本:           3.10.0-1160.el7.x86_64
25-Mar-2025 16:24:32.888 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 架构:              amd64
25-Mar-2025 16:24:32.888 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java 环境变量:     /home/<USER>/onekey-deploy/jdk1.8.0_181/jre
25-Mar-2025 16:24:32.888 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java虚拟机版本:    1.8.0_181-b13
25-Mar-2025 16:24:32.888 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.供应商:        Oracle Corporation
25-Mar-2025 16:24:32.888 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:24:32.888 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:24:32.889 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.config.file=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/conf/logging.properties
25-Mar-2025 16:24:32.889 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
25-Mar-2025 16:24:32.889 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djdk.tls.ephemeralDHKeySize=2048
25-Mar-2025 16:24:32.889 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
25-Mar-2025 16:24:32.891 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
25-Mar-2025 16:24:32.892 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dignore.endorsed.dirs=
25-Mar-2025 16:24:32.892 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.base=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:24:32.892 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.home=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:24:32.892 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.io.tmpdir=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/temp
25-Mar-2025 16:24:32.892 信息 [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent 在java.library.path:[/usr/java/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib]上找不到基于APR的Apache Tomcat本机库，该库允许在生产环境中获得最佳性能
25-Mar-2025 16:24:32.966 信息 [main] org.apache.coyote.AbstractProtocol.init 初始化协议处理器 ["http-nio-8080"]
25-Mar-2025 16:24:32.978 信息 [main] org.apache.catalina.startup.Catalina.load Initialization processed in 359 ms
25-Mar-2025 16:24:32.996 信息 [main] org.apache.catalina.core.StandardService.startInternal 正在启动服务[Catalina]
25-Mar-2025 16:24:32.996 信息 [main] org.apache.catalina.core.StandardEngine.startInternal 正在启动 Servlet 引擎：[Apache Tomcat/8.5.100]
25-Mar-2025 16:24:35.109 信息 [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars 至少有一个JAR被扫描用于TLD但尚未包含TLD。 为此记录器启用调试日志记录，以获取已扫描但未在其中找到TLD的完整JAR列表。 在扫描期间跳过不需要的JAR可以缩短启动时间和JSP编译时间。
log4j:WARN No appenders could be found for logger (org.springframework.web.context.ContextLoader).
log4j:WARN Please initialize the log4j system properly.
25-Mar-2025 16:24:40.111 信息 [main] org.apache.coyote.AbstractProtocol.start 开始协议处理句柄["http-nio-8080"]
25-Mar-2025 16:24:40.128 信息 [main] org.apache.catalina.startup.Catalina.start Server startup in 7149 ms
25-Mar-2025 16:27:09.414 信息 [main] org.apache.catalina.core.StandardServer.await 通过关闭端口接收到有效的关闭命令。正在停止服务器实例。
25-Mar-2025 16:27:09.414 信息 [main] org.apache.coyote.AbstractProtocol.pause 暂停ProtocolHandler["http-nio-8080"]
25-Mar-2025 16:27:09.417 信息 [main] org.apache.catalina.core.StandardService.stopInternal 正在停止服务[Catalina]
25-Mar-2025 16:27:09.444 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc Web应用程序 [app] 注册了JDBC驱动程序 [com.mysql.cj.jdbc.Driver]，但在Web应用程序停止时无法注销它。 为防止内存泄漏，JDBC驱动程序已被强制取消注册。
25-Mar-2025 16:27:09.446 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Abandoned connection cleanup thread]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:144)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)]
25-Mar-2025 16:27:09.447 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[MessageServiceImpl]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Thread.sleep(Native Method)
 com.fingard.app.delegate.core.service.impl.MessageServiceImpl$1.run(MessageServiceImpl.java:106)]
25-Mar-2025 16:27:09.447 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Timer-0]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.util.TimerThread.mainLoop(Timer.java:552)
 java.util.TimerThread.run(Timer.java:505)]
25-Mar-2025 16:27:09.458 信息 [main] org.apache.coyote.AbstractProtocol.stop 正在停止ProtocolHandler ["http-nio-8080"]
25-Mar-2025 16:27:09.470 信息 [main] org.apache.coyote.AbstractProtocol.destroy 正在摧毁协议处理器 ["http-nio-8080"]
25-Mar-2025 16:27:11.822 警告 [main] org.apache.tomcat.util.digester.SetPropertiesRule.begin [SetPropertiesRule]{Server/Service/Engine/Host/Context} Setting property 'debug' to '0' did not find a matching property.
25-Mar-2025 16:27:11.838 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Server.服务器版本: Apache Tomcat/8.5.100
25-Mar-2025 16:27:11.838 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器构建:        Mar 19 2024 13:54:42 UTC
25-Mar-2025 16:27:11.838 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器版本号:      *********
25-Mar-2025 16:27:11.838 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 操作系统名称:      Linux
25-Mar-2025 16:27:11.838 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log OS.版本:           3.10.0-1160.el7.x86_64
25-Mar-2025 16:27:11.838 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 架构:              amd64
25-Mar-2025 16:27:11.838 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java 环境变量:     /home/<USER>/onekey-deploy/jdk1.8.0_181/jre
25-Mar-2025 16:27:11.838 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java虚拟机版本:    1.8.0_181-b13
25-Mar-2025 16:27:11.839 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.供应商:        Oracle Corporation
25-Mar-2025 16:27:11.839 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:27:11.839 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:27:11.839 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.config.file=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/conf/logging.properties
25-Mar-2025 16:27:11.839 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
25-Mar-2025 16:27:11.839 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djdk.tls.ephemeralDHKeySize=2048
25-Mar-2025 16:27:11.839 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
25-Mar-2025 16:27:11.842 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
25-Mar-2025 16:27:11.842 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dignore.endorsed.dirs=
25-Mar-2025 16:27:11.842 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.base=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:27:11.842 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.home=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:27:11.842 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.io.tmpdir=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/temp
25-Mar-2025 16:27:11.842 信息 [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent 在java.library.path:[/usr/java/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib]上找不到基于APR的Apache Tomcat本机库，该库允许在生产环境中获得最佳性能
25-Mar-2025 16:27:11.923 信息 [main] org.apache.coyote.AbstractProtocol.init 初始化协议处理器 ["http-nio-8080"]
25-Mar-2025 16:27:11.936 信息 [main] org.apache.catalina.startup.Catalina.load Initialization processed in 388 ms
25-Mar-2025 16:27:11.956 信息 [main] org.apache.catalina.core.StandardService.startInternal 正在启动服务[Catalina]
25-Mar-2025 16:27:11.956 信息 [main] org.apache.catalina.core.StandardEngine.startInternal 正在启动 Servlet 引擎：[Apache Tomcat/8.5.100]
25-Mar-2025 16:27:14.039 信息 [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars 至少有一个JAR被扫描用于TLD但尚未包含TLD。 为此记录器启用调试日志记录，以获取已扫描但未在其中找到TLD的完整JAR列表。 在扫描期间跳过不需要的JAR可以缩短启动时间和JSP编译时间。
log4j:WARN No appenders could be found for logger (org.springframework.web.context.ContextLoader).
log4j:WARN Please initialize the log4j system properly.
25-Mar-2025 16:27:19.282 信息 [main] org.apache.coyote.AbstractProtocol.start 开始协议处理句柄["http-nio-8080"]
25-Mar-2025 16:27:19.295 信息 [main] org.apache.catalina.startup.Catalina.start Server startup in 7358 ms
25-Mar-2025 16:30:14.624 信息 [main] org.apache.catalina.core.StandardServer.await 通过关闭端口接收到有效的关闭命令。正在停止服务器实例。
25-Mar-2025 16:30:14.625 信息 [main] org.apache.coyote.AbstractProtocol.pause 暂停ProtocolHandler["http-nio-8080"]
25-Mar-2025 16:30:14.628 信息 [main] org.apache.catalina.core.StandardService.stopInternal 正在停止服务[Catalina]
25-Mar-2025 16:30:14.660 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc Web应用程序 [app] 注册了JDBC驱动程序 [com.mysql.cj.jdbc.Driver]，但在Web应用程序停止时无法注销它。 为防止内存泄漏，JDBC驱动程序已被强制取消注册。
25-Mar-2025 16:30:14.662 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Abandoned connection cleanup thread]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:144)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)]
25-Mar-2025 16:30:14.662 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[MessageServiceImpl]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Thread.sleep(Native Method)
 com.fingard.app.delegate.core.service.impl.MessageServiceImpl$1.run(MessageServiceImpl.java:106)]
25-Mar-2025 16:30:14.663 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Timer-0]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.util.TimerThread.mainLoop(Timer.java:552)
 java.util.TimerThread.run(Timer.java:505)]
25-Mar-2025 16:30:14.672 信息 [main] org.apache.coyote.AbstractProtocol.stop 正在停止ProtocolHandler ["http-nio-8080"]
25-Mar-2025 16:30:14.681 信息 [main] org.apache.coyote.AbstractProtocol.destroy 正在摧毁协议处理器 ["http-nio-8080"]
25-Mar-2025 16:30:21.969 警告 [main] org.apache.tomcat.util.digester.SetPropertiesRule.begin [SetPropertiesRule]{Server/Service/Engine/Host/Context} Setting property 'debug' to '0' did not find a matching property.
25-Mar-2025 16:30:21.986 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Server.服务器版本: Apache Tomcat/8.5.100
25-Mar-2025 16:30:21.986 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器构建:        Mar 19 2024 13:54:42 UTC
25-Mar-2025 16:30:21.986 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器版本号:      *********
25-Mar-2025 16:30:21.986 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 操作系统名称:      Linux
25-Mar-2025 16:30:21.986 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log OS.版本:           3.10.0-1160.el7.x86_64
25-Mar-2025 16:30:21.986 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 架构:              amd64
25-Mar-2025 16:30:21.986 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java 环境变量:     /home/<USER>/onekey-deploy/jdk1.8.0_181/jre
25-Mar-2025 16:30:21.987 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java虚拟机版本:    1.8.0_181-b13
25-Mar-2025 16:30:21.987 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.供应商:        Oracle Corporation
25-Mar-2025 16:30:21.987 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:30:21.987 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:30:21.987 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.config.file=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/conf/logging.properties
25-Mar-2025 16:30:21.987 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
25-Mar-2025 16:30:21.987 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djdk.tls.ephemeralDHKeySize=2048
25-Mar-2025 16:30:21.987 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
25-Mar-2025 16:30:21.990 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
25-Mar-2025 16:30:21.990 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dignore.endorsed.dirs=
25-Mar-2025 16:30:21.990 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.base=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:30:21.990 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.home=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:30:21.990 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.io.tmpdir=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/temp
25-Mar-2025 16:30:21.990 信息 [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent 在java.library.path:[/usr/java/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib]上找不到基于APR的Apache Tomcat本机库，该库允许在生产环境中获得最佳性能
25-Mar-2025 16:30:22.072 信息 [main] org.apache.coyote.AbstractProtocol.init 初始化协议处理器 ["http-nio-8080"]
25-Mar-2025 16:30:22.086 信息 [main] org.apache.catalina.startup.Catalina.load Initialization processed in 390 ms
25-Mar-2025 16:30:22.104 信息 [main] org.apache.catalina.core.StandardService.startInternal 正在启动服务[Catalina]
25-Mar-2025 16:30:22.104 信息 [main] org.apache.catalina.core.StandardEngine.startInternal 正在启动 Servlet 引擎：[Apache Tomcat/8.5.100]
25-Mar-2025 16:30:24.194 信息 [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars 至少有一个JAR被扫描用于TLD但尚未包含TLD。 为此记录器启用调试日志记录，以获取已扫描但未在其中找到TLD的完整JAR列表。 在扫描期间跳过不需要的JAR可以缩短启动时间和JSP编译时间。
log4j:WARN No appenders could be found for logger (org.springframework.web.context.ContextLoader).
log4j:WARN Please initialize the log4j system properly.
25-Mar-2025 16:30:29.292 信息 [main] org.apache.coyote.AbstractProtocol.start 开始协议处理句柄["http-nio-8080"]
25-Mar-2025 16:30:29.304 信息 [main] org.apache.catalina.startup.Catalina.start Server startup in 7218 ms
25-Mar-2025 16:34:13.787 信息 [main] org.apache.catalina.core.StandardServer.await 通过关闭端口接收到有效的关闭命令。正在停止服务器实例。
25-Mar-2025 16:34:13.787 信息 [main] org.apache.coyote.AbstractProtocol.pause 暂停ProtocolHandler["http-nio-8080"]
25-Mar-2025 16:34:13.788 信息 [main] org.apache.catalina.core.StandardService.stopInternal 正在停止服务[Catalina]
25-Mar-2025 16:34:13.804 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc Web应用程序 [app] 注册了JDBC驱动程序 [com.mysql.cj.jdbc.Driver]，但在Web应用程序停止时无法注销它。 为防止内存泄漏，JDBC驱动程序已被强制取消注册。
25-Mar-2025 16:34:13.805 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Abandoned connection cleanup thread]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:144)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)]
25-Mar-2025 16:34:13.805 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[MessageServiceImpl]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Thread.sleep(Native Method)
 com.fingard.app.delegate.core.service.impl.MessageServiceImpl$1.run(MessageServiceImpl.java:106)]
25-Mar-2025 16:34:13.806 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Timer-0]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.util.TimerThread.mainLoop(Timer.java:552)
 java.util.TimerThread.run(Timer.java:505)]
25-Mar-2025 16:34:13.814 信息 [main] org.apache.coyote.AbstractProtocol.stop 正在停止ProtocolHandler ["http-nio-8080"]
25-Mar-2025 16:34:13.821 信息 [main] org.apache.coyote.AbstractProtocol.destroy 正在摧毁协议处理器 ["http-nio-8080"]
25-Mar-2025 16:34:17.018 警告 [main] org.apache.tomcat.util.digester.SetPropertiesRule.begin [SetPropertiesRule]{Server/Service/Engine/Host/Context} Setting property 'debug' to '0' did not find a matching property.
25-Mar-2025 16:34:17.034 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Server.服务器版本: Apache Tomcat/8.5.100
25-Mar-2025 16:34:17.034 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器构建:        Mar 19 2024 13:54:42 UTC
25-Mar-2025 16:34:17.034 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器版本号:      *********
25-Mar-2025 16:34:17.034 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 操作系统名称:      Linux
25-Mar-2025 16:34:17.034 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log OS.版本:           3.10.0-1160.el7.x86_64
25-Mar-2025 16:34:17.034 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 架构:              amd64
25-Mar-2025 16:34:17.034 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java 环境变量:     /home/<USER>/onekey-deploy/jdk1.8.0_181/jre
25-Mar-2025 16:34:17.035 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java虚拟机版本:    1.8.0_181-b13
25-Mar-2025 16:34:17.035 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.供应商:        Oracle Corporation
25-Mar-2025 16:34:17.035 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:34:17.035 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:34:17.035 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.config.file=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/conf/logging.properties
25-Mar-2025 16:34:17.035 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
25-Mar-2025 16:34:17.035 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djdk.tls.ephemeralDHKeySize=2048
25-Mar-2025 16:34:17.035 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
25-Mar-2025 16:34:17.038 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
25-Mar-2025 16:34:17.038 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dignore.endorsed.dirs=
25-Mar-2025 16:34:17.038 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.base=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:34:17.038 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.home=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:34:17.038 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.io.tmpdir=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/temp
25-Mar-2025 16:34:17.038 信息 [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent 在java.library.path:[/usr/java/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib]上找不到基于APR的Apache Tomcat本机库，该库允许在生产环境中获得最佳性能
25-Mar-2025 16:34:17.119 信息 [main] org.apache.coyote.AbstractProtocol.init 初始化协议处理器 ["http-nio-8080"]
25-Mar-2025 16:34:17.133 信息 [main] org.apache.catalina.startup.Catalina.load Initialization processed in 382 ms
25-Mar-2025 16:34:17.153 信息 [main] org.apache.catalina.core.StandardService.startInternal 正在启动服务[Catalina]
25-Mar-2025 16:34:17.153 信息 [main] org.apache.catalina.core.StandardEngine.startInternal 正在启动 Servlet 引擎：[Apache Tomcat/8.5.100]
25-Mar-2025 16:34:19.267 信息 [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars 至少有一个JAR被扫描用于TLD但尚未包含TLD。 为此记录器启用调试日志记录，以获取已扫描但未在其中找到TLD的完整JAR列表。 在扫描期间跳过不需要的JAR可以缩短启动时间和JSP编译时间。
log4j:WARN No appenders could be found for logger (org.springframework.web.context.ContextLoader).
log4j:WARN Please initialize the log4j system properly.
25-Mar-2025 16:34:24.985 信息 [main] org.apache.coyote.AbstractProtocol.start 开始协议处理句柄["http-nio-8080"]
25-Mar-2025 16:34:24.998 信息 [main] org.apache.catalina.startup.Catalina.start Server startup in 7865 ms
appserver:https://appbackserver:80/app
25-Mar-2025 16:41:47.472 信息 [main] org.apache.catalina.core.StandardServer.await 通过关闭端口接收到有效的关闭命令。正在停止服务器实例。
25-Mar-2025 16:41:47.473 信息 [main] org.apache.coyote.AbstractProtocol.pause 暂停ProtocolHandler["http-nio-8080"]
25-Mar-2025 16:41:47.475 信息 [main] org.apache.catalina.core.StandardService.stopInternal 正在停止服务[Catalina]
25-Mar-2025 16:41:47.500 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc Web应用程序 [app] 注册了JDBC驱动程序 [com.mysql.cj.jdbc.Driver]，但在Web应用程序停止时无法注销它。 为防止内存泄漏，JDBC驱动程序已被强制取消注册。
25-Mar-2025 16:41:47.500 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Abandoned connection cleanup thread]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:144)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)]
25-Mar-2025 16:41:47.501 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[MessageServiceImpl]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Thread.sleep(Native Method)
 com.fingard.app.delegate.core.service.impl.MessageServiceImpl$1.run(MessageServiceImpl.java:106)]
25-Mar-2025 16:41:47.501 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Timer-0]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.util.TimerThread.mainLoop(Timer.java:552)
 java.util.TimerThread.run(Timer.java:505)]
25-Mar-2025 16:41:47.509 信息 [main] org.apache.coyote.AbstractProtocol.stop 正在停止ProtocolHandler ["http-nio-8080"]
25-Mar-2025 16:41:47.519 信息 [main] org.apache.coyote.AbstractProtocol.destroy 正在摧毁协议处理器 ["http-nio-8080"]
25-Mar-2025 16:45:17.071 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Server.服务器版本: Apache Tomcat/8.5.100
25-Mar-2025 16:45:17.074 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器构建:        Mar 19 2024 13:54:42 UTC
25-Mar-2025 16:45:17.074 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器版本号:      *********
25-Mar-2025 16:45:17.074 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 操作系统名称:      Linux
25-Mar-2025 16:45:17.074 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log OS.版本:           3.10.0-1160.el7.x86_64
25-Mar-2025 16:45:17.074 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 架构:              amd64
25-Mar-2025 16:45:17.074 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java 环境变量:     /home/<USER>/onekey-deploy/jdk1.8.0_181/jre
25-Mar-2025 16:45:17.075 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java虚拟机版本:    1.8.0_181-b13
25-Mar-2025 16:45:17.075 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.供应商:        Oracle Corporation
25-Mar-2025 16:45:17.075 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:45:17.075 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:45:17.075 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.config.file=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/conf/logging.properties
25-Mar-2025 16:45:17.075 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
25-Mar-2025 16:45:17.075 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djdk.tls.ephemeralDHKeySize=2048
25-Mar-2025 16:45:17.075 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
25-Mar-2025 16:45:17.075 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
25-Mar-2025 16:45:17.075 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dignore.endorsed.dirs=
25-Mar-2025 16:45:17.075 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.base=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:45:17.075 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.home=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
25-Mar-2025 16:45:17.078 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.io.tmpdir=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/temp
25-Mar-2025 16:45:17.078 信息 [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent 在java.library.path:[/usr/java/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib]上找不到基于APR的Apache Tomcat本机库，该库允许在生产环境中获得最佳性能
25-Mar-2025 16:45:17.157 信息 [main] org.apache.coyote.AbstractProtocol.init 初始化协议处理器 ["http-nio-8080"]
25-Mar-2025 16:45:17.170 信息 [main] org.apache.catalina.startup.Catalina.load Initialization processed in 349 ms
25-Mar-2025 16:45:17.187 信息 [main] org.apache.catalina.core.StandardService.startInternal 正在启动服务[Catalina]
25-Mar-2025 16:45:17.187 信息 [main] org.apache.catalina.core.StandardEngine.startInternal 正在启动 Servlet 引擎：[Apache Tomcat/8.5.100]
25-Mar-2025 16:45:17.193 信息 [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory 把web 应用程序部署到目录 [/home/<USER>/FinancialBox/apache-tomcat-8.5.100/webapps/app]
25-Mar-2025 16:45:19.499 信息 [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars 至少有一个JAR被扫描用于TLD但尚未包含TLD。 为此记录器启用调试日志记录，以获取已扫描但未在其中找到TLD的完整JAR列表。 在扫描期间跳过不需要的JAR可以缩短启动时间和JSP编译时间。
log4j:WARN No appenders could be found for logger (org.springframework.web.context.ContextLoader).
log4j:WARN Please initialize the log4j system properly.
25-Mar-2025 16:45:25.205 信息 [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Web应用程序目录[/home/<USER>/FinancialBox/apache-tomcat-8.5.100/webapps/app]的部署已在[8,012]毫秒内完成
25-Mar-2025 16:45:25.207 信息 [main] org.apache.coyote.AbstractProtocol.start 开始协议处理句柄["http-nio-8080"]
25-Mar-2025 16:45:25.217 信息 [main] org.apache.catalina.startup.Catalina.start Server startup in 8047 ms
25-Mar-2025 16:51:00.550 信息 [main] org.apache.catalina.core.StandardServer.await 通过关闭端口接收到有效的关闭命令。正在停止服务器实例。
25-Mar-2025 16:51:00.550 信息 [main] org.apache.coyote.AbstractProtocol.pause 暂停ProtocolHandler["http-nio-8080"]
25-Mar-2025 16:51:00.553 信息 [main] org.apache.catalina.core.StandardService.stopInternal 正在停止服务[Catalina]
25-Mar-2025 16:51:00.575 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc Web应用程序 [app] 注册了JDBC驱动程序 [com.mysql.cj.jdbc.Driver]，但在Web应用程序停止时无法注销它。 为防止内存泄漏，JDBC驱动程序已被强制取消注册。
25-Mar-2025 16:51:00.575 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Abandoned connection cleanup thread]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:144)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)]
25-Mar-2025 16:51:00.576 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[MessageServiceImpl]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Thread.sleep(Native Method)
 com.fingard.app.delegate.core.service.impl.MessageServiceImpl$1.run(MessageServiceImpl.java:106)]
25-Mar-2025 16:51:00.576 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Timer-0]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.util.TimerThread.mainLoop(Timer.java:552)
 java.util.TimerThread.run(Timer.java:505)]
25-Mar-2025 16:51:00.581 信息 [main] org.apache.coyote.AbstractProtocol.stop 正在停止ProtocolHandler ["http-nio-8080"]
25-Mar-2025 16:51:00.585 信息 [main] org.apache.coyote.AbstractProtocol.destroy 正在摧毁协议处理器 ["http-nio-8080"]
26-Mar-2025 13:38:57.817 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Server.服务器版本: Apache Tomcat/8.5.100
26-Mar-2025 13:38:57.820 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器构建:        Mar 19 2024 13:54:42 UTC
26-Mar-2025 13:38:57.820 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器版本号:      *********
26-Mar-2025 13:38:57.820 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 操作系统名称:      Linux
26-Mar-2025 13:38:57.820 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log OS.版本:           3.10.0-1160.el7.x86_64
26-Mar-2025 13:38:57.820 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 架构:              amd64
26-Mar-2025 13:38:57.820 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java 环境变量:     /home/<USER>/onekey-deploy/jdk1.8.0_181/jre
26-Mar-2025 13:38:57.820 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java虚拟机版本:    1.8.0_181-b13
26-Mar-2025 13:38:57.821 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.供应商:        Oracle Corporation
26-Mar-2025 13:38:57.821 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
26-Mar-2025 13:38:57.821 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
26-Mar-2025 13:38:57.821 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.config.file=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/conf/logging.properties
26-Mar-2025 13:38:57.821 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
26-Mar-2025 13:38:57.821 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djdk.tls.ephemeralDHKeySize=2048
26-Mar-2025 13:38:57.821 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
26-Mar-2025 13:38:57.821 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
26-Mar-2025 13:38:57.821 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dignore.endorsed.dirs=
26-Mar-2025 13:38:57.821 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.base=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
26-Mar-2025 13:38:57.821 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.home=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
26-Mar-2025 13:38:57.821 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.io.tmpdir=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/temp
26-Mar-2025 13:38:57.824 信息 [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent 在java.library.path:[/usr/java/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib]上找不到基于APR的Apache Tomcat本机库，该库允许在生产环境中获得最佳性能
26-Mar-2025 13:38:57.903 信息 [main] org.apache.coyote.AbstractProtocol.init 初始化协议处理器 ["http-nio-8080"]
26-Mar-2025 13:38:57.916 信息 [main] org.apache.catalina.startup.Catalina.load Initialization processed in 352 ms
26-Mar-2025 13:38:57.933 信息 [main] org.apache.catalina.core.StandardService.startInternal 正在启动服务[Catalina]
26-Mar-2025 13:38:57.934 信息 [main] org.apache.catalina.core.StandardEngine.startInternal 正在启动 Servlet 引擎：[Apache Tomcat/8.5.100]
26-Mar-2025 13:38:57.940 信息 [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory 把web 应用程序部署到目录 [/home/<USER>/FinancialBox/apache-tomcat-8.5.100/webapps/app]
26-Mar-2025 13:39:00.240 信息 [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars 至少有一个JAR被扫描用于TLD但尚未包含TLD。 为此记录器启用调试日志记录，以获取已扫描但未在其中找到TLD的完整JAR列表。 在扫描期间跳过不需要的JAR可以缩短启动时间和JSP编译时间。
log4j:WARN No appenders could be found for logger (org.springframework.web.context.ContextLoader).
log4j:WARN Please initialize the log4j system properly.
26-Mar-2025 13:39:05.480 信息 [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Web应用程序目录[/home/<USER>/FinancialBox/apache-tomcat-8.5.100/webapps/app]的部署已在[7,539]毫秒内完成
26-Mar-2025 13:39:05.483 信息 [main] org.apache.coyote.AbstractProtocol.start 开始协议处理句柄["http-nio-8080"]
26-Mar-2025 13:39:05.493 信息 [main] org.apache.catalina.startup.Catalina.start Server startup in 7576 ms
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
26-Mar-2025 13:49:31.405 信息 [main] org.apache.catalina.core.StandardServer.await 通过关闭端口接收到有效的关闭命令。正在停止服务器实例。
26-Mar-2025 13:49:31.405 信息 [main] org.apache.coyote.AbstractProtocol.pause 暂停ProtocolHandler["http-nio-8080"]
26-Mar-2025 13:49:31.406 信息 [main] org.apache.catalina.core.StandardService.stopInternal 正在停止服务[Catalina]
26-Mar-2025 13:49:31.430 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc Web应用程序 [app] 注册了JDBC驱动程序 [com.mysql.cj.jdbc.Driver]，但在Web应用程序停止时无法注销它。 为防止内存泄漏，JDBC驱动程序已被强制取消注册。
26-Mar-2025 13:49:31.431 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Abandoned connection cleanup thread]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:144)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)]
26-Mar-2025 13:49:31.431 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[MessageServiceImpl]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Thread.sleep(Native Method)
 com.fingard.app.delegate.core.service.impl.MessageServiceImpl$1.run(MessageServiceImpl.java:106)]
26-Mar-2025 13:49:31.432 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Timer-0]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.util.TimerThread.mainLoop(Timer.java:552)
 java.util.TimerThread.run(Timer.java:505)]
26-Mar-2025 13:49:31.439 信息 [main] org.apache.coyote.AbstractProtocol.stop 正在停止ProtocolHandler ["http-nio-8080"]
26-Mar-2025 13:49:31.449 信息 [main] org.apache.coyote.AbstractProtocol.destroy 正在摧毁协议处理器 ["http-nio-8080"]
26-Mar-2025 13:49:38.655 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Server.服务器版本: Apache Tomcat/8.5.100
26-Mar-2025 13:49:38.658 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器构建:        Mar 19 2024 13:54:42 UTC
26-Mar-2025 13:49:38.659 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器版本号:      *********
26-Mar-2025 13:49:38.659 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 操作系统名称:      Linux
26-Mar-2025 13:49:38.659 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log OS.版本:           3.10.0-1160.el7.x86_64
26-Mar-2025 13:49:38.659 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 架构:              amd64
26-Mar-2025 13:49:38.659 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java 环境变量:     /home/<USER>/onekey-deploy/jdk1.8.0_181/jre
26-Mar-2025 13:49:38.659 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java虚拟机版本:    1.8.0_181-b13
26-Mar-2025 13:49:38.659 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.供应商:        Oracle Corporation
26-Mar-2025 13:49:38.659 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
26-Mar-2025 13:49:38.659 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
26-Mar-2025 13:49:38.659 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.config.file=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/conf/logging.properties
26-Mar-2025 13:49:38.659 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
26-Mar-2025 13:49:38.660 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djdk.tls.ephemeralDHKeySize=2048
26-Mar-2025 13:49:38.660 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
26-Mar-2025 13:49:38.660 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
26-Mar-2025 13:49:38.660 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dignore.endorsed.dirs=
26-Mar-2025 13:49:38.662 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.base=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
26-Mar-2025 13:49:38.663 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.home=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
26-Mar-2025 13:49:38.663 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.io.tmpdir=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/temp
26-Mar-2025 13:49:38.663 信息 [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent 在java.library.path:[/usr/java/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib]上找不到基于APR的Apache Tomcat本机库，该库允许在生产环境中获得最佳性能
26-Mar-2025 13:49:38.746 信息 [main] org.apache.coyote.AbstractProtocol.init 初始化协议处理器 ["http-nio-8080"]
26-Mar-2025 13:49:38.759 信息 [main] org.apache.catalina.startup.Catalina.load Initialization processed in 358 ms
26-Mar-2025 13:49:38.779 信息 [main] org.apache.catalina.core.StandardService.startInternal 正在启动服务[Catalina]
26-Mar-2025 13:49:38.779 信息 [main] org.apache.catalina.core.StandardEngine.startInternal 正在启动 Servlet 引擎：[Apache Tomcat/8.5.100]
26-Mar-2025 13:49:38.785 信息 [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory 把web 应用程序部署到目录 [/home/<USER>/FinancialBox/apache-tomcat-8.5.100/webapps/app]
26-Mar-2025 13:49:41.129 信息 [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars 至少有一个JAR被扫描用于TLD但尚未包含TLD。 为此记录器启用调试日志记录，以获取已扫描但未在其中找到TLD的完整JAR列表。 在扫描期间跳过不需要的JAR可以缩短启动时间和JSP编译时间。
log4j:WARN No appenders could be found for logger (org.springframework.web.context.ContextLoader).
log4j:WARN Please initialize the log4j system properly.
26-Mar-2025 13:49:45.896 信息 [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Web应用程序目录[/home/<USER>/FinancialBox/apache-tomcat-8.5.100/webapps/app]的部署已在[7,111]毫秒内完成
26-Mar-2025 13:49:45.899 信息 [main] org.apache.coyote.AbstractProtocol.start 开始协议处理句柄["http-nio-8080"]
26-Mar-2025 13:49:45.910 信息 [main] org.apache.catalina.startup.Catalina.start Server startup in 7150 ms
26-Mar-2025 13:57:32.734 信息 [main] org.apache.catalina.core.StandardServer.await 通过关闭端口接收到有效的关闭命令。正在停止服务器实例。
26-Mar-2025 13:57:32.735 信息 [main] org.apache.coyote.AbstractProtocol.pause 暂停ProtocolHandler["http-nio-8080"]
26-Mar-2025 13:57:32.738 信息 [main] org.apache.catalina.core.StandardService.stopInternal 正在停止服务[Catalina]
26-Mar-2025 13:57:32.759 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc Web应用程序 [app] 注册了JDBC驱动程序 [com.mysql.cj.jdbc.Driver]，但在Web应用程序停止时无法注销它。 为防止内存泄漏，JDBC驱动程序已被强制取消注册。
26-Mar-2025 13:57:32.759 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Abandoned connection cleanup thread]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:144)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)]
26-Mar-2025 13:57:32.760 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[MessageServiceImpl]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Thread.sleep(Native Method)
 com.fingard.app.delegate.core.service.impl.MessageServiceImpl$1.run(MessageServiceImpl.java:106)]
26-Mar-2025 13:57:32.760 警告 [localhost-startStop-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Timer-0]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.util.TimerThread.mainLoop(Timer.java:552)
 java.util.TimerThread.run(Timer.java:505)]
26-Mar-2025 13:57:32.766 信息 [main] org.apache.coyote.AbstractProtocol.stop 正在停止ProtocolHandler ["http-nio-8080"]
26-Mar-2025 13:57:32.771 信息 [main] org.apache.coyote.AbstractProtocol.destroy 正在摧毁协议处理器 ["http-nio-8080"]
27-Mar-2025 13:46:52.573 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Server.服务器版本: Apache Tomcat/8.5.100
27-Mar-2025 13:46:52.576 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器构建:        Mar 19 2024 13:54:42 UTC
27-Mar-2025 13:46:52.577 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器版本号:      *********
27-Mar-2025 13:46:52.577 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 操作系统名称:      Linux
27-Mar-2025 13:46:52.577 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log OS.版本:           3.10.0-1160.el7.x86_64
27-Mar-2025 13:46:52.577 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 架构:              amd64
27-Mar-2025 13:46:52.577 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java 环境变量:     /home/<USER>/onekey-deploy/jdk1.8.0_181/jre
27-Mar-2025 13:46:52.577 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java虚拟机版本:    1.8.0_181-b13
27-Mar-2025 13:46:52.577 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.供应商:        Oracle Corporation
27-Mar-2025 13:46:52.577 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
27-Mar-2025 13:46:52.577 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
27-Mar-2025 13:46:52.577 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.config.file=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/conf/logging.properties
27-Mar-2025 13:46:52.577 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
27-Mar-2025 13:46:52.577 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djdk.tls.ephemeralDHKeySize=2048
27-Mar-2025 13:46:52.578 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
27-Mar-2025 13:46:52.578 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
27-Mar-2025 13:46:52.578 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dignore.endorsed.dirs=
27-Mar-2025 13:46:52.578 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.base=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
27-Mar-2025 13:46:52.578 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.home=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
27-Mar-2025 13:46:52.578 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.io.tmpdir=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/temp
27-Mar-2025 13:46:52.578 信息 [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent 在java.library.path:[/usr/java/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib]上找不到基于APR的Apache Tomcat本机库，该库允许在生产环境中获得最佳性能
27-Mar-2025 13:46:52.665 信息 [main] org.apache.coyote.AbstractProtocol.init 初始化协议处理器 ["http-nio-8080"]
27-Mar-2025 13:46:52.679 信息 [main] org.apache.catalina.startup.Catalina.load Initialization processed in 390 ms
27-Mar-2025 13:46:52.700 信息 [main] org.apache.catalina.core.StandardService.startInternal 正在启动服务[Catalina]
27-Mar-2025 13:46:52.701 信息 [main] org.apache.catalina.core.StandardEngine.startInternal 正在启动 Servlet 引擎：[Apache Tomcat/8.5.100]
27-Mar-2025 13:46:52.707 信息 [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory 把web 应用程序部署到目录 [/home/<USER>/FinancialBox/apache-tomcat-8.5.100/webapps/app]
27-Mar-2025 13:46:55.147 信息 [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars 至少有一个JAR被扫描用于TLD但尚未包含TLD。 为此记录器启用调试日志记录，以获取已扫描但未在其中找到TLD的完整JAR列表。 在扫描期间跳过不需要的JAR可以缩短启动时间和JSP编译时间。
log4j:WARN No appenders could be found for logger (org.springframework.web.context.ContextLoader).
log4j:WARN Please initialize the log4j system properly.
27-Mar-2025 13:46:59.950 信息 [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Web应用程序目录[/home/<USER>/FinancialBox/apache-tomcat-8.5.100/webapps/app]的部署已在[7,242]毫秒内完成
27-Mar-2025 13:46:59.953 信息 [main] org.apache.coyote.AbstractProtocol.start 开始协议处理句柄["http-nio-8080"]
27-Mar-2025 13:46:59.966 信息 [main] org.apache.catalina.startup.Catalina.start Server startup in 7287 ms
appserver:https://appbackserver:80/app
27-Mar-2025 13:48:09.965 信息 [ContainerBackgroundProcessor[StandardEngine[Catalina]]] org.apache.catalina.users.MemoryUserDatabase.backgroundProcess 从更新的字眼[file:/home/<USER>/FinancialBox/apache-tomcat-8.5.100/conf/tomcat-users.xml]重新加载内存用户数据库
27-Mar-2025 13:48:09.966 严重 [ContainerBackgroundProcessor[StandardEngine[Catalina]]] org.apache.catalina.users.MemoryUserDatabase.open 指定用户数据库[conf/tomcat-users.xml]未找到
27-Mar-2025 13:48:09.966 警告 [ContainerBackgroundProcessor[StandardEngine[Catalina]]] org.apache.catalina.users.MemoryUserDatabase.open 关闭 [conf/tomcat-users.xml] 失败
	java.io.FileNotFoundException: /home/<USER>/FinancialBox/apache-tomcat-8.5.100/conf/tomcat-users.xml (没有那个文件或目录)
		at java.io.FileInputStream.open0(Native Method)
		at java.io.FileInputStream.open(FileInputStream.java:195)
		at java.io.FileInputStream.<init>(FileInputStream.java:138)
		at java.io.FileInputStream.<init>(FileInputStream.java:93)
		at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90)
		at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188)
		at org.apache.catalina.users.MemoryUserDatabase.open(MemoryUserDatabase.java:470)
		at org.apache.catalina.users.MemoryUserDatabase.backgroundProcess(MemoryUserDatabase.java:759)
		at org.apache.catalina.realm.UserDatabaseRealm.backgroundProcess(UserDatabaseRealm.java:136)
		at org.apache.catalina.realm.CombinedRealm.backgroundProcess(CombinedRealm.java:254)
		at org.apache.catalina.core.ContainerBase.backgroundProcess(ContainerBase.java:1094)
		at org.apache.catalina.core.ContainerBase$ContainerBackgroundProcessor.processChildren(ContainerBase.java:1333)
		at org.apache.catalina.core.ContainerBase$ContainerBackgroundProcessor.run(ContainerBase.java:1305)
		at java.lang.Thread.run(Thread.java:748)
27-Mar-2025 13:48:10.468 信息 [ContainerBackgroundProcessor[StandardEngine[Catalina]]] org.apache.catalina.startup.HostConfig.undeploy 正在取消部署上下文[/app]
27-Mar-2025 13:48:10.477 严重 [ContainerBackgroundProcessor[StandardEngine[Catalina]]] org.apache.catalina.session.StandardManager.stopInternal 卸载会话到持久存储的异常
	java.io.FileNotFoundException: /home/<USER>/FinancialBox/apache-tomcat-8.5.100/work/Catalina/localhost/app/SESSIONS.ser (没有那个文件或目录)
		at java.io.FileOutputStream.open0(Native Method)
		at java.io.FileOutputStream.open(FileOutputStream.java:270)
		at java.io.FileOutputStream.<init>(FileOutputStream.java:213)
		at java.io.FileOutputStream.<init>(FileOutputStream.java:101)
		at org.apache.catalina.session.StandardManager.doUnload(StandardManager.java:293)
		at org.apache.catalina.session.StandardManager.unload(StandardManager.java:259)
		at org.apache.catalina.session.StandardManager.stopInternal(StandardManager.java:374)
		at org.apache.catalina.util.LifecycleBase.stop(LifecycleBase.java:242)
		at org.apache.catalina.core.StandardContext.stopInternal(StandardContext.java:5123)
		at org.apache.catalina.util.LifecycleBase.stop(LifecycleBase.java:242)
		at org.apache.catalina.core.ContainerBase.removeChild(ContainerBase.java:796)
		at org.apache.catalina.startup.HostConfig.undeploy(HostConfig.java:1439)
		at org.apache.catalina.startup.HostConfig.checkResources(HostConfig.java:1368)
		at org.apache.catalina.startup.HostConfig.check(HostConfig.java:1621)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:309)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:114)
		at org.apache.catalina.core.ContainerBase.backgroundProcess(ContainerBase.java:1108)
		at org.apache.catalina.core.ContainerBase$ContainerBackgroundProcessor.processChildren(ContainerBase.java:1333)
		at org.apache.catalina.core.ContainerBase$ContainerBackgroundProcessor.processChildren(ContainerBase.java:1337)
		at org.apache.catalina.core.ContainerBase$ContainerBackgroundProcessor.run(ContainerBase.java:1305)
		at java.lang.Thread.run(Thread.java:748)
27-Mar-2025 13:48:10.485 警告 [ContainerBackgroundProcessor[StandardEngine[Catalina]]] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc Web应用程序 [app] 的JDBC驱动程序注销失败
	java.lang.IllegalStateException: java.io.FileNotFoundException: /home/<USER>/FinancialBox/apache-tomcat-8.5.100/webapps/app/WEB-INF/lib/zwdd-2.0.0.jar (没有那个文件或目录)
		at org.apache.catalina.webresources.AbstractSingleArchiveResourceSet.getArchiveEntry(AbstractSingleArchiveResourceSet.java:99)
		at org.apache.catalina.webresources.AbstractArchiveResourceSet.getResource(AbstractArchiveResourceSet.java:242)
		at org.apache.catalina.webresources.StandardRoot.getResourceInternal(StandardRoot.java:271)
		at org.apache.catalina.webresources.Cache.getResource(Cache.java:61)
		at org.apache.catalina.webresources.StandardRoot.getResource(StandardRoot.java:210)
		at org.apache.catalina.webresources.StandardRoot.getClassLoaderResource(StandardRoot.java:219)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResourceAsStream(WebappClassLoaderBase.java:1096)
		at org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc(WebappClassLoaderBase.java:1637)
		at org.apache.catalina.loader.WebappClassLoaderBase.clearReferences(WebappClassLoaderBase.java:1583)
		at org.apache.catalina.loader.WebappClassLoaderBase.stop(WebappClassLoaderBase.java:1522)
		at org.apache.catalina.loader.WebappLoader.stopInternal(WebappLoader.java:435)
		at org.apache.catalina.util.LifecycleBase.stop(LifecycleBase.java:242)
		at org.apache.catalina.core.StandardContext.stopInternal(StandardContext.java:5164)
		at org.apache.catalina.util.LifecycleBase.stop(LifecycleBase.java:242)
		at org.apache.catalina.core.ContainerBase.removeChild(ContainerBase.java:796)
		at org.apache.catalina.startup.HostConfig.undeploy(HostConfig.java:1439)
		at org.apache.catalina.startup.HostConfig.checkResources(HostConfig.java:1368)
		at org.apache.catalina.startup.HostConfig.check(HostConfig.java:1621)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:309)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:114)
		at org.apache.catalina.core.ContainerBase.backgroundProcess(ContainerBase.java:1108)
		at org.apache.catalina.core.ContainerBase$ContainerBackgroundProcessor.processChildren(ContainerBase.java:1333)
		at org.apache.catalina.core.ContainerBase$ContainerBackgroundProcessor.processChildren(ContainerBase.java:1337)
		at org.apache.catalina.core.ContainerBase$ContainerBackgroundProcessor.run(ContainerBase.java:1305)
		at java.lang.Thread.run(Thread.java:748)
	Caused by: java.io.FileNotFoundException: /home/<USER>/FinancialBox/apache-tomcat-8.5.100/webapps/app/WEB-INF/lib/zwdd-2.0.0.jar (没有那个文件或目录)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:256)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:241)
		at org.apache.catalina.webresources.AbstractArchiveResourceSet.openJarFile(AbstractArchiveResourceSet.java:291)
		at org.apache.catalina.webresources.AbstractSingleArchiveResourceSet.getArchiveEntry(AbstractSingleArchiveResourceSet.java:95)
		... 24 more
27-Mar-2025 13:48:10.486 警告 [ContainerBackgroundProcessor[StandardEngine[Catalina]]] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Abandoned connection cleanup thread]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:144)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)]
27-Mar-2025 13:48:10.486 警告 [ContainerBackgroundProcessor[StandardEngine[Catalina]]] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[MessageServiceImpl]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Thread.sleep(Native Method)
 com.fingard.app.delegate.core.service.impl.MessageServiceImpl$1.run(MessageServiceImpl.java:106)]
27-Mar-2025 13:48:10.487 警告 [ContainerBackgroundProcessor[StandardEngine[Catalina]]] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Timer-0]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.util.TimerThread.mainLoop(Timer.java:552)
 java.util.TimerThread.run(Timer.java:505)]
27-Mar-2025 13:48:10.488 警告 [ContainerBackgroundProcessor[StandardEngine[Catalina]]] org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks 检查ThreadLocal引用失败，web应用程序：[app]
	java.lang.IllegalStateException: java.io.FileNotFoundException: /home/<USER>/FinancialBox/apache-tomcat-8.5.100/webapps/app/WEB-INF/lib/zwdd-2.0.0.jar (没有那个文件或目录)
		at org.apache.catalina.webresources.AbstractSingleArchiveResourceSet.getArchiveEntry(AbstractSingleArchiveResourceSet.java:99)
		at org.apache.catalina.webresources.AbstractArchiveResourceSet.getResource(AbstractArchiveResourceSet.java:242)
		at org.apache.catalina.webresources.StandardRoot.getResourceInternal(StandardRoot.java:271)
		at org.apache.catalina.webresources.Cache.getResource(Cache.java:61)
		at org.apache.catalina.webresources.StandardRoot.getResource(StandardRoot.java:210)
		at org.apache.catalina.webresources.StandardRoot.getClassLoaderResource(StandardRoot.java:219)
		at org.apache.catalina.loader.WebappClassLoaderBase.findClassInternal(WebappClassLoaderBase.java:2227)
		at org.apache.catalina.loader.WebappClassLoaderBase.findClass(WebappClassLoaderBase.java:817)
		at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1302)
		at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1150)
		at java.lang.Class.getDeclaringClass0(Native Method)
		at java.lang.Class.getDeclaringClass(Class.java:1235)
		at java.lang.Class.getEnclosingClass(Class.java:1277)
		at java.lang.Class.getCanonicalName(Class.java:1392)
		at org.apache.catalina.loader.WebappClassLoaderBase.getPrettyClassName(WebappClassLoaderBase.java:1983)
		at org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalMapForLeaks(WebappClassLoaderBase.java:1944)
		at org.apache.catalina.loader.WebappClassLoaderBase.checkThreadLocalsForLeaks(WebappClassLoaderBase.java:1889)
		at org.apache.catalina.loader.WebappClassLoaderBase.clearReferences(WebappClassLoaderBase.java:1595)
		at org.apache.catalina.loader.WebappClassLoaderBase.stop(WebappClassLoaderBase.java:1522)
		at org.apache.catalina.loader.WebappLoader.stopInternal(WebappLoader.java:435)
		at org.apache.catalina.util.LifecycleBase.stop(LifecycleBase.java:242)
		at org.apache.catalina.core.StandardContext.stopInternal(StandardContext.java:5164)
		at org.apache.catalina.util.LifecycleBase.stop(LifecycleBase.java:242)
		at org.apache.catalina.core.ContainerBase.removeChild(ContainerBase.java:796)
		at org.apache.catalina.startup.HostConfig.undeploy(HostConfig.java:1439)
		at org.apache.catalina.startup.HostConfig.checkResources(HostConfig.java:1368)
		at org.apache.catalina.startup.HostConfig.check(HostConfig.java:1621)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:309)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:114)
		at org.apache.catalina.core.ContainerBase.backgroundProcess(ContainerBase.java:1108)
		at org.apache.catalina.core.ContainerBase$ContainerBackgroundProcessor.processChildren(ContainerBase.java:1333)
		at org.apache.catalina.core.ContainerBase$ContainerBackgroundProcessor.processChildren(ContainerBase.java:1337)
		at org.apache.catalina.core.ContainerBase$ContainerBackgroundProcessor.run(ContainerBase.java:1305)
		at java.lang.Thread.run(Thread.java:748)
	Caused by: java.io.FileNotFoundException: /home/<USER>/FinancialBox/apache-tomcat-8.5.100/webapps/app/WEB-INF/lib/zwdd-2.0.0.jar (没有那个文件或目录)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:225)
		at java.util.zip.ZipFile.<init>(ZipFile.java:155)
		at java.util.jar.JarFile.<init>(JarFile.java:166)
		at java.util.jar.JarFile.<init>(JarFile.java:130)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:256)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:241)
		at org.apache.catalina.webresources.AbstractArchiveResourceSet.openJarFile(AbstractArchiveResourceSet.java:291)
		at org.apache.catalina.webresources.AbstractSingleArchiveResourceSet.getArchiveEntry(AbstractSingleArchiveResourceSet.java:95)
		... 33 more
27-Mar-2025 13:48:11.213 信息 [Abandoned connection cleanup thread] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading 非法访问：此Web应用程序实例已停止。无法加载[]。为了调试以及终止导致非法访问的线程，将抛出以下堆栈跟踪。
	java.lang.IllegalStateException: 非法访问：此Web应用程序实例已停止。无法加载[]。为了调试以及终止导致非法访问的线程，将抛出以下堆栈跟踪。
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1358)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:990)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkContextClassLoaders(AbandonedConnectionCleanupThread.java:96)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:69)
		at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
		at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
		at java.lang.Thread.run(Thread.java:748)
27-Mar-2025 13:48:18.380 信息 [MessageServiceImpl] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading 非法访问：此Web应用程序实例已停止。无法加载[org.hibernate.exception.JDBCExceptionHelper]。为了调试以及终止导致非法访问的线程，将抛出以下堆栈跟踪。
	java.lang.IllegalStateException: 非法访问：此Web应用程序实例已停止。无法加载[org.hibernate.exception.JDBCExceptionHelper]。为了调试以及终止导致非法访问的线程，将抛出以下堆栈跟踪。
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1358)
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForClassLoading(WebappClassLoaderBase.java:1346)
		at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1183)
		at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1150)
		at org.hibernate.jdbc.ConnectionManager.openConnection(ConnectionManager.java:449)
		at org.hibernate.jdbc.ConnectionManager.getConnection(ConnectionManager.java:167)
		at org.hibernate.jdbc.AbstractBatcher.prepareQueryStatement(AbstractBatcher.java:161)
		at org.hibernate.loader.Loader.prepareQueryStatement(Loader.java:1573)
		at org.hibernate.loader.Loader.doQuery(Loader.java:696)
		at org.hibernate.loader.Loader.doQueryAndInitializeNonLazyCollections(Loader.java:259)
		at org.hibernate.loader.Loader.doList(Loader.java:2228)
		at org.hibernate.loader.Loader.listIgnoreQueryCache(Loader.java:2125)
		at org.hibernate.loader.Loader.list(Loader.java:2120)
		at org.hibernate.loader.hql.QueryLoader.list(QueryLoader.java:401)
		at org.hibernate.hql.ast.QueryTranslatorImpl.list(QueryTranslatorImpl.java:361)
		at org.hibernate.engine.query.HQLQueryPlan.performList(HQLQueryPlan.java:196)
		at org.hibernate.impl.SessionImpl.list(SessionImpl.java:1148)
		at org.hibernate.impl.QueryImpl.list(QueryImpl.java:102)
		at org.springframework.orm.hibernate3.HibernateTemplate$30.doInHibernate(HibernateTemplate.java:921)
		at org.springframework.orm.hibernate3.HibernateTemplate$30.doInHibernate(HibernateTemplate.java:1)
		at org.springframework.orm.hibernate3.HibernateTemplate.doExecute(HibernateTemplate.java:406)
		at org.springframework.orm.hibernate3.HibernateTemplate.executeWithNativeSession(HibernateTemplate.java:374)
		at org.springframework.orm.hibernate3.HibernateTemplate.find(HibernateTemplate.java:912)
		at org.springframework.orm.hibernate3.HibernateTemplate.find(HibernateTemplate.java:904)
		at com.fingard.app.delegate.core.dao.impl.CommonDaoImpl.findQueryList(CommonDaoImpl.java:119)
		at com.fingard.app.delegate.core.service.impl.MessageServiceImpl$1.run(MessageServiceImpl.java:57)
Exception in thread "MessageServiceImpl" java.lang.NoClassDefFoundError: org/hibernate/exception/JDBCExceptionHelper
	at org.hibernate.jdbc.ConnectionManager.openConnection(ConnectionManager.java:449)
	at org.hibernate.jdbc.ConnectionManager.getConnection(ConnectionManager.java:167)
	at org.hibernate.jdbc.AbstractBatcher.prepareQueryStatement(AbstractBatcher.java:161)
	at org.hibernate.loader.Loader.prepareQueryStatement(Loader.java:1573)
	at org.hibernate.loader.Loader.doQuery(Loader.java:696)
	at org.hibernate.loader.Loader.doQueryAndInitializeNonLazyCollections(Loader.java:259)
	at org.hibernate.loader.Loader.doList(Loader.java:2228)
	at org.hibernate.loader.Loader.listIgnoreQueryCache(Loader.java:2125)
	at org.hibernate.loader.Loader.list(Loader.java:2120)
	at org.hibernate.loader.hql.QueryLoader.list(QueryLoader.java:401)
	at org.hibernate.hql.ast.QueryTranslatorImpl.list(QueryTranslatorImpl.java:361)
	at org.hibernate.engine.query.HQLQueryPlan.performList(HQLQueryPlan.java:196)
	at org.hibernate.impl.SessionImpl.list(SessionImpl.java:1148)
	at org.hibernate.impl.QueryImpl.list(QueryImpl.java:102)
	at org.springframework.orm.hibernate3.HibernateTemplate$30.doInHibernate(HibernateTemplate.java:921)
	at org.springframework.orm.hibernate3.HibernateTemplate$30.doInHibernate(HibernateTemplate.java:1)
	at org.springframework.orm.hibernate3.HibernateTemplate.doExecute(HibernateTemplate.java:406)
	at org.springframework.orm.hibernate3.HibernateTemplate.executeWithNativeSession(HibernateTemplate.java:374)
	at org.springframework.orm.hibernate3.HibernateTemplate.find(HibernateTemplate.java:912)
	at org.springframework.orm.hibernate3.HibernateTemplate.find(HibernateTemplate.java:904)
	at com.fingard.app.delegate.core.dao.impl.CommonDaoImpl.findQueryList(CommonDaoImpl.java:119)
	at com.fingard.app.delegate.core.service.impl.MessageServiceImpl$1.run(MessageServiceImpl.java:57)
Caused by: java.lang.ClassNotFoundException: 非法访问：此Web应用程序实例已停止。无法加载[org.hibernate.exception.JDBCExceptionHelper]。为了调试以及终止导致非法访问的线程，将抛出以下堆栈跟踪。
	at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForClassLoading(WebappClassLoaderBase.java:1348)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1183)
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1150)
	... 22 more
Caused by: java.lang.IllegalStateException: 非法访问：此Web应用程序实例已停止。无法加载[org.hibernate.exception.JDBCExceptionHelper]。为了调试以及终止导致非法访问的线程，将抛出以下堆栈跟踪。
	at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1358)
	at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForClassLoading(WebappClassLoaderBase.java:1346)
	... 24 more
27-Mar-2025 13:48:30.495 信息 [ContainerBackgroundProcessor[StandardEngine[Catalina]]] org.apache.catalina.users.MemoryUserDatabase.backgroundProcess 从更新的字眼[file:/home/<USER>/FinancialBox/apache-tomcat-8.5.100/conf/tomcat-users.xml]重新加载内存用户数据库
27-Mar-2025 13:48:30.497 信息 [localhost-startStop-2] org.apache.catalina.startup.HostConfig.deployDirectory 把web 应用程序部署到目录 [/home/<USER>/FinancialBox/apache-tomcat-8.5.100/webapps/app]
27-Mar-2025 13:48:32.372 信息 [localhost-startStop-2] org.apache.jasper.servlet.TldScanner.scanJars 至少有一个JAR被扫描用于TLD但尚未包含TLD。 为此记录器启用调试日志记录，以获取已扫描但未在其中找到TLD的完整JAR列表。 在扫描期间跳过不需要的JAR可以缩短启动时间和JSP编译时间。
log4j:WARN No appenders could be found for logger (org.springframework.web.context.ContextLoader).
log4j:WARN Please initialize the log4j system properly.
27-Mar-2025 13:48:36.870 信息 [localhost-startStop-2] org.apache.catalina.startup.HostConfig.deployDirectory Web应用程序目录[/home/<USER>/FinancialBox/apache-tomcat-8.5.100/webapps/app]的部署已在[6,373]毫秒内完成
27-Mar-2025 13:54:24.315 信息 [main] org.apache.catalina.core.StandardServer.await 通过关闭端口接收到有效的关闭命令。正在停止服务器实例。
27-Mar-2025 13:54:24.315 信息 [main] org.apache.coyote.AbstractProtocol.pause 暂停ProtocolHandler["http-nio-8080"]
27-Mar-2025 13:54:24.317 信息 [main] org.apache.catalina.core.StandardService.stopInternal 正在停止服务[Catalina]
27-Mar-2025 13:54:24.340 警告 [localhost-startStop-3] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc Web应用程序 [app] 注册了JDBC驱动程序 [com.mysql.cj.jdbc.Driver]，但在Web应用程序停止时无法注销它。 为防止内存泄漏，JDBC驱动程序已被强制取消注册。
27-Mar-2025 13:54:24.341 警告 [localhost-startStop-3] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Abandoned connection cleanup thread]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:144)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:70)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:748)]
27-Mar-2025 13:54:24.341 警告 [localhost-startStop-3] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[MessageServiceImpl]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Thread.sleep(Native Method)
 com.fingard.app.delegate.core.service.impl.MessageServiceImpl$1.run(MessageServiceImpl.java:106)]
27-Mar-2025 13:54:24.342 警告 [localhost-startStop-3] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads Web应用程序[app]似乎启动了一个名为[Timer-1]的线程，但未能停止它。这很可能会造成内存泄漏。线程的堆栈跟踪：[
 java.lang.Object.wait(Native Method)
 java.util.TimerThread.mainLoop(Timer.java:552)
 java.util.TimerThread.run(Timer.java:505)]
27-Mar-2025 13:54:24.348 信息 [main] org.apache.coyote.AbstractProtocol.stop 正在停止ProtocolHandler ["http-nio-8080"]
27-Mar-2025 13:54:24.362 信息 [main] org.apache.coyote.AbstractProtocol.destroy 正在摧毁协议处理器 ["http-nio-8080"]
27-Mar-2025 13:55:45.240 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Server.服务器版本: Apache Tomcat/8.5.100
27-Mar-2025 13:55:45.243 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器构建:        Mar 19 2024 13:54:42 UTC
27-Mar-2025 13:55:45.243 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 服务器版本号:      *********
27-Mar-2025 13:55:45.243 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 操作系统名称:      Linux
27-Mar-2025 13:55:45.243 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log OS.版本:           3.10.0-1160.el7.x86_64
27-Mar-2025 13:55:45.243 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 架构:              amd64
27-Mar-2025 13:55:45.244 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java 环境变量:     /home/<USER>/onekey-deploy/jdk1.8.0_181/jre
27-Mar-2025 13:55:45.244 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log Java虚拟机版本:    1.8.0_181-b13
27-Mar-2025 13:55:45.244 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log JVM.供应商:        Oracle Corporation
27-Mar-2025 13:55:45.244 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
27-Mar-2025 13:55:45.244 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:     /home/<USER>/FinancialBox/apache-tomcat-8.5.100
27-Mar-2025 13:55:45.244 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.config.file=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/conf/logging.properties
27-Mar-2025 13:55:45.244 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
27-Mar-2025 13:55:45.244 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djdk.tls.ephemeralDHKeySize=2048
27-Mar-2025 13:55:45.244 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
27-Mar-2025 13:55:45.244 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
27-Mar-2025 13:55:45.244 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dignore.endorsed.dirs=
27-Mar-2025 13:55:45.244 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.base=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
27-Mar-2025 13:55:45.245 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Dcatalina.home=/home/<USER>/FinancialBox/apache-tomcat-8.5.100
27-Mar-2025 13:55:45.245 信息 [main] org.apache.catalina.startup.VersionLoggerListener.log 命令行参数：       -Djava.io.tmpdir=/home/<USER>/FinancialBox/apache-tomcat-8.5.100/temp
27-Mar-2025 13:55:45.245 信息 [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent 在java.library.path:[/usr/java/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib]上找不到基于APR的Apache Tomcat本机库，该库允许在生产环境中获得最佳性能
27-Mar-2025 13:55:45.328 信息 [main] org.apache.coyote.AbstractProtocol.init 初始化协议处理器 ["http-nio-8080"]
27-Mar-2025 13:55:45.340 信息 [main] org.apache.catalina.startup.Catalina.load Initialization processed in 367 ms
27-Mar-2025 13:55:45.357 信息 [main] org.apache.catalina.core.StandardService.startInternal 正在启动服务[Catalina]
27-Mar-2025 13:55:45.358 信息 [main] org.apache.catalina.core.StandardEngine.startInternal 正在启动 Servlet 引擎：[Apache Tomcat/8.5.100]
27-Mar-2025 13:55:45.364 信息 [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory 把web 应用程序部署到目录 [/home/<USER>/FinancialBox/apache-tomcat-8.5.100/webapps/app]
27-Mar-2025 13:55:47.492 信息 [localhost-startStop-1] org.apache.jasper.servlet.TldScanner.scanJars 至少有一个JAR被扫描用于TLD但尚未包含TLD。 为此记录器启用调试日志记录，以获取已扫描但未在其中找到TLD的完整JAR列表。 在扫描期间跳过不需要的JAR可以缩短启动时间和JSP编译时间。
log4j:WARN No appenders could be found for logger (org.springframework.web.context.ContextLoader).
log4j:WARN Please initialize the log4j system properly.
27-Mar-2025 13:55:52.571 信息 [localhost-startStop-1] org.apache.catalina.startup.HostConfig.deployDirectory Web应用程序目录[/home/<USER>/FinancialBox/apache-tomcat-8.5.100/webapps/app]的部署已在[7,206]毫秒内完成
27-Mar-2025 13:55:52.574 信息 [main] org.apache.coyote.AbstractProtocol.start 开始协议处理句柄["http-nio-8080"]
27-Mar-2025 13:55:52.587 信息 [main] org.apache.catalina.startup.Catalina.start Server startup in 7246 ms
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
Exception in thread "MessageServiceImpl" org.springframework.dao.DataAccessResourceFailureException: could not execute query; nested exception is org.hibernate.exception.JDBCConnectionException: could not execute query
	at org.springframework.orm.hibernate3.SessionFactoryUtils.convertHibernateAccessException(SessionFactoryUtils.java:625)
	at org.springframework.orm.hibernate3.HibernateAccessor.convertHibernateAccessException(HibernateAccessor.java:412)
	at org.springframework.orm.hibernate3.HibernateTemplate.doExecute(HibernateTemplate.java:411)
	at org.springframework.orm.hibernate3.HibernateTemplate.executeWithNativeSession(HibernateTemplate.java:374)
	at org.springframework.orm.hibernate3.HibernateTemplate.find(HibernateTemplate.java:912)
	at org.springframework.orm.hibernate3.HibernateTemplate.find(HibernateTemplate.java:904)
	at com.fingard.app.delegate.core.dao.impl.CommonDaoImpl.findQueryList(CommonDaoImpl.java:119)
	at com.fingard.app.delegate.core.service.impl.MessageServiceImpl$1.run(MessageServiceImpl.java:57)
Caused by: org.hibernate.exception.JDBCConnectionException: could not execute query
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:97)
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:66)
	at org.hibernate.loader.Loader.doList(Loader.java:2231)
	at org.hibernate.loader.Loader.listIgnoreQueryCache(Loader.java:2125)
	at org.hibernate.loader.Loader.list(Loader.java:2120)
	at org.hibernate.loader.hql.QueryLoader.list(QueryLoader.java:401)
	at org.hibernate.hql.ast.QueryTranslatorImpl.list(QueryTranslatorImpl.java:361)
	at org.hibernate.engine.query.HQLQueryPlan.performList(HQLQueryPlan.java:196)
	at org.hibernate.impl.SessionImpl.list(SessionImpl.java:1148)
	at org.hibernate.impl.QueryImpl.list(QueryImpl.java:102)
	at org.springframework.orm.hibernate3.HibernateTemplate$30.doInHibernate(HibernateTemplate.java:921)
	at org.springframework.orm.hibernate3.HibernateTemplate$30.doInHibernate(HibernateTemplate.java:1)
	at org.springframework.orm.hibernate3.HibernateTemplate.doExecute(HibernateTemplate.java:406)
	... 5 more
Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 938,501 milliseconds ago.  The last packet sent successfully to the server was 928,501 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:172)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:960)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeQuery(ClientPreparedStatement.java:1019)
	at com.mchange.v2.c3p0.impl.NewProxyPreparedStatement.executeQuery(NewProxyPreparedStatement.java:1471)
	at org.hibernate.jdbc.AbstractBatcher.getResultSet(AbstractBatcher.java:208)
	at org.hibernate.loader.Loader.getResultSet(Loader.java:1808)
	at org.hibernate.loader.Loader.doQuery(Loader.java:697)
	at org.hibernate.loader.Loader.doQueryAndInitializeNonLazyCollections(Loader.java:259)
	at org.hibernate.loader.Loader.doList(Loader.java:2228)
	... 15 more
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 938,501 milliseconds ago.  The last packet sent successfully to the server was 928,501 milliseconds ago.
	at sun.reflect.GeneratedConstructorAccessor75.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:59)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:103)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:149)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:165)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:563)
	at com.mysql.cj.protocol.a.NativeProtocol.checkErrorMessage(NativeProtocol.java:735)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:674)
	at com.mysql.cj.protocol.a.NativeProtocol.sendQueryPacket(NativeProtocol.java:966)
	at com.mysql.cj.NativeSession.execSQL(NativeSession.java:1165)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:937)
	... 22 more
Caused by: java.net.SocketException: 没有到主机的路由 (Read failed)
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mysql.cj.protocol.ReadAheadInputStream.fill(ReadAheadInputStream.java:107)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:150)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:557)
	... 27 more
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
09-Apr-2025 18:12:51.062 信息 [http-nio-8080-exec-10] org.apache.coyote.http11.Http11Processor.service 解析 HTTP 请求 header 错误
 注意：HTTP请求解析错误的进一步发生将记录在DEBUG级别。
	java.lang.IllegalArgumentException: 在请求目标中找到无效字符[/ibis2fi2.do?<script>document.cookie=%22testayya=9180;%22</script> ]。有效字符在RFC 7230和RFC 3986中定义
		at org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:497)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:492)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:934)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1690)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.lang.Thread.run(Thread.java:748)
09-Apr-2025 18:13:04.791 信息 [http-nio-8080-exec-8] org.apache.coyote.http11.Http11Processor.service 解析 HTTP 请求 header 错误
 注意：HTTP请求解析错误的进一步发生将记录在DEBUG级别。
	java.lang.IllegalArgumentException: 在方法名称[0x160x030x010x020x000x010x000x010xfc0x030x030x18s0xfa0xffY0xc20x940xc00xb10x9e1V0x1bf0xb6V0xd00x17)W;(0xd5G0xdf0xd70xeb0xd7i0xf00x920xa50x000x000xe40xc000xc0,0xc0(0xc0$0xc00x140xc00x0a0x000xa50x000xa30x000xa10x000x9f0x00k0x00j0x00i0x00h0x0090x0080x0070x0060x000x880x000x870x000x860x000x850xc00x190x000xa70x00m0x00:0x000x890xc020xc0.0xc0*0xc0&0xc00x0f0xc00x050x000x9d0x00=0x0050x000x840xc0/0xc0+0xc0'0xc0#0xc00x130xc00x090x000xa40x000xa20x000xa00x000x9e0x00g0x00@0x00?0x00>0x0030x0020x0010x0000x000x9a0x000x990x000x980x000x970x00E0x00D0x00C0x00B0xc00x180x000xa60x00l0x0040x000x9b0x00F0xc010xc0-0xc0)0xc0%0xc00x0e0xc00x040x000x9c0x00<0x00/0x000x960x00A0x000x070xc00x110xc00x070xc00x160x000x180xc00x0c0xc00x020x000x050x000x040xc00x120xc00x080x000x160x000x130x000x100x000x0d0xc00x170x000x1b0xc00x0d0xc00x030x000x0a0x000x150x000x120x000x0f0x000x0c0x000x1a0x000x090x000x140x000x110x000x190x000x080x000x060x000x170x000x030x000xff0x020x010x000x000xee0x000x0b0x000x040x030x000x010x020x000x0a0x000x1c0x000x1a0x000x170x000x190x000x1c0x000x1b0x000x180x000x1a0x000x160x000x0e0x000x0d0x000x0b0x000x0c0x000x090x000x0a0x00#0x000x000x000x0d0x00 ]中发现无效的字符串, HTTP 方法名必须是有效的符号.
		at org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:422)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:492)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:934)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1690)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.lang.Thread.run(Thread.java:748)
09-Apr-2025 18:13:23.965 信息 [http-nio-8080-exec-9] org.apache.coyote.http11.Http11Processor.service 解析 HTTP 请求 header 错误
 注意：HTTP请求解析错误的进一步发生将记录在DEBUG级别。
	java.lang.IllegalArgumentException: 在请求目标中找到无效字符[/2fa01plc.nsf?<meta%20http-equiv=Set-Cookie%20content=%22testcgzl=1614%22> ]。有效字符在RFC 7230和RFC 3986中定义
		at org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:497)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:492)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:934)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1690)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.lang.Thread.run(Thread.java:748)
09-Apr-2025 18:13:23.970 信息 [http-nio-8080-exec-8] org.apache.coyote.http11.Http11Processor.service 解析 HTTP 请求 header 错误
 注意：HTTP请求解析错误的进一步发生将记录在DEBUG级别。
	java.lang.IllegalArgumentException: 在请求目标中找到无效字符[/2fa01plc.dll?<meta%20http-equiv=Set-Cookie%20content=%22testcgzl=1614%22> ]。有效字符在RFC 7230和RFC 3986中定义
		at org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:497)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:492)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:934)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1690)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.lang.Thread.run(Thread.java:748)
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
appserver:https://appbackserver:80/app
