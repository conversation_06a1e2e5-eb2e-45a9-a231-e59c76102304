{"data": [{"node": "10.136.98.127", "name": "ZJLwebSrv", "modify": true, "group": [{"name": "system", "status": true, "entries": [{"target": "Dec-SystemCheck_Os_Info", "currentValues": [{"locKey": "Linux amd64"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "01010000"}, {"target": "Dec-SystemCheck_Server_Time", "currentValues": [{"locKey": "2025-04-13 11:00:00"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "0102000"}, {"target": "Dec-SystemCheck_TimeZone", "currentValues": [{"locKey": "中国标准时间 Asia/Shanghai"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "01030000"}, {"target": "Dec-SystemCheck_Glibc_Version", "currentValues": [{"locKey": "ldd (GNU libc) 2.17\nCopyright (C) 2012 Free Software Foundation, Inc.\nThis is free software; see the source for copying conditions.  There is NO\nwarranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.\n由 <PERSON> 和 <PERSON> 编写。\n"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "01040000"}, {"target": "Dec-SystemCheck_Hostname", "currentValues": [{"locKey": "ZJLwebSrv"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "01050000"}, {"target": "Dec-SystemCheck_Hosts", "currentValues": [{"locKey": "127.0.0.1   localhost localhost.localdomain localhost4 localhost4.localdomain4\n::1         localhost localhost.localdomain localhost6 localhost6.localdomain6\n10.136.98.127 ZJLwebSrv"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "01060000"}, {"target": "Dec-SystemCheck_Os_Cpu", "currentValues": [{"locKey": "2.4Ghz"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "01070001"}, {"target": "Dec-SystemCheck_Os_Core", "currentValues": [{"locKey": "18"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "01080001"}, {"target": "Dec-SystemCheck_Os_Memory", "currentValues": [{"locKey": "62.8GB"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "01090001"}, {"target": "Dec-SystemCheck_Swap", "currentValues": [{"locKey": "61.5GB"}], "status": 1, "recommends": [{"locKey": "Dec-System<PERSON>heck_Recommend_Swap"}], "changeAble": false, "changeValue": null, "index": "01100021"}, {"target": "Dec-<PERSON><PERSON><PERSON>ck_Os_Memory_Used", "currentValues": [{"locKey": "42.4GB"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "01110000"}, {"target": "Dec-<PERSON><PERSON><PERSON>ck_Os_Memory_Free", "currentValues": [{"locKey": "0.3GB"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "01120000"}, {"target": "Dec-SystemCheck_Cpu_Name", "currentValues": [{"locKey": "Intel(R) Xeon(R) Silver 4314 CPU @ 2.40GHz\n"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "01130000"}, {"target": "Dec-SystemCheck_Font_Encoding", "currentValues": [{"locKey": "zh_CN.UTF-8\n"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "01140000"}, {"target": "Dec-SystemCheck_Font_Pack", "currentValues": [{"locKey": "zh_CN\nzh_CN.gb18030\nzh_CN.gb2312\nzh_CN.gbk\nzh_CN.utf8\n"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "01150000"}]}, {"name": "runtime", "status": true, "entries": [{"target": "Dec-SystemCheck_Start_Duration", "currentValues": [{"locKey": "11946ms"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "02010000"}, {"target": "Dec-SystemCheck_User_Dir", "currentValues": [{"locKey": "/home/<USER>/bin"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "02030000"}, {"target": "Dec-SystemCheck_Source_File_Size", "currentValues": [{"locKey": "Dec-SystemCheck_Value_Source_File_Size", "params": ["plugins", "400M"]}, {"locKey": "Dec-SystemCheck_Value_Source_File_Size", "params": ["lib", "360M"]}, {"locKey": "Dec-SystemCheck_Value_Source_File_Size", "params": ["assets", "48M"]}, {"locKey": "Dec-SystemCheck_Value_Source_File_Size", "params": ["reportlets", "45M"]}, {"locKey": "Dec-SystemCheck_Value_Source_File_Size", "params": ["assist", "38M"]}, {"locKey": "Dec-SystemCheck_Value_Source_File_Size", "params": ["embed", "9M"]}, {"locKey": "Dec-SystemCheck_Value_Source_File_Size", "params": ["treasures", "3M"]}, {"locKey": "Dec-SystemCheck_Value_Source_File_Size", "params": ["local", "2M"]}, {"locKey": "Dec-SystemCheck_Value_Source_File_Size", "params": ["temp", "0M"]}, {"locKey": "Dec-SystemCheck_Value_Source_File_Size", "params": ["resources", "0M"]}, {"locKey": "Dec-SystemCheck_Value_Source_File_Size", "params": ["config", "0M"]}, {"locKey": "Dec-SystemCheck_Value_Source_File_Size", "params": ["classes", "0M"]}, {"locKey": "Dec-SystemCheck_Value_Source_File_Size", "params": ["customLib", "0M"]}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "02040001"}, {"target": "Dec-SystemCheck_Plugin_Info", "currentValues": [{"locKey": "Dec-SystemCheck_Value_Plugin_Info", "params": ["HTML5移动端展现", "11.0.892"]}, {"locKey": "Dec-SystemCheck_Value_Plugin_Enabled"}, {"locKey": "Dec-SystemCheck_Value_Plugin_Info", "params": ["FineVis数据可视化", "2.9.1"]}, {"locKey": "Dec-SystemCheck_Value_Plugin_Enabled"}, {"locKey": "Dec-SystemCheck_Value_Plugin_Info", "params": ["数据预警", "1.7.3"]}, {"locKey": "Dec-SystemCheck_Value_Plugin_Enabled"}, {"locKey": "Dec-SystemCheck_Value_Plugin_Info", "params": ["扩展图表", "4.6.16"]}, {"locKey": "Dec-SystemCheck_Value_Plugin_Enabled"}, {"locKey": "Dec-SystemCheck_Value_Plugin_Info", "params": ["数据门户", "1.12.0"]}, {"locKey": "Dec-SystemCheck_Value_Plugin_Enabled"}, {"locKey": "Dec-SystemCheck_Value_Plugin_Info", "params": ["模板助手", "1.2.28"]}, {"locKey": "Dec-SystemCheck_Value_Plugin_Enabled"}, {"locKey": "Dec-SystemCheck_Value_Plugin_Info", "params": ["Html解析", "1.0.7"]}, {"locKey": "Dec-SystemCheck_Value_Plugin_Enabled"}, {"locKey": "Dec-SystemCheck_Value_Plugin_Info", "params": ["系统运维", "3.2.1"]}, {"locKey": "Dec-SystemCheck_Value_Plugin_Enabled"}, {"locKey": "Dec-SystemCheck_Value_Plugin_Info", "params": ["云端运维", "3.32.0.20241202"]}, {"locKey": "Dec-SystemCheck_Value_Plugin_Enabled"}, {"locKey": "Dec-SystemCheck_Value_Plugin_Info", "params": ["系统检查", "1.4.9"]}, {"locKey": "Dec-SystemCheck_Value_Plugin_Enabled"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "02050000"}, {"target": "Dec-SystemCheck_Register_Info", "currentValues": [{"locKey": "Dec-SystemCheck_Value_Register_Success"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "02060000"}, {"target": "Dec-SystemCheck_Web_Xml", "currentValues": [{"locKey": "Dec-SystemCheck_Value_No"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "02070000"}, {"target": "Dec-SystemCheck_Server_Info", "currentValues": [{"locKey": "Apache Tomcat/9.0.89"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "02080000"}, {"target": "Dec-SystemCheck_Temp_Dir", "currentValues": [{"locKey": "/home/<USER>/temp[495579M(484.0G) available]"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "02110000"}, {"target": "Dec-SystemCheck_Jar_Version", "currentValues": [{"locKey": "Fine-Core_Module_Name"}, {"locKey": " 11.0(11.0.29) Build#persist-2024.08.27.10.31.32.908"}, {"locKey": "Fine-<PERSON><PERSON><PERSON><PERSON>_Module_Name"}, {"locKey": " 11.0(4.1.11) Build#persist-2024.08.15.17.35.09.473"}, {"locKey": "Fine-Dec_Module_Name"}, {"locKey": " 11.0(11.0.29) Build#persist-2024.08.27.10.34.27.882"}, {"locKey": "Fine-Engine_Report_Module_Name"}, {"locKey": " 11.0(11.0.29) Build#persist-2024.08.27.10.44.48.678"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "02090000"}]}, {"name": "port", "status": true, "entries": [{"target": "Dec-SystemCheck_Websocket_Port", "currentValues": [], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "03010000"}, {"target": "Dec-SystemCheck_Down_Operation_Port", "currentValues": [{"locKey": "Dec-SystemCheck_Reasonable"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "03030001"}]}, {"name": "disk", "status": true, "entries": [{"target": "Dec-<PERSON><PERSON><PERSON><PERSON>_Seq_Read", "currentValues": [{"locKey": "[/home/<USER>/temp]:记录了1024+0 的读入记录了1024+0 的写出1073741824字节(1.1 GB)已复制，0.266962 秒，4.0 GB/秒"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "05010000"}, {"target": "Dec-SystemCheck_Seq_Write", "currentValues": [{"locKey": "[/home/<USER>/temp]:记录了1024+0 的读入记录了1024+0 的写出1073741824字节(1.1 GB)已复制，2.29613 秒，468 MB/秒"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "05020000"}, {"target": "Dec-SystemCheck_File_Create", "currentValues": [{"locKey": "[/home/<USER>/temp]:28409.09 nums per second"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "05040000"}, {"target": "Dec-SystemCheck_Disk_Space", "currentValues": [{"locKey": "[/home/<USER>/temp]:644.1GB"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "05060001"}, {"target": "Dec-SystemCheck_Disk_Space_Used", "currentValues": [{"locKey": "[/home/<USER>/temp]:160.1GB"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "05070000"}, {"target": "Dec-SystemCheck_Disk_Space_Free", "currentValues": [{"locKey": "[/home/<USER>/temp]:484.0GB"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "05080002"}, {"target": "Dec-SystemCheck_Seq_Read_Direct", "currentValues": [{"locKey": "[/home/<USER>/temp]:记录了1024+0 的读入记录了1024+0 的写出1073741824字节(1.1 GB)已复制，2.2198 秒，484 MB/秒"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "05090000"}, {"target": "Dec-SystemCheck_Seq_Write_Direct", "currentValues": [{"locKey": "[/home/<USER>/temp]:记录了1024+0 的读入记录了1024+0 的写出1073741824字节(1.1 GB)已复制，5.28809 秒，203 MB/秒"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "05100000"}, {"target": "Dec-SystemCheck_File_Delete", "currentValues": [{"locKey": "[/home/<USER>/temp]:52356.02 nums per second"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "05050000"}]}, {"name": "deploy", "status": true, "entries": [{"target": "Dec-SystemCheck_Process_User", "currentValues": [{"locKey": "root"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "06010000"}, {"target": "Dec-SystemCheck_Dir_Permission", "currentValues": [{"locKey": "Dec-SystemCheck_Value_File_Permission_Read_Write", "params": ["/root/.FineReport110"]}, {"locKey": "Dec-SystemCheck_Value_File_Permission_Read_Write", "params": ["/home/<USER>/webapps/webroot/WEB-INF/assist"]}, {"locKey": "Dec-SystemCheck_Value_File_Permission_Read_Write", "params": ["/home/<USER>/webapps/webroot/WEB-INF/resources"]}, {"locKey": "Dec-SystemCheck_Value_File_Permission_Read_Write", "params": ["/home/<USER>/webapps/webroot/WEB-INF/assets"]}, {"locKey": "Dec-SystemCheck_Value_File_Permission_Read_Write", "params": ["/home/<USER>/webapps/webroot/WEB-INF/embed"]}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "06020000"}, {"target": "Dec-SystemCheck_Multiply_Process", "currentValues": [{"locKey": "Dec-SystemCheck_Reasonable"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "06030002"}]}, {"name": "env", "status": true, "entries": [{"target": "Dec-SystemCheck_Vm_Max_Map_Count", "currentValues": [{"locKey": "655360"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "07010002"}, {"target": "Dec-SystemCheck_Open_Files", "currentValues": [{"locKey": "65536"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "07020002"}, {"target": "Dec-SystemCheck_Vm_Overcommit_Memory", "currentValues": [{"locKey": "0"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "07030002"}, {"target": "Dec-SystemCheck_Vm_Overcommit_Ratio", "currentValues": [{"locKey": "50"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "07040002"}, {"target": "Dec-SystemCheck_Glibc", "currentValues": [{"locKey": "Dec-SystemCheck_Not_Configured"}], "status": 1, "recommends": [{"locKey": "Dec-SystemCheck_Recommend_Glibc"}], "changeAble": true, "changeValue": "MALLOC_ARENA_MAX=1", "index": "07050002"}]}, {"name": "jvm", "status": true, "entries": [{"target": "Dec-SystemCheck-BI-key_nio", "currentValues": [{"locKey": "3.8GB"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "08010102"}, {"target": "Dec-SystemCheck-BI-key_fineRead", "currentValues": [{"locKey": "2.0GB"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "08020102"}, {"target": "Dec-SystemCheck-BI-key_fineWrite", "currentValues": [{"locKey": "1.0GB"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "08030102"}, {"target": "Dec-<PERSON><PERSON><PERSON><PERSON>_Garbage_Collector", "currentValues": [{"locKey": "PS"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "08040002"}, {"target": "Dec-SystemCheck_System_GC", "currentValues": [{"locKey": "Dec-SystemCheck_Not_Configured"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "08050102"}, {"target": "Dec-SystemCheck_JDK_Version", "currentValues": [{"locKey": "1.8.0_402"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "08060002"}, {"target": "Dec-<PERSON><PERSON><PERSON><PERSON>_Headless", "currentValues": [{"locKey": "Dec-SystemCheck_Enabled"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "08070002"}, {"target": "Dec-SystemCheck-BI-key_dump", "currentValues": [{"locKey": "Dec-SystemCheck_Configured"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "08080002"}, {"target": "Dec-SystemCheck_bytecode_cutoff", "currentValues": [{"locKey": "Dec-SystemCheck_Not_Configured"}], "status": 1, "recommends": [{"locKey": "Dec-SystemCheck-recommend_bytecode_cutoff", "params": []}], "changeAble": true, "changeValue": "-XX:PerBytecodeRecompilationCutoff=-1", "index": "08090002"}, {"target": "Dec-SystemCheck_method_cutoff", "currentValues": [{"locKey": "Dec-SystemCheck_Not_Configured"}], "status": 1, "recommends": [{"locKey": "Dec-SystemCheck-recommend_method_cutoff", "params": []}], "changeAble": true, "changeValue": "-XX:PerMethodRecompilationCutoff=-1", "index": "08100002"}, {"target": "Dec-System<PERSON><PERSON><PERSON>_App_Deploy", "currentValues": [{"locKey": "Dec-SystemCheck_Value_No"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "08110022"}, {"target": "Dec-SystemCheck_Code_Cache", "currentValues": [{"locKey": "240.0m"}], "status": 1, "recommends": [{"locKey": "Dec-System<PERSON>heck_Recommend_Code_Cache"}], "changeAble": true, "changeValue": "-XX:ReservedCodeCacheSize=512m", "index": "08130002"}, {"target": "Dec-SystemCheck_User_Country", "currentValues": [{"locKey": "Dec-SystemCheck_Not_Configured"}], "status": 1, "recommends": [{"locKey": "Dec-SystemCheck_Recommend_User_Country"}], "changeAble": false, "changeValue": null, "index": "08140001"}, {"target": "Dec-SystemCheck_User_Language", "currentValues": [{"locKey": "Dec-SystemCheck_Not_Configured"}], "status": 1, "recommends": [{"locKey": "Dec-SystemCheck_Recommend_User_Language"}], "changeAble": false, "changeValue": null, "index": "08150001"}, {"target": "Dec-SystemCheck_Memory_Mapping", "currentValues": [{"locKey": "Dec-SystemCheck_Not_Configured"}], "status": 1, "recommends": [{"locKey": "Dec-System<PERSON><PERSON>ck_Recommend_Memory_Mapping"}], "changeAble": true, "changeValue": "-Dsun.zip.disableMemoryMapping=true", "index": "08160002"}, {"target": "Dec-SystemCheck_Xss", "currentValues": [{"locKey": "1024KB"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "08180102"}, {"target": "Dec-SystemCheck-BI-key_jdwp", "currentValues": [{"locKey": "Dec-SystemCheck_Not_Enabled"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "08190002"}, {"target": "Dec-SystemCheck-BI-key_xmx", "currentValues": [{"locKey": "4.0GB"}], "status": 1, "recommends": [{"locKey": "Dec-SystemCheck-BI-des_xmx", "params": ["42.1GB"]}], "changeAble": true, "changeValue": "42", "index": "08200102"}, {"target": "Dec-SystemCheck_Xms", "currentValues": [{"locKey": "4096m"}], "status": 0, "recommends": [], "changeAble": true, "changeValue": null, "index": "08210102"}, {"target": "Dec-SystemCheck_JDK_Location", "currentValues": [{"locKey": "/home/<USER>/jdk/"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "08220000"}, {"target": "Dec-SystemCheck_MetaSpace_Size", "currentValues": [{"locKey": "2048MB"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "08230102"}, {"target": "Dec-System<PERSON><PERSON><PERSON>_New_Ratio", "currentValues": [{"locKey": "2.00"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "08170102"}]}, {"name": "service_connect", "status": true, "entries": [{"target": "Dec-System<PERSON>heck_Fine_Db_Connect", "currentValues": [{"locKey": "Dec-SystemCheck_Reasonable"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "09030002"}]}, {"name": "business", "status": true, "entries": [{"target": "Dec-<PERSON><PERSON><PERSON><PERSON>_Fine_Db_Transfer", "currentValues": [{"locKey": "mysql"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "10010002"}, {"target": "Dec-SystemCheck_Log_Level", "currentValues": [{"locKey": "ERROR"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "10020002"}, {"target": "Dec-SystemCheck_Log_Clean", "currentValues": [{"locKey": "Dec-SystemCheck_Configured"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "10030002"}, {"target": "Dec-<PERSON><PERSON><PERSON><PERSON>_Back_Up_Memory", "currentValues": [{"locKey": "1024MB"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "10040001"}, {"target": "Dec-<PERSON><PERSON><PERSON><PERSON>_Back_Up_Number", "currentValues": [{"locKey": "5"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "10050001"}, {"target": "Dec-SystemCheck_Schedule_Task_Clean", "currentValues": [{"locKey": "Dec-SystemCheck_Reasonable"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "10060001"}, {"target": "Dec-<PERSON><PERSON><PERSON><PERSON>_Fine_Db_charset", "currentValues": [{"locKey": "utf8mb3_bin"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "10140000"}, {"target": "Dec-SystemCheck_Cluster_Config", "currentValues": [{"locKey": "Dec-SystemCheck_Value_Cluster_Config", "params": ["90", "8", "90"]}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "10150000"}, {"target": "Dec-SystemCheck_Ehcache_Disable_Cache", "currentValues": [{"locKey": "Dec-SystemCheck_Enabled"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "10170000"}, {"target": "Dec-SystemCheck_Sql_Time_Control", "currentValues": [{"locKey": "Dec-SystemCheck_Enabled"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "10180001"}, {"target": "Dec-SystemCheck_Cell_Control", "currentValues": [{"locKey": "Dec-SystemCheck_Enabled"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "10190001"}, {"target": "Dec-SystemCheck_Import_Cell_Control", "currentValues": [{"locKey": "Dec-SystemCheck_Enabled"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "10200001"}, {"target": "Dec-SystemCheck_Row_Control", "currentValues": [{"locKey": "Dec-SystemCheck_Enabled"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "10220001"}, {"target": "Dec-SystemCheck_Memory_Alarm", "currentValues": [{"locKey": "Dec-SystemCheck_Not_Enabled"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "10230000"}, {"target": "Dec-SystemCheck_Cloud_Operation", "currentValues": [{"locKey": "Dec-SystemCheck_Value_No"}], "status": 0, "recommends": [], "changeAble": false, "changeValue": null, "index": "10250000"}, {"target": "Dec-SystemCheck_Password_Strength", "currentValues": [{"locKey": "Dec-SystemCheck_Not_Configured"}], "status": 1, "recommends": [{"locKey": "Dec-System<PERSON>heck_Recommend_Password_Strength"}], "changeAble": false, "changeValue": null, "index": "10260001"}]}]}], "version": "1.0"}